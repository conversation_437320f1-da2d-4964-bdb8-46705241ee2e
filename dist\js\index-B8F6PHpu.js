const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/Index-iqIE3INL.js","js/chunk-L3uak9dD.js","js/chunk-D3Ns84uO.js","js/chunk-DJqUFuPP.js","js/chunk-Bthhypug.js","js/chunk-Bt4ub4Kz.js","js/chunk-CS50z45r.js","js/chunk-Bc9oLI49.js","js/chunk-8nFA7SZG.js","js/chunk-DylKGAq6.js","js/NotFound-B6a2Ckv1.js","js/PrivacyPolicy-D08PdUAt.js","js/AnalyticsProvider-y-YxUwtL.js","js/chunk-DzkTCSaD.js","js/BackToTop-CYkP7fYD.js","js/FluidGradientBackground-fz9tBY22.js","js/LazyScripts-Chw9cytQ.js","js/CookieConsent-DDWHkk1W.js"])))=>i.map(i=>d[i]);
var e,t,o=Object.defineProperty,a=(e,t,a)=>((e,t,a)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a)(e,"symbol"!=typeof t?t+"":t,a);import{j as r}from"./chunk-L3uak9dD.js";import{R as i,r as n,b as s,a as l,v as c,B as d,c as u,d as p}from"./chunk-D3Ns84uO.js";import{a as m,g}from"./chunk-DJqUFuPP.js";import{c as f,u as h,a as b,B as v,P as y,b as w,d as x,e as k,f as T,V as j,R as E,g as S,h as C,i as P,j as I,C as A,k as D}from"./chunk-Bt4ub4Kz.js";import{X as R,U as M,F as z,R as N,M as L,a as U,b as O}from"./chunk-Bc9oLI49.js";import{Q as B,a as V}from"./chunk-DylKGAq6.js";import{i as q,B as F,a as _,u as $}from"./chunk-CS50z45r.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}(),"undefined"!=typeof window&&(window.React=i,window.react=i),"undefined"!=typeof globalThis&&(globalThis.React=i,globalThis.react=i);var G,X={};var H=function(){if(G)return X;G=1;var e=m();return X.createRoot=e.createRoot,X.hydrateRoot=e.hydrateRoot,X}();const W={},Y=function(e,t,o){let a=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),r=(null==o?void 0:o.nonce)||(null==o?void 0:o.getAttribute("nonce"));a=e(t.map((e=>{if((e=function(e){return"/portfolio/"+e}(e))in W)return;W[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${o}`))return;const a=document.createElement("link");return a.rel=t?"stylesheet":"modulepreload",t||(a.as="script"),a.crossOrigin="",a.href=e,r&&a.setAttribute("nonce",r),document.head.appendChild(a),t?new Promise(((t,o)=>{a.addEventListener("load",t),a.addEventListener("error",(()=>o(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function r(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then((t=>{for(const e of t||[])"rejected"===e.status&&r(e.reason);return e().catch(r)}))};let K=0;const Q=new Map,J=e=>{if(Q.has(e))return;const t=setTimeout((()=>{Q.delete(e),oe({type:"REMOVE_TOAST",toastId:e})}),1e6);Q.set(e,t)},Z=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case"DISMISS_TOAST":{const{toastId:o}=t;return o?J(o):e.toasts.forEach((e=>{J(e.id)})),{...e,toasts:e.toasts.map((e=>e.id===o||void 0===o?{...e,open:!1}:e))}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))}}},ee=[];let te={toasts:[]};function oe(e){te=Z(te,e),ee.forEach((e=>{e(te)}))}function ae({...e}){const t=(K=(K+1)%Number.MAX_SAFE_INTEGER,K.toString()),o=()=>oe({type:"DISMISS_TOAST",toastId:t});return oe({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||o()}}}),{id:t,dismiss:o,update:e=>oe({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function re(e){const t=e+"CollectionProvider",[o,a]=f(t),[i,n]=o(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{const{scope:t,children:o}=e,a=s.useRef(null),n=s.useRef(new Map).current;return r.jsx(i,{scope:t,itemMap:n,collectionRef:a,children:o})};l.displayName=t;const c=e+"CollectionSlot",d=b(c),u=s.forwardRef(((e,t)=>{const{scope:o,children:a}=e,i=n(c,o),s=h(t,i.collectionRef);return r.jsx(d,{ref:s,children:a})}));u.displayName=c;const p=e+"CollectionItemSlot",m="data-radix-collection-item",g=b(p),v=s.forwardRef(((e,t)=>{const{scope:o,children:a,...i}=e,l=s.useRef(null),c=h(t,l),d=n(p,o);return s.useEffect((()=>(d.itemMap.set(l,{ref:l,...i}),()=>{d.itemMap.delete(l)}))),r.jsx(g,{[m]:"",ref:c,children:a})}));return v.displayName=p,[{Provider:l,Slot:u,ItemSlot:v},function(t){const o=n(e+"CollectionConsumer",t);return s.useCallback((()=>{const e=o.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(o.itemMap.values()).sort(((e,o)=>t.indexOf(e.ref.current)-t.indexOf(o.ref.current)))}),[o.collectionRef,o.itemMap])},a]}var ie="ToastProvider",[ne,se,le]=re("Toast"),[ce,de]=f("Toast",[le]),[ue,pe]=ce(ie),me=e=>{const{__scopeToast:t,label:o="Notification",duration:a=5e3,swipeDirection:i="right",swipeThreshold:s=50,children:l}=e,[c,d]=n.useState(null),[u,p]=n.useState(0),m=n.useRef(!1),g=n.useRef(!1);return o.trim()||console.error(`Invalid prop \`label\` supplied to \`${ie}\`. Expected non-empty \`string\`.`),r.jsx(ne.Provider,{scope:t,children:r.jsx(ue,{scope:t,label:o,duration:a,swipeDirection:i,swipeThreshold:s,toastCount:u,viewport:c,onViewportChange:d,onToastAdd:n.useCallback((()=>p((e=>e+1))),[]),onToastRemove:n.useCallback((()=>p((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:g,children:l})})};me.displayName=ie;var ge="ToastViewport",fe=["F8"],he="toast.viewportPause",be="toast.viewportResume",ve=n.forwardRef(((e,t)=>{const{__scopeToast:o,hotkey:a=fe,label:i="Notifications ({hotkey})",...s}=e,l=pe(ge,o),c=se(o),d=n.useRef(null),u=n.useRef(null),p=n.useRef(null),m=n.useRef(null),g=h(t,m,l.onViewportChange),f=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=l.toastCount>0;n.useEffect((()=>{const e=e=>{var t;0!==a.length&&a.every((t=>e[t]||e.code===t))&&(null==(t=m.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[a]),n.useEffect((()=>{const e=d.current,t=m.current;if(b&&e&&t){const o=()=>{if(!l.isClosePausedRef.current){const e=new CustomEvent(he);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},a=()=>{if(l.isClosePausedRef.current){const e=new CustomEvent(be);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},r=t=>{!e.contains(t.relatedTarget)&&a()},i=()=>{e.contains(document.activeElement)||a()};return e.addEventListener("focusin",o),e.addEventListener("focusout",r),e.addEventListener("pointermove",o),e.addEventListener("pointerleave",i),window.addEventListener("blur",o),window.addEventListener("focus",a),()=>{e.removeEventListener("focusin",o),e.removeEventListener("focusout",r),e.removeEventListener("pointermove",o),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",o),window.removeEventListener("focus",a)}}}),[b,l.isClosePausedRef]);const w=n.useCallback((({tabbingDirection:e})=>{const t=c().map((t=>{const o=t.ref.current,a=[o,...Ue(o)];return"forwards"===e?a:a.reverse()}));return("forwards"===e?t.reverse():t).flat()}),[c]);return n.useEffect((()=>{const e=m.current;if(e){const t=t=>{var o,a,r;const i=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!i){const i=document.activeElement,n=t.shiftKey;if(t.target===e&&n)return void(null==(o=u.current)||o.focus());const s=w({tabbingDirection:n?"backwards":"forwards"}),l=s.findIndex((e=>e===i));Oe(s.slice(l+1))?t.preventDefault():n?null==(a=u.current)||a.focus():null==(r=p.current)||r.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[c,w]),r.jsxs(v,{ref:d,role:"region","aria-label":i.replace("{hotkey}",f),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&r.jsx(we,{ref:u,onFocusFromOutsideViewport:()=>{Oe(w({tabbingDirection:"forwards"}))}}),r.jsx(ne.Slot,{scope:o,children:r.jsx(y.ol,{tabIndex:-1,...s,ref:g})}),b&&r.jsx(we,{ref:p,onFocusFromOutsideViewport:()=>{Oe(w({tabbingDirection:"backwards"}))}})]})}));ve.displayName=ge;var ye="ToastFocusProxy",we=n.forwardRef(((e,t)=>{const{__scopeToast:o,onFocusFromOutsideViewport:a,...i}=e,n=pe(ye,o);return r.jsx(j,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;const o=e.relatedTarget;!(null==(t=n.viewport)?void 0:t.contains(o))&&a()}})}));we.displayName=ye;var xe="Toast",ke=n.forwardRef(((e,t)=>{const{forceMount:o,open:a,defaultOpen:i,onOpenChange:n,...s}=e,[l,c]=w({prop:a,defaultProp:i??!0,onChange:n,caller:xe});return r.jsx(x,{present:o||l,children:r.jsx(Ee,{open:l,...s,ref:t,onClose:()=>c(!1),onPause:T(e.onPause),onResume:T(e.onResume),onSwipeStart:k(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:k(e.onSwipeMove,(e=>{const{x:t,y:o}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${o}px`)})),onSwipeCancel:k(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:k(e.onSwipeEnd,(e=>{const{x:t,y:o}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${o}px`),c(!1)}))})})}));ke.displayName=xe;var[Te,je]=ce(xe,{onClose(){}}),Ee=n.forwardRef(((e,t)=>{const{__scopeToast:o,type:a="foreground",duration:i,open:s,onClose:c,onEscapeKeyDown:d,onPause:u,onResume:p,onSwipeStart:m,onSwipeMove:g,onSwipeCancel:f,onSwipeEnd:b,...v}=e,w=pe(xe,o),[x,j]=n.useState(null),S=h(t,(e=>j(e))),C=n.useRef(null),P=n.useRef(null),I=i||w.duration,A=n.useRef(0),D=n.useRef(I),R=n.useRef(0),{onToastAdd:M,onToastRemove:z}=w,N=T((()=>{var e;(null==x?void 0:x.contains(document.activeElement))&&(null==(e=w.viewport)||e.focus()),c()})),L=n.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(R.current),A.current=(new Date).getTime(),R.current=window.setTimeout(N,e))}),[N]);n.useEffect((()=>{const e=w.viewport;if(e){const t=()=>{L(D.current),null==p||p()},o=()=>{const e=(new Date).getTime()-A.current;D.current=D.current-e,window.clearTimeout(R.current),null==u||u()};return e.addEventListener(he,o),e.addEventListener(be,t),()=>{e.removeEventListener(he,o),e.removeEventListener(be,t)}}}),[w.viewport,I,u,p,L]),n.useEffect((()=>{s&&!w.isClosePausedRef.current&&L(I)}),[s,I,w.isClosePausedRef,L]),n.useEffect((()=>(M(),()=>z())),[M,z]);const U=n.useMemo((()=>x?ze(x):null),[x]);return w.viewport?r.jsxs(r.Fragment,{children:[U&&r.jsx(Se,{__scopeToast:o,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:U}),r.jsx(Te,{scope:o,onClose:N,children:l.createPortal(r.jsx(ne.ItemSlot,{scope:o,children:r.jsx(E,{asChild:!0,onEscapeKeyDown:k(d,(()=>{w.isFocusedToastEscapeKeyDownRef.current||N(),w.isFocusedToastEscapeKeyDownRef.current=!1})),children:r.jsx(y.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":w.swipeDirection,...v,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:k(e.onKeyDown,(e=>{"Escape"===e.key&&(null==d||d(e.nativeEvent),e.nativeEvent.defaultPrevented||(w.isFocusedToastEscapeKeyDownRef.current=!0,N()))})),onPointerDown:k(e.onPointerDown,(e=>{0===e.button&&(C.current={x:e.clientX,y:e.clientY})})),onPointerMove:k(e.onPointerMove,(e=>{if(!C.current)return;const t=e.clientX-C.current.x,o=e.clientY-C.current.y,a=Boolean(P.current),r=["left","right"].includes(w.swipeDirection),i=["left","up"].includes(w.swipeDirection)?Math.min:Math.max,n=r?i(0,t):0,s=r?0:i(0,o),l="touch"===e.pointerType?10:2,c={x:n,y:s},d={originalEvent:e,delta:c};a?(P.current=c,Ne("toast.swipeMove",g,d,{discrete:!1})):Le(c,w.swipeDirection,l)?(P.current=c,Ne("toast.swipeStart",m,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(o)>l)&&(C.current=null)})),onPointerUp:k(e.onPointerUp,(e=>{const t=P.current,o=e.target;if(o.hasPointerCapture(e.pointerId)&&o.releasePointerCapture(e.pointerId),P.current=null,C.current=null,t){const o=e.currentTarget,a={originalEvent:e,delta:t};Le(t,w.swipeDirection,w.swipeThreshold)?Ne("toast.swipeEnd",b,a,{discrete:!0}):Ne("toast.swipeCancel",f,a,{discrete:!0}),o.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))})})}),w.viewport)})]}):null})),Se=e=>{const{__scopeToast:t,children:o,...a}=e,i=pe(xe,t),[s,l]=n.useState(!1),[c,d]=n.useState(!1);return function(e=()=>{}){const t=T(e);C((()=>{let e=0,o=0;return e=window.requestAnimationFrame((()=>o=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(o)}}),[t])}((()=>l(!0))),n.useEffect((()=>{const e=window.setTimeout((()=>d(!0)),1e3);return()=>window.clearTimeout(e)}),[]),c?null:r.jsx(S,{asChild:!0,children:r.jsx(j,{...a,children:s&&r.jsxs(r.Fragment,{children:[i.label," ",o]})})})},Ce=n.forwardRef(((e,t)=>{const{__scopeToast:o,...a}=e;return r.jsx(y.div,{...a,ref:t})}));Ce.displayName="ToastTitle";var Pe=n.forwardRef(((e,t)=>{const{__scopeToast:o,...a}=e;return r.jsx(y.div,{...a,ref:t})}));Pe.displayName="ToastDescription";var Ie="ToastAction",Ae=n.forwardRef(((e,t)=>{const{altText:o,...a}=e;return o.trim()?r.jsx(Me,{altText:o,asChild:!0,children:r.jsx(Re,{...a,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Ie}\`. Expected non-empty \`string\`.`),null)}));Ae.displayName=Ie;var De="ToastClose",Re=n.forwardRef(((e,t)=>{const{__scopeToast:o,...a}=e,i=je(De,o);return r.jsx(Me,{asChild:!0,children:r.jsx(y.button,{type:"button",...a,ref:t,onClick:k(e.onClick,i.onClose)})})}));Re.displayName=De;var Me=n.forwardRef(((e,t)=>{const{__scopeToast:o,altText:a,...i}=e;return r.jsx(y.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":a||void 0,...i,ref:t})}));function ze(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const o=e.ariaHidden||e.hidden||"none"===e.style.display,a=""===e.dataset.radixToastAnnounceExclude;if(!o)if(a){const o=e.dataset.radixToastAnnounceAlt;o&&t.push(o)}else t.push(...ze(e))}})),t}function Ne(e,t,o,{discrete:a}){const r=o.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:o});t&&r.addEventListener(e,t,{once:!0}),a?P(r,i):r.dispatchEvent(i)}var Le=(e,t,o=0)=>{const a=Math.abs(e.x),r=Math.abs(e.y),i=a>r;return"left"===t||"right"===t?i&&a>o:!i&&r>o};function Ue(e){const t=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)t.push(o.currentNode);return t}function Oe(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var Be=me,Ve=ve,qe=ke,Fe=Ce,_e=Pe,$e=Ae,Ge=Re;const Xe=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,He=I,We=(e,t)=>o=>{var a;if(null==(null==t?void 0:t.variants))return He(e,null==o?void 0:o.class,null==o?void 0:o.className);const{variants:r,defaultVariants:i}=t,n=Object.keys(r).map((e=>{const t=null==o?void 0:o[e],a=null==i?void 0:i[e];if(null===t)return null;const n=Xe(t)||Xe(a);return r[e][n]})),s=o&&Object.entries(o).reduce(((e,t)=>{let[o,a]=t;return void 0===a||(e[o]=a),e}),{}),l=null==t||null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce(((e,t)=>{let{class:o,className:a,...r}=t;return Object.entries(r).every((e=>{let[t,o]=e;return Array.isArray(o)?o.includes({...i,...s}[t]):{...i,...s}[t]===o}))?[...e,o,a]:e}),[]);return He(e,n,l,null==o?void 0:o.class,null==o?void 0:o.className)},Ye=e=>{const t=Ze(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{const o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),Ke(o,t)||Je(e)},getConflictingClassGroupIds:(e,t)=>{const r=o[e]||[];return t&&a[e]?[...r,...a[e]]:r}}},Ke=(e,t)=>{var o;if(0===e.length)return t.classGroupId;const a=e[0],r=t.nextPart.get(a),i=r?Ke(e.slice(1),r):void 0;if(i)return i;if(0===t.validators.length)return;const n=e.join("-");return null==(o=t.validators.find((({validator:e})=>e(n))))?void 0:o.classGroupId},Qe=/^\[(.+)\]$/,Je=e=>{if(Qe.test(e)){const t=Qe.exec(e)[1],o=null==t?void 0:t.substring(0,t.indexOf(":"));if(o)return"arbitrary.."+o}},Ze=e=>{const{theme:t,prefix:o}=e,a={nextPart:new Map,validators:[]};return at(Object.entries(e.classGroups),o).forEach((([e,o])=>{et(o,a,e,t)})),a},et=(e,t,o,a)=>{e.forEach((e=>{if("string"!=typeof e){if("function"==typeof e)return ot(e)?void et(e(a),t,o,a):void t.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach((([e,r])=>{et(r,tt(t,e),o,a)}))}else{(""===e?t:tt(t,e)).classGroupId=o}}))},tt=(e,t)=>{let o=e;return t.split("-").forEach((e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)})),o},ot=e=>e.isThemeGetter,at=(e,t)=>t?e.map((([e,o])=>[e,o.map((e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,o])=>[t+e,o]))):e))])):e,rt=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,o=new Map,a=new Map;const r=(r,i)=>{o.set(r,i),t++,t>e&&(t=0,a=o,o=new Map)};return{get(e){let t=o.get(e);return void 0!==t?t:void 0!==(t=a.get(e))?(r(e,t),t):void 0},set(e,t){o.has(e)?o.set(e,t):r(e,t)}}},it=e=>{const{separator:t,experimentalParseClassName:o}=e,a=1===t.length,r=t[0],i=t.length,n=e=>{const o=[];let n,s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===r&&(a||e.slice(u,u+i)===t)){o.push(e.slice(l,u)),l=u+i;continue}if("/"===c){n=u;continue}}"["===c?s++:"]"===c&&s--}const c=0===o.length?e:e.substring(l),d=c.startsWith("!");return{modifiers:o,hasImportantModifier:d,baseClassName:d?c.substring(1):c,maybePostfixModifierPosition:n&&n>l?n-l:void 0}};return o?e=>o({className:e,parseClassName:n}):n},nt=e=>{if(e.length<=1)return e;const t=[];let o=[];return e.forEach((e=>{"["===e[0]?(t.push(...o.sort(),e),o=[]):o.push(e)})),t.push(...o.sort()),t},st=/\s+/;function lt(){let e,t,o=0,a="";for(;o<arguments.length;)(e=arguments[o++])&&(t=ct(e))&&(a&&(a+=" "),a+=t);return a}const ct=e=>{if("string"==typeof e)return e;let t,o="";for(let a=0;a<e.length;a++)e[a]&&(t=ct(e[a]))&&(o&&(o+=" "),o+=t);return o};function dt(e,...t){let o,a,r,i=function(s){const l=t.reduce(((e,t)=>t(e)),e());return o=(e=>({cache:rt(e.cacheSize),parseClassName:it(e),...Ye(e)}))(l),a=o.cache.get,r=o.cache.set,i=n,n(s)};function n(e){const t=a(e);if(t)return t;const i=((e,t)=>{const{parseClassName:o,getClassGroupId:a,getConflictingClassGroupIds:r}=t,i=[],n=e.trim().split(st);let s="";for(let l=n.length-1;l>=0;l-=1){const e=n[l],{modifiers:t,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=o(e);let p=Boolean(u),m=a(p?d.substring(0,u):d);if(!m){if(!p){s=e+(s.length>0?" "+s:s);continue}if(m=a(d),!m){s=e+(s.length>0?" "+s:s);continue}p=!1}const g=nt(t).join(":"),f=c?g+"!":g,h=f+m;if(i.includes(h))continue;i.push(h);const b=r(m,p);for(let o=0;o<b.length;++o){const e=b[o];i.push(f+e)}s=e+(s.length>0?" "+s:s)}return s})(e,o);return r(e,i),i}return function(){return i(lt.apply(null,arguments))}}const ut=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},pt=/^\[(?:([a-z-]+):)?(.+)\]$/i,mt=/^\d+\/\d+$/,gt=new Set(["px","full","screen"]),ft=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ht=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,bt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,vt=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,yt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,wt=e=>kt(e)||gt.has(e)||mt.test(e),xt=e=>Nt(e,"length",Lt),kt=e=>Boolean(e)&&!Number.isNaN(Number(e)),Tt=e=>Nt(e,"number",kt),jt=e=>Boolean(e)&&Number.isInteger(Number(e)),Et=e=>e.endsWith("%")&&kt(e.slice(0,-1)),St=e=>pt.test(e),Ct=e=>ft.test(e),Pt=new Set(["length","size","percentage"]),It=e=>Nt(e,Pt,Ut),At=e=>Nt(e,"position",Ut),Dt=new Set(["image","url"]),Rt=e=>Nt(e,Dt,Bt),Mt=e=>Nt(e,"",Ot),zt=()=>!0,Nt=(e,t,o)=>{const a=pt.exec(e);return!!a&&(a[1]?"string"==typeof t?a[1]===t:t.has(a[1]):o(a[2]))},Lt=e=>ht.test(e)&&!bt.test(e),Ut=()=>!1,Ot=e=>vt.test(e),Bt=e=>yt.test(e),Vt=dt((()=>{const e=ut("colors"),t=ut("spacing"),o=ut("blur"),a=ut("brightness"),r=ut("borderColor"),i=ut("borderRadius"),n=ut("borderSpacing"),s=ut("borderWidth"),l=ut("contrast"),c=ut("grayscale"),d=ut("hueRotate"),u=ut("invert"),p=ut("gap"),m=ut("gradientColorStops"),g=ut("gradientColorStopPositions"),f=ut("inset"),h=ut("margin"),b=ut("opacity"),v=ut("padding"),y=ut("saturate"),w=ut("scale"),x=ut("sepia"),k=ut("skew"),T=ut("space"),j=ut("translate"),E=()=>["auto",St,t],S=()=>[St,t],C=()=>["",wt,xt],P=()=>["auto",kt,St],I=()=>["","0",St],A=()=>[kt,St];return{cacheSize:500,separator:":",theme:{colors:[zt],spacing:[wt,xt],blur:["none","",Ct,St],brightness:A(),borderColor:[e],borderRadius:["none","","full",Ct,St],borderSpacing:S(),borderWidth:C(),contrast:A(),grayscale:I(),hueRotate:A(),invert:I(),gap:S(),gradientColorStops:[e],gradientColorStopPositions:[Et,xt],inset:E(),margin:E(),opacity:A(),padding:S(),saturate:A(),scale:A(),sepia:I(),skew:A(),space:S(),translate:S()},classGroups:{aspect:[{aspect:["auto","square","video",St]}],container:["container"],columns:[{columns:[Ct]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",St]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",jt,St]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",St]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",jt,St]}],"grid-cols":[{"grid-cols":[zt]}],"col-start-end":[{col:["auto",{span:["full",jt,St]},St]}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":[zt]}],"row-start-end":[{row:["auto",{span:[jt,St]},St]}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",St]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",St]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[T]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[T]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",St,t]}],"min-w":[{"min-w":[St,t,"min","max","fit"]}],"max-w":[{"max-w":[St,t,"none","full","min","max","fit","prose",{screen:[Ct]},Ct]}],h:[{h:[St,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[St,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[St,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[St,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ct,xt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Tt]}],"font-family":[{font:[zt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",St]}],"line-clamp":[{"line-clamp":["none",kt,Tt]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",wt,St]}],"list-image":[{"list-image":["none",St]}],"list-style-type":[{list:["none","disc","decimal",St]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",wt,xt]}],"underline-offset":[{"underline-offset":["auto",wt,St]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",St]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",St]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",At]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",It]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Rt]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[wt,St]}],"outline-w":[{outline:[wt,xt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:C()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[wt,xt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ct,Mt]}],"shadow-color":[{shadow:[zt]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[o]}],brightness:[{brightness:[a]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Ct,St]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[o]}],"backdrop-brightness":[{"backdrop-brightness":[a]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",St]}],duration:[{duration:A()}],ease:[{ease:["linear","in","out","in-out",St]}],delay:[{delay:A()}],animate:[{animate:["none","spin","ping","pulse","bounce",St]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[jt,St]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",St]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",St]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",St]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[wt,xt,Tt]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}));function qt(...e){return Vt(I(e))}const Ft=Be,_t=n.forwardRef((({className:e,...t},o)=>r.jsx(Ve,{ref:o,className:qt("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t})));_t.displayName=Ve.displayName;const $t=We("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Gt=n.forwardRef((({className:e,variant:t,...o},a)=>r.jsx(qe,{ref:a,className:qt($t({variant:t}),e),...o})));Gt.displayName=qe.displayName;n.forwardRef((({className:e,...t},o)=>r.jsx($e,{ref:o,className:qt("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}))).displayName=$e.displayName;const Xt=n.forwardRef((({className:e,...t},o)=>r.jsx(Ge,{ref:o,className:qt("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:r.jsx(R,{className:"h-4 w-4"})})));Xt.displayName=Ge.displayName;const Ht=n.forwardRef((({className:e,...t},o)=>r.jsx(Fe,{ref:o,className:qt("text-sm font-semibold",e),...t})));Ht.displayName=Fe.displayName;const Wt=n.forwardRef((({className:e,...t},o)=>r.jsx(_e,{ref:o,className:qt("text-sm opacity-90",e),...t})));function Yt(){const{toasts:e}=function(){const[e,t]=n.useState(te);return n.useEffect((()=>(ee.push(t),()=>{const e=ee.indexOf(t);e>-1&&ee.splice(e,1)})),[e]),{...e,toast:ae,dismiss:e=>oe({type:"DISMISS_TOAST",toastId:e})}}();return r.jsxs(Ft,{children:[e.map((function({id:e,title:t,description:o,action:a,...i}){return r.jsxs(Gt,{...i,children:[r.jsxs("div",{className:"grid gap-1",children:[t&&r.jsx(Ht,{children:t}),o&&r.jsx(Wt,{children:o})]}),a,r.jsx(Xt,{})]},e)})),r.jsx(_t,{})]})}Wt.displayName=_e.displayName;const Kt=n.createContext(void 0),Qt=({children:e,defaultTheme:t="system",storageKey:o="portfolio-theme"})=>{const[a,i]=n.useState(t),[s,l]=n.useState("light"),[c,d]=n.useState(!0),u=()=>"undefined"!=typeof window&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",p=e=>"system"===e?u():e,m=e=>{const t=document.documentElement;t.classList.remove("light","dark"),t.classList.add(e),t.setAttribute("data-theme",e)};n.useEffect((()=>{if("undefined"!=typeof window){const e=localStorage.getItem(o),a=e&&["light","dark","system"].includes(e)?e:t;i(a);const r=p(a);l(r),m(r),d(!1)}}),[t,o]),n.useEffect((()=>{if("undefined"==typeof window)return;const e=window.matchMedia("(prefers-color-scheme: dark)"),t=()=>{if("system"===a){const e=u();l(e),m(e)}};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)}),[a]);const g={theme:a,setTheme:e=>{i(e),"undefined"!=typeof window&&localStorage.setItem(o,e);const t=p(e);l(t),m(t)},resolvedTheme:s,isLoading:c};return r.jsx(Kt.Provider,{value:g,children:e})};var Jt=Array(12).fill(0),Zt=({visible:e,className:t})=>s.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},s.createElement("div",{className:"sonner-spinner"},Jt.map(((e,t)=>s.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`}))))),eo=s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},s.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),to=s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},s.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),oo=s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},s.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),ao=s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},s.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ro=s.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},s.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),io=1,no=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach((t=>t(e)))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:o,...a}=e,r="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:io++,i=this.toasts.find((e=>e.id===r)),n=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(r)&&this.dismissedToasts.delete(r),i?this.toasts=this.toasts.map((t=>t.id===r?(this.publish({...t,...e,id:r,title:o}),{...t,...e,id:r,dismissible:n,title:o}):t)):this.addToast({title:o,...a,dismissible:n,id:r}),r},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach((e=>{this.subscribers.forEach((t=>t({id:e.id,dismiss:!0})))})),this.subscribers.forEach((t=>t({id:e,dismiss:!0}))),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let o;void 0!==t.loading&&(o=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let a,r=e instanceof Promise?e:e(),i=void 0!==o,n=r.then((async e=>{if(a=["resolve",e],s.isValidElement(e))i=!1,this.create({id:o,type:"default",message:e});else if(so(e)&&!e.ok){i=!1;let a="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,r="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:o,type:"error",message:a,description:r})}else if(void 0!==t.success){i=!1;let a="function"==typeof t.success?await t.success(e):t.success,r="function"==typeof t.description?await t.description(e):t.description;this.create({id:o,type:"success",message:a,description:r})}})).catch((async e=>{if(a=["reject",e],void 0!==t.error){i=!1;let a="function"==typeof t.error?await t.error(e):t.error,r="function"==typeof t.description?await t.description(e):t.description;this.create({id:o,type:"error",message:a,description:r})}})).finally((()=>{var e;i&&(this.dismiss(o),o=void 0),null==(e=t.finally)||e.call(t)})),l=()=>new Promise(((e,t)=>n.then((()=>"reject"===a[0]?t(a[1]):e(a[1]))).catch(t)));return"string"!=typeof o&&"number"!=typeof o?{unwrap:l}:Object.assign(o,{unwrap:l})},this.custom=(e,t)=>{let o=(null==t?void 0:t.id)||io++;return this.create({jsx:e(o),id:o,...t}),o},this.getActiveToasts=()=>this.toasts.filter((e=>!this.dismissedToasts.has(e.id))),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},so=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,lo=(e,t)=>{let o=(null==t?void 0:t.id)||io++;return no.addToast({title:e,...t,id:o}),o};function co(e){return void 0!==e.label}Object.assign(lo,{success:no.success,info:no.info,warning:no.warning,error:no.error,custom:no.custom,message:no.message,promise:no.promise,dismiss:no.dismiss,loading:no.loading},{getHistory:()=>no.toasts,getToasts:()=>no.getActiveToasts()}),function(e,{insertAt:t}={}){if("undefined"==typeof document)return;let o=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===t&&o.firstChild?o.insertBefore(a,o.firstChild):o.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}(':where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n');var uo=3,po=14;function mo(...e){return e.filter(Boolean).join(" ")}var go=e=>{var t,o,a,r,i,l,c,d,u,p,m;let{invert:g,toast:f,unstyled:h,interacting:b,setHeights:v,visibleToasts:y,heights:w,index:x,toasts:k,expanded:T,removeToast:j,defaultRichColors:E,closeButton:S,style:C,cancelButtonStyle:P,actionButtonStyle:I,className:A="",descriptionClassName:D="",duration:R,position:M,gap:z,loadingIcon:N,expandByDefault:L,classNames:U,icons:O,closeButtonAriaLabel:B="Close toast",pauseWhenPageIsHidden:V}=e,[q,F]=s.useState(null),[_,$]=s.useState(null),[G,X]=s.useState(!1),[H,W]=s.useState(!1),[Y,K]=s.useState(!1),[Q,J]=s.useState(!1),[Z,ee]=s.useState(!1),[te,oe]=s.useState(0),[ae,re]=s.useState(0),ie=s.useRef(f.duration||R||4e3),ne=s.useRef(null),se=s.useRef(null),le=0===x,ce=x+1<=y,de=f.type,ue=!1!==f.dismissible,pe=f.className||"",me=f.descriptionClassName||"",ge=s.useMemo((()=>w.findIndex((e=>e.toastId===f.id))||0),[w,f.id]),fe=s.useMemo((()=>{var e;return null!=(e=f.closeButton)?e:S}),[f.closeButton,S]),he=s.useMemo((()=>f.duration||R||4e3),[f.duration,R]),be=s.useRef(0),ve=s.useRef(0),ye=s.useRef(0),we=s.useRef(null),[xe,ke]=M.split("-"),Te=s.useMemo((()=>w.reduce(((e,t,o)=>o>=ge?e:e+t.height),0)),[w,ge]),je=(()=>{let[e,t]=s.useState(document.hidden);return s.useEffect((()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)}),[]),e})(),Ee=f.invert||g,Se="loading"===de;ve.current=s.useMemo((()=>ge*z+Te),[ge,Te]),s.useEffect((()=>{ie.current=he}),[he]),s.useEffect((()=>{X(!0)}),[]),s.useEffect((()=>{let e=se.current;if(e){let t=e.getBoundingClientRect().height;return re(t),v((e=>[{toastId:f.id,height:t,position:f.position},...e])),()=>v((e=>e.filter((e=>e.toastId!==f.id))))}}),[v,f.id]),s.useLayoutEffect((()=>{if(!G)return;let e=se.current,t=e.style.height;e.style.height="auto";let o=e.getBoundingClientRect().height;e.style.height=t,re(o),v((e=>e.find((e=>e.toastId===f.id))?e.map((e=>e.toastId===f.id?{...e,height:o}:e)):[{toastId:f.id,height:o,position:f.position},...e]))}),[G,f.title,f.description,v,f.id]);let Ce=s.useCallback((()=>{W(!0),oe(ve.current),v((e=>e.filter((e=>e.toastId!==f.id)))),setTimeout((()=>{j(f)}),200)}),[f,j,v,ve]);return s.useEffect((()=>{if(f.promise&&"loading"===de||f.duration===1/0||"loading"===f.type)return;let e;return T||b||V&&je?(()=>{if(ye.current<be.current){let e=(new Date).getTime()-be.current;ie.current=ie.current-e}ye.current=(new Date).getTime()})():ie.current!==1/0&&(be.current=(new Date).getTime(),e=setTimeout((()=>{var e;null==(e=f.onAutoClose)||e.call(f,f),Ce()}),ie.current)),()=>clearTimeout(e)}),[T,b,f,de,V,je,Ce]),s.useEffect((()=>{f.delete&&Ce()}),[Ce,f.delete]),s.createElement("li",{tabIndex:0,ref:se,className:mo(A,pe,null==U?void 0:U.toast,null==(t=null==f?void 0:f.classNames)?void 0:t.toast,null==U?void 0:U.default,null==U?void 0:U[de],null==(o=null==f?void 0:f.classNames)?void 0:o[de]),"data-sonner-toast":"","data-rich-colors":null!=(a=f.richColors)?a:E,"data-styled":!(f.jsx||f.unstyled||h),"data-mounted":G,"data-promise":!!f.promise,"data-swiped":Z,"data-removed":H,"data-visible":ce,"data-y-position":xe,"data-x-position":ke,"data-index":x,"data-front":le,"data-swiping":Y,"data-dismissible":ue,"data-type":de,"data-invert":Ee,"data-swipe-out":Q,"data-swipe-direction":_,"data-expanded":!!(T||L&&G),style:{"--index":x,"--toasts-before":x,"--z-index":k.length-x,"--offset":`${H?te:ve.current}px`,"--initial-height":L?"auto":`${ae}px`,...C,...f.style},onDragEnd:()=>{K(!1),F(null),we.current=null},onPointerDown:e=>{Se||!ue||(ne.current=new Date,oe(ve.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(K(!0),we.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,o,a;if(Q||!ue)return;we.current=null;let r=Number((null==(e=se.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),i=Number((null==(t=se.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),n=(new Date).getTime()-(null==(o=ne.current)?void 0:o.getTime()),s="x"===q?r:i,l=Math.abs(s)/n;if(Math.abs(s)>=20||l>.11)return oe(ve.current),null==(a=f.onDismiss)||a.call(f,f),$("x"===q?r>0?"right":"left":i>0?"down":"up"),Ce(),J(!0),void ee(!1);K(!1),F(null)},onPointerMove:t=>{var o,a,r,i;if(!we.current||!ue||(null==(o=window.getSelection())?void 0:o.toString().length)>0)return;let n=t.clientY-we.current.y,s=t.clientX-we.current.x,l=null!=(a=e.swipeDirections)?a:function(e){let[t,o]=e.split("-"),a=[];return t&&a.push(t),o&&a.push(o),a}(M);!q&&(Math.abs(s)>1||Math.abs(n)>1)&&F(Math.abs(s)>Math.abs(n)?"x":"y");let c={x:0,y:0};"y"===q?(l.includes("top")||l.includes("bottom"))&&(l.includes("top")&&n<0||l.includes("bottom")&&n>0)&&(c.y=n):"x"===q&&(l.includes("left")||l.includes("right"))&&(l.includes("left")&&s<0||l.includes("right")&&s>0)&&(c.x=s),(Math.abs(c.x)>0||Math.abs(c.y)>0)&&ee(!0),null==(r=se.current)||r.style.setProperty("--swipe-amount-x",`${c.x}px`),null==(i=se.current)||i.style.setProperty("--swipe-amount-y",`${c.y}px`)}},fe&&!f.jsx?s.createElement("button",{"aria-label":B,"data-disabled":Se,"data-close-button":!0,onClick:Se||!ue?()=>{}:()=>{var e;Ce(),null==(e=f.onDismiss)||e.call(f,f)},className:mo(null==U?void 0:U.closeButton,null==(r=null==f?void 0:f.classNames)?void 0:r.closeButton)},null!=(i=null==O?void 0:O.close)?i:ro):null,f.jsx||n.isValidElement(f.title)?f.jsx?f.jsx:"function"==typeof f.title?f.title():f.title:s.createElement(s.Fragment,null,de||f.icon||f.promise?s.createElement("div",{"data-icon":"",className:mo(null==U?void 0:U.icon,null==(l=null==f?void 0:f.classNames)?void 0:l.icon)},f.promise||"loading"===f.type&&!f.icon?f.icon||(null!=O&&O.loading?s.createElement("div",{className:mo(null==U?void 0:U.loader,null==(Pe=null==f?void 0:f.classNames)?void 0:Pe.loader,"sonner-loader"),"data-visible":"loading"===de},O.loading):N?s.createElement("div",{className:mo(null==U?void 0:U.loader,null==(Ie=null==f?void 0:f.classNames)?void 0:Ie.loader,"sonner-loader"),"data-visible":"loading"===de},N):s.createElement(Zt,{className:mo(null==U?void 0:U.loader,null==(Ae=null==f?void 0:f.classNames)?void 0:Ae.loader),visible:"loading"===de})):null,"loading"!==f.type?f.icon||(null==O?void 0:O[de])||(e=>{switch(e){case"success":return eo;case"info":return oo;case"warning":return to;case"error":return ao;default:return null}})(de):null):null,s.createElement("div",{"data-content":"",className:mo(null==U?void 0:U.content,null==(c=null==f?void 0:f.classNames)?void 0:c.content)},s.createElement("div",{"data-title":"",className:mo(null==U?void 0:U.title,null==(d=null==f?void 0:f.classNames)?void 0:d.title)},"function"==typeof f.title?f.title():f.title),f.description?s.createElement("div",{"data-description":"",className:mo(D,me,null==U?void 0:U.description,null==(u=null==f?void 0:f.classNames)?void 0:u.description)},"function"==typeof f.description?f.description():f.description):null),n.isValidElement(f.cancel)?f.cancel:f.cancel&&co(f.cancel)?s.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||P,onClick:e=>{var t,o;co(f.cancel)&&ue&&(null==(o=(t=f.cancel).onClick)||o.call(t,e),Ce())},className:mo(null==U?void 0:U.cancelButton,null==(p=null==f?void 0:f.classNames)?void 0:p.cancelButton)},f.cancel.label):null,n.isValidElement(f.action)?f.action:f.action&&co(f.action)?s.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||I,onClick:e=>{var t,o;co(f.action)&&(null==(o=(t=f.action).onClick)||o.call(t,e),!e.defaultPrevented&&Ce())},className:mo(null==U?void 0:U.actionButton,null==(m=null==f?void 0:f.classNames)?void 0:m.actionButton)},f.action.label):null));var Pe,Ie,Ae};function fo(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}function ho(e,t){let o={};return[e,t].forEach(((e,t)=>{let a=1===t,r=a?"--mobile-offset":"--offset",i=a?"16px":"32px";function n(e){["top","right","bottom","left"].forEach((t=>{o[`${r}-${t}`]="number"==typeof e?`${e}px`:e}))}"number"==typeof e||"string"==typeof e?n(e):"object"==typeof e?["top","right","bottom","left"].forEach((t=>{void 0===e[t]?o[`${r}-${t}`]=i:o[`${r}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]})):n(i)})),o}var bo=n.forwardRef((function(e,t){let{invert:o,position:a="bottom-right",hotkey:r=["altKey","KeyT"],expand:i,closeButton:n,className:l,offset:d,mobileOffset:u,theme:p="light",richColors:m,duration:g,style:f,visibleToasts:h=uo,toastOptions:b,dir:v=fo(),gap:y=po,loadingIcon:w,icons:x,containerAriaLabel:k="Notifications",pauseWhenPageIsHidden:T}=e,[j,E]=s.useState([]),S=s.useMemo((()=>Array.from(new Set([a].concat(j.filter((e=>e.position)).map((e=>e.position)))))),[j,a]),[C,P]=s.useState([]),[I,A]=s.useState(!1),[D,R]=s.useState(!1),[M,z]=s.useState("system"!==p?p:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),N=s.useRef(null),L=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),U=s.useRef(null),O=s.useRef(!1),B=s.useCallback((e=>{E((t=>{var o;return null!=(o=t.find((t=>t.id===e.id)))&&o.delete||no.dismiss(e.id),t.filter((({id:t})=>t!==e.id))}))}),[]);return s.useEffect((()=>no.subscribe((e=>{e.dismiss?E((t=>t.map((t=>t.id===e.id?{...t,delete:!0}:t)))):setTimeout((()=>{c.flushSync((()=>{E((t=>{let o=t.findIndex((t=>t.id===e.id));return-1!==o?[...t.slice(0,o),{...t[o],...e},...t.slice(o+1)]:[e,...t]}))}))}))}))),[]),s.useEffect((()=>{if("system"!==p)return void z(p);if("system"===p&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?z("dark"):z("light")),"undefined"==typeof window)return;let e=window.matchMedia("(prefers-color-scheme: dark)");try{e.addEventListener("change",(({matches:e})=>{z(e?"dark":"light")}))}catch(t){e.addListener((({matches:e})=>{try{z(e?"dark":"light")}catch(t){console.error(t)}}))}}),[p]),s.useEffect((()=>{j.length<=1&&A(!1)}),[j]),s.useEffect((()=>{let e=e=>{var t,o;r.every((t=>e[t]||e.code===t))&&(A(!0),null==(t=N.current)||t.focus()),"Escape"===e.code&&(document.activeElement===N.current||null!=(o=N.current)&&o.contains(document.activeElement))&&A(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[r]),s.useEffect((()=>{if(N.current)return()=>{U.current&&(U.current.focus({preventScroll:!0}),U.current=null,O.current=!1)}}),[N.current]),s.createElement("section",{ref:t,"aria-label":`${k} ${L}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},S.map(((t,a)=>{var r;let[c,p]=t.split("-");return j.length?s.createElement("ol",{key:t,dir:"auto"===v?fo():v,tabIndex:-1,ref:N,className:l,"data-sonner-toaster":!0,"data-theme":M,"data-y-position":c,"data-lifted":I&&j.length>1&&!i,"data-x-position":p,style:{"--front-toast-height":`${(null==(r=C[0])?void 0:r.height)||0}px`,"--width":"356px","--gap":`${y}px`,...f,...ho(d,u)},onBlur:e=>{O.current&&!e.currentTarget.contains(e.relatedTarget)&&(O.current=!1,U.current&&(U.current.focus({preventScroll:!0}),U.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||O.current||(O.current=!0,U.current=e.relatedTarget)},onMouseEnter:()=>A(!0),onMouseMove:()=>A(!0),onMouseLeave:()=>{D||A(!1)},onDragEnd:()=>A(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||R(!0)},onPointerUp:()=>R(!1)},j.filter((e=>!e.position&&0===a||e.position===t)).map(((a,r)=>{var l,c;return s.createElement(go,{key:a.id,icons:x,index:r,toast:a,defaultRichColors:m,duration:null!=(l=null==b?void 0:b.duration)?l:g,className:null==b?void 0:b.className,descriptionClassName:null==b?void 0:b.descriptionClassName,invert:o,visibleToasts:h,closeButton:null!=(c=null==b?void 0:b.closeButton)?c:n,interacting:D,position:t,style:null==b?void 0:b.style,unstyled:null==b?void 0:b.unstyled,classNames:null==b?void 0:b.classNames,cancelButtonStyle:null==b?void 0:b.cancelButtonStyle,actionButtonStyle:null==b?void 0:b.actionButtonStyle,removeToast:B,toasts:j.filter((e=>e.position==a.position)),heights:C.filter((e=>e.position==a.position)),setHeights:P,expandByDefault:i,gap:y,loadingIcon:w,expanded:I,pauseWhenPageIsHidden:T,swipeDirections:e.swipeDirections})}))):null})))}));const vo=({...e})=>{const{theme:t="system"}=(()=>{const e=n.useContext(Kt);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e})();return r.jsx(bo,{theme:t,className:"toaster group",position:"top-center",expand:!1,richColors:!0,closeButton:!0,toastOptions:{duration:3e3,classNames:{toast:"group toast group-[.toaster]:bg-[var(--color-surface)] group-[.toaster]:text-[var(--color-text)] group-[.toaster]:border-[var(--color-border)] group-[.toaster]:shadow-xl group-[.toaster]:backdrop-blur-sm",description:"group-[.toast]:text-[var(--color-muted)]",actionButton:"group-[.toast]:bg-[var(--color-primary)] group-[.toast]:text-white",cancelButton:"group-[.toast]:bg-[var(--color-neutral)] group-[.toast]:text-[var(--color-text)]",closeButton:"group-[.toast]:bg-[var(--color-surface)] group-[.toast]:text-[var(--color-muted)] group-[.toast]:border-[var(--color-border)]",success:"group-[.toaster]:bg-green-50 group-[.toaster]:text-green-900 group-[.toaster]:border-green-200 dark:group-[.toaster]:bg-green-900/20 dark:group-[.toaster]:text-green-100 dark:group-[.toaster]:border-green-800",error:"group-[.toaster]:bg-red-50 group-[.toaster]:text-red-900 group-[.toaster]:border-red-200 dark:group-[.toaster]:bg-red-900/20 dark:group-[.toaster]:text-red-100 dark:group-[.toaster]:border-red-800",warning:"group-[.toaster]:bg-yellow-50 group-[.toaster]:text-yellow-900 group-[.toaster]:border-yellow-200 dark:group-[.toaster]:bg-yellow-900/20 dark:group-[.toaster]:text-yellow-100 dark:group-[.toaster]:border-yellow-800",info:"group-[.toaster]:bg-blue-50 group-[.toaster]:text-blue-900 group-[.toaster]:border-blue-200 dark:group-[.toaster]:bg-blue-900/20 dark:group-[.toaster]:text-blue-100 dark:group-[.toaster]:border-blue-800"},style:{borderRadius:"12px",padding:"16px",fontSize:"14px",fontWeight:"500"}},...e})},yo=D;var wo,xo;n.forwardRef((({className:e,sideOffset:t=4,...o},a)=>r.jsx(A,{ref:a,sideOffset:t,className:qt("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...o}))).displayName=A.displayName;const ko=g(function(){if(xo)return wo;xo=1;var e="undefined"!=typeof Element,t="function"==typeof Map,o="function"==typeof Set,a="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function r(i,n){if(i===n)return!0;if(i&&n&&"object"==typeof i&&"object"==typeof n){if(i.constructor!==n.constructor)return!1;var s,l,c,d;if(Array.isArray(i)){if((s=i.length)!=n.length)return!1;for(l=s;0!==l--;)if(!r(i[l],n[l]))return!1;return!0}if(t&&i instanceof Map&&n instanceof Map){if(i.size!==n.size)return!1;for(d=i.entries();!(l=d.next()).done;)if(!n.has(l.value[0]))return!1;for(d=i.entries();!(l=d.next()).done;)if(!r(l.value[1],n.get(l.value[0])))return!1;return!0}if(o&&i instanceof Set&&n instanceof Set){if(i.size!==n.size)return!1;for(d=i.entries();!(l=d.next()).done;)if(!n.has(l.value[0]))return!1;return!0}if(a&&ArrayBuffer.isView(i)&&ArrayBuffer.isView(n)){if((s=i.length)!=n.length)return!1;for(l=s;0!==l--;)if(i[l]!==n[l])return!1;return!0}if(i.constructor===RegExp)return i.source===n.source&&i.flags===n.flags;if(i.valueOf!==Object.prototype.valueOf&&"function"==typeof i.valueOf&&"function"==typeof n.valueOf)return i.valueOf()===n.valueOf();if(i.toString!==Object.prototype.toString&&"function"==typeof i.toString&&"function"==typeof n.toString)return i.toString()===n.toString();if((s=(c=Object.keys(i)).length)!==Object.keys(n).length)return!1;for(l=s;0!==l--;)if(!Object.prototype.hasOwnProperty.call(n,c[l]))return!1;if(e&&i instanceof Element)return!1;for(l=s;0!==l--;)if(("_owner"!==c[l]&&"__v"!==c[l]&&"__o"!==c[l]||!i.$$typeof)&&!r(i[c[l]],n[c[l]]))return!1;return!0}return i!=i&&n!=n}return wo=function(e,t){try{return r(e,t)}catch(o){if((o.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw o}}}());var To,jo;const Eo=g(jo?To:(jo=1,To=function(e,t,o,a,r,i,n,s){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[o,a,r,i,n,s],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}));var So,Co;const Po=g(Co?So:(Co=1,So=function(e,t,o,a){var r=o?o.call(a,e,t):void 0;if(void 0!==r)return!!r;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var i=Object.keys(e),n=Object.keys(t);if(i.length!==n.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),l=0;l<i.length;l++){var c=i[l];if(!s(c))return!1;var d=e[c],u=t[c];if(!1===(r=o?o.call(a,d,u,c):void 0)||void 0===r&&d!==u)return!1}return!0}));var Io=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(Io||{}),Ao={rel:["amphtml","canonical","alternate"]},Do={type:["application/ld+json"]},Ro={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},Mo=Object.values(Io),zo={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},No=Object.entries(zo).reduce(((e,[t,o])=>(e[o]=t,e)),{}),Lo="data-rh",Uo="defaultTitle",Oo="defer",Bo="encodeSpecialCharacters",Vo="onChangeClientState",qo="titleTemplate",Fo="prioritizeSeoTags",_o=(e,t)=>{for(let o=e.length-1;o>=0;o-=1){const a=e[o];if(Object.prototype.hasOwnProperty.call(a,t))return a[t]}return null},$o=e=>{let t=_o(e,"title");const o=_o(e,qo);if(Array.isArray(t)&&(t=t.join("")),o&&t)return o.replace(/%s/g,(()=>t));const a=_o(e,Uo);return t||a||void 0},Go=e=>_o(e,Vo)||(()=>{}),Xo=(e,t)=>t.filter((t=>void 0!==t[e])).map((t=>t[e])).reduce(((e,t)=>({...e,...t})),{}),Ho=(e,t)=>t.filter((e=>void 0!==e.base)).map((e=>e.base)).reverse().reduce(((t,o)=>{if(!t.length){const a=Object.keys(o);for(let r=0;r<a.length;r+=1){const i=a[r].toLowerCase();if(-1!==e.indexOf(i)&&o[i])return t.concat(o)}}return t}),[]),Wo=(e,t,o)=>{const a={};return o.filter((t=>{return!!Array.isArray(t[e])||(void 0!==t[e]&&(o=`Helmet: ${e} should be of type "Array". Instead found type "${typeof t[e]}"`,console&&"function"==typeof console.warn&&console.warn(o)),!1);var o})).map((t=>t[e])).reverse().reduce(((e,o)=>{const r={};o.filter((e=>{let o;const i=Object.keys(e);for(let a=0;a<i.length;a+=1){const r=i[a],n=r.toLowerCase();-1===t.indexOf(n)||"rel"===o&&"canonical"===e[o].toLowerCase()||"rel"===n&&"stylesheet"===e[n].toLowerCase()||(o=n),-1===t.indexOf(r)||"innerHTML"!==r&&"cssText"!==r&&"itemprop"!==r||(o=r)}if(!o||!e[o])return!1;const n=e[o].toLowerCase();return a[o]||(a[o]={}),r[o]||(r[o]={}),!a[o][n]&&(r[o][n]=!0,!0)})).reverse().forEach((t=>e.push(t)));const i=Object.keys(r);for(let t=0;t<i.length;t+=1){const e=i[t],o={...a[e],...r[e]};a[e]=o}return e}),[]).reverse()},Yo=(e,t)=>{if(Array.isArray(e)&&e.length)for(let o=0;o<e.length;o+=1){if(e[o][t])return!0}return!1},Ko=e=>Array.isArray(e)?e.join(""):e,Qo=(e,t)=>Array.isArray(e)?e.reduce(((e,o)=>(((e,t)=>{const o=Object.keys(e);for(let a=0;a<o.length;a+=1)if(t[o[a]]&&t[o[a]].includes(e[o[a]]))return!0;return!1})(o,t)?e.priority.push(o):e.default.push(o),e)),{priority:[],default:[]}):{default:e,priority:[]},Jo=(e,t)=>({...e,[t]:void 0}),Zo=["noscript","script","style"],ea=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),ta=e=>Object.keys(e).reduce(((t,o)=>{const a=void 0!==e[o]?`${o}="${e[o]}"`:`${o}`;return t?`${t} ${a}`:a}),""),oa=(e,t={})=>Object.keys(e).reduce(((t,o)=>(t[zo[o]||o]=e[o],t)),t),aa=(e,t)=>t.map(((t,o)=>{const a={key:o,[Lo]:!0};return Object.keys(t).forEach((e=>{const o=zo[e]||e;if("innerHTML"===o||"cssText"===o){const e=t.innerHTML||t.cssText;a.dangerouslySetInnerHTML={__html:e}}else a[o]=t[e]})),s.createElement(e,a)})),ra=(e,t,o=!0)=>{switch(e){case"title":return{toComponent:()=>((e,t,o)=>{const a=oa(o,{key:t,[Lo]:!0});return[s.createElement("title",a,t)]})(0,t.title,t.titleAttributes),toString:()=>((e,t,o,a)=>{const r=ta(o),i=Ko(t);return r?`<${e} ${Lo}="true" ${r}>${ea(i,a)}</${e}>`:`<${e} ${Lo}="true">${ea(i,a)}</${e}>`})(e,t.title,t.titleAttributes,o)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>oa(t),toString:()=>ta(t)};default:return{toComponent:()=>aa(e,t),toString:()=>((e,t,o=!0)=>t.reduce(((t,a)=>{const r=a,i=Object.keys(r).filter((e=>!("innerHTML"===e||"cssText"===e))).reduce(((e,t)=>{const a=void 0===r[t]?t:`${t}="${ea(r[t],o)}"`;return e?`${e} ${a}`:a}),""),n=r.innerHTML||r.cssText||"",s=-1===Zo.indexOf(e);return`${t}<${e} ${Lo}="true" ${i}${s?"/>":`>${n}</${e}>`}`}),""))(e,t,o)}}},ia=e=>{const{baseTag:t,bodyAttributes:o,encode:a=!0,htmlAttributes:r,noscriptTags:i,styleTags:n,title:s="",titleAttributes:l,prioritizeSeoTags:c}=e;let{linkTags:d,metaTags:u,scriptTags:p}=e,m={toComponent:()=>{},toString:()=>""};return c&&({priorityMethods:m,linkTags:d,metaTags:u,scriptTags:p}=(({metaTags:e,linkTags:t,scriptTags:o,encode:a})=>{const r=Qo(e,Ro),i=Qo(t,Ao),n=Qo(o,Do);return{priorityMethods:{toComponent:()=>[...aa("meta",r.priority),...aa("link",i.priority),...aa("script",n.priority)],toString:()=>`${ra("meta",r.priority,a)} ${ra("link",i.priority,a)} ${ra("script",n.priority,a)}`},metaTags:r.default,linkTags:i.default,scriptTags:n.default}})(e)),{priority:m,base:ra("base",t,a),bodyAttributes:ra("bodyAttributes",o,a),htmlAttributes:ra("htmlAttributes",r,a),link:ra("link",d,a),meta:ra("meta",u,a),noscript:ra("noscript",i,a),script:ra("script",p,a),style:ra("style",n,a),title:ra("title",{title:s,titleAttributes:l},a)}},na=[],sa=!("undefined"==typeof window||!window.document||!window.document.createElement),la=class{constructor(e,t){a(this,"instances",[]),a(this,"canUseDOM",sa),a(this,"context"),a(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?na:this.instances,add:e=>{(this.canUseDOM?na:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?na:this.instances).indexOf(e);(this.canUseDOM?na:this.instances).splice(t,1)}}}),this.context=e,this.canUseDOM=t||!1,t||(e.helmet=ia({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},ca=s.createContext({}),da=(e=class extends n.Component{constructor(t){super(t),a(this,"helmetData"),this.helmetData=new la(this.props.context||{},e.canUseDOM)}render(){return s.createElement(ca.Provider,{value:this.helmetData.value},this.props.children)}},a(e,"canUseDOM",sa),e),ua=(e,t)=>{const o=document.head||document.querySelector("head"),a=o.querySelectorAll(`${e}[${Lo}]`),r=[].slice.call(a),i=[];let n;return t&&t.length&&t.forEach((t=>{const o=document.createElement(e);for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))if("innerHTML"===e)o.innerHTML=t.innerHTML;else if("cssText"===e)o.styleSheet?o.styleSheet.cssText=t.cssText:o.appendChild(document.createTextNode(t.cssText));else{const a=e,r=void 0===t[a]?"":t[a];o.setAttribute(e,r)}o.setAttribute(Lo,"true"),r.some(((e,t)=>(n=t,o.isEqualNode(e))))?r.splice(n,1):i.push(o)})),r.forEach((e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),i.forEach((e=>o.appendChild(e))),{oldTags:r,newTags:i}},pa=(e,t)=>{const o=document.getElementsByTagName(e)[0];if(!o)return;const a=o.getAttribute(Lo),r=a?a.split(","):[],i=[...r],n=Object.keys(t);for(const s of n){const e=t[s]||"";o.getAttribute(s)!==e&&o.setAttribute(s,e),-1===r.indexOf(s)&&r.push(s);const a=i.indexOf(s);-1!==a&&i.splice(a,1)}for(let s=i.length-1;s>=0;s-=1)o.removeAttribute(i[s]);r.length===i.length?o.removeAttribute(Lo):o.getAttribute(Lo)!==n.join(",")&&o.setAttribute(Lo,n.join(","))},ma=(e,t)=>{const{baseTag:o,bodyAttributes:a,htmlAttributes:r,linkTags:i,metaTags:n,noscriptTags:s,onChangeClientState:l,scriptTags:c,styleTags:d,title:u,titleAttributes:p}=e;pa("body",a),pa("html",r),((e,t)=>{void 0!==e&&document.title!==e&&(document.title=Ko(e)),pa("title",t)})(u,p);const m={baseTag:ua("base",o),linkTags:ua("link",i),metaTags:ua("meta",n),noscriptTags:ua("noscript",s),scriptTags:ua("script",c),styleTags:ua("style",d)},g={},f={};Object.keys(m).forEach((e=>{const{newTags:t,oldTags:o}=m[e];t.length&&(g[e]=t),o.length&&(f[e]=m[e].oldTags)})),t&&t(),l(e,g,f)},ga=null,fa=e=>{ga&&cancelAnimationFrame(ga),e.defer?ga=requestAnimationFrame((()=>{ma(e,(()=>{ga=null}))})):(ma(e),ga=null)},ha=class extends n.Component{constructor(){super(...arguments),a(this,"rendered",!1)}shouldComponentUpdate(e){return!Po(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:t}=this.props.context;let o=null;const a=(r=e.get().map((e=>{const t={...e.props};return delete t.context,t})),{baseTag:Ho(["href"],r),bodyAttributes:Xo("bodyAttributes",r),defer:_o(r,Oo),encode:_o(r,Bo),htmlAttributes:Xo("htmlAttributes",r),linkTags:Wo("link",["rel","href"],r),metaTags:Wo("meta",["name","charset","http-equiv","property","itemprop"],r),noscriptTags:Wo("noscript",["innerHTML"],r),onChangeClientState:Go(r),scriptTags:Wo("script",["src","innerHTML"],r),styleTags:Wo("style",["cssText"],r),title:$o(r),titleAttributes:Xo("titleAttributes",r),prioritizeSeoTags:Yo(r,Fo)});var r;da.canUseDOM?fa(a):ia&&(o=ia(a)),t(o)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},ba=(t=class extends n.Component{shouldComponentUpdate(e){return!ko(Jo(this.props,"helmetData"),Jo(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,o,a){return{...t,[e.type]:[...t[e.type]||[],{...o,...this.mapNestedChildrenToProps(e,a)}]}}mapObjectTypeChildren(e,t,o,a){switch(e.type){case"title":return{...t,[e.type]:a,titleAttributes:{...o}};case"body":return{...t,bodyAttributes:{...o}};case"html":return{...t,htmlAttributes:{...o}};default:return{...t,[e.type]:{...o}}}}mapArrayTypeChildrenToProps(e,t){let o={...t};return Object.keys(e).forEach((t=>{o={...o,[t]:e[t]}})),o}warnOnInvalidChildren(e,t){return Eo(Mo.some((t=>e.type===t)),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${Mo.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),Eo(!t||"string"==typeof t||Array.isArray(t)&&!t.some((e=>"string"!=typeof e)),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,t){let o={};return s.Children.forEach(e,(e=>{if(!e||!e.props)return;const{children:a,...r}=e.props,i=Object.keys(r).reduce(((e,t)=>(e[No[t]||t]=r[t],e)),{});let{type:n}=e;switch("symbol"==typeof n?n=n.toString():this.warnOnInvalidChildren(e,a),n){case"Symbol(react.fragment)":t=this.mapChildrenToProps(a,t);break;case"link":case"meta":case"noscript":case"script":case"style":o=this.flattenArrayTypeChildren(e,o,i,a);break;default:t=this.mapObjectTypeChildren(e,t,i,a)}})),this.mapArrayTypeChildrenToProps(o,t)}render(){const{children:e,...t}=this.props;let o={...t},{helmetData:a}=t;if(e&&(o=this.mapChildrenToProps(e,o)),a&&!(a instanceof la)){a=new la(a.context,!0),delete o.helmetData}return a?s.createElement(ha,{...o,context:a.value}):s.createElement(ca.Consumer,null,(e=>s.createElement(ha,{...o,context:e})))}},a(t,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),t);const va=()=>r.jsx("div",{className:"flex items-center justify-center py-12",children:r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"w-8 h-8 border-4 border-[var(--color-border)] border-t-[var(--color-primary)] rounded-full animate-spin"}),r.jsx("span",{className:"sr-only",children:"Carregando..."})]})}),ya={"pt-BR":{translation:{navigation:{profile:"Perfil",projects:"Projetos",backlog:"Backlog",contact:"Contato",home:"Início",goToProfile:"Ir para sessão Perfil",goToProjetos:"Ir para sessão Projetos",goToBacklog:"Ir para sessão Backlog",goToContato:"Ir para sessão Contato",menu:{open:"Abrir menu de navegação",close:"Fechar menu de navegação",toggle:"Alternar menu de navegação"},settings:{title:"Configurações",open:"Abrir configurações",close:"Fechar configurações",description:"Personalize sua experiência",theme:{toggle:"Alternar tema",lightMode:"Modo claro ativado",darkMode:"Modo escuro ativado"},language:{select:"Selecionar idioma"},sound:{toggle:"Alternar som",enabled:"Som ativado",disabled:"Som desativado"},accessibility:{menu:"Menu de acessibilidade",description:"Recursos de acessibilidade"},feedback:{open:"Abrir feedback",description:"Compartilhe sua experiência e sugestões"}}},profile:{title:"UX/Product Designer com foco em estratégia, impacto e experiência",bio:"Sou UX/Product Designer com forte atuação no design de produtos digitais focados em experiência do usuário, conversão e impacto de negócio. Com background em Marketing Digital, SEO e IA, integro estratégia, design e usabilidade em processos contínuos de melhoria e inovação.",exploreProjects:"Explore projetos",letsChat:"Vamos Conversar",downloadCV:"Download CV",linkedin:"LinkedIn",name:"Tarcisio Bispo de Araujo",ixdf:"IxDF | Interaction Design Foundation",hero:{greeting:"Olá, eu sou",roles:{uxDesigner:"UX Designer",productDesigner:"Product Designer",designStrategist:"Design Strategist",interactionDesigner:"Interaction Designer"}}},projects:{title:"Projetos",description:"Casos reais de UX/Product Design com foco em estratégia, impacto e resultados mensuráveis para negócios e usuários.",overview:"Visão Geral",discovery:"Descoberta",solution:"Solução",iteration:"Iteração",outcomes:"Resultados",insights:"Insights",seeMore:"Ver detalhes",seeLess:"Ocultar detalhes",projectImage:"Imagem do projeto",badges:{usability:"Usabilidade",informationArchitecture:"Arquitetura da Informação",userTesting:"Testes de Usuário",uxResearch:"UX Research",journeyMapping:"Mapa de Jornada",stakeholderManagement:"Stakeholder Management",productStrategy:"Product Strategy",seo:"SEO",productValidation:"Validação de Produto",visualDesign:"Design Visual",communication:"Comunicação",engagement:"Engajamento"},fgvLaw:{title:"FGV LAW",category:"Navegação e Usabilidade",overview:"Reestruturação da área de cursos jurídicos da Direito GV com foco em usabilidade e organização da informação para melhorar a experiência dos usuários.",discovery:"Identifiquei que os usuários enfrentavam dificuldade para localizar e comparar cursos na plataforma da Direito GV.",solution:"Projetei um novo painel com sistema de abas e filtros temáticos específicos para o contexto jurídico.",iteration:"Após testes com usuários, simplificamos a terminologia dos filtros e ajustamos a hierarquia de informações.",outcomes:["Melhora de 25% na visibilidade dos cursos e 35% mais interações com páginas específicas em 3 meses","Aumento de 18% na taxa de conversão de acessos em inscrições, passando de aproximadamente 8% para 10%","Redução de 30% no tempo médio de navegação, de cerca de 4 minutos para 3 minutos até a escolha do curso"],insights:"A estrutura de navegação precisa guiar, não apenas mostrar. Clareza e agrupamento relevante influenciam diretamente a percepção de valor de um curso."},direitoGV:{title:"Pesquisa Direito FGV",category:"Mapas, Fluxos e Pesquisa",overview:"Reorganização da área de pesquisa para melhorar visibilidade dos projetos acadêmicos e facilitar acesso a pesquisadores.",discovery:"A área de pesquisa estava fragmentada e pouco acessível. Pesquisadores tinham dificuldade para divulgar seus trabalhos e usuários externos não conseguiam encontrar informações relevantes sobre projetos em andamento.",solution:"Desenvolvi uma nova arquitetura de informação com categorização por áreas temáticas, perfis de pesquisadores e linha do tempo de projetos. Criei também um sistema de busca avançada.",iteration:"Realizamos testes com alunos, professores e pesquisadores. A navegação foi ajustada com base em feedback sobre nomenclatura e ordem de prioridades. Validei cada alteração com os stakeholders envolvidos.",outcomes:["Redução de 65% no tempo de navegação para encontrar projetos específicos, de aproximadamente 6 minutos para 2 minutos","Aumento de 85% nas visitas às páginas de pesquisadores, passando de cerca de 150 para mais de 280 acessos mensais","Crescimento de 40% na consulta de publicações acadêmicas e 25% mais solicitações de parcerias em 5 meses"],insights:"Áreas institucionais ganham relevância quando são navegáveis, atualizadas e refletidas de forma estratégica na arquitetura da informação."},taliparts:{title:"Taliparts",category:"UX Estratégico + B2B",overview:"Estruturação e validação digital da Taliparts para publicação de peças automotivas no Mercado Livre com foco em aprendizado rápido.",discovery:"Conduzi benchmark detalhado com concorrentes do setor automotivo. Entrevistei mecânicos e lojistas, modelei personas e apliquei a Matriz CSD para identificar certezas, suposições e dúvidas no catálogo físico.",solution:"Criei uma estratégia de validação com SEO para Mercado Livre, padronização visual de anúncios, categorização centrada no vocabulário do comprador e histórico de buscas. Também organizei KPIs e defini plano de priorização de produtos.",iteration:"Testei produtos por blocos temáticos, monitorando cliques, perguntas e taxa de conversão. Refinei descrições, títulos e até a seleção de itens com base em performance real.",outcomes:["Crescimento de 45% nas vendas dos produtos priorizados, gerando aproximadamente R$ 6.500 em receita adicional em 4 meses","Redução de 40% nas dúvidas dos compradores, diminuindo de cerca de 20 para 12 perguntas por produto publicado","Criação de processo que aumentou a eficiência de publicação em 50%, permitindo análise de mais de 80 produtos em 2 meses"],insights:"Validar digitalmente com baixo custo é possível — e necessário. A lógica de produto precisa considerar contexto físico, vocabulário técnico e diferenciais percebidos pelo cliente."},tvInstitucional:{title:"FGV TV Institucional",category:"Engajamento e Comunicação Visual",overview:"Sistema visual para TVs no hall da FGV para comunicar eventos e atualizações institucionais de forma atrativa e dinâmica.",discovery:"Alunos ignoravam murais físicos e e-mails institucionais. Identifiquei que a linguagem dos canais era desatualizada e pouco integrada com a rotina visual dos espaços.",solution:"Implementei um painel digital com curadoria de conteúdo semanal, foco em ritmo visual e clareza imediata das mensagens. A plataforma foi pensada para ser automatizada, com flexibilidade de atualização remota.",iteration:"Testamos tipos de animações, tempo de exibição e contraste. Ajustamos o calendário visual e otimizamos o layout com base em feedback de alunos e coordenação.",outcomes:["Aumento de 35% na visibilidade de eventos institucionais, melhorando o conhecimento dos alunos sobre atividades do campus","Crescimento de 20% na participação em eventos, com maior engajamento da comunidade acadêmica","Melhora de 40% na retenção de informações institucionais comparado aos métodos anteriores de comunicação"],insights:"Ambientes físicos também são interfaces. Quando bem projetados, informam, engajam e conectam — sem precisar de login."}},backlog:{title:"Ciclo de Backlogs Estratégicos",description:"Demonstração prática de como transformo desafios de negócio em soluções de UX mensuráveis. Cada caso apresenta metodologia aplicada, resultados alcançados e insights estratégicos que geram impacto real para stakeholders e usuários.",solution:"Solução",result:"Resultado",note:"Nota",noItems:"Nenhum item nesta página.",previous:"Anterior",next:"Próxima",items:[{challenge:"Usuários abandonavam formulários longos sem completar o cadastro",solution:"Implementei formulário em etapas com barra de progresso e validação em tempo real",result:"Aumento de 40% na taxa de conclusão de cadastros em 2 semanas",note:"Dividir tarefas complexas em etapas menores reduz a ansiedade e melhora a experiência."},{challenge:"Baixa adesão a recursos premium devido à falta de clareza sobre benefícios",solution:"Criei onboarding interativo com demonstrações práticas dos recursos premium",result:"Crescimento de 60% nas conversões para planos pagos em 1 mês",note:"Mostrar valor através de experiência prática é mais eficaz que apenas listar funcionalidades."},{challenge:"Usuários não encontravam facilmente o suporte quando precisavam de ajuda",solution:"Redesenhei sistema de ajuda contextual com chatbot inteligente e FAQ dinâmico",result:"Redução de 50% nos tickets de suporte e aumento de 35% na satisfação",note:"Ajuda contextual no momento certo previne frustrações e melhora a autonomia do usuário."},{challenge:"Interface complexa causava confusão em usuários iniciantes",solution:"Desenvolvi modo simplificado com tutorial progressivo e tooltips adaptativos",result:"Diminuição de 45% na taxa de abandono de novos usuários",note:"Adaptar a complexidade ao nível de experiência do usuário melhora significativamente a adoção."},{challenge:"Processo de checkout tinha alta taxa de abandono no último passo",solution:"Simplifiquei fluxo removendo campos desnecessários e adicionando opções de pagamento express",result:"Aumento de 30% na conclusão de compras e redução de 25% no tempo de checkout",note:"Cada campo extra no checkout é uma barreira potencial. Simplicidade gera conversão."},{challenge:"Usuários não percebiam atualizações importantes do produto",solution:"Implementei sistema de notificações in-app com design não intrusivo e personalização",result:"Melhoria de 55% no engajamento com novas funcionalidades",note:"Comunicação eficaz sobre mudanças mantém usuários informados sem interromper o fluxo."},{challenge:"Dificuldade para encontrar conteúdo relevante em base de conhecimento extensa",solution:"Criei sistema de busca inteligente com filtros contextuais e sugestões automáticas",result:"Aumento de 70% na utilização da base de conhecimento e redução de 40% em consultas repetitivas",note:"Busca eficiente transforma informação abundante em conhecimento acessível."},{challenge:"Baixo engajamento em funcionalidades colaborativas da plataforma",solution:"Redesenhei interface de colaboração com indicadores visuais de atividade e gamificação sutil",result:"Crescimento de 80% na colaboração entre usuários em 6 semanas",note:"Tornar a colaboração visível e recompensadora incentiva naturalmente a participação."},{challenge:"Usuários perdiam progresso ao navegar entre seções do aplicativo",solution:"Implementei sistema de auto-save com indicadores visuais de status de salvamento",result:"Eliminação de 95% das reclamações sobre perda de dados",note:"Confiança na tecnologia cresce quando o usuário vê que seu trabalho está sempre protegido."},{challenge:"Interface não responsiva causava frustração em dispositivos móveis",solution:"Desenvolvi versão mobile-first com gestos intuitivos e navegação otimizada para toque",result:"Aumento de 120% no uso mobile e melhoria de 4.2 para 4.7 na avaliação da app store",note:"Design mobile-first garante experiência consistente independente do dispositivo usado."},{challenge:"Usuários com deficiência visual enfrentavam barreiras de acessibilidade",solution:"Implementei padrões WCAG 2.1 com navegação por teclado e compatibilidade com leitores de tela",result:"Aumento de 200% no uso por pessoas com deficiência e reconhecimento em prêmio de acessibilidade",note:"Acessibilidade não é apenas compliance — é design inclusivo que beneficia todos os usuários."},{challenge:"Textos técnicos confundiam usuários não especializados",solution:"Reescrevi microcopy com linguagem clara e adicionei explicações contextuais quando necessário",result:"Redução de 60% em dúvidas de usuários e melhoria na percepção de facilidade de uso",note:"Pequenas decisões no texto têm grande impacto na experiência de leitura e compreensão."}]},contact:{title:"Vamos conversar?",description:"Estou sempre aberto a novas oportunidades e colaborações. Entre em contato para discutir projetos, parcerias ou apenas trocar ideias sobre UX e design de produtos.",form:{name:"Nome",namePlaceholder:"Digite seu nome completo",email:"E-mail",emailPlaceholder:"Digite seu melhor e-mail",subject:"Assunto",subjectPlaceholder:"Sobre o que você gostaria de conversar?",message:"Mensagem",messagePlaceholder:"Conte-me sobre seu projeto, oportunidade ou como posso ajudar...",send:"Enviar mensagem",sending:"Enviando...",success:"✅ Mensagem enviada com sucesso! Eu retorno em breve.",error:"❌ Ops! Não conseguimos enviar sua mensagem. Tente novamente ou entre em contato diretamente.",nameRequired:"Nome é obrigatório",emailRequired:"E-mail é obrigatório",emailInvalid:"E-mail inválido",subjectRequired:"Assunto é obrigatório",messageRequired:"Mensagem é obrigatória",messageMinLength:"Mensagem deve ter pelo menos 10 caracteres"},info:{email:"<EMAIL>",location:"São Paulo, Brasil",availability:"Disponível para projetos freelance e oportunidades full-time"},social:{linkedin:"LinkedIn",whatsapp:"WhatsApp",email:"E-mail"}},accessibility:{title:"Acessibilidade",subtitle:"Personalize sua experiência de navegação",description:"Configure opções de acessibilidade para melhorar sua experiência.",instructions:"Use as opções abaixo para personalizar a interface.",close:"Fechar",open:"Abrir",menuLabel:"Menu de Acessibilidade",menuTooltip:"Configurações de acessibilidade (Shift + A)",fontSize:{label:"Tamanho da fonte",increase:"Aumentar",decrease:"Diminuir",reset:"Resetar",increaseLabel:"Aumentar tamanho da fonte",decreaseLabel:"Diminuir tamanho da fonte",resetLabel:"Resetar tamanho da fonte"},contrast:{label:"Alto contraste",enable:"Ativar alto contraste",disable:"Desativar alto contraste",enabled:"Alto contraste ativado",disabled:"Alto contraste desativado"},readingMode:{label:"Modo leitura",enable:"Ativar modo leitura",disable:"Desativar modo leitura",enabled:"Modo leitura ativado",disabled:"Modo leitura desativado"},screenReader:{label:"Leitor de tela",enable:"Ativar leitor de tela",disable:"Desativar leitor de tela",enabled:"Leitor de tela ativado",disabled:"Leitor de tela desativado"},reset:{label:"Resetar configurações",action:"Configurações de acessibilidade resetadas",success:"Configurações resetadas com sucesso"},features:{fontSize:"Tamanho da fonte",fontSizeIncrease:"Aumentar fonte",fontSizeDecrease:"Diminuir fonte",fontSizeReset:"Resetar fonte",contrast:"Alto contraste",contrastEnable:"Ativar contraste",contrastDisable:"Desativar contraste",readingMode:"Modo leitura",readingModeEnable:"Ativar leitura",readingModeDisable:"Desativar leitura"},skipLinks:{skipToContent:"Pular para conteúdo principal",skipToNavigation:"Pular para navegação"},status:{enabled:"Ativado",disabled:"Desativado",fontIncreased:"Fonte aumentada",fontDecreased:"Fonte diminuída",fontReset:"Fonte resetada",contrastEnabled:"Alto contraste ativado",contrastDisabled:"Alto contraste desativado",readingEnabled:"Modo leitura ativado",readingDisabled:"Modo leitura desativado"}},sound:{enabled:"Som ativado",disabled:"Som desativado",enabledDesc:"Efeitos sonoros ativados",disabledDesc:"Efeitos sonoros desativados",enable:"Ativar som",disable:"Desativar som",toggle:"Alternar som",changed:"Som alterado para",volume:"Volume",volumeControl:"Ajustar volume do som"},tooltips:{theme:{light:"Alternar para modo claro",dark:"Alternar para modo escuro",system:"Usar preferência do sistema"},language:{switch:"Trocar idioma",current:"Idioma atual",available:"Idiomas disponíveis"},navigation:{home:"Ir para início",profile:"Ir para perfil",projects:"Ver projetos",backlog:"Ver backlog estratégico",contact:"Entrar em contato"},social:{linkedin:"Perfil no LinkedIn",email:"Enviar e-mail",whatsapp:"Conversar no WhatsApp"},sound:{enable:"Ativar efeitos sonoros",disable:"Desativar efeitos sonoros"},actions:{expand:"Expandir detalhes",collapse:"Recolher detalhes",download:"Baixar arquivo",share:"Compartilhar",copy:"Copiar link",print:"Imprimir página",backToTop:"Voltar ao topo"}},toasts:{success:{title:"Sucesso!",messageSent:"Mensagem enviada com sucesso!",settingsSaved:"Configurações salvas",linkCopied:"Link copiado para área de transferência",themeChanged:"Tema alterado",languageChanged:"Idioma alterado"},error:{title:"Erro",messageNotSent:"Erro ao enviar mensagem",networkError:"Erro de conexão",genericError:"Algo deu errado",tryAgain:"Tente novamente"},info:{title:"Informação",loading:"Carregando...",processing:"Processando...",saving:"Salvando..."},warning:{title:"Aviso",unsavedChanges:"Você tem alterações não salvas",confirmAction:"Tem certeza que deseja continuar?"}},seo:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer especializado em estratégia, impacto e experiência do usuário. Veja meus projetos de design de produtos digitais e soluções UX.",keywords:"UX Designer, Product Designer, Design de Produto, Experiência do Usuário, UI/UX, Portfolio, São Paulo",author:"Tarcisio Bispo de Araujo",pages:{home:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer focado em estratégia, impacto e experiência do usuário. Especialista em design de produtos digitais, conversão e impacto de negócio."},projects:{title:"Projetos - Tarcisio Bispo | UX Designer",description:"Veja meus principais projetos de UX/Product Design: FGV LAW, Direito GV, Taliparts e FGV TV Institucional. Casos reais com resultados mensuráveis."},backlog:{title:"Backlog Estratégico - Tarcisio Bispo | UX Designer",description:"Demonstração prática de como transformo desafios de negócio em soluções UX mensuráveis. Metodologia aplicada, resultados alcançados e insights estratégicos."},contact:{title:"Contato - Tarcisio Bispo | UX Designer",description:"Entre em contato para discutir projetos, parcerias ou oportunidades. Disponível para projetos freelance e oportunidades full-time."}}},schema:{person:{name:"Tarcisio Bispo de Araujo",jobTitle:"UX/Product Designer",description:"UX/Product Designer especializado em estratégia, impacto e experiência do usuário",location:"São Paulo, Brasil",email:"<EMAIL>",skills:["UX Design","Product Design","Estratégia de Design","Pesquisa de Usuário","Prototipagem","Testes de Usabilidade"]},organization:{name:"Tarcisio Bispo Portfolio",description:"Portfolio profissional de UX/Product Design",location:"São Paulo, Brasil"}},alts:{profile:{photo:"Foto de perfil de Tarcisio Bispo",ixdfLogo:"Logo da Interaction Design Foundation",ixdfSeal:"Selo de certificação IxDF"},projects:{fgvLaw:"Screenshot do projeto FGV LAW - designers trabalhando em equipe no escritório",direitoGV:"Screenshot do projeto Pesquisa Direito FGV - mesa de madeira com livro em frente a estante",taliparts:"Screenshot do projeto Taliparts - e-commerce de peças automotivas",tvInstitucional:"Screenshot do projeto FGV TV Institucional - placa branca pendurada na lateral de um edifício"},icons:{menu:"Ícone de menu",close:"Ícone de fechar",expand:"Ícone de expandir",collapse:"Ícone de recolher",external:"Ícone de link externo",download:"Ícone de download",email:"Ícone de e-mail",phone:"Ícone de telefone",linkedin:"Ícone do LinkedIn",whatsapp:"Ícone do WhatsApp",github:"Ícone do GitHub",sun:"Ícone de sol",moon:"Ícone de lua",globe:"Ícone de globo",volume:"Ícone de volume",success:"Ícone de sucesso",error:"Ícone de erro",warning:"Ícone de aviso",info:"Ícone de informação"},decorative:{gradient:"Gradiente decorativo",pattern:"Padrão decorativo",divider:"Divisor visual",background:"Imagem de fundo decorativa"}},language:{changed:"Idioma alterado com sucesso",current:"Idioma atual",available:"Idiomas disponíveis",portuguese:"Português",english:"English",spanish:"Español",select:"Selecionar idioma"},theme:{toggle:"Alternar tema",changed:"Tema alterado com sucesso",light:"Modo claro ativado",dark:"Modo escuro ativado",system:"Usando preferência do sistema"},footer:{copyright:"© 2024 Tarcisio Bispo. Todos os direitos reservados.",title:"UX/Product Designer"},feedback:{title:"Feedback",subtitle:"Sua opinião importa",description:"Compartilhe sua experiência e sugestões",typeQuestion:"Que tipo de feedback você gostaria de compartilhar?",close:"Fechar",back:"Voltar",send:"Enviar feedback",sending:"Enviando...",includeEmail:"Incluir meu e-mail para resposta",privacyPolicy:"Política de Privacidade",problem:"Reportar Problema",idea:"Compartilhar Ideia",praise:"Dar Elogio",problemTitle:"Reportar Problema",ideaTitle:"Compartilhar Ideia",praiseTitle:"Dar Elogio",defaultTitle:"Enviar Feedback",problemInstruction:"Descreva o problema que você encontrou em detalhes",ideaInstruction:"Compartilhe sua ideia ou sugestão de melhoria",praiseInstruction:"Conte-nos o que você gostou",defaultInstruction:"Compartilhe seu feedback conosco",problemPlaceholder:"Descreva o problema que você encontrou...",ideaPlaceholder:"Compartilhe sua ideia ou sugestão...",praisePlaceholder:"Conte-nos o que você gostou...",defaultPlaceholder:"Compartilhe seu feedback...",validation:{messageRequired:"Mensagem é obrigatória",messageMinLength:"Mínimo 5 caracteres",emailInvalid:"E-mail inválido"},form:{type:"Tipo de feedback",message:"Sua mensagem",email:"Seu e-mail (opcional)",send:"Enviar feedback",sending:"Enviando...",success:"✅ Obrigado pelo seu feedback! Sua opinião é muito importante para nós.",error:"❌ Ops! Não conseguimos enviar seu feedback. Tente novamente ou entre em contato diretamente.",messageRequired:"A mensagem é obrigatória"},status:{success:"Obrigado pelo seu feedback!",error:"Erro ao enviar feedback. Tente novamente.",sending:"Enviando feedback..."},types:{bug:"Reportar bug",suggestion:"Sugestão",compliment:"Elogio",other:"Outro"}},cookies:{title:"Este site usa cookies",description:"Usamos cookies para melhorar sua experiência, analisar o tráfego do site e personalizar conteúdo.",learnMore:"Saiba mais",acceptAll:"Aceitar todos",rejectAll:"Rejeitar todos",managePreferences:"Gerenciar preferências",savePreferences:"Salvar preferências",required:"Obrigatório",preferences:{title:"Preferências de Cookies"},types:{necessary:{title:"Cookies Necessários",description:"Estes cookies são essenciais para o funcionamento do site e não podem ser desativados."},analytics:{title:"Cookies de Análise",description:"Nos ajudam a entender como os visitantes interagem com o site coletando informações de forma anônima.",providers:"Provedores"},marketing:{title:"Cookies de Marketing",description:"São usados para rastrear visitantes em sites para exibir anúncios relevantes e atraentes."}}},common:{close:"Fechar",open:"Abrir",save:"Salvar",cancel:"Cancelar",confirm:"Confirmar",yes:"Sim",no:"Não",loading:"Carregando...",error:"Erro",success:"Sucesso",warning:"Aviso",info:"Informação"}}},"en-US":{translation:{navigation:{profile:"Profile",projects:"Projects",backlog:"Backlog",contact:"Contact",home:"Home",goToProfile:"Go to Profile section",goToProjetos:"Go to Projects section",goToBacklog:"Go to Backlog section",goToContato:"Go to Contact section",menu:{open:"Open navigation menu",close:"Close navigation menu",toggle:"Toggle navigation menu"},settings:{title:"Settings",open:"Open settings",close:"Close settings",description:"Customize your experience",theme:{toggle:"Toggle theme",lightMode:"Light mode enabled",darkMode:"Dark mode enabled"},language:{select:"Select language"},sound:{toggle:"Toggle sound",enabled:"Sound enabled",disabled:"Sound disabled"},accessibility:{menu:"Accessibility menu",description:"Accessibility features"},feedback:{open:"Open feedback",description:"Share your experience and suggestions"}}},profile:{title:"UX/Product Designer focused on strategy, impact and experience",bio:"I'm a UX/Product Designer with strong expertise in designing digital products focused on user experience, conversion and business impact. With a background in Digital Marketing, SEO and AI, I integrate strategy, design and usability in continuous improvement and innovation processes.",exploreProjects:"Explore projects",letsChat:"Let's Chat",downloadCV:"Download CV",linkedin:"LinkedIn",name:"Tarcisio Bispo de Araujo",ixdf:"IxDF | Interaction Design Foundation",hero:{greeting:"Hello, I'm",roles:{uxDesigner:"UX Designer",productDesigner:"Product Designer",designStrategist:"Design Strategist",interactionDesigner:"Interaction Designer"}}},projects:{title:"Projects",description:"Real UX/Product Design cases focused on strategy, impact and measurable results for businesses and users.",overview:"Overview",discovery:"Discovery",solution:"Solution",iteration:"Iteration",outcomes:"Outcomes",insights:"Insights",seeMore:"See details",seeLess:"Hide details",projectImage:"Project image",badges:{usability:"Usability",informationArchitecture:"Information Architecture",userTesting:"User Testing",uxResearch:"UX Research",journeyMapping:"Journey Mapping",stakeholderManagement:"Stakeholder Management",productStrategy:"Product Strategy",seo:"SEO",productValidation:"Product Validation",visualDesign:"Visual Design",communication:"Communication",engagement:"Engagement"},fgvLaw:{title:"FGV LAW",category:"Navigation and Usability",overview:"Restructuring of the legal courses area at Direito GV focusing on usability and information organization to improve user experience.",discovery:"I identified that users faced difficulty locating and comparing courses on the Direito GV platform.",solution:"I designed a new panel with tab system and thematic filters specific to the legal context.",iteration:"After user testing, we simplified filter terminology and adjusted information hierarchy.",outcomes:["25% improvement in course visibility and 35% more interactions with specific pages in 3 months","18% increase in conversion rate from visits to enrollments, rising from approximately 8% to 10%","30% reduction in average navigation time, from about 4 minutes to 3 minutes until course selection"],insights:"Navigation structure needs to guide, not just show. Clarity and relevant grouping directly influence the perception of a course's value."},direitoGV:{title:"FGV Law Research",category:"Maps, Flows and Research",overview:"Reorganization of the research area to improve visibility of academic projects and facilitate access to researchers.",discovery:"The research area was fragmented and poorly accessible. Researchers had difficulty promoting their work and external users couldn't find relevant information about ongoing projects.",solution:"I developed a new information architecture with categorization by thematic areas, researcher profiles and project timeline. I also created an advanced search system.",iteration:"We conducted tests with students, professors and researchers. Navigation was adjusted based on feedback about nomenclature and priority order. I validated each change with involved stakeholders.",outcomes:["65% reduction in navigation time to find specific projects, from approximately 6 minutes to 2 minutes","85% increase in visits to researcher pages, rising from about 150 to over 280 monthly visits","40% growth in academic publication consultation and 25% more partnership requests in 5 months"],insights:"Institutional areas gain relevance when they are navigable, updated and strategically reflected in information architecture."},taliparts:{title:"Taliparts",category:"Strategic UX + B2B",overview:"Structuring and digital validation of Taliparts for publishing automotive parts on Mercado Livre with focus on rapid learning.",discovery:"I conducted detailed benchmark with automotive sector competitors. I interviewed mechanics and store owners, modeled personas and applied CSD Matrix to identify certainties, assumptions and doubts in the physical catalog.",solution:"I created a validation strategy with SEO for Mercado Livre, visual standardization of ads, categorization centered on buyer vocabulary and search history. I also organized KPIs and defined product prioritization plan.",iteration:"I tested products by thematic blocks, monitoring clicks, questions and conversion rate. I refined descriptions, titles and even item selection based on real performance.",outcomes:["45% growth in sales of prioritized products, generating approximately R$ 6,500 in additional revenue in 4 months","40% reduction in buyer doubts, decreasing from about 20 to 12 questions per published product","Creation of process that increased publication efficiency by 50%, allowing analysis of over 80 products in 2 months"],insights:"Digital validation at low cost is possible — and necessary. Product logic needs to consider physical context, technical vocabulary and differentials perceived by the customer."},tvInstitucional:{title:"FGV Institutional TV",category:"Engagement and Visual Communication",overview:"Visual system for TVs in FGV hall to communicate events and institutional updates in an attractive and dynamic way.",discovery:"Students ignored physical bulletin boards and institutional emails. I identified that the language of channels was outdated and poorly integrated with the visual routine of spaces.",solution:"I implemented a digital panel with weekly content curation, focus on visual rhythm and immediate clarity of messages. The platform was designed to be automated, with remote update flexibility.",iteration:"We tested types of animations, display time and contrast. We adjusted the visual calendar and optimized layout based on feedback from students and coordination.",outcomes:["35% increase in institutional event visibility, improving student knowledge about campus activities","20% growth in event participation, with greater engagement from the academic community","40% improvement in institutional information retention compared to previous communication methods"],insights:"Physical environments are also interfaces. When well designed, they inform, engage and connect — without needing login."}},backlog:{title:"Strategic Backlog Cycle",description:"Practical demonstration of how I transform business challenges into measurable UX solutions. Each case presents applied methodology, achieved results and strategic insights that generate real impact for stakeholders and users.",solution:"Solution",result:"Result",note:"Note",noItems:"No items on this page.",previous:"Previous",next:"Next",items:[{challenge:"Users abandoned long forms without completing registration",solution:"Implemented step-by-step form with progress bar and real-time validation",result:"40% increase in registration completion rate in 2 weeks",note:"Breaking complex tasks into smaller steps reduces anxiety and improves experience."},{challenge:"Low adoption of premium features due to lack of clarity about benefits",solution:"Created interactive onboarding with practical demonstrations of premium features",result:"60% growth in conversions to paid plans in 1 month",note:"Showing value through practical experience is more effective than just listing features."},{challenge:"Users couldn't easily find support when they needed help",solution:"Redesigned contextual help system with intelligent chatbot and dynamic FAQ",result:"50% reduction in support tickets and 35% increase in satisfaction",note:"Contextual help at the right moment prevents frustrations and improves user autonomy."},{challenge:"Complex interface caused confusion in beginner users",solution:"Developed simplified mode with progressive tutorial and adaptive tooltips",result:"45% decrease in new user abandonment rate",note:"Adapting complexity to user experience level significantly improves adoption."},{challenge:"Checkout process had high abandonment rate at the last step",solution:"Simplified flow by removing unnecessary fields and adding express payment options",result:"30% increase in purchase completion and 25% reduction in checkout time",note:"Each extra field in checkout is a potential barrier. Simplicity generates conversion."},{challenge:"Users didn't notice important product updates",solution:"Implemented in-app notification system with non-intrusive design and personalization",result:"55% improvement in engagement with new features",note:"Effective communication about changes keeps users informed without interrupting flow."},{challenge:"Difficulty finding relevant content in extensive knowledge base",solution:"Created intelligent search system with contextual filters and automatic suggestions",result:"70% increase in knowledge base usage and 40% reduction in repetitive queries",note:"Efficient search transforms abundant information into accessible knowledge."},{challenge:"Low engagement in platform's collaborative features",solution:"Redesigned collaboration interface with visual activity indicators and subtle gamification",result:"80% growth in user collaboration in 6 weeks",note:"Making collaboration visible and rewarding naturally encourages participation."},{challenge:"Users lost progress when navigating between app sections",solution:"Implemented auto-save system with visual indicators of save status",result:"Elimination of 95% of complaints about data loss",note:"Trust in technology grows when users see their work is always protected."},{challenge:"Non-responsive interface caused frustration on mobile devices",solution:"Developed mobile-first version with intuitive gestures and touch-optimized navigation",result:"120% increase in mobile usage and improvement from 4.2 to 4.7 in app store rating",note:"Mobile-first design ensures consistent experience regardless of device used."},{challenge:"Users with visual impairments faced accessibility barriers",solution:"Implemented WCAG 2.1 standards with keyboard navigation and screen reader compatibility",result:"200% increase in usage by people with disabilities and recognition in accessibility award",note:"Accessibility is not just compliance — it's inclusive design that benefits all users."},{challenge:"Technical texts confused non-specialized users",solution:"Rewrote microcopy with clear language and added contextual explanations when necessary",result:"60% reduction in user doubts and improvement in perceived ease of use",note:"Small decisions in text have great impact on reading experience and comprehension."}]},contact:{title:"Let's talk?",description:"I'm always open to new opportunities and collaborations. Get in touch to discuss projects, partnerships or just exchange ideas about UX and product design.",form:{name:"Name",namePlaceholder:"Enter your full name",email:"Email",emailPlaceholder:"Enter your best email",subject:"Subject",subjectPlaceholder:"What would you like to talk about?",message:"Message",messagePlaceholder:"Tell me about your project, opportunity or how I can help...",send:"Send message",sending:"Sending...",success:"✅ Message sent successfully! I'll get back to you soon.",error:"❌ Oops! We couldn't send your message. Try again or contact directly.",nameRequired:"Name is required",emailRequired:"Email is required",emailInvalid:"Invalid email",subjectRequired:"Subject is required",messageRequired:"Message is required",messageMinLength:"Message must be at least 10 characters"},info:{email:"<EMAIL>",location:"São Paulo, Brazil",availability:"Available for freelance projects and full-time opportunities"},social:{linkedin:"LinkedIn",whatsapp:"WhatsApp",email:"Email"}},accessibility:{title:"Accessibility",subtitle:"Customize your browsing experience",description:"Configure accessibility options to improve your experience.",instructions:"Use the options below to customize the interface.",close:"Close",open:"Open",menuLabel:"Accessibility Menu",menuTooltip:"Accessibility settings (Shift + A)",fontSize:{label:"Font size",increase:"Increase",decrease:"Decrease",reset:"Reset",increaseLabel:"Increase font size",decreaseLabel:"Decrease font size",resetLabel:"Reset font size"},contrast:{label:"High contrast",enable:"Enable high contrast",disable:"Disable high contrast",enabled:"High contrast enabled",disabled:"High contrast disabled"},readingMode:{label:"Reading mode",enable:"Enable reading mode",disable:"Disable reading mode",enabled:"Reading mode enabled",disabled:"Reading mode disabled"},screenReader:{label:"Screen reader",enable:"Enable screen reader",disable:"Disable screen reader",enabled:"Screen reader enabled",disabled:"Screen reader disabled"},reset:{label:"Reset settings",action:"Accessibility settings reset",success:"Settings reset successfully"},features:{fontSize:"Font size",fontSizeIncrease:"Increase font",fontSizeDecrease:"Decrease font",fontSizeReset:"Reset font",contrast:"High contrast",contrastEnable:"Enable contrast",contrastDisable:"Disable contrast",readingMode:"Reading mode",readingModeEnable:"Enable reading",readingModeDisable:"Disable reading"},skipLinks:{skipToContent:"Skip to main content",skipToNavigation:"Skip to navigation"},status:{enabled:"Enabled",disabled:"Disabled",fontIncreased:"Font increased",fontDecreased:"Font decreased",fontReset:"Font reset",contrastEnabled:"High contrast enabled",contrastDisabled:"High contrast disabled",readingEnabled:"Reading mode enabled",readingDisabled:"Reading mode disabled"}},sound:{enabled:"Sound enabled",disabled:"Sound disabled",enabledDesc:"Sound effects enabled",disabledDesc:"Sound effects disabled",enable:"Enable sound",disable:"Disable sound",toggle:"Toggle sound",changed:"Sound changed to",volume:"Volume",volumeControl:"Adjust sound volume"},tooltips:{theme:{light:"Switch to light mode",dark:"Switch to dark mode",system:"Use system preference"},language:{switch:"Switch language",current:"Current language",available:"Available languages"},navigation:{home:"Go to home",profile:"Go to profile",projects:"View projects",backlog:"View strategic backlog",contact:"Get in touch"},social:{linkedin:"LinkedIn profile",email:"Send email",whatsapp:"Chat on WhatsApp"},sound:{enable:"Enable sound effects",disable:"Disable sound effects"},actions:{expand:"Expand details",collapse:"Collapse details",download:"Download file",share:"Share",copy:"Copy link",print:"Print page",backToTop:"Back to top"}},toasts:{success:{title:"Success!",messageSent:"Message sent successfully!",settingsSaved:"Settings saved",linkCopied:"Link copied to clipboard",themeChanged:"Theme changed",languageChanged:"Language changed"},error:{title:"Error",messageNotSent:"Error sending message",networkError:"Connection error",genericError:"Something went wrong",tryAgain:"Try again"},info:{title:"Information",loading:"Loading...",processing:"Processing...",saving:"Saving..."},warning:{title:"Warning",unsavedChanges:"You have unsaved changes",confirmAction:"Are you sure you want to continue?"}},seo:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer specialized in strategy, impact and user experience. See my digital product design projects and UX solutions.",keywords:"UX Designer, Product Designer, Product Design, User Experience, UI/UX, Portfolio, São Paulo",author:"Tarcisio Bispo de Araujo",pages:{home:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer focused on strategy, impact and user experience. Expert in digital product design, conversion and business impact."},projects:{title:"Projects - Tarcisio Bispo | UX Designer",description:"See my main UX/Product Design projects: FGV LAW, Direito GV, Taliparts and FGV Institutional TV. Real cases with measurable results."},backlog:{title:"Strategic Backlog - Tarcisio Bispo | UX Designer",description:"Practical demonstration of how I transform business challenges into measurable UX solutions. Applied methodology, achieved results and strategic insights."},contact:{title:"Contact - Tarcisio Bispo | UX Designer",description:"Get in touch to discuss projects, partnerships or opportunities. Available for freelance projects and full-time opportunities."}}},schema:{person:{name:"Tarcisio Bispo de Araujo",jobTitle:"UX/Product Designer",description:"UX/Product Designer specialized in strategy, impact and user experience",location:"São Paulo, Brazil",email:"<EMAIL>",skills:["UX Design","Product Design","Design Strategy","User Research","Prototyping","Usability Testing"]},organization:{name:"Tarcisio Bispo Portfolio",description:"Professional UX/Product Design portfolio",location:"São Paulo, Brazil"}},alts:{profile:{photo:"Tarcisio Bispo profile photo",ixdfLogo:"Interaction Design Foundation logo",ixdfSeal:"IxDF certification seal"},projects:{fgvLaw:"FGV LAW project screenshot - designers working as a team in the office",direitoGV:"FGV Law Research project screenshot - wooden table with book in front of bookshelf",taliparts:"Taliparts project screenshot - automotive parts e-commerce",tvInstitucional:"FGV Institutional TV project screenshot - white sign hanging on the side of a building"},icons:{menu:"Menu icon",close:"Close icon",expand:"Expand icon",collapse:"Collapse icon",external:"External link icon",download:"Download icon",email:"Email icon",phone:"Phone icon",linkedin:"LinkedIn icon",whatsapp:"WhatsApp icon",github:"GitHub icon",sun:"Sun icon",moon:"Moon icon",globe:"Globe icon",volume:"Volume icon",success:"Success icon",error:"Error icon",warning:"Warning icon",info:"Information icon"},decorative:{gradient:"Decorative gradient",pattern:"Decorative pattern",divider:"Visual divider",background:"Decorative background image"}},language:{changed:"Language changed successfully",current:"Current language",available:"Available languages",portuguese:"Português",english:"English",spanish:"Español",select:"Select language"},theme:{toggle:"Toggle theme",changed:"Theme changed successfully",light:"Light mode enabled",dark:"Dark mode enabled",system:"Using system preference"},footer:{copyright:"© 2024 Tarcisio Bispo. All rights reserved.",title:"UX/Product Designer"},feedback:{title:"Feedback",subtitle:"Your opinion matters",description:"Share your experience and suggestions",typeQuestion:"What type of feedback would you like to share?",close:"Close",back:"Back",send:"Send feedback",sending:"Sending...",includeEmail:"Include my email for response",privacyPolicy:"Privacy Policy",problem:"Report Problem",idea:"Share Idea",praise:"Give Praise",problemTitle:"Report Problem",ideaTitle:"Share Idea",praiseTitle:"Give Praise",defaultTitle:"Send Feedback",problemInstruction:"Describe the problem you found in detail",ideaInstruction:"Share your idea or improvement suggestion",praiseInstruction:"Tell us what you liked",defaultInstruction:"Share your feedback with us",problemPlaceholder:"Describe the problem you found...",ideaPlaceholder:"Share your idea or suggestion...",praisePlaceholder:"Tell us what you liked...",defaultPlaceholder:"Share your feedback...",validation:{messageRequired:"Message is required",messageMinLength:"Minimum 5 characters",emailInvalid:"Invalid email"},form:{type:"Feedback type",message:"Your message",email:"Your email (optional)",send:"Send feedback",sending:"Sending...",success:"✅ Thank you for your feedback! Your opinion is very important to us.",error:"❌ Oops! We couldn't send your feedback. Try again or contact directly.",messageRequired:"Message is required"},status:{success:"Thank you for your feedback!",error:"Error sending feedback. Please try again.",sending:"Sending feedback..."},types:{bug:"Report bug",suggestion:"Suggestion",compliment:"Compliment",other:"Other"}},cookies:{title:"This site uses cookies",description:"We use cookies to improve your experience, analyze site traffic and personalize content.",learnMore:"Learn more",acceptAll:"Accept all",rejectAll:"Reject all",managePreferences:"Manage preferences",savePreferences:"Save preferences",required:"Required",preferences:{title:"Cookie Preferences"},types:{necessary:{title:"Necessary Cookies",description:"These cookies are essential for the website to function and cannot be disabled."},analytics:{title:"Analytics Cookies",description:"Help us understand how visitors interact with the site by collecting information anonymously.",providers:"Providers"},marketing:{title:"Marketing Cookies",description:"Used to track visitors across websites to display relevant and engaging ads."}}},common:{close:"Close",open:"Open",save:"Save",cancel:"Cancel",confirm:"Confirm",yes:"Yes",no:"No",loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Information"}}},"es-ES":{translation:{navigation:{profile:"Perfil",projects:"Proyectos",backlog:"Backlog",contact:"Contacto",home:"Inicio",goToProfile:"Ir a sección Perfil",goToProjetos:"Ir a sección Proyectos",goToBacklog:"Ir a sección Backlog",goToContato:"Ir a sección Contacto",menu:{open:"Abrir menú de navegación",close:"Cerrar menú de navegación",toggle:"Alternar menú de navegación"},settings:{title:"Configuraciones",open:"Abrir configuraciones",close:"Cerrar configuraciones",description:"Personaliza tu experiencia",theme:{toggle:"Alternar tema",lightMode:"Modo claro activado",darkMode:"Modo oscuro activado"},language:{select:"Seleccionar idioma"},sound:{toggle:"Alternar sonido",enabled:"Sonido activado",disabled:"Sonido desactivado"},accessibility:{menu:"Menú de accesibilidad",description:"Funciones de accesibilidad"},feedback:{open:"Abrir feedback",description:"Comparte tu experiencia y sugerencias"}}},profile:{title:"UX/Product Designer enfocado en estrategia, impacto y experiencia",bio:"Soy UX/Product Designer con fuerte experiencia en el diseño de productos digitales enfocados en experiencia del usuario, conversión e impacto empresarial. Con experiencia en Marketing Digital, SEO e IA, integro estrategia, diseño y usabilidad en procesos continuos de mejora e innovación.",exploreProjects:"Explorar proyectos",letsChat:"Conversemos",downloadCV:"Descargar CV",linkedin:"LinkedIn",name:"Tarcisio Bispo de Araujo",ixdf:"IxDF | Interaction Design Foundation",hero:{greeting:"Hola, soy",roles:{uxDesigner:"UX Designer",productDesigner:"Product Designer",designStrategist:"Design Strategist",interactionDesigner:"Interaction Designer"}}},projects:{title:"Proyectos",description:"Casos reales de UX/Product Design enfocados en estrategia, impacto y resultados medibles para empresas y usuarios.",overview:"Resumen",discovery:"Descubrimiento",solution:"Solución",iteration:"Iteración",outcomes:"Resultados",insights:"Insights",seeMore:"Ver detalles",seeLess:"Ocultar detalles",projectImage:"Imagen del proyecto",badges:{usability:"Usabilidad",informationArchitecture:"Arquitectura de la Información",userTesting:"Pruebas de Usuario",uxResearch:"UX Research",journeyMapping:"Mapeo de Jornada",stakeholderManagement:"Gestión de Stakeholders",productStrategy:"Estrategia de Producto",seo:"SEO",productValidation:"Validación de Producto",visualDesign:"Diseño Visual",communication:"Comunicación",engagement:"Engagement"},fgvLaw:{title:"FGV LAW",category:"Navegación y Usabilidad",overview:"Reestructuración del área de cursos jurídicos de Direito GV enfocada en usabilidad y organización de la información para mejorar la experiencia del usuario.",discovery:"Identifiqué que los usuarios tenían dificultades para localizar y comparar cursos en la plataforma de Direito GV.",solution:"Diseñé un nuevo panel con sistema de pestañas y filtros temáticos específicos para el contexto jurídico.",iteration:"Después de las pruebas con usuarios, simplificamos la terminología de los filtros y ajustamos la jerarquía de información.",outcomes:["Mejora del 25% en la visibilidad de cursos y 35% más interacciones con páginas específicas en 3 meses","Aumento del 18% en la tasa de conversión de visitas a inscripciones, pasando de aproximadamente 8% a 10%","Reducción del 30% en el tiempo promedio de navegación, de cerca de 4 minutos a 3 minutos hasta la selección del curso"],insights:"La estructura de navegación necesita guiar, no solo mostrar. La claridad y agrupación relevante influyen directamente en la percepción de valor de un curso."},direitoGV:{title:"Investigación Derecho FGV",category:"Mapas, Flujos e Investigación",overview:"Reorganización del área de investigación para mejorar la visibilidad de proyectos académicos y facilitar el acceso a investigadores.",discovery:"El área de investigación estaba fragmentada y era poco accesible. Los investigadores tenían dificultades para promocionar su trabajo y los usuarios externos no podían encontrar información relevante sobre proyectos en curso.",solution:"Desarrollé una nueva arquitectura de información con categorización por áreas temáticas, perfiles de investigadores y línea de tiempo de proyectos. También creé un sistema de búsqueda avanzada.",iteration:"Realizamos pruebas con estudiantes, profesores e investigadores. La navegación se ajustó basándose en comentarios sobre nomenclatura y orden de prioridades. Validé cada cambio con los stakeholders involucrados.",outcomes:["Reducción del 65% en el tiempo de navegación para encontrar proyectos específicos, de aproximadamente 6 minutos a 2 minutos","Aumento del 85% en visitas a páginas de investigadores, pasando de cerca de 150 a más de 280 visitas mensuales","Crecimiento del 40% en consulta de publicaciones académicas y 25% más solicitudes de asociaciones en 5 meses"],insights:"Las áreas institucionales ganan relevancia cuando son navegables, actualizadas y reflejadas estratégicamente en la arquitectura de información."},taliparts:{title:"Taliparts",category:"UX Estratégico + B2B",overview:"Estructuración y validación digital de Taliparts para publicación de autopartes en Mercado Libre con enfoque en aprendizaje rápido.",discovery:"Realicé benchmark detallado con competidores del sector automotriz. Entrevisté mecánicos y propietarios de tiendas, modelé personas y apliqué la Matriz CSD para identificar certezas, suposiciones y dudas en el catálogo físico.",solution:"Creé una estrategia de validación con SEO para Mercado Libre, estandarización visual de anuncios, categorización centrada en el vocabulario del comprador e historial de búsquedas. También organicé KPIs y definí plan de priorización de productos.",iteration:"Probé productos por bloques temáticos, monitoreando clics, preguntas y tasa de conversión. Refiné descripciones, títulos e incluso selección de artículos basándome en rendimiento real.",outcomes:["Crecimiento del 45% en ventas de productos priorizados, generando aproximadamente R$ 6,500 en ingresos adicionales en 4 meses","Reducción del 40% en dudas de compradores, disminuyendo de cerca de 20 a 12 preguntas por producto publicado","Creación de proceso que aumentó la eficiencia de publicación en 50%, permitiendo análisis de más de 80 productos en 2 meses"],insights:"La validación digital a bajo costo es posible — y necesaria. La lógica del producto necesita considerar contexto físico, vocabulario técnico y diferenciales percibidos por el cliente."},tvInstitucional:{title:"FGV TV Institucional",category:"Engagement y Comunicación Visual",overview:"Sistema visual para TVs en el hall de FGV para comunicar eventos y actualizaciones institucionales de manera atractiva y dinámica.",discovery:"Los estudiantes ignoraban carteleras físicas y emails institucionales. Identifiqué que el lenguaje de los canales estaba desactualizado y mal integrado con la rutina visual de los espacios.",solution:"Implementé un panel digital con curaduría de contenido semanal, enfoque en ritmo visual y claridad inmediata de mensajes. La plataforma fue diseñada para ser automatizada, con flexibilidad de actualización remota.",iteration:"Probamos tipos de animaciones, tiempo de visualización y contraste. Ajustamos el calendario visual y optimizamos el layout basándose en comentarios de estudiantes y coordinación.",outcomes:["Aumento del 35% en visibilidad de eventos institucionales, mejorando el conocimiento de estudiantes sobre actividades del campus","Crecimiento del 20% en participación en eventos, con mayor engagement de la comunidad académica","Mejora del 40% en retención de información institucional comparado con métodos anteriores de comunicación"],insights:"Los ambientes físicos también son interfaces. Cuando están bien diseñados, informan, involucran y conectan — sin necesidad de login."}},backlog:{title:"Ciclo de Backlog Estratégico",description:"Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Cada caso presenta metodología aplicada, resultados alcanzados e insights estratégicos que generan impacto real para stakeholders y usuarios.",solution:"Solución",result:"Resultado",note:"Nota",noItems:"No hay elementos en esta página.",previous:"Anterior",next:"Siguiente",items:[{challenge:"Los usuarios abandonaban formularios largos sin completar el registro",solution:"Implementé formulario paso a paso con barra de progreso y validación en tiempo real",result:"Aumento del 40% en la tasa de finalización de registros en 2 semanas",note:"Dividir tareas complejas en pasos más pequeños reduce la ansiedad y mejora la experiencia."},{challenge:"Baja adopción de funciones premium debido a falta de claridad sobre beneficios",solution:"Creé onboarding interactivo con demostraciones prácticas de funciones premium",result:"Crecimiento del 60% en conversiones a planes pagos en 1 mes",note:"Mostrar valor a través de experiencia práctica es más efectivo que solo listar funcionalidades."},{challenge:"Los usuarios no encontraban fácilmente soporte cuando necesitaban ayuda",solution:"Rediseñé sistema de ayuda contextual con chatbot inteligente y FAQ dinámico",result:"Reducción del 50% en tickets de soporte y aumento del 35% en satisfacción",note:"Ayuda contextual en el momento correcto previene frustraciones y mejora la autonomía del usuario."},{challenge:"Interfaz compleja causaba confusión en usuarios principiantes",solution:"Desarrollé modo simplificado con tutorial progresivo y tooltips adaptativos",result:"Disminución del 45% en tasa de abandono de nuevos usuarios",note:"Adaptar la complejidad al nivel de experiencia del usuario mejora significativamente la adopción."},{challenge:"Proceso de checkout tenía alta tasa de abandono en el último paso",solution:"Simplifiqué flujo eliminando campos innecesarios y agregando opciones de pago express",result:"Aumento del 30% en finalización de compras y reducción del 25% en tiempo de checkout",note:"Cada campo extra en checkout es una barrera potencial. La simplicidad genera conversión."},{challenge:"Los usuarios no percibían actualizaciones importantes del producto",solution:"Implementé sistema de notificaciones in-app con diseño no intrusivo y personalización",result:"Mejora del 55% en engagement con nuevas funcionalidades",note:"Comunicación efectiva sobre cambios mantiene a usuarios informados sin interrumpir el flujo."},{challenge:"Dificultad para encontrar contenido relevante en base de conocimiento extensa",solution:"Creé sistema de búsqueda inteligente con filtros contextuales y sugerencias automáticas",result:"Aumento del 70% en utilización de base de conocimiento y reducción del 40% en consultas repetitivas",note:"Búsqueda eficiente transforma información abundante en conocimiento accesible."},{challenge:"Bajo engagement en funcionalidades colaborativas de la plataforma",solution:"Rediseñé interfaz de colaboración con indicadores visuales de actividad y gamificación sutil",result:"Crecimiento del 80% en colaboración entre usuarios en 6 semanas",note:"Hacer la colaboración visible y gratificante incentiva naturalmente la participación."},{challenge:"Los usuarios perdían progreso al navegar entre secciones de la app",solution:"Implementé sistema de auto-guardado con indicadores visuales de estado de guardado",result:"Eliminación del 95% de quejas sobre pérdida de datos",note:"La confianza en la tecnología crece cuando los usuarios ven que su trabajo está siempre protegido."},{challenge:"Interfaz no responsiva causaba frustración en dispositivos móviles",solution:"Desarrollé versión mobile-first con gestos intuitivos y navegación optimizada para toque",result:"Aumento del 120% en uso móvil y mejora de 4.2 a 4.7 en calificación de app store",note:"Diseño mobile-first garantiza experiencia consistente independientemente del dispositivo usado."},{challenge:"Usuarios con discapacidad visual enfrentaban barreras de accesibilidad",solution:"Implementé estándares WCAG 2.1 con navegación por teclado y compatibilidad con lectores de pantalla",result:"Aumento del 200% en uso por personas con discapacidad y reconocimiento en premio de accesibilidad",note:"Accesibilidad no es solo cumplimiento — es diseño inclusivo que beneficia a todos los usuarios."},{challenge:"Textos técnicos confundían a usuarios no especializados",solution:"Reescribí microcopy con lenguaje claro y agregué explicaciones contextuales cuando era necesario",result:"Reducción del 60% en dudas de usuarios y mejora en percepción de facilidad de uso",note:"Pequeñas decisiones en el texto tienen gran impacto en la experiencia de lectura y comprensión."}]},contact:{title:"¿Conversamos?",description:"Siempre estoy abierto a nuevas oportunidades y colaboraciones. Ponte en contacto para discutir proyectos, asociaciones o simplemente intercambiar ideas sobre UX y diseño de productos.",form:{name:"Nombre",namePlaceholder:"Ingresa tu nombre completo",email:"Email",emailPlaceholder:"Ingresa tu mejor email",subject:"Asunto",subjectPlaceholder:"¿De qué te gustaría hablar?",message:"Mensaje",messagePlaceholder:"Cuéntame sobre tu proyecto, oportunidad o cómo puedo ayudar...",send:"Enviar mensaje",sending:"Enviando...",success:"✅ ¡Mensaje enviado con éxito! Te responderé pronto.",error:"❌ ¡Ups! No pudimos enviar tu mensaje. Intenta de nuevo o contacta directamente.",nameRequired:"El nombre es obligatorio",emailRequired:"El email es obligatorio",emailInvalid:"Email inválido",subjectRequired:"El asunto es obligatorio",messageRequired:"El mensaje es obligatorio",messageMinLength:"El mensaje debe tener al menos 10 caracteres"},info:{email:"<EMAIL>",location:"São Paulo, Brasil",availability:"Disponible para proyectos freelance y oportunidades full-time"},social:{linkedin:"LinkedIn",whatsapp:"WhatsApp",email:"Email"}},accessibility:{title:"Accesibilidad",subtitle:"Personaliza tu experiencia de navegación",description:"Configura opciones de accesibilidad para mejorar tu experiencia.",instructions:"Usa las opciones a continuación para personalizar la interfaz.",close:"Cerrar",open:"Abrir",menuLabel:"Menú de Accesibilidad",menuTooltip:"Configuraciones de accesibilidad (Shift + A)",fontSize:{label:"Tamaño de fuente",increase:"Aumentar",decrease:"Disminuir",reset:"Resetear",increaseLabel:"Aumentar tamaño de fuente",decreaseLabel:"Disminuir tamaño de fuente",resetLabel:"Resetear tamaño de fuente"},contrast:{label:"Alto contraste",enable:"Activar alto contraste",disable:"Desactivar alto contraste",enabled:"Alto contraste activado",disabled:"Alto contraste desactivado"},readingMode:{label:"Modo lectura",enable:"Activar modo lectura",disable:"Desactivar modo lectura",enabled:"Modo lectura activado",disabled:"Modo lectura desactivado"},screenReader:{label:"Lector de pantalla",enable:"Activar lector de pantalla",disable:"Desactivar lector de pantalla",enabled:"Lector de pantalla activado",disabled:"Lector de pantalla desactivado"},reset:{label:"Resetear configuraciones",action:"Configuraciones de accesibilidad reseteadas",success:"Configuraciones reseteadas con éxito"},features:{fontSize:"Tamaño de fuente",fontSizeIncrease:"Aumentar fuente",fontSizeDecrease:"Disminuir fuente",fontSizeReset:"Resetear fuente",contrast:"Alto contraste",contrastEnable:"Activar contraste",contrastDisable:"Desactivar contraste",readingMode:"Modo lectura",readingModeEnable:"Activar lectura",readingModeDisable:"Desactivar lectura"},skipLinks:{skipToContent:"Saltar al contenido principal",skipToNavigation:"Saltar a navegación"},status:{enabled:"Activado",disabled:"Desactivado",fontIncreased:"Fuente aumentada",fontDecreased:"Fuente disminuida",fontReset:"Fuente reseteada",contrastEnabled:"Alto contraste activado",contrastDisabled:"Alto contraste desactivado",readingEnabled:"Modo lectura activado",readingDisabled:"Modo lectura desactivado"}},sound:{enabled:"Sonido activado",disabled:"Sonido desactivado",enabledDesc:"Efectos de sonido activados",disabledDesc:"Efectos de sonido desactivados",enable:"Activar sonido",disable:"Desactivar sonido",toggle:"Alternar sonido",changed:"Sonido cambiado a",volume:"Volumen",volumeControl:"Ajustar volumen del sonido"},tooltips:{theme:{light:"Cambiar a modo claro",dark:"Cambiar a modo oscuro",system:"Usar preferencia del sistema"},language:{switch:"Cambiar idioma",current:"Idioma actual",available:"Idiomas disponibles"},navigation:{home:"Ir al inicio",profile:"Ir al perfil",projects:"Ver proyectos",backlog:"Ver backlog estratégico",contact:"Ponerse en contacto"},social:{linkedin:"Perfil de LinkedIn",email:"Enviar email",whatsapp:"Chatear en WhatsApp"},sound:{enable:"Activar efectos de sonido",disable:"Desactivar efectos de sonido"},actions:{expand:"Expandir detalles",collapse:"Contraer detalles",download:"Descargar archivo",share:"Compartir",copy:"Copiar enlace",print:"Imprimir página",backToTop:"Volver arriba"}},toasts:{success:{title:"¡Éxito!",messageSent:"¡Mensaje enviado con éxito!",settingsSaved:"Configuraciones guardadas",linkCopied:"Enlace copiado al portapapeles",themeChanged:"Tema cambiado",languageChanged:"Idioma cambiado"},error:{title:"Error",messageNotSent:"Error al enviar mensaje",networkError:"Error de conexión",genericError:"Algo salió mal",tryAgain:"Intenta de nuevo"},info:{title:"Información",loading:"Cargando...",processing:"Procesando...",saving:"Guardando..."},warning:{title:"Advertencia",unsavedChanges:"Tienes cambios no guardados",confirmAction:"¿Estás seguro de que quieres continuar?"}},seo:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer especializado en estrategia, impacto y experiencia del usuario. Ve mis proyectos de diseño de productos digitales y soluciones UX.",keywords:"UX Designer, Product Designer, Diseño de Producto, Experiencia del Usuario, UI/UX, Portfolio, São Paulo",author:"Tarcisio Bispo de Araujo",pages:{home:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer enfocado en estrategia, impacto y experiencia del usuario. Experto en diseño de productos digitales, conversión e impacto empresarial."},projects:{title:"Proyectos - Tarcisio Bispo | UX Designer",description:"Ve mis principales proyectos de UX/Product Design: FGV LAW, Direito GV, Taliparts y FGV TV Institucional. Casos reales con resultados medibles."},backlog:{title:"Backlog Estratégico - Tarcisio Bispo | UX Designer",description:"Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Metodología aplicada, resultados alcanzados e insights estratégicos."},contact:{title:"Contacto - Tarcisio Bispo | UX Designer",description:"Ponte en contacto para discutir proyectos, asociaciones u oportunidades. Disponible para proyectos freelance y oportunidades full-time."}}},schema:{person:{name:"Tarcisio Bispo de Araujo",jobTitle:"UX/Product Designer",description:"UX/Product Designer especializado en estrategia, impacto y experiencia del usuario",location:"São Paulo, Brasil",email:"<EMAIL>",skills:["UX Design","Product Design","Estrategia de Diseño","Investigación de Usuario","Prototipado","Pruebas de Usabilidad"]},organization:{name:"Tarcisio Bispo Portfolio",description:"Portfolio profesional de UX/Product Design",location:"São Paulo, Brasil"}},alts:{profile:{photo:"Foto de perfil de Tarcisio Bispo",ixdfLogo:"Logo de Interaction Design Foundation",ixdfSeal:"Sello de certificación IxDF"},projects:{fgvLaw:"Captura de pantalla del proyecto FGV LAW - diseñadores trabajando en equipo en la oficina",direitoGV:"Captura de pantalla del proyecto Investigación Derecho FGV - mesa de madera con libro frente a estantería",taliparts:"Captura de pantalla del proyecto Taliparts - e-commerce de autopartes",tvInstitucional:"Captura de pantalla del proyecto FGV TV Institucional - cartel blanco colgado en el lateral de un edificio"},icons:{menu:"Ícono de menú",close:"Ícono de cerrar",expand:"Ícono de expandir",collapse:"Ícono de contraer",external:"Ícono de enlace externo",download:"Ícono de descarga",email:"Ícono de email",phone:"Ícono de teléfono",linkedin:"Ícono de LinkedIn",whatsapp:"Ícono de WhatsApp",github:"Ícono de GitHub",sun:"Ícono de sol",moon:"Ícono de luna",globe:"Ícono de globo",volume:"Ícono de volumen",success:"Ícono de éxito",error:"Ícono de error",warning:"Ícono de advertencia",info:"Ícono de información"},decorative:{gradient:"Gradiente decorativo",pattern:"Patrón decorativo",divider:"Divisor visual",background:"Imagen de fondo decorativa"}},language:{changed:"Idioma cambiado con éxito",current:"Idioma actual",available:"Idiomas disponibles",portuguese:"Português",english:"English",spanish:"Español",select:"Seleccionar idioma"},theme:{toggle:"Alternar tema",changed:"Tema cambiado con éxito",light:"Modo claro activado",dark:"Modo oscuro activado",system:"Usando preferencia del sistema"},footer:{copyright:"© 2024 Tarcisio Bispo. Todos los derechos reservados.",title:"UX/Product Designer"},feedback:{title:"Feedback",subtitle:"Tu opinión importa",description:"Comparte tu experiencia y sugerencias",typeQuestion:"¿Qué tipo de feedback te gustaría compartir?",close:"Cerrar",back:"Volver",send:"Enviar feedback",sending:"Enviando...",includeEmail:"Incluir mi email para respuesta",privacyPolicy:"Política de Privacidad",problem:"Reportar Problema",idea:"Compartir Idea",praise:"Dar Elogio",problemTitle:"Reportar Problema",ideaTitle:"Compartir Idea",praiseTitle:"Dar Elogio",defaultTitle:"Enviar Feedback",problemInstruction:"Describe el problema que encontraste en detalle",ideaInstruction:"Comparte tu idea o sugerencia de mejora",praiseInstruction:"Cuéntanos qué te gustó",defaultInstruction:"Comparte tu feedback con nosotros",problemPlaceholder:"Describe el problema que encontraste...",ideaPlaceholder:"Comparte tu idea o sugerencia...",praisePlaceholder:"Cuéntanos qué te gustó...",defaultPlaceholder:"Comparte tu feedback...",validation:{messageRequired:"El mensaje es obligatorio",messageMinLength:"Mínimo 5 caracteres",emailInvalid:"Email inválido"},form:{type:"Tipo de feedback",message:"Tu mensaje",email:"Tu email (opcional)",send:"Enviar feedback",sending:"Enviando...",success:"✅ ¡Gracias por tu feedback! Tu opinión es muy importante para nosotros.",error:"❌ ¡Ups! No pudimos enviar tu feedback. Intenta de nuevo o contacta directamente.",messageRequired:"El mensaje es obligatorio"},status:{success:"¡Gracias por tu feedback!",error:"Error al enviar feedback. Inténtalo de nuevo.",sending:"Enviando feedback..."},types:{bug:"Reportar bug",suggestion:"Sugerencia",compliment:"Elogio",other:"Otro"}},cookies:{title:"Este sitio usa cookies",description:"Usamos cookies para mejorar tu experiencia, analizar el tráfico del sitio y personalizar contenido.",learnMore:"Saber más",acceptAll:"Aceptar todas",rejectAll:"Rechazar todas",managePreferences:"Gestionar preferencias",savePreferences:"Guardar preferencias",required:"Obligatorio",preferences:{title:"Preferencias de Cookies"},types:{necessary:{title:"Cookies Necesarias",description:"Estas cookies son esenciales para el funcionamiento del sitio web y no se pueden desactivar."},analytics:{title:"Cookies de Análisis",description:"Nos ayudan a entender cómo los visitantes interactúan con el sitio recopilando información de forma anónima.",providers:"Proveedores"},marketing:{title:"Cookies de Marketing",description:"Se utilizan para rastrear visitantes en sitios web para mostrar anuncios relevantes y atractivos."}}},common:{close:"Cerrar",open:"Abrir",save:"Guardar",cancel:"Cancelar",confirm:"Confirmar",yes:"Sí",no:"No",loading:"Cargando...",error:"Error",success:"Éxito",warning:"Advertencia",info:"Información"}}}};q.use(F).use(_).init({resources:ya,lng:"pt-BR",fallbackLng:"pt-BR",debug:!1,react:{useSuspense:!1},interpolation:{escapeValue:!1},defaultNS:"translation",detection:{order:["localStorage","navigator"],caches:["localStorage"]}}).then((()=>{})).catch((e=>{console.error("❌ i18n initialization failed:",e)}));const wa=({children:e})=>{const[t,o]=n.useState(!1),{t:a}=$();return n.useEffect((()=>{const e=()=>{q.isInitialized&&q.hasResourceBundle("pt-BR","translation")?o(!0):setTimeout(e,100)};e();const t=()=>{o(!0)};return q.on("languageChanged",t),q.on("initialized",(()=>{e()})),()=>{q.off("languageChanged",t),q.off("initialized",e)}}),[a]),t?r.jsx(r.Fragment,{children:e}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-white dark:bg-gray-900",children:r.jsxs("div",{className:"text-center",children:[r.jsx(va,{}),r.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Carregando traduções..."})]})})},xa=[{href:"#perfil",icon:M,sectionId:"perfil",i18nKey:"navigation.profile"},{href:"#projetos",icon:z,sectionId:"projetos",i18nKey:"navigation.projects"},{href:"#backlog",icon:N,sectionId:"backlog",i18nKey:"navigation.backlog"},{href:"#contato",icon:L,sectionId:"contato",i18nKey:"navigation.contact"}],ka=({isOpen:e,onClose:t,activeSection:o,onNavigate:a,items:i})=>e?r.jsxs("div",{className:"fixed inset-0 z-50 md:hidden",children:[r.jsx("div",{className:"fixed inset-0 bg-black/50",onClick:t}),r.jsx("div",{className:"fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-800 shadow-xl",children:r.jsxs("div",{className:"p-6",children:[r.jsxs("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold",children:"Menu"}),r.jsx("button",{onClick:t,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded",children:r.jsx(R,{className:"w-5 h-5"})})]}),r.jsx("nav",{className:"space-y-4",children:i.map((e=>r.jsxs("a",{href:e.href,onClick:o=>{o.preventDefault(),a(e.sectionId),t()},className:"flex items-center gap-3 p-3 rounded-lg transition-colors "+(o===e.sectionId?"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400":"hover:bg-gray-100 dark:hover:bg-gray-700"),children:[e.icon,r.jsx("span",{children:e.text})]},e.id)))})]})})]}):null;function Ta(){const[e,t]=n.useState(!1),[o,a]=n.useState("perfil"),[i,s]=n.useState(!1),[l,c]=n.useState(!1);n.useEffect((()=>{const e=()=>{t(window.scrollY>10);let e="perfil";for(const t of xa){const o=document.getElementById(t.sectionId);if(o){const a=o.getBoundingClientRect();if(a.top<=80&&a.bottom>80){e=t.sectionId;break}}}a(e)};return window.addEventListener("scroll",e,{passive:!0}),e(),()=>window.removeEventListener("scroll",e)}),[]);const d=n.useCallback(((e,t)=>{e.preventDefault();const o=document.getElementById(t);o&&(window.scrollTo({top:o.offsetTop-70,behavior:"smooth"}),a(t))}),[]),u=n.useCallback((e=>{const t=document.getElementById(e);t&&(window.scrollTo({top:t.offsetTop-70,behavior:"smooth"}),a(e))}),[]);return r.jsxs("header",{className:`fixed top-0 left-0 w-full z-50 transition-all duration-300 border-b border-gray-200 dark:border-gray-700\n        ${e?"bg-white dark:bg-gray-900 shadow-md":"bg-white/80 dark:bg-gray-900/80 backdrop-blur"}\n      `,children:[r.jsxs("nav",{className:"max-w-7xl mx-auto flex items-center justify-between px-6 py-2",children:[r.jsx("a",{href:"#perfil",className:"hidden md:flex items-center gap-2 text-2xl font-bold tracking-tight text-blue-600",children:r.jsx("span",{className:"rounded bg-blue-600 text-white px-2 py-1 text-lg font-black shadow-sm",children:"TBA"})}),r.jsx("ul",{className:"hidden md:flex gap-8 items-end",children:xa.map((e=>{const t=e.icon,a=o===e.sectionId;return r.jsx("li",{children:r.jsxs("a",{href:e.href,onClick:t=>d(t,e.sectionId),className:"flex flex-col items-center px-3 py-2 transition-colors "+(a?"text-blue-600":"text-gray-700 dark:text-gray-300 hover:text-blue-600"),children:[r.jsx(t,{className:"mb-1 w-5 h-5"}),r.jsxs("span",{className:"text-sm font-medium",children:["perfil"===e.sectionId&&"Perfil","projetos"===e.sectionId&&"Projetos","backlog"===e.sectionId&&"Backlog","contato"===e.sectionId&&"Contato"]}),a&&r.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-0.5 bg-blue-600"})]})},e.href)}))}),r.jsxs("div",{className:"flex gap-2 items-center",children:[r.jsx("div",{className:"md:hidden",children:r.jsx("button",{onClick:()=>c(!l),className:"flex items-center justify-center w-11 h-11 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow hover:shadow-lg transition-all",children:l?r.jsx(R,{className:"w-5 h-5"}):r.jsx(U,{className:"w-5 h-5"})})}),r.jsx("div",{className:"hidden md:flex gap-2 items-center",children:r.jsx("button",{onClick:()=>s(!0),className:"flex items-center justify-center w-10 h-10 rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 shadow hover:shadow-lg transition-all text-blue-600",children:r.jsx(O,{className:"w-5 h-5"})})})]})]}),r.jsx(ka,{isOpen:l,onClose:()=>c(!1),activeSection:o,onNavigate:u,items:[{id:"perfil",sectionId:"perfil",text:"Perfil",href:"#perfil",icon:r.jsx(M,{className:"w-5 h-5"})},{id:"projetos",sectionId:"projetos",text:"Projetos",href:"#projetos",icon:r.jsx(z,{className:"w-5 h-5"})},{id:"backlog",sectionId:"backlog",text:"Backlog",href:"#backlog",icon:r.jsx(N,{className:"w-5 h-5"})},{id:"contato",sectionId:"contato",text:"Contato",href:"#contato",icon:r.jsx(L,{className:"w-5 h-5"})}]}),i&&r.jsx("div",{className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center",onClick:()=>s(!1),children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full mx-4",children:[r.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Feedback"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Modal de feedback funcionando!"}),r.jsx("button",{onClick:()=>s(!1),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Fechar"})]})})]})}const ja=n.lazy((()=>Y((()=>import("./Index-iqIE3INL.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])))),Ea=n.lazy((()=>Y((()=>import("./NotFound-B6a2Ckv1.js")),__vite__mapDeps([10,1,2,3,4,5,7,9,6])))),Sa=n.lazy((()=>Y((()=>import("./PrivacyPolicy-D08PdUAt.js")),__vite__mapDeps([11,1,2,3,7])))),Ca=n.lazy((()=>Y((()=>import("./AnalyticsProvider-y-YxUwtL.js")),__vite__mapDeps([12,1,2,3,8,13,5,7,9,6])))),Pa=n.lazy((()=>Y((()=>import("./BackToTop-CYkP7fYD.js")),__vite__mapDeps([14,1,2,3,7])))),Ia=n.lazy((()=>Y((()=>import("./FluidGradientBackground-fz9tBY22.js")),__vite__mapDeps([15,1,2,3])).then((e=>({default:e.default}))))),Aa=n.lazy((()=>Y((()=>import("./LazyScripts-Chw9cytQ.js")),__vite__mapDeps([16,2,3])))),Da=n.lazy((()=>Y((()=>import("./CookieConsent-DDWHkk1W.js")),__vite__mapDeps([17,1,2,3,6,7])))),Ra=new B,Ma=()=>{const{t:e}=$();return r.jsx(da,{children:r.jsx(V,{client:Ra,children:r.jsx(wa,{children:r.jsx(yo,{children:r.jsx(n.Suspense,{fallback:r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(va,{})}),children:r.jsxs(Ca,{children:[r.jsx("a",{href:"#main-content",className:"skip-link",children:e("accessibility.features.skipToContent")}),r.jsx("a",{href:"#navigation",className:"skip-link",children:e("accessibility.features.skipToNavigation")}),r.jsx(Yt,{}),r.jsx(vo,{}),r.jsx(n.Suspense,{fallback:null,children:r.jsx(Ia,{})}),!1,!1,r.jsx(n.Suspense,{fallback:null,children:r.jsx(Pa,{})}),r.jsx(n.Suspense,{fallback:null,children:r.jsx(Aa,{delay:2e3})}),r.jsx(n.Suspense,{fallback:null,children:r.jsx(Da,{})}),!1,r.jsx(Ta,{}),r.jsx(d,{basename:"/portfolio",future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:r.jsx(n.Suspense,{fallback:r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(va,{})}),children:r.jsxs(u,{children:[r.jsx(p,{path:"/",element:r.jsx(ja,{})}),r.jsx(p,{path:"/privacy-policy",element:r.jsx(Sa,{})}),r.jsx(p,{path:"*",element:r.jsx(Ea,{})})]})})})]})})})})})})},za=()=>{(()=>{const e="/portfolio/";[{href:`${e}images/tarcisio_bispo.webp`,as:"image",type:"image/webp",fetchpriority:"high"},{href:`${e}images/tarcisio_bispo.png`,as:"image",type:"image/png",fetchpriority:"high"}].forEach((e=>{const t=document.createElement("link");t.rel="preload",t.href=e.href,t.as=e.as,e.type&&(t.type=e.type),e.fetchpriority&&t.setAttribute("fetchpriority",e.fetchpriority),e.crossorigin&&""!==e.crossorigin&&t.setAttribute("crossorigin",e.crossorigin),t.onerror=()=>{console.warn(`Failed to preload resource: ${e.href}`)},document.head.appendChild(t)}))})(),document.querySelectorAll('link[href*="fonts.googleapis.com"]').forEach((e=>{const t=e.getAttribute("href");if(t&&!t.includes("display=swap")){const o=new URL(t);o.searchParams.set("display","swap"),e.setAttribute("href",o.toString())}})),(async()=>{if("serviceWorker"in navigator)try{const e="/portfolio/sw.js";return await navigator.serviceWorker.register(e)}catch(e){return console.warn("Service Worker registration failed:",e),null}})();const e=(()=>{if("IntersectionObserver"in window){const e=new IntersectionObserver((t=>{t.forEach((t=>{if(t.isIntersecting){const o=t.target;o.dataset.src&&(o.src=o.dataset.src,o.removeAttribute("data-src")),o.dataset.srcset&&(o.srcset=o.dataset.srcset,o.removeAttribute("data-srcset")),o.classList.remove("lazy-loading"),o.classList.add("lazy-loaded"),e.unobserve(o)}}))}),{rootMargin:"50px 0px",threshold:.01});return e}return null})();if(e){document.querySelectorAll("img[data-src]").forEach((t=>e.observe(t)))}{const e=[];e.length>0&&(e=>{"requestIdleCallback"in window?requestIdleCallback((()=>{e.forEach((e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}))})):setTimeout((()=>{e.forEach((e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}))}),2e3)})(e)}},Na=e=>new Promise(((t,o)=>{const a=new Image;a.onload=()=>t(),a.onerror=()=>o(new Error(`Failed to load image: ${e}`)),a.src=e})),La=e=>new Promise((t=>setTimeout(t,e))),Ua=()=>{(()=>{const e="/portfolio/";[`${e}images/tarcisio_bispo.webp`,`${e}images/tarcisio_bispo.png`,`${e}images/ixdf-symbol-dark.png`,`${e}images/ixdf-symbol-white.png`].forEach((e=>{const t=document.createElement("link");t.rel="preload",t.as="image",t.href=e,t.onerror=()=>{console.warn(`Failed to preload image: ${e}`)},document.head.appendChild(t)}))})();const e=(t=t=>{t.forEach((t=>{if(t.isIntersecting){const o=t.target,a=o.dataset.src;a&&(async e=>{const{src:t,fallback:o,retryAttempts:a=3,retryDelay:r=1e3}=e;for(let n=0;n<a;n++)try{return await Na(t),t}catch(i){console.warn(`Image load attempt ${n+1} failed for: ${t}`),n<a-1&&await La(r)}if(o)try{return await Na(o),o}catch(i){console.warn(`Fallback image also failed: ${o}`)}return e.placeholder||""})({src:a}).then((e=>{o.src=e,o.removeAttribute("data-src"),o.classList.remove("lazy-loading"),o.classList.add("lazy-loaded")})).catch((e=>{console.warn("Failed to load lazy image:",e),o.classList.add("lazy-error")})),null==e||e.unobserve(o)}}))},"IntersectionObserver"in window?new IntersectionObserver(t,{rootMargin:"50px 0px",threshold:.01}):null);var t;if(e){document.querySelectorAll("img[data-src]").forEach((t=>e.observe(t)))}};class Oa{static parseJSON(e){try{return JSON.parse(e)}catch(t){return console.error("CSPSecurity: Invalid JSON string:",t),null}}static interpolateTemplate(e,t){return e.replace(/\{\{(\w+)\}\}/g,((e,o)=>void 0!==t[o]?String(t[o]):e))}static getNestedProperty(e,t){return t.split(".").reduce(((e,t)=>e&&void 0!==e[t]?e[t]:void 0),e)}static setNestedProperty(e,t,o){const a=t.split("."),r=a.pop();if(!r)return;a.reduce(((e,t)=>(e[t]&&"object"==typeof e[t]||(e[t]={}),e[t])),e)[r]=o}static executeSafeOperation(e,t){const o={add:(e,t)=>e+t,subtract:(e,t)=>e-t,multiply:(e,t)=>e*t,divide:(e,t)=>0!==t?e/t:0,concat:(e,t)=>e+t,uppercase:e=>e.toUpperCase(),lowercase:e=>e.toLowerCase(),length:e=>e.length,reverse:e=>[...e].reverse(),sort:e=>[...e].sort()};return o[e]?o[e]:(console.warn("CSPSecurity: Unsafe operation blocked:",e),null)}static secureSetTimeout(e,t){return"function"!=typeof e?(console.error("CSPSecurity: setTimeout callback must be a function"),0):window.setTimeout(e,t)}static secureSetInterval(e,t){return"function"!=typeof e?(console.error("CSPSecurity: setInterval callback must be a function"),0):window.setInterval(e,t)}static async secureImport(e){if(!this.isValidModulePath(e))throw new Error("CSPSecurity: Invalid module path");try{return await import(e)}catch(t){throw console.error("CSPSecurity: Failed to import module:",e,t),t}}static isValidModulePath(e){return[/^\.\//,/^\.\.\//,/^@?[a-zA-Z0-9_-]+\/[a-zA-Z0-9_/-]+$/,/^[a-zA-Z0-9_-]+$/].some((t=>t.test(e)))}static sanitizeHTML(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}static buildSecureURL(e,t){try{const o=new URL(e);return Object.entries(t).forEach((([e,t])=>{o.searchParams.set(e,t)})),o.toString()}catch(o){return console.error("CSPSecurity: Invalid URL construction:",o),e}}static installCSPInterceptors(){if("undefined"!=typeof window){window.eval=function(e){throw console.error("CSPSecurity: eval() usage blocked for security. Use secure alternatives."),new Error("eval() is not allowed due to Content Security Policy")},window.Function=function(...e){throw console.error("CSPSecurity: Function constructor blocked for security."),new Error("Function constructor is not allowed due to Content Security Policy")};const e=window.setTimeout,t=window.setInterval;window.setTimeout=function(t,o,...a){if("string"==typeof t)throw console.error("CSPSecurity: setTimeout with string blocked. Use function instead."),new Error("setTimeout with string is not allowed due to Content Security Policy");return e.call(this,t,o,...a)},window.setInterval=function(e,o,...a){if("string"==typeof e)throw console.error("CSPSecurity: setInterval with string blocked. Use function instead."),new Error("setInterval with string is not allowed due to Content Security Policy");return t.call(this,e,o,...a)}}}static generateNonce(){const e=new Uint8Array(16);return crypto.getRandomValues(e),btoa(String.fromCharCode(...e))}static validateCSPCompliance(){const e=[];if("undefined"!=typeof window){window.eval.toString().includes("eval() usage blocked")||e.push("eval() interceptor not installed");window.Function.toString().includes("Function constructor blocked")||e.push("Function constructor interceptor not installed")}return!(e.length>0)||(console.warn("CSPSecurity: CSP violations detected:",e),!1)}}"undefined"!=typeof window&&(window.React=i);const Ba=document.getElementById("root");if(!Ba)throw new Error("Root element not found");const Va=H.createRoot(Ba);"undefined"!=typeof window&&(za(),Ua(),setTimeout((()=>{"undefined"!=typeof window&&(Oa.installCSPInterceptors(),setTimeout((()=>{Oa.validateCSPCompliance()}),100))}),1e3)),Va.render(r.jsx(n.StrictMode,{children:r.jsx(Qt,{defaultTheme:"system",storageKey:"portfolio-theme",children:r.jsx(Ma,{})})}));export{ba as H,va as L,Y as _,We as a,re as b,qt as c};
//# sourceMappingURL=index-B8F6PHpu.js.map

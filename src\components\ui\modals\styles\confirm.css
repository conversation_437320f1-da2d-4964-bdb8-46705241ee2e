/**
 * 🎯 ESTILOS ESPECÍFICOS PARA CONFIRM MODAL
 * 
 * Estilos customizados para modais de confirmação
 * Inclui variantes de perigo, sucesso e padrão
 */

/* ===== CONFIRM MODAL ESPECÍFICO ===== */
.confirm-modal {
  /* Cores específicas do confirm */
  --confirm-primary: #3b82f6;
  --confirm-danger: #ef4444;
  --confirm-success: #10b981;
  --confirm-warning: #f59e0b;
}

/* ===== ICON CONTAINER ===== */
.confirm-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin: 0 auto 1rem;
  background: var(--color-surface-hover);
}

.confirm-icon-container.primary {
  background: rgba(59, 130, 246, 0.1);
}

.confirm-icon-container.danger {
  background: rgba(239, 68, 68, 0.1);
}

.confirm-icon-container.success {
  background: rgba(16, 185, 129, 0.1);
}

.confirm-icon-container.warning {
  background: rgba(245, 158, 11, 0.1);
}

/* ===== ICON STYLES ===== */
.confirm-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.confirm-icon.primary {
  color: var(--confirm-primary);
}

.confirm-icon.danger {
  color: var(--confirm-danger);
}

.confirm-icon.success {
  color: var(--confirm-success);
}

.confirm-icon.warning {
  color: var(--confirm-warning);
}

/* ===== TITLE AND DESCRIPTION ===== */
.confirm-title {
  font-size: 1.125rem;
  font-weight: 600;
  text-align: center;
  color: var(--color-text);
  margin-bottom: 0.5rem;
}

.confirm-description {
  font-size: 0.875rem;
  text-align: center;
  color: var(--color-text-muted);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

/* ===== ACTION BUTTONS ===== */
.confirm-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.confirm-button {
  flex: 1;
  max-width: 8rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 2px solid transparent;
  cursor: pointer;
  transition: var(--modal-transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Cancel button */
.confirm-button.cancel {
  background: transparent;
  color: var(--color-text-muted);
  border-color: var(--color-border);
}

.confirm-button.cancel:hover {
  background: var(--color-surface-hover);
  color: var(--color-text);
  border-color: var(--color-border-hover);
}

.confirm-button.cancel:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Confirm button variants */
.confirm-button.primary {
  background: var(--confirm-primary);
  color: white;
  border-color: var(--confirm-primary);
}

.confirm-button.primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.confirm-button.danger {
  background: var(--confirm-danger);
  color: white;
  border-color: var(--confirm-danger);
}

.confirm-button.danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.confirm-button.success {
  background: var(--confirm-success);
  color: white;
  border-color: var(--confirm-success);
}

.confirm-button.success:hover {
  background: #059669;
  border-color: #059669;
}

/* Loading state */
.confirm-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.confirm-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ===== LOADING SPINNER ===== */
.confirm-loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== MODO ESCURO ===== */
.dark .confirm-icon-container {
  background: var(--color-surface-hover-dark, #374151);
}

.dark .confirm-icon-container.primary {
  background: rgba(59, 130, 246, 0.2);
}

.dark .confirm-icon-container.danger {
  background: rgba(239, 68, 68, 0.2);
}

.dark .confirm-icon-container.success {
  background: rgba(16, 185, 129, 0.2);
}

.dark .confirm-icon-container.warning {
  background: rgba(245, 158, 11, 0.2);
}

.dark .confirm-title {
  color: var(--color-text-dark, #f9fafb);
}

.dark .confirm-description {
  color: var(--color-text-muted-dark, #d1d5db);
}

.dark .confirm-button.cancel {
  color: var(--color-text-muted-dark, #9ca3af);
  border-color: var(--color-border-dark, #4b5563);
}

.dark .confirm-button.cancel:hover {
  background: var(--color-surface-hover-dark, #374151);
  color: var(--color-text-dark, #f9fafb);
  border-color: var(--color-border-hover-dark, #6b7280);
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 640px) {
  .confirm-actions {
    flex-direction: column;
  }
  
  .confirm-button {
    max-width: none;
    padding: 0.75rem 1rem;
  }
  
  .confirm-title {
    font-size: 1rem;
  }
  
  .confirm-description {
    font-size: 0.8rem;
  }
  
  .confirm-icon-container {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .confirm-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
}

/* ===== ANIMAÇÕES ESPECÍFICAS ===== */
.confirm-modal-enter {
  opacity: 0;
  transform: scale(0.95);
}

.confirm-modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.confirm-modal-exit {
  opacity: 1;
  transform: scale(1);
}

.confirm-modal-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.15s ease, transform 0.15s ease;
}

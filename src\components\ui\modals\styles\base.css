/**
 * 🎯 ESTILOS BASE PARA MODAIS
 * 
 * Estilos fundamentais compartilhados por todos os modais
 */

/* ===== HEADER STYLES ===== */
.modal-header {
  padding: var(--modal-padding) var(--modal-padding) 0;
  border-bottom: 1px solid transparent;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.5;
  color: var(--color-text);
  margin: 0 0 0.5rem 0;
}

.modal-description {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-muted);
  margin: 0;
}

/* ===== BODY STYLES ===== */
.modal-body {
  padding: var(--modal-padding);
}

.modal-body-no-header {
  padding-top: var(--modal-padding);
}

/* ===== FOOTER STYLES ===== */
.modal-footer {
  padding: 0 var(--modal-padding) var(--modal-padding);
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.modal-footer-center {
  justify-content: center;
}

.modal-footer-start {
  justify-content: flex-start;
}

.modal-footer-between {
  justify-content: space-between;
}

/* ===== CLOSE BUTTON ===== */
.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  border: none;
  background: transparent;
  color: var(--color-text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--modal-transition-fast);
  z-index: 10;
}

.modal-close:hover {
  background: var(--color-surface-hover);
  color: var(--color-text);
}

.modal-close:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== MODO ESCURO ===== */
.dark .modal-title {
  color: var(--color-text-dark, #f9fafb);
}

.dark .modal-description {
  color: var(--color-text-muted-dark, #d1d5db);
}

.dark .modal-close {
  color: var(--color-text-muted-dark, #9ca3af);
}

.dark .modal-close:hover {
  background: var(--color-surface-hover-dark, #374151);
  color: var(--color-text-dark, #f9fafb);
}

/* ===== FORM ELEMENTS ===== */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.modal-form-row {
  display: flex;
  gap: 0.75rem;
}

.modal-form-row > * {
  flex: 1;
}

/* ===== INPUT STYLES ===== */
.modal-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--color-border);
  border-radius: 0.5rem;
  font-size: 1rem;
  background: var(--color-surface);
  color: var(--color-text);
  transition: var(--modal-transition-fast);
}

.modal-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-input::placeholder {
  color: var(--color-text-muted);
}

/* Input states */
.modal-input-error {
  border-color: var(--color-error);
}

.modal-input-success {
  border-color: var(--color-success);
}

/* ===== TEXTAREA ===== */
.modal-textarea {
  min-height: 6rem;
  resize: vertical;
}

/* ===== CHECKBOX/RADIO ===== */
.modal-checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-checkbox {
  width: 1rem;
  height: 1rem;
  accent-color: var(--color-primary);
}

.modal-checkbox-label {
  font-size: 0.875rem;
  color: var(--color-text);
  cursor: pointer;
  user-select: none;
}

/* ===== VALIDATION MESSAGES ===== */
.modal-validation {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.modal-validation-error {
  color: var(--color-error);
}

.modal-validation-success {
  color: var(--color-success);
}

.modal-validation-info {
  color: var(--color-text-muted);
}

/* ===== MODO ESCURO PARA INPUTS ===== */
.dark .modal-input {
  background: var(--color-surface-dark, #374151);
  border-color: var(--color-border-dark, #4b5563);
  color: var(--color-text-dark, #f9fafb);
}

.dark .modal-input::placeholder {
  color: var(--color-text-muted-dark, #9ca3af);
}

.dark .modal-checkbox-label {
  color: var(--color-text-dark, #f9fafb);
}

/* ===== FEEDBACK BUTTON STYLES ===== */
/* Estilos para FloatingFeedbackButton.tsx */

.button-feedback {
  /* Dimensions */
  width: 56px;
  height: 56px;
  
  /* Position */
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 40;
  
  /* Appearance */
  border-radius: 50%;
  border: none;
  background: var(--color-primary);
  color: white;
  
  /* Effects */
  box-shadow: 0 8px 16px rgba(var(--primary), 0.3);
  transition: var(--button-transition);
}

.button-feedback:hover {
  background: var(--color-primary-dark);
  transform: scale(1.1);
  box-shadow: 0 12px 24px rgba(var(--primary), 0.4);
}

.button-feedback:active {
  transform: scale(0.95);
}

/* Pulse Animation */
.button-feedback::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 2px solid var(--color-primary);
  opacity: 0;
  animation: feedback-pulse 2s infinite;
}

@keyframes feedback-pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .button-feedback {
    bottom: 80px; /* Above mobile navigation */
    right: 16px;
    width: 48px;
    height: 48px;
  }
}

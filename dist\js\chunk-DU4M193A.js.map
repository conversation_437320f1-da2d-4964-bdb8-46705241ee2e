{"version": 3, "file": "chunk-DU4M193A.js", "sources": ["../../node_modules/@radix-ui/primitive/dist/index.mjs", "../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../node_modules/@radix-ui/react-context/dist/index.mjs", "../../node_modules/@radix-ui/react-slot/dist/index.mjs", "../../node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../node_modules/@radix-ui/react-portal/dist/index.mjs", "../../node_modules/@radix-ui/react-presence/dist/index.mjs", "../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "../../node_modules/clsx/dist/clsx.mjs", "../../node_modules/@radix-ui/react-id/dist/index.mjs", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../node_modules/@radix-ui/react-arrow/dist/index.mjs", "../../node_modules/@radix-ui/react-popper/dist/index.mjs", "../../node_modules/@radix-ui/react-use-size/dist/index.mjs", "../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "../../node_modules/@headlessui/react/dist/utils/env.js", "../../node_modules/@headlessui/react/dist/utils/owner.js", "../../node_modules/@headlessui/react/dist/utils/micro-task.js", "../../node_modules/@headlessui/react/dist/utils/disposables.js", "../../node_modules/@headlessui/react/dist/hooks/use-disposables.js", "../../node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js", "../../node_modules/@headlessui/react/dist/hooks/use-latest-value.js", "../../node_modules/@headlessui/react/dist/hooks/use-event.js", "../../node_modules/@headlessui/react/dist/internal/disabled.js", "../../node_modules/@headlessui/react/dist/utils/class-names.js", "../../node_modules/@headlessui/react/dist/utils/match.js", "../../node_modules/@headlessui/react/dist/utils/render.js", "../../node_modules/@headlessui/react/dist/internal/hidden.js", "../../node_modules/@headlessui/react/dist/utils/dom.js", "../../node_modules/@headlessui/react/dist/hooks/use-sync-refs.js", "../../node_modules/@headlessui/react/dist/components/description/description.js", "../../node_modules/@headlessui/react/dist/components/keyboard.js", "../../node_modules/@headlessui/react/dist/internal/close-provider.js", "../../node_modules/@headlessui/react/dist/utils/default-map.js", "../../node_modules/@headlessui/react/dist/machine.js", "../../node_modules/@headlessui/react/dist/machines/stack-machine.js", "../../node_modules/use-sync-external-store/with-selector.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.production.js", "../../node_modules/@headlessui/react/dist/react-glue.js", "../../node_modules/@headlessui/react/dist/hooks/use-is-top-layer.js", "../../node_modules/@headlessui/react/dist/hooks/use-inert-others.js", "../../node_modules/@headlessui/react/dist/utils/focus-management.js", "../../node_modules/@headlessui/react/dist/utils/platform.js", "../../node_modules/@headlessui/react/dist/hooks/use-document-event.js", "../../node_modules/@headlessui/react/dist/hooks/use-window-event.js", "../../node_modules/@headlessui/react/dist/hooks/use-outside-click.js", "../../node_modules/@headlessui/react/dist/hooks/use-owner.js", "../../node_modules/@headlessui/react/dist/hooks/use-event-listener.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js", "../../node_modules/@headlessui/react/dist/utils/store.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js", "../../node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js", "../../node_modules/@headlessui/react/dist/hooks/use-store.js", "../../node_modules/@headlessui/react/dist/hooks/use-transition.js", "../../node_modules/@headlessui/react/dist/hooks/use-flags.js", "../../node_modules/@headlessui/react/dist/hooks/use-watch.js", "../../node_modules/@headlessui/react/dist/internal/open-closed.js", "../../node_modules/@headlessui/react/dist/utils/active-element-history.js", "../../node_modules/@headlessui/react/dist/hooks/use-on-unmount.js", "../../node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js", "../../node_modules/@headlessui/react/dist/utils/document-ready.js", "../../node_modules/@headlessui/react/dist/internal/portal-force-root.js", "../../node_modules/@headlessui/react/dist/components/portal/portal.js", "../../node_modules/@headlessui/react/dist/hooks/use-root-containers.js", "../../node_modules/@headlessui/react/dist/hooks/use-is-mounted.js", "../../node_modules/@headlessui/react/dist/hooks/use-tab-direction.js", "../../node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.js", "../../node_modules/@headlessui/react/dist/components/transition/transition.js", "../../node_modules/@headlessui/react/dist/components/dialog/dialog.js", "../../node_modules/@headlessui/react/dist/hooks/use-escape.js", "../../node_modules/@headlessui/react/dist/hooks/use-scroll-lock.js", "../../node_modules/@headlessui/react/dist/hooks/use-on-disappear.js", "../../node_modules/@headlessui/react/dist/hooks/use-is-touch-device.js"], "sourcesContent": ["// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport {\n  Presence,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VISUALLY_HIDDEN_STYLES,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "// src/arrow.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Arrow\";\nvar Arrow = React.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ jsx(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\nexport {\n  Arrow,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/popper.tsx\nimport * as React from \"react\";\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size\n} from \"@floating-ui/react-dom\";\nimport * as ArrowPrimitive from \"@radix-ui/react-arrow\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SIDE_OPTIONS = [\"top\", \"right\", \"bottom\", \"left\"];\nvar ALIGN_OPTIONS = [\"start\", \"center\", \"end\"];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState(null);\n  return /* @__PURE__ */ jsx(PopperProvider, { scope: __scopePopper, anchor, onAnchorChange: setAnchor, children });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    React.useEffect(() => {\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ jsx(Primitive.div, { ...anchorProps, ref: composedRefs });\n  }\n);\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = \"bottom\",\n      sideOffset = 0,\n      align = \"center\",\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = \"partial\",\n      hideWhenDetached = false,\n      updatePositionStrategy = \"optimized\",\n      onPlaced,\n      ...contentProps\n    } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [arrow, setArrow] = React.useState(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: \"fixed\",\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === \"always\"\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions && shift({\n          mainAxis: true,\n          crossAxis: false,\n          limiter: sticky === \"partial\" ? limitShift() : void 0,\n          ...detectOverflowOptions\n        }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n          }\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: \"referenceHidden\", ...detectOverflowOptions })\n      ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = React.useState();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n          // keep off the page when measuring\n          minWidth: \"max-content\",\n          zIndex: contentZIndex,\n          [\"--radix-popper-transform-origin\"]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y\n          ].join(\" \"),\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...middlewareData.hide?.referenceHidden && {\n            visibility: \"hidden\",\n            pointerEvents: \"none\"\n          }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ jsx(\n          PopperContentProvider,\n          {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                  ...contentProps.style,\n                  // if the PopperContent hasn't been placed yet (not all measurements done)\n                  // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                  animation: !isPositioned ? \"none\" : void 0\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nvar PopperArrow = React.forwardRef(function PopperArrow2(props, forwardedRef) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ jsx(\n      \"span\",\n      {\n        ref: contentContext.onArrowChange,\n        style: {\n          position: \"absolute\",\n          left: contentContext.arrowX,\n          top: contentContext.arrowY,\n          [baseSide]: 0,\n          transformOrigin: {\n            top: \"\",\n            right: \"0 0\",\n            bottom: \"center 0\",\n            left: \"100% 0\"\n          }[contentContext.placedSide],\n          transform: {\n            top: \"translateY(100%)\",\n            right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n            bottom: `rotate(180deg)`,\n            left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n          }[contentContext.placedSide],\n          visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ jsx(\n          ArrowPrimitive.Root,\n          {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n              ...arrowProps.style,\n              // ensures the element can be measured correctly (mostly for if SVG)\n              display: \"block\"\n            }\n          }\n        )\n      }\n    )\n  );\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n  return value !== null;\n}\nvar transformOrigin = (options) => ({\n  name: \"transformOrigin\",\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: \"0%\", center: \"50%\", end: \"100%\" }[placedAlign];\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n    let x = \"\";\n    let y = \"\";\n    if (placedSide === \"bottom\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === \"top\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === \"right\") {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === \"left\") {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  }\n});\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\nexport {\n  ALIGN_OPTIONS,\n  Anchor,\n  Arrow,\n  Content,\n  Popper,\n  PopperAnchor,\n  PopperArrow,\n  PopperContent,\n  Root2 as Root,\n  SIDE_OPTIONS,\n  createPopperScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/tooltip.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createSlottable } from \"@radix-ui/react-slot\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport * as VisuallyHiddenPrimitive from \"@radix-ui/react-visually-hidden\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar [createTooltipContext, createTooltipScope] = createContextScope(\"Tooltip\", [\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children\n  } = props;\n  const isOpenDelayedRef = React.useRef(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n  return /* @__PURE__ */ jsx(\n    TooltipProviderContextProvider,\n    {\n      scope: __scopeTooltip,\n      isOpenDelayedRef,\n      delayDuration,\n      onOpen: React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        isOpenDelayedRef.current = false;\n      }, []),\n      onClose: React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => isOpenDelayedRef.current = true,\n          skipDelayDuration\n        );\n      }, [skipDelayDuration]),\n      isPointerInTransitRef,\n      onPointerInTransitChange: React.useCallback((inTransit) => {\n        isPointerInTransitRef.current = inTransit;\n      }, []),\n      disableHoverableContent,\n      children\n    }\n  );\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: (open2) => {\n      if (open2) {\n        providerContext.onOpen();\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open2);\n    },\n    caller: TOOLTIP_NAME\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n  }, [open]);\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    setOpen(false);\n  }, [setOpen]);\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n      openTimerRef.current = 0;\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n  React.useEffect(() => {\n    return () => {\n      if (openTimerRef.current) {\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n      }\n    };\n  }, []);\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    TooltipContextProvider,\n    {\n      scope: __scopeTooltip,\n      contentId,\n      open,\n      stateAttribute,\n      trigger,\n      onTriggerChange: setTrigger,\n      onTriggerEnter: React.useCallback(() => {\n        if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n        else handleOpen();\n      }, [providerContext.isOpenDelayedRef, handleDelayedOpen, handleOpen]),\n      onTriggerLeave: React.useCallback(() => {\n        if (disableHoverableContent) {\n          handleClose();\n        } else {\n          window.clearTimeout(openTimerRef.current);\n          openTimerRef.current = 0;\n        }\n      }, [handleClose, disableHoverableContent]),\n      onOpen: handleOpen,\n      onClose: handleClose,\n      disableHoverableContent,\n      children\n    }\n  ) });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => isPointerDownRef.current = false, []);\n    React.useEffect(() => {\n      return () => document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [handlePointerUp]);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        \"aria-describedby\": context.open ? context.contentId : void 0,\n        \"data-state\": context.stateAttribute,\n        ...triggerProps,\n        ref: composedRefs,\n        onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n          if (event.pointerType === \"touch\") return;\n          if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n            context.onTriggerEnter();\n            hasPointerMoveOpenedRef.current = true;\n          }\n        }),\n        onPointerLeave: composeEventHandlers(props.onPointerLeave, () => {\n          context.onTriggerLeave();\n          hasPointerMoveOpenedRef.current = false;\n        }),\n        onPointerDown: composeEventHandlers(props.onPointerDown, () => {\n          if (context.open) {\n            context.onClose();\n          }\n          isPointerDownRef.current = true;\n          document.addEventListener(\"pointerup\", handlePointerUp, { once: true });\n        }),\n        onFocus: composeEventHandlers(props.onFocus, () => {\n          if (!isPointerDownRef.current) context.onOpen();\n        }),\n        onBlur: composeEventHandlers(props.onBlur, context.onClose),\n        onClick: composeEventHandlers(props.onClick, context.onClose)\n      }\n    ) });\n  }\n);\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar TooltipPortal = (props) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeTooltip, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.disableHoverableContent ? /* @__PURE__ */ jsx(TooltipContentImpl, { side, ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(TooltipContentHoverable, { side, ...contentProps, ref: forwardedRef }) });\n  }\n);\nvar TooltipContentHoverable = React.forwardRef((props, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState(null);\n  const { trigger, onClose } = context;\n  const content = ref.current;\n  const { onPointerInTransitChange } = providerContext;\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n  const handleCreateGraceArea = React.useCallback(\n    (event, hoverTarget) => {\n      const currentTarget = event.currentTarget;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event) => handleCreateGraceArea(event, trigger);\n      trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n      content.addEventListener(\"pointerleave\", handleContentLeave);\n      return () => {\n        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n        content.removeEventListener(\"pointerleave\", handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event) => {\n        const target = event.target;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n      return () => document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n  return /* @__PURE__ */ jsx(TooltipContentImpl, { ...props, ref: composedRefs });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, { isInside: false });\nvar Slottable = createSlottable(\"TooltipContent\");\nvar TooltipContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      \"aria-label\": ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event) => {\n          const target = event.target;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener(\"scroll\", handleScroll, { capture: true });\n        return () => window.removeEventListener(\"scroll\", handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n    return /* @__PURE__ */ jsx(\n      DismissableLayer,\n      {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event) => event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ jsxs(\n          PopperPrimitive.Content,\n          {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n              ...contentProps.style,\n              // re-namespace exposed content custom properties\n              ...{\n                \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n              }\n            },\n            children: [\n              /* @__PURE__ */ jsx(Slottable, { children }),\n              /* @__PURE__ */ jsx(VisuallyHiddenContentContextProvider, { scope: __scopeTooltip, isInside: true, children: /* @__PURE__ */ jsx(VisuallyHiddenPrimitive.Root, { id: context.contentId, role: \"tooltip\", children: ariaLabel || children }) })\n            ]\n          }\n        )\n      }\n    );\n  }\n);\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return \"left\";\n    case right:\n      return \"right\";\n    case top:\n      return \"top\";\n    case bottom:\n      return \"bottom\";\n    default:\n      throw new Error(\"unreachable\");\n  }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n  const paddedExitPoints = [];\n  switch (exitSide) {\n    case \"top\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case \"bottom\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case \"left\":\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case \"right\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom }\n  ];\n}\nfunction isPointInPolygon(point, polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i];\n    const jj = polygon[j];\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n    const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) inside = !inside;\n  }\n  return inside;\n}\nfunction getHull(points) {\n  const newPoints = points.slice();\n  newPoints.sort((a, b) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return 1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return 1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n  if (points.length <= 1) return points.slice();\n  const upperHull = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i];\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1];\n      const r = upperHull[upperHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n  const lowerHull = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i];\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1];\n      const r = lowerHull[lowerHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n  if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  Portal,\n  Provider,\n  Root3 as Root,\n  Tooltip,\n  TooltipArrow,\n  TooltipContent,\n  TooltipPortal,\n  TooltipProvider,\n  TooltipTrigger,\n  Trigger,\n  createTooltipScope\n};\n//# sourceMappingURL=index.mjs.map\n", "var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n", "import{env as t}from'./env.js';function o(n){var e,r;return t.isServer?null:n?\"ownerDocument\"in n?n.ownerDocument:\"current\"in n?(r=(e=n.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}export{o as getOwnerDocument};\n", "function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n", "import{microTask as a}from'./micro-task.js';function o(){let s=[],r={addEventListener(e,t,n,i){return e.addEventListener(t,n,i),r.add(()=>e.removeEventListener(t,n,i))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return a(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,n){let i=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:i})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return s.includes(e)||s.push(e),()=>{let t=s.indexOf(e);if(t>=0)for(let n of s.splice(t,1))n()}},dispose(){for(let e of s.splice(0))e()}};return r}export{o as disposables};\n", "import{useEffect as s,useState as o}from\"react\";import{disposables as t}from'../utils/disposables.js';function p(){let[e]=o(t);return s(()=>()=>e.dispose(),[e]),e}export{p as useDisposables};\n", "import{useEffect as f,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let n=(e,t)=>{i.isServer?f(e,t):c(e,t)};export{n as useIsoMorphicEffect};\n", "import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n", "import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n", "import n,{createContext as r,useContext as i}from\"react\";let e=r(void 0);function a(){return i(e)}function l({value:t,children:o}){return n.createElement(e.Provider,{value:t},o)}export{l as DisabledProvider,a as useDisabled};\n", "function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n", "function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n", "import E,{Fragment as b,cloneElement as j,createElement as v,forwardRef as S,isValidElement as w,use<PERSON><PERSON>back as x,useRef as k}from\"react\";import{classNames as N}from'./class-names.js';import{match as M}from'./match.js';var O=(a=>(a[a.None=0]=\"None\",a[a.RenderStrategy=1]=\"RenderStrategy\",a[a.Static=2]=\"Static\",a))(O||{}),A=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(A||{});function L(){let n=U();return x(r=>C({mergeRefs:n,...r}),[n])}function C({ourProps:n,theirProps:r,slot:e,defaultTag:a,features:s,visible:t=!0,name:l,mergeRefs:i}){i=i!=null?i:$;let o=P(r,n);if(t)return F(o,e,a,l,i);let y=s!=null?s:0;if(y&2){let{static:f=!1,...u}=o;if(f)return F(u,e,a,l,i)}if(y&1){let{unmount:f=!0,...u}=o;return M(f?0:1,{[0](){return null},[1](){return F({...u,hidden:!0,style:{display:\"none\"}},e,a,l,i)}})}return F(o,e,a,l,i)}function F(n,r={},e,a,s){let{as:t=e,children:l,refName:i=\"ref\",...o}=h(n,[\"unmount\",\"static\"]),y=n.ref!==void 0?{[i]:n.ref}:{},f=typeof l==\"function\"?l(r):l;\"className\"in o&&o.className&&typeof o.className==\"function\"&&(o.className=o.className(r)),o[\"aria-labelledby\"]&&o[\"aria-labelledby\"]===o.id&&(o[\"aria-labelledby\"]=void 0);let u={};if(r){let d=!1,p=[];for(let[c,T]of Object.entries(r))typeof T==\"boolean\"&&(d=!0),T===!0&&p.push(c.replace(/([A-Z])/g,g=>`-${g.toLowerCase()}`));if(d){u[\"data-headlessui-state\"]=p.join(\" \");for(let c of p)u[`data-${c}`]=\"\"}}if(t===b&&(Object.keys(m(o)).length>0||Object.keys(m(u)).length>0))if(!w(f)||Array.isArray(f)&&f.length>1){if(Object.keys(m(o)).length>0)throw new Error(['Passing props on \"Fragment\"!',\"\",`The current component <${a} /> is rendering a \"Fragment\".`,\"However we need to passthrough the following props:\",Object.keys(m(o)).concat(Object.keys(m(u))).map(d=>`  - ${d}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"Fragment\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(d=>`  - ${d}`).join(`\n`)].join(`\n`))}else{let d=f.props,p=d==null?void 0:d.className,c=typeof p==\"function\"?(...R)=>N(p(...R),o.className):N(p,o.className),T=c?{className:c}:{},g=P(f.props,m(h(o,[\"ref\"])));for(let R in u)R in g&&delete u[R];return j(f,Object.assign({},g,u,y,{ref:s(H(f),y.ref)},T))}return v(t,Object.assign({},h(o,[\"ref\"]),t!==b&&y,t!==b&&u),f)}function U(){let n=k([]),r=x(e=>{for(let a of n.current)a!=null&&(typeof a==\"function\"?a(e):a.current=e)},[]);return(...e)=>{if(!e.every(a=>a==null))return n.current=e,r}}function $(...n){return n.every(r=>r==null)?void 0:r=>{for(let e of n)e!=null&&(typeof e==\"function\"?e(r):e.current=r)}}function P(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];if(r.disabled||r[\"aria-disabled\"])for(let s in e)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(s)&&(e[s]=[t=>{var l;return(l=t==null?void 0:t.preventDefault)==null?void 0:l.call(t)}]);for(let s in e)Object.assign(r,{[s](t,...l){let i=e[s];for(let o of i){if((t instanceof Event||(t==null?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...l)}}});return r}function _(...n){var a;if(n.length===0)return{};if(n.length===1)return n[0];let r={},e={};for(let s of n)for(let t in s)t.startsWith(\"on\")&&typeof s[t]==\"function\"?((a=e[t])!=null||(e[t]=[]),e[t].push(s[t])):r[t]=s[t];for(let s in e)Object.assign(r,{[s](...t){let l=e[s];for(let i of l)i==null||i(...t)}});return r}function K(n){var r;return Object.assign(S(n),{displayName:(r=n.displayName)!=null?r:n.name})}function m(n){let r=Object.assign({},n);for(let e in r)r[e]===void 0&&delete r[e];return r}function h(n,r=[]){let e=Object.assign({},n);for(let a of r)a in e&&delete e[a];return e}function H(n){return E.version.split(\".\")[0]>=\"19\"?n.props.ref:n.ref}export{O as RenderFeatures,A as RenderStrategy,m as compact,K as forwardRefWithAs,_ as mergeProps,L as useRender};\n", "import{forwardRefWithAs as i,useRender as p}from'../utils/render.js';let a=\"span\";var s=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(s||{});function l(t,r){var n;let{features:d=1,...e}=t,o={ref:r,\"aria-hidden\":(d&2)===2?!0:(n=e[\"aria-hidden\"])!=null?n:void 0,hidden:(d&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(d&4)===4&&(d&2)!==2&&{display:\"none\"}}};return p()({ourProps:o,theirProps:e,slot:{},defaultTag:a,name:\"Hidden\"})}let f=i(l);export{f as Hidden,s as HiddenFeatures};\n", "function o(e){return typeof e!=\"object\"||e===null?!1:\"nodeType\"in e}function t(e){return o(e)&&\"tagName\"in e}function n(e){return t(e)&&\"accessKey\"in e}function i(e){return t(e)&&\"tabIndex\"in e}function r(e){return t(e)&&\"style\"in e}function u(e){return n(e)&&e.nodeName===\"IFRAME\"}function l(e){return n(e)&&e.nodeName===\"INPUT\"}function s(e){return n(e)&&e.nodeName===\"TEXTAREA\"}function m(e){return n(e)&&e.nodeName===\"LABEL\"}function a(e){return n(e)&&e.nodeName===\"FIELDSET\"}function E(e){return n(e)&&e.nodeName===\"LEGEND\"}function L(e){return t(e)?e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type=\"hidden\"]),label,select,textarea,video[controls]'):!1}export{r as hasInlineStyle,t as isElement,n as isHTMLElement,a as isHTMLFieldSetElement,u as isHTMLIframeElement,l as isHTMLInputElement,m as isHTMLLabelElement,E as isHTMLLegendElement,s as isHTMLTextAreaElement,i as isHTMLorSVGElement,L as isInteractiveElement,o as isNode};\n", "import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n", "\"use client\";import m,{createContext as T,useContext as u,useMemo as c,useState as P}from\"react\";import{useEvent as g}from'../../hooks/use-event.js';import{useId as x}from'../../hooks/use-id.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as E}from'../../hooks/use-sync-refs.js';import{useDisabled as v}from'../../internal/disabled.js';import{forwardRefWithAs as R,useRender as I}from'../../utils/render.js';let a=T(null);a.displayName=\"DescriptionContext\";function f(){let r=u(a);if(r===null){let e=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return r}function U(){var r,e;return(e=(r=u(a))==null?void 0:r.value)!=null?e:void 0}function w(){let[r,e]=P([]);return[r.length>0?r.join(\" \"):void 0,c(()=>function(t){let i=g(n=>(e(s=>[...s,n]),()=>e(s=>{let o=s.slice(),p=o.indexOf(n);return p!==-1&&o.splice(p,1),o}))),l=c(()=>({register:i,slot:t.slot,name:t.name,props:t.props,value:t.value}),[i,t.slot,t.name,t.props,t.value]);return m.createElement(a.Provider,{value:l},t.children)},[e])]}let S=\"p\";function C(r,e){let d=x(),t=v(),{id:i=`headlessui-description-${d}`,...l}=r,n=f(),s=E(e);y(()=>n.register(i),[i,n.register]);let o=t||!1,p=c(()=>({...n.slot,disabled:o}),[n.slot,o]),D={ref:s,...n.props,id:i};return I()({ourProps:D,theirProps:l,slot:p,defaultTag:S,name:n.name||\"Description\"})}let _=R(C),H=Object.assign(_,{});export{H as Description,U as useDescribedBy,w as useDescriptions};\n", "var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n", "\"use client\";import r,{createContext as n,useContext as i}from\"react\";let e=n(()=>{});function u(){return i(e)}function C({value:t,children:o}){return r.createElement(e.Provider,{value:t},o)}export{C as CloseProvider,u as useClose};\n", "class a extends Map{constructor(t){super();this.factory=t}get(t){let e=super.get(t);return e===void 0&&(e=this.factory(t),this.set(t,e)),e}}export{a as DefaultMap};\n", "var p=Object.defineProperty;var h=(t,e,r)=>e in t?p(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var f=(t,e,r)=>(h(t,typeof e!=\"symbol\"?e+\"\":e,r),r),b=(t,e,r)=>{if(!e.has(t))throw TypeError(\"Cannot \"+r)};var n=(t,e,r)=>(b(t,e,\"read from private field\"),r?r.call(t):e.get(t)),c=(t,e,r)=>{if(e.has(t))throw TypeError(\"Cannot add the same private member more than once\");e instanceof WeakSet?e.add(t):e.set(t,r)},u=(t,e,r,s)=>(b(t,e,\"write to private field\"),s?s.call(t,r):e.set(t,r),r);var i,a,o;import{DefaultMap as v}from'./utils/default-map.js';import{disposables as S}from'./utils/disposables.js';class E{constructor(e){c(this,i,{});c(this,a,new v(()=>new Set));c(this,o,new Set);f(this,\"disposables\",S());u(this,i,e)}dispose(){this.disposables.dispose()}get state(){return n(this,i)}subscribe(e,r){let s={selector:e,callback:r,current:e(n(this,i))};return n(this,o).add(s),this.disposables.add(()=>{n(this,o).delete(s)})}on(e,r){return n(this,a).get(e).add(r),this.disposables.add(()=>{n(this,a).get(e).delete(r)})}send(e){let r=this.reduce(n(this,i),e);if(r!==n(this,i)){u(this,i,r);for(let s of n(this,o)){let l=s.selector(n(this,i));j(s.current,l)||(s.current=l,s.callback(l))}for(let s of n(this,a).get(e.type))s(n(this,i),e)}}}i=new WeakMap,a=new WeakMap,o=new WeakMap;function j(t,e){return Object.is(t,e)?!0:typeof t!=\"object\"||t===null||typeof e!=\"object\"||e===null?!1:Array.isArray(t)&&Array.isArray(e)?t.length!==e.length?!1:d(t[Symbol.iterator](),e[Symbol.iterator]()):t instanceof Map&&e instanceof Map||t instanceof Set&&e instanceof Set?t.size!==e.size?!1:d(t.entries(),e.entries()):y(t)&&y(e)?d(Object.entries(t)[Symbol.iterator](),Object.entries(e)[Symbol.iterator]()):!1}function d(t,e){do{let r=t.next(),s=e.next();if(r.done&&s.done)return!0;if(r.done||s.done||!Object.is(r.value,s.value))return!1}while(!0)}function y(t){if(Object.prototype.toString.call(t)!==\"[object Object]\")return!1;let e=Object.getPrototypeOf(t);return e===null||Object.getPrototypeOf(e)===null}function x(t){let[e,r]=t(),s=S();return(...l)=>{e(...l),s.dispose(),s.microTask(r)}}export{E as Machine,x as batch,j as shallowEqual};\n", "var a=Object.defineProperty;var r=(e,c,t)=>c in e?a(e,c,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[c]=t;var p=(e,c,t)=>(r(e,typeof c!=\"symbol\"?c+\"\":c,t),t);import{Machine as d}from'../machine.js';import{DefaultMap as l}from'../utils/default-map.js';import{match as u}from'../utils/match.js';var k=(t=>(t[t.Push=0]=\"Push\",t[t.Pop=1]=\"Pop\",t))(k||{});let y={[0](e,c){let t=c.id,s=e.stack,i=e.stack.indexOf(t);if(i!==-1){let n=e.stack.slice();return n.splice(i,1),n.push(t),s=n,{...e,stack:s}}return{...e,stack:[...e.stack,t]}},[1](e,c){let t=c.id,s=e.stack.indexOf(t);if(s===-1)return e;let i=e.stack.slice();return i.splice(s,1),{...e,stack:i}}};class o extends d{constructor(){super(...arguments);p(this,\"actions\",{push:t=>this.send({type:0,id:t}),pop:t=>this.send({type:1,id:t})});p(this,\"selectors\",{isTop:(t,s)=>t.stack[t.stack.length-1]===s,inStack:(t,s)=>t.stack.includes(s)})}static new(){return new o({stack:[]})}reduce(t,s){return u(s.type,y,t,s)}}const x=new l(()=>o.new());export{k as ActionTypes,x as stackMachines};\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.production.js');\n} else {\n  module.exports = require('./cjs/use-sync-external-store-with-selector.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-with-selector.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useSyncExternalStore = React.useSyncExternalStore,\n  useRef = React.useRef,\n  useEffect = React.useEffect,\n  useMemo = React.useMemo,\n  useDebugValue = React.useDebugValue;\nexports.useSyncExternalStoreWithSelector = function (\n  subscribe,\n  getSnapshot,\n  getServerSnapshot,\n  selector,\n  isEqual\n) {\n  var instRef = useRef(null);\n  if (null === instRef.current) {\n    var inst = { hasValue: !1, value: null };\n    instRef.current = inst;\n  } else inst = instRef.current;\n  instRef = useMemo(\n    function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot))\n              return (memoizedSelection = currentSelection);\n          }\n          return (memoizedSelection = nextSnapshot);\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n          return (memoizedSnapshot = nextSnapshot), currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return (memoizedSelection = nextSelection);\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot =\n          void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [\n        function () {\n          return memoizedSelector(getSnapshot());\n        },\n        null === maybeGetServerSnapshot\n          ? void 0\n          : function () {\n              return memoizedSelector(maybeGetServerSnapshot());\n            }\n      ];\n    },\n    [getSnapshot, getServerSnapshot, selector, isEqual]\n  );\n  var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n  useEffect(\n    function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    },\n    [value]\n  );\n  useDebugValue(value);\n  return value;\n};\n", "import{useSyncExternalStoreWithSelector as a}from\"use-sync-external-store/with-selector\";import{useEvent as t}from'./hooks/use-event.js';import{shallowEqual as o}from'./machine.js';function S(e,n,r=o){return a(t(i=>e.subscribe(s,i)),t(()=>e.state),t(()=>e.state),t(n),r)}function s(e){return e}export{S as useSlice};\n", "import{useCallback as n,useId as u}from\"react\";import{stackMachines as p}from'../machines/stack-machine.js';import{useSlice as f}from'../react-glue.js';import{useIsoMorphicEffect as a}from'./use-iso-morphic-effect.js';function I(o,s){let t=u(),r=p.get(s),[i,c]=f(r,n(e=>[r.selectors.isTop(e,t),r.selectors.inStack(e,t)],[r,t]));return a(()=>{if(o)return r.actions.push(t),()=>r.actions.pop(t)},[r,o,t]),o?c?i:!0:!1}export{I as useIsTopLayer};\n", "import{disposables as M}from'../utils/disposables.js';import{getOwnerDocument as b}from'../utils/owner.js';import{useIsTopLayer as L}from'./use-is-top-layer.js';import{useIsoMorphicEffect as T}from'./use-iso-morphic-effect.js';let f=new Map,u=new Map;function h(t){var e;let r=(e=u.get(t))!=null?e:0;return u.set(t,r+1),r!==0?()=>m(t):(f.set(t,{\"aria-hidden\":t.getAttribute(\"aria-hidden\"),inert:t.inert}),t.setAttribute(\"aria-hidden\",\"true\"),t.inert=!0,()=>m(t))}function m(t){var i;let r=(i=u.get(t))!=null?i:1;if(r===1?u.delete(t):u.set(t,r-1),r!==1)return;let e=f.get(t);e&&(e[\"aria-hidden\"]===null?t.removeAttribute(\"aria-hidden\"):t.setAttribute(\"aria-hidden\",e[\"aria-hidden\"]),t.inert=e.inert,f.delete(t))}function y(t,{allowed:r,disallowed:e}={}){let i=L(t,\"inert-others\");T(()=>{var d,c;if(!i)return;let a=M();for(let n of(d=e==null?void 0:e())!=null?d:[])n&&a.add(h(n));let s=(c=r==null?void 0:r())!=null?c:[];for(let n of s){if(!n)continue;let l=b(n);if(!l)continue;let o=n.parentElement;for(;o&&o!==l.body;){for(let p of o.children)s.some(E=>p.contains(E))||a.add(h(p));o=o.parentElement}}return a.dispose},[i,r,e])}export{y as useInertOthers};\n", "import{disposables as N}from'./disposables.js';import*as p from'./dom.js';import{match as L}from'./match.js';import{getOwnerDocument as E}from'./owner.js';let f=[\"[contentEditable=true]\",\"[tabindex]\",\"a[href]\",\"area[href]\",\"button:not([disabled])\",\"iframe\",\"input:not([disabled])\",\"select:not([disabled])\",\"textarea:not([disabled])\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\"),F=[\"[data-autofocus]\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\");var T=(n=>(n[n.First=1]=\"First\",n[n.Previous=2]=\"Previous\",n[n.Next=4]=\"Next\",n[n.Last=8]=\"Last\",n[n.WrapAround=16]=\"WrapAround\",n[n.NoScroll=32]=\"NoScroll\",n[n.AutoFocus=64]=\"AutoFocus\",n))(T||{}),y=(o=>(o[o.Error=0]=\"Error\",o[o.Overflow=1]=\"Overflow\",o[o.Success=2]=\"Success\",o[o.Underflow=3]=\"Underflow\",o))(y||{}),S=(t=>(t[t.Previous=-1]=\"Previous\",t[t.Next=1]=\"Next\",t))(S||{});function b(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(f)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function O(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(F)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=(t=>(t[t.Strict=0]=\"Strict\",t[t.Loose=1]=\"Loose\",t))(h||{});function A(e,r=0){var t;return e===((t=E(e))==null?void 0:t.body)?!1:L(r,{[0](){return e.matches(f)},[1](){let l=e;for(;l!==null;){if(l.matches(f))return!0;l=l.parentElement}return!1}})}function V(e){let r=E(e);N().nextFrame(()=>{r&&p.isHTMLorSVGElement(r.activeElement)&&!A(r.activeElement,0)&&I(e)})}var H=(t=>(t[t.Keyboard=0]=\"Keyboard\",t[t.Mouse=1]=\"Mouse\",t))(H||{});typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"keydown\",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0),document.addEventListener(\"click\",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0));function I(e){e==null||e.focus({preventScroll:!0})}let w=[\"textarea\",\"input\"].join(\",\");function _(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,w))!=null?t:!1}function P(e,r=t=>t){return e.slice().sort((t,l)=>{let o=r(t),c=r(l);if(o===null||c===null)return 0;let u=o.compareDocumentPosition(c);return u&Node.DOCUMENT_POSITION_FOLLOWING?-1:u&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function j(e,r){return g(b(),r,{relativeTo:e})}function g(e,r,{sorted:t=!0,relativeTo:l=null,skipElements:o=[]}={}){let c=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?t?P(e):e:r&64?O(e):b(e);o.length>0&&u.length>1&&(u=u.filter(s=>!o.some(a=>a!=null&&\"current\"in a?(a==null?void 0:a.current)===s:a===s))),l=l!=null?l:c.activeElement;let n=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),x=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,u.indexOf(l))-1;if(r&4)return Math.max(0,u.indexOf(l))+1;if(r&8)return u.length-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),M=r&32?{preventScroll:!0}:{},m=0,d=u.length,i;do{if(m>=d||m+d<=0)return 0;let s=x+m;if(r&16)s=(s+d)%d;else{if(s<0)return 3;if(s>=d)return 1}i=u[s],i==null||i.focus(M),m+=n}while(i!==c.activeElement);return r&6&&_(i)&&i.select(),2}export{T as Focus,y as FocusResult,h as FocusableMode,I as focusElement,j as focusFrom,g as focusIn,f as focusableSelector,O as getAutoFocusableElements,b as getFocusableElements,A as isFocusableElement,V as restoreFocusIfNecessary,P as sortByDomNode};\n", "function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n", "import{useEffect as c}from\"react\";import{useLatestValue as a}from'./use-latest-value.js';function i(t,e,o,n){let u=a(o);c(()=>{if(!t)return;function r(m){u.current(m)}return document.addEventListener(e,r,n),()=>document.removeEventListener(e,r,n)},[t,e,n])}export{i as useDocumentEvent};\n", "import{useEffect as a}from\"react\";import{useLatestValue as f}from'./use-latest-value.js';function s(t,e,o,n){let i=f(o);a(()=>{if(!t)return;function r(d){i.current(d)}return window.addEventListener(e,r,n),()=>window.removeEventListener(e,r,n)},[t,e,n])}export{s as useWindowEvent};\n", "import{useCallback as T,useRef as E}from\"react\";import*as d from'../utils/dom.js';import{FocusableMode as g,isFocusableElement as y}from'../utils/focus-management.js';import{isMobile as p}from'../utils/platform.js';import{useDocumentEvent as a}from'./use-document-event.js';import{useLatestValue as L}from'./use-latest-value.js';import{useWindowEvent as x}from'./use-window-event.js';const C=30;function k(o,f,h){let m=L(h),s=T(function(e,c){if(e.defaultPrevented)return;let r=c(e);if(r===null||!r.getRootNode().contains(r)||!r.isConnected)return;let M=function u(n){return typeof n==\"function\"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let u of M)if(u!==null&&(u.contains(r)||e.composed&&e.composedPath().includes(u)))return;return!y(r,g.Loose)&&r.tabIndex!==-1&&e.preventDefault(),m.current(e,r)},[m,f]),i=E(null);a(o,\"pointerdown\",t=>{var e,c;p()||(i.current=((c=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:c[0])||t.target)},!0),a(o,\"pointerup\",t=>{if(p()||!i.current)return;let e=i.current;return i.current=null,s(t,()=>e)},!0);let l=E({x:0,y:0});a(o,\"touchstart\",t=>{l.current.x=t.touches[0].clientX,l.current.y=t.touches[0].clientY},!0),a(o,\"touchend\",t=>{let e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY};if(!(Math.abs(e.x-l.current.x)>=C||Math.abs(e.y-l.current.y)>=C))return s(t,()=>d.isHTMLorSVGElement(t.target)?t.target:null)},!0),x(o,\"blur\",t=>s(t,()=>d.isHTMLIframeElement(window.document.activeElement)?window.document.activeElement:null),!0)}export{k as useOutsideClick};\n", "import{useMemo as t}from\"react\";import{getOwnerDocument as o}from'../utils/owner.js';function n(...e){return t(()=>o(...e),[...e])}export{n as useOwnerDocument};\n", "import{useEffect as d}from\"react\";import{useLatestValue as s}from'./use-latest-value.js';function E(n,e,a,t){let i=s(a);d(()=>{n=n!=null?n:window;function r(o){i.current(o)}return n.addEventListener(e,r,t),()=>n.removeEventListener(e,r,t)},[n,e,t])}export{E as useEventListener};\n", "function d(){let r;return{before({doc:e}){var l;let o=e.documentElement,t=(l=e.defaultView)!=null?l:window;r=Math.max(0,t.innerWidth-o.clientWidth)},after({doc:e,d:o}){let t=e.documentElement,l=Math.max(0,t.clientWidth-t.offsetWidth),n=Math.max(0,r-l);o.style(t,\"paddingRight\",`${n}px`)}}}export{d as adjustScrollbarPadding};\n", "import{disposables as u}from'../../utils/disposables.js';import*as o from'../../utils/dom.js';import{isIOS as p}from'../../utils/platform.js';function w(){return p()?{before({doc:n,d:l,meta:f}){function i(a){return f.containers.flatMap(r=>r()).some(r=>r.contains(a))}l.microTask(()=>{var c;if(window.getComputedStyle(n.documentElement).scrollBehavior!==\"auto\"){let t=u();t.style(n.documentElement,\"scrollBehavior\",\"auto\"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(c=window.scrollY)!=null?c:window.pageYOffset,r=null;l.addEventListener(n,\"click\",t=>{if(o.isHTMLorSVGElement(t.target))try{let e=t.target.closest(\"a\");if(!e)return;let{hash:m}=new URL(e.href),s=n.querySelector(m);o.isHTMLorSVGElement(s)&&!i(s)&&(r=s)}catch{}},!0),l.addEventListener(n,\"touchstart\",t=>{if(o.isHTMLorSVGElement(t.target)&&o.hasInlineStyle(t.target))if(i(t.target)){let e=t.target;for(;e.parentElement&&i(e.parentElement);)e=e.parentElement;l.style(e,\"overscrollBehavior\",\"contain\")}else l.style(t.target,\"touchAction\",\"none\")}),l.addEventListener(n,\"touchmove\",t=>{if(o.isHTMLorSVGElement(t.target)){if(o.isHTMLInputElement(t.target))return;if(i(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()}},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),r&&r.isConnected&&(r.scrollIntoView({block:\"nearest\"}),r=null)})})}}:{}}export{w as handleIOSLocking};\n", "import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n", "function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n", "function r(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{r as preventScroll};\n", "import{useStore as s}from'../../hooks/use-store.js';import{useIsoMorphicEffect as u}from'../use-iso-morphic-effect.js';import{overflows as t}from'./overflow-store.js';function a(r,e,n=()=>({containers:[]})){let f=s(t),o=e?f.get(e):void 0,i=o?o.count>0:!1;return u(()=>{if(!(!e||!r))return t.dispatch(\"PUSH\",e,n),()=>t.dispatch(\"POP\",e,n)},[r,e]),i}export{a as useDocumentOverflowLockedEffect};\n", "import{useSyncExternalStore as e}from\"react\";function o(t){return e(t.subscribe,t.getSnapshot,t.getSnapshot)}export{o as useStore};\n", "var T,b;import{useRef as c,useState as S}from\"react\";import{disposables as m}from'../utils/disposables.js';import{useDisposables as g}from'./use-disposables.js';import{useFlags as y}from'./use-flags.js';import{useIsoMorphicEffect as A}from'./use-iso-morphic-effect.js';typeof process!=\"undefined\"&&typeof globalThis!=\"undefined\"&&typeof Element!=\"undefined\"&&((T=process==null?void 0:process.env)==null?void 0:T[\"NODE_ENV\"])===\"test\"&&typeof((b=Element==null?void 0:Element.prototype)==null?void 0:b.getAnimations)==\"undefined\"&&(Element.prototype.getAnimations=function(){return console.warn([\"Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\",\"Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\",\"\",\"Example usage:\",\"```js\",\"import { mockAnimationsApi } from 'jsdom-testing-mocks'\",\"mockAnimationsApi()\",\"```\"].join(`\n`)),[]});var L=(r=>(r[r.None=0]=\"None\",r[r.Closed=1]=\"Closed\",r[r.Enter=2]=\"Enter\",r[r.Leave=4]=\"Leave\",r))(L||{});function R(t){let n={};for(let e in t)t[e]===!0&&(n[`data-${e}`]=\"\");return n}function x(t,n,e,i){let[r,o]=S(e),{hasFlag:s,addFlag:a,removeFlag:l}=y(t&&r?3:0),u=c(!1),f=c(!1),E=g();return A(()=>{var d;if(t){if(e&&o(!0),!n){e&&a(3);return}return(d=i==null?void 0:i.start)==null||d.call(i,e),C(n,{inFlight:u,prepare(){f.current?f.current=!1:f.current=u.current,u.current=!0,!f.current&&(e?(a(3),l(4)):(a(4),l(2)))},run(){f.current?e?(l(3),a(4)):(l(4),a(3)):e?l(1):a(1)},done(){var p;f.current&&typeof n.getAnimations==\"function\"&&n.getAnimations().length>0||(u.current=!1,l(7),e||o(!1),(p=i==null?void 0:i.end)==null||p.call(i,e))}})}},[t,e,n,E]),t?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[e,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function C(t,{prepare:n,run:e,done:i,inFlight:r}){let o=m();return j(t,{prepare:n,inFlight:r}),o.nextFrame(()=>{e(),o.requestAnimationFrame(()=>{o.add(M(t,i))})}),o.dispose}function M(t,n){var o,s;let e=m();if(!t)return e.dispose;let i=!1;e.add(()=>{i=!0});let r=(s=(o=t.getAnimations)==null?void 0:o.call(t).filter(a=>a instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),e.dispose):(Promise.allSettled(r.map(a=>a.finished)).then(()=>{i||n()}),e.dispose)}function j(t,{inFlight:n,prepare:e}){if(n!=null&&n.current){e();return}let i=t.style.transition;t.style.transition=\"none\",e(),t.offsetHeight,t.style.transition=i}export{R as transitionDataAttributes,x as useTransition};\n", "import{useCallback as r,useState as b}from\"react\";function c(u=0){let[t,l]=b(u),g=r(e=>l(e),[t]),s=r(e=>l(a=>a|e),[t]),m=r(e=>(t&e)===e,[t]),n=r(e=>l(a=>a&~e),[l]),F=r(e=>l(a=>a^e),[l]);return{flags:t,setFlag:g,addFlag:s,hasFlag:m,removeFlag:n,toggleFlag:F}}export{c as useFlags};\n", "import{useEffect as f,useRef as s}from\"react\";import{useEvent as i}from'./use-event.js';function m(u,t){let e=s([]),r=i(u);f(()=>{let o=[...e.current];for(let[a,l]of t.entries())if(e.current[a]!==l){let n=r(t,o);return e.current=t,n}},[r,...t])}export{m as useWatch};\n", "import r,{createContext as l,useContext as d}from\"react\";let n=l(null);n.displayName=\"OpenClosedContext\";var i=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(i||{});function u(){return d(n)}function c({value:o,children:t}){return r.createElement(n.Provider,{value:o},t)}function s({children:o}){return r.createElement(n.Provider,{value:null},o)}export{c as OpenClosedProvider,s as ResetOpenClosedProvider,i as State,u as useOpenClosed};\n", "import{onDocumentReady as d}from'./document-ready.js';import*as u from'./dom.js';import{focusableSelector as i}from'./focus-management.js';let n=[];d(()=>{function e(t){if(!u.isHTMLorSVGElement(t.target)||t.target===document.body||n[0]===t.target)return;let r=t.target;r=r.closest(i),n.unshift(r!=null?r:t.target),n=n.filter(o=>o!=null&&o.isConnected),n.splice(10)}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{n as history};\n", "import{useEffect as u,useRef as n}from\"react\";import{microTask as o}from'../utils/micro-task.js';import{useEvent as f}from'./use-event.js';function c(t){let r=f(t),e=n(!1);u(()=>(e.current=!1,()=>{e.current=!0,o(()=>{e.current&&r()})}),[r])}export{c as useOnUnmount};\n", "import*as t from\"react\";import{env as f}from'../utils/env.js';function s(){let r=typeof document==\"undefined\";return\"useSyncExternalStore\"in t?(o=>o.useSyncExternalStore)(t)(()=>()=>{},()=>!1,()=>!r):!1}function l(){let r=s(),[e,n]=t.useState(f.isHandoffComplete);return e&&f.isHandoffComplete===!1&&n(!1),t.useEffect(()=>{e!==!0&&n(!0)},[e]),t.useEffect(()=>f.handoff(),[]),r?!1:e}export{l as useServerHandoffComplete};\n", "function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n", "import t,{createContext as r,useContext as c}from\"react\";let e=r(!1);function a(){return c(e)}function l(o){return t.createElement(e.Provider,{value:o.force},o.children)}export{l as ForcePortalRoot,a as usePortalRoot};\n", "\"use client\";import T,{Fragment as E,createContext as A,useContext as d,useEffect as G,useMemo as x,useRef as L,useState as c}from\"react\";import{createPortal as h}from\"react-dom\";import{useEvent as _}from'../../hooks/use-event.js';import{useIsoMorphicEffect as C}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as F}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as U}from'../../hooks/use-owner.js';import{useServerHandoffComplete as N}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as S,useSyncRefs as m}from'../../hooks/use-sync-refs.js';import{usePortalRoot as W}from'../../internal/portal-force-root.js';import*as j from'../../utils/dom.js';import{env as v}from'../../utils/env.js';import{forwardRefWithAs as y,useRender as R}from'../../utils/render.js';function I(e){let l=W(),o=d(H),[r,u]=c(()=>{var i;if(!l&&o!==null)return(i=o.current)!=null?i:null;if(v.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let a=e.createElement(\"div\");return a.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(a)});return G(()=>{r!==null&&(e!=null&&e.body.contains(r)||e==null||e.body.appendChild(r))},[r,e]),G(()=>{l||o!==null&&u(o.current)},[o,u,l]),r}let M=E,D=y(function(l,o){let{ownerDocument:r=null,...u}=l,t=L(null),a=m(S(s=>{t.current=s}),o),i=U(t),f=r!=null?r:i,p=I(f),[n]=c(()=>{var s;return v.isServer?null:(s=f==null?void 0:f.createElement(\"div\"))!=null?s:null}),P=d(g),O=N();C(()=>{!p||!n||p.contains(n)||(n.setAttribute(\"data-headlessui-portal\",\"\"),p.appendChild(n))},[p,n]),C(()=>{if(n&&P)return P.register(n)},[P,n]),F(()=>{var s;!p||!n||(j.isNode(n)&&p.contains(n)&&p.removeChild(n),p.childNodes.length<=0&&((s=p.parentElement)==null||s.removeChild(p)))});let b=R();return O?!p||!n?null:h(b({ourProps:{ref:a},theirProps:u,slot:{},defaultTag:M,name:\"Portal\"}),n):null});function J(e,l){let o=m(l),{enabled:r=!0,ownerDocument:u,...t}=e,a=R();return r?T.createElement(D,{...t,ownerDocument:u,ref:o}):a({ourProps:{ref:o},theirProps:t,slot:{},defaultTag:M,name:\"Portal\"})}let X=E,H=A(null);function k(e,l){let{target:o,...r}=e,t={ref:m(l)},a=R();return T.createElement(H.Provider,{value:o},a({ourProps:t,theirProps:r,defaultTag:X,name:\"Popover.Group\"}))}let g=A(null);function oe(){let e=d(g),l=L([]),o=_(t=>(l.current.push(t),e&&e.register(t),()=>r(t))),r=_(t=>{let a=l.current.indexOf(t);a!==-1&&l.current.splice(a,1),e&&e.unregister(t)}),u=x(()=>({register:o,unregister:r,portals:l}),[o,r,l]);return[l,x(()=>function({children:a}){return T.createElement(g.Provider,{value:u},a)},[u])]}let B=y(J),q=y(k),ne=Object.assign(B,{Group:q});export{ne as Portal,q as PortalGroup,oe as useNestedPortals};\n", "import s,{createContext as E,useContext as h,useState as p}from\"react\";import{Hidden as b,HiddenFeatures as M}from'../internal/hidden.js';import*as f from'../utils/dom.js';import{getOwnerDocument as v}from'../utils/owner.js';import{useEvent as m}from'./use-event.js';import{useOwnerDocument as x}from'./use-owner.js';function H({defaultContainers:r=[],portals:n,mainTreeNode:o}={}){let l=x(o),u=m(()=>{var i,c;let t=[];for(let e of r)e!==null&&(f.isElement(e)?t.push(e):\"current\"in e&&f.isElement(e.current)&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&f.isElement(e)&&e.id!==\"headlessui-portal-root\"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(d=>e.contains(d))||t.push(e));return t});return{resolveContainers:u,contains:m(t=>u().some(i=>i.contains(t)))}}let a=E(null);function P({children:r,node:n}){let[o,l]=p(null),u=y(n!=null?n:o);return s.createElement(a.Provider,{value:u},r,u===null&&s.createElement(b,{features:M.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=v(t))==null?void 0:i.querySelectorAll(\"html > *, body > *\"))!=null?c:[])if(e!==document.body&&e!==document.head&&f.isElement(e)&&e!=null&&e.contains(t)){l(e);break}}}}))}function y(r=null){var n;return(n=h(a))!=null?n:r}export{P as MainTreeProvider,y as useMainTreeNode,H as useRootContainers};\n", "import{useRef as r}from\"react\";import{useIsoMorphicEffect as t}from'./use-iso-morphic-effect.js';function f(){let e=r(!1);return t(()=>(e.current=!0,()=>{e.current=!1}),[]),e}export{f as useIsMounted};\n", "import{useRef as o}from\"react\";import{useWindowEvent as t}from'./use-window-event.js';var a=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(a||{});function u(){let e=o(0);return t(!0,\"keydown\",r=>{r.key===\"Tab\"&&(e.current=r.shiftKey?1:0)},!0),e}export{a as Direction,u as useTabDirection};\n", "\"use client\";import F,{useRef as M}from\"react\";import{useDisposables as W}from'../../hooks/use-disposables.js';import{useEvent as O}from'../../hooks/use-event.js';import{useEventListener as K}from'../../hooks/use-event-listener.js';import{useIsMounted as P}from'../../hooks/use-is-mounted.js';import{useIsTopLayer as C}from'../../hooks/use-is-top-layer.js';import{useOnUnmount as q}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as J}from'../../hooks/use-owner.js';import{useServerHandoffComplete as X}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as z}from'../../hooks/use-sync-refs.js';import{Direction as y,useTabDirection as Q}from'../../hooks/use-tab-direction.js';import{useWatch as R}from'../../hooks/use-watch.js';import{Hidden as _,HiddenFeatures as S}from'../../internal/hidden.js';import{history as H}from'../../utils/active-element-history.js';import*as T from'../../utils/dom.js';import{Focus as i,FocusResult as h,focusElement as p,focusIn as d}from'../../utils/focus-management.js';import{match as j}from'../../utils/match.js';import{microTask as U}from'../../utils/micro-task.js';import{forwardRefWithAs as Y,useRender as Z}from'../../utils/render.js';function x(s){if(!s)return new Set;if(typeof s==\"function\")return new Set(s());let e=new Set;for(let t of s.current)T.isElement(t.current)&&e.add(t.current);return e}let $=\"div\";var G=(n=>(n[n.None=0]=\"None\",n[n.InitialFocus=1]=\"InitialFocus\",n[n.TabLock=2]=\"TabLock\",n[n.FocusLock=4]=\"FocusLock\",n[n.RestoreFocus=8]=\"RestoreFocus\",n[n.AutoFocus=16]=\"AutoFocus\",n))(G||{});function D(s,e){let t=M(null),r=z(t,e),{initialFocus:o,initialFocusFallback:a,containers:n,features:u=15,...f}=s;X()||(u=0);let l=J(t);te(u,{ownerDocument:l});let m=re(u,{ownerDocument:l,container:t,initialFocus:o,initialFocusFallback:a});ne(u,{ownerDocument:l,container:t,containers:n,previousActiveElement:m});let g=Q(),v=O(c=>{if(!T.isHTMLElement(t.current))return;let E=t.current;(V=>V())(()=>{j(g.current,{[y.Forwards]:()=>{d(E,i.First,{skipElements:[c.relatedTarget,a]})},[y.Backwards]:()=>{d(E,i.Last,{skipElements:[c.relatedTarget,a]})}})})}),A=C(!!(u&2),\"focus-trap#tab-lock\"),N=W(),b=M(!1),k={ref:r,onKeyDown(c){c.key==\"Tab\"&&(b.current=!0,N.requestAnimationFrame(()=>{b.current=!1}))},onBlur(c){if(!(u&4))return;let E=x(n);T.isHTMLElement(t.current)&&E.add(t.current);let L=c.relatedTarget;T.isHTMLorSVGElement(L)&&L.dataset.headlessuiFocusGuard!==\"true\"&&(I(E,L)||(b.current?d(t.current,j(g.current,{[y.Forwards]:()=>i.Next,[y.Backwards]:()=>i.Previous})|i.WrapAround,{relativeTo:c.target}):T.isHTMLorSVGElement(c.target)&&p(c.target)))}},B=Z();return F.createElement(F.Fragment,null,A&&F.createElement(_,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:v,features:S.Focusable}),B({ourProps:k,theirProps:f,defaultTag:$,name:\"FocusTrap\"}),A&&F.createElement(_,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:v,features:S.Focusable}))}let w=Y(D),Re=Object.assign(w,{features:G});function ee(s=!0){let e=M(H.slice());return R(([t],[r])=>{r===!0&&t===!1&&U(()=>{e.current.splice(0)}),r===!1&&t===!0&&(e.current=H.slice())},[s,H,e]),O(()=>{var t;return(t=e.current.find(r=>r!=null&&r.isConnected))!=null?t:null})}function te(s,{ownerDocument:e}){let t=!!(s&8),r=ee(t);R(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&p(r())},[t]),q(()=>{t&&p(r())})}function re(s,{ownerDocument:e,container:t,initialFocus:r,initialFocusFallback:o}){let a=M(null),n=C(!!(s&1),\"focus-trap#initial-focus\"),u=P();return R(()=>{if(s===0)return;if(!n){o!=null&&o.current&&p(o.current);return}let f=t.current;f&&U(()=>{if(!u.current)return;let l=e==null?void 0:e.activeElement;if(r!=null&&r.current){if((r==null?void 0:r.current)===l){a.current=l;return}}else if(f.contains(l)){a.current=l;return}if(r!=null&&r.current)p(r.current);else{if(s&16){if(d(f,i.First|i.AutoFocus)!==h.Error)return}else if(d(f,i.First)!==h.Error)return;if(o!=null&&o.current&&(p(o.current),(e==null?void 0:e.activeElement)===o.current))return;console.warn(\"There are no focusable elements inside the <FocusTrap />\")}a.current=e==null?void 0:e.activeElement})},[o,n,s]),a}function ne(s,{ownerDocument:e,container:t,containers:r,previousActiveElement:o}){let a=P(),n=!!(s&4);K(e==null?void 0:e.defaultView,\"focus\",u=>{if(!n||!a.current)return;let f=x(r);T.isHTMLElement(t.current)&&f.add(t.current);let l=o.current;if(!l)return;let m=u.target;T.isHTMLElement(m)?I(f,m)?(o.current=m,p(m)):(u.preventDefault(),u.stopPropagation(),p(l)):p(o.current)},!0)}function I(s,e){for(let t of s)if(t.contains(e))return!0;return!1}export{Re as FocusTrap,G as FocusTrapFeatures};\n", "\"use client\";import c,{Fragment as O,createContext as ne,useContext as q,useEffect as ge,useMemo as ie,useRef as b,useState as V}from\"react\";import{useDisposables as ve}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useIsMounted as be}from'../../hooks/use-is-mounted.js';import{useIsoMorphicEffect as D}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ee}from'../../hooks/use-latest-value.js';import{useServerHandoffComplete as re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as oe}from'../../hooks/use-sync-refs.js';import{transitionDataAttributes as Se,useTransition as Re}from'../../hooks/use-transition.js';import{OpenClosedProvider as ye,State as x,useOpenClosed as se}from'../../internal/open-closed.js';import{classNames as Pe}from'../../utils/class-names.js';import{match as le}from'../../utils/match.js';import{RenderFeatures as xe,RenderStrategy as P,compact as Ne,forwardRefWithAs as J,useRender as ae}from'../../utils/render.js';function ue(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((t=e.as)!=null?t:de)!==O||c.Children.count(e.children)===1}let w=ne(null);w.displayName=\"TransitionContext\";var _e=(n=>(n.Visible=\"visible\",n.Hidden=\"hidden\",n))(_e||{});function De(){let e=q(w);if(e===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return e}function He(){let e=q(M);if(e===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return e}let M=ne(null);M.displayName=\"NestingContext\";function U(e){return\"children\"in e?U(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t===\"visible\").length>0}function Te(e,t){let n=Ee(e),l=b([]),S=be(),R=ve(),d=E((o,i=P.Hidden)=>{let a=l.current.findIndex(({el:s})=>s===o);a!==-1&&(le(i,{[P.Unmount](){l.current.splice(a,1)},[P.Hidden](){l.current[a].state=\"hidden\"}}),R.microTask(()=>{var s;!U(l)&&S.current&&((s=n.current)==null||s.call(n))}))}),y=E(o=>{let i=l.current.find(({el:a})=>a===o);return i?i.state!==\"visible\"&&(i.state=\"visible\"):l.current.push({el:o,state:\"visible\"}),()=>d(o,P.Unmount)}),C=b([]),p=b(Promise.resolve()),h=b({enter:[],leave:[]}),g=E((o,i,a)=>{C.current.splice(0),t&&(t.chains.current[i]=t.chains.current[i].filter(([s])=>s!==o)),t==null||t.chains.current[i].push([o,new Promise(s=>{C.current.push(s)})]),t==null||t.chains.current[i].push([o,new Promise(s=>{Promise.all(h.current[i].map(([r,f])=>f)).then(()=>s())})]),i===\"enter\"?p.current=p.current.then(()=>t==null?void 0:t.wait.current).then(()=>a(i)):a(i)}),v=E((o,i,a)=>{Promise.all(h.current[i].splice(0).map(([s,r])=>r)).then(()=>{var s;(s=C.current.shift())==null||s()}).then(()=>a(i))});return ie(()=>({children:l,register:y,unregister:d,onStart:g,onStop:v,wait:p,chains:h}),[y,d,l,g,v,h,p])}let de=O,fe=xe.RenderStrategy;function Ae(e,t){var ee,te;let{transition:n=!0,beforeEnter:l,afterEnter:S,beforeLeave:R,afterLeave:d,enter:y,enterFrom:C,enterTo:p,entered:h,leave:g,leaveFrom:v,leaveTo:o,...i}=e,[a,s]=V(null),r=b(null),f=ue(e),j=oe(...f?[r,t,s]:t===null?[]:[t]),H=(ee=i.unmount)==null||ee?P.Unmount:P.Hidden,{show:u,appear:z,initial:K}=De(),[m,G]=V(u?\"visible\":\"hidden\"),Q=He(),{register:A,unregister:I}=Q;D(()=>A(r),[A,r]),D(()=>{if(H===P.Hidden&&r.current){if(u&&m!==\"visible\"){G(\"visible\");return}return le(m,{[\"hidden\"]:()=>I(r),[\"visible\"]:()=>A(r)})}},[m,r,A,I,u,H]);let B=re();D(()=>{if(f&&B&&m===\"visible\"&&r.current===null)throw new Error(\"Did you forget to passthrough the `ref` to the actual DOM node?\")},[r,m,B,f]);let ce=K&&!z,Y=z&&u&&K,W=b(!1),L=Te(()=>{W.current||(G(\"hidden\"),I(r))},Q),Z=E(k=>{W.current=!0;let F=k?\"enter\":\"leave\";L.onStart(r,F,_=>{_===\"enter\"?l==null||l():_===\"leave\"&&(R==null||R())})}),$=E(k=>{let F=k?\"enter\":\"leave\";W.current=!1,L.onStop(r,F,_=>{_===\"enter\"?S==null||S():_===\"leave\"&&(d==null||d())}),F===\"leave\"&&!U(L)&&(G(\"hidden\"),I(r))});ge(()=>{f&&n||(Z(u),$(u))},[u,f,n]);let pe=(()=>!(!n||!f||!B||ce))(),[,T]=Re(pe,a,u,{start:Z,end:$}),Ce=Ne({ref:j,className:((te=Pe(i.className,Y&&y,Y&&C,T.enter&&y,T.enter&&T.closed&&C,T.enter&&!T.closed&&p,T.leave&&g,T.leave&&!T.closed&&v,T.leave&&T.closed&&o,!T.transition&&u&&h))==null?void 0:te.trim())||void 0,...Se(T)}),N=0;m===\"visible\"&&(N|=x.Open),m===\"hidden\"&&(N|=x.Closed),u&&m===\"hidden\"&&(N|=x.Opening),!u&&m===\"visible\"&&(N|=x.Closing);let he=ae();return c.createElement(M.Provider,{value:L},c.createElement(ye,{value:N},he({ourProps:Ce,theirProps:i,defaultTag:de,features:fe,visible:m===\"visible\",name:\"Transition.Child\"})))}function Ie(e,t){let{show:n,appear:l=!1,unmount:S=!0,...R}=e,d=b(null),y=ue(e),C=oe(...y?[d,t]:t===null?[]:[t]);re();let p=se();if(n===void 0&&p!==null&&(n=(p&x.Open)===x.Open),n===void 0)throw new Error(\"A <Transition /> is used but it is missing a `show={true | false}` prop.\");let[h,g]=V(n?\"visible\":\"hidden\"),v=Te(()=>{n||g(\"hidden\")}),[o,i]=V(!0),a=b([n]);D(()=>{o!==!1&&a.current[a.current.length-1]!==n&&(a.current.push(n),i(!1))},[a,n]);let s=ie(()=>({show:n,appear:l,initial:o}),[n,l,o]);D(()=>{n?g(\"visible\"):!U(v)&&d.current!==null&&g(\"hidden\")},[n,v]);let r={unmount:S},f=E(()=>{var u;o&&i(!1),(u=e.beforeEnter)==null||u.call(e)}),j=E(()=>{var u;o&&i(!1),(u=e.beforeLeave)==null||u.call(e)}),H=ae();return c.createElement(M.Provider,{value:v},c.createElement(w.Provider,{value:s},H({ourProps:{...r,as:O,children:c.createElement(me,{ref:C,...r,...R,beforeEnter:f,beforeLeave:j})},theirProps:{},defaultTag:O,features:fe,visible:h===\"visible\",name:\"Transition\"})))}function Le(e,t){let n=q(w)!==null,l=se()!==null;return c.createElement(c.Fragment,null,!n&&l?c.createElement(X,{ref:t,...e}):c.createElement(me,{ref:t,...e}))}let X=J(Ie),me=J(Ae),Fe=J(Le),ze=Object.assign(X,{Child:Fe,Root:X});export{ze as Transition,Fe as TransitionChild};\n", "\"use client\";import l,{Fragment as $,createContext as se,createRef as pe,use<PERSON><PERSON>back as de,useContext as ue,useEffect as fe,useMemo as A,useReducer as Te,useRef as j}from\"react\";import{useEscape as ge}from'../../hooks/use-escape.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as k}from'../../hooks/use-id.js';import{useInertOthers as ce}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as me}from'../../hooks/use-is-touch-device.js';import{useIsoMorphicEffect as De}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as Pe}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ye}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Ee}from'../../hooks/use-owner.js';import{MainTreeProvider as Y,useMainTreeNode as Ae,useRootContainers as _e}from'../../hooks/use-root-containers.js';import{useScrollLock as Ce}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Fe}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as be,State as x,useOpenClosed as J}from'../../internal/open-closed.js';import{ForcePortalRoot as K}from'../../internal/portal-force-root.js';import{stackMachines as ve}from'../../machines/stack-machine.js';import{useSlice as Le}from'../../react-glue.js';import{match as xe}from'../../utils/match.js';import{RenderFeatures as X,forwardRefWithAs as C,useRender as h}from'../../utils/render.js';import{Description as V,useDescriptions as he}from'../description/description.js';import{FocusTrap as Oe,FocusTrapFeatures as R}from'../focus-trap/focus-trap.js';import{Portal as Se,PortalGroup as Ie,useNestedPortals as Me}from'../portal/portal.js';import{Transition as ke,TransitionChild as q}from'../transition/transition.js';var Ge=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))(Ge||{}),we=(t=>(t[t.SetTitleId=0]=\"SetTitleId\",t))(we||{});let Be={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},w=se(null);w.displayName=\"DialogContext\";function O(e){let t=ue(w);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return t}function Ue(e,t){return xe(t.type,Be,e,t)}let z=C(function(t,o){let a=k(),{id:n=`headlessui-dialog-${a}`,open:i,onClose:s,initialFocus:d,role:p=\"dialog\",autoFocus:T=!0,__demoMode:u=!1,unmount:y=!1,...S}=t,F=j(!1);p=function(){return p===\"dialog\"||p===\"alertdialog\"?p:(F.current||(F.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \\`dialog\\` and and \\`alertdialog\\` are supported. Using \\`dialog\\` instead.`)),\"dialog\")}();let c=J();i===void 0&&c!==null&&(i=(c&x.Open)===x.Open);let f=j(null),I=G(f,o),b=Ee(f),g=i?0:1,[v,Q]=Te(Ue,{titleId:null,descriptionId:null,panelRef:pe()}),m=_(()=>s(!1)),B=_(r=>Q({type:0,id:r})),D=Re()?g===0:!1,[Z,ee]=Me(),te={get current(){var r;return(r=v.panelRef.current)!=null?r:f.current}},L=Ae(),{resolveContainers:M}=_e({mainTreeNode:L,portals:Z,defaultContainers:[te]}),U=c!==null?(c&x.Closing)===x.Closing:!1;ce(u||U?!1:D,{allowed:_(()=>{var r,W;return[(W=(r=f.current)==null?void 0:r.closest(\"[data-headlessui-portal]\"))!=null?W:null]}),disallowed:_(()=>{var r;return[(r=L==null?void 0:L.closest(\"body > *:not(#headlessui-portal-root)\"))!=null?r:null]})});let P=ve.get(null);De(()=>{if(D)return P.actions.push(n),()=>P.actions.pop(n)},[P,n,D]);let H=Le(P,de(r=>P.selectors.isTop(r,n),[P,n]));ye(H,M,r=>{r.preventDefault(),m()}),ge(H,b==null?void 0:b.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&\"blur\"in document.activeElement&&typeof document.activeElement.blur==\"function\"&&document.activeElement.blur(),m()}),Ce(u||U?!1:D,b,M),Pe(D,f,m);let[oe,ne]=he(),re=A(()=>[{dialogState:g,close:m,setTitleId:B,unmount:y},v],[g,v,m,B,y]),N=A(()=>({open:g===0}),[g]),le={ref:I,id:n,role:p,tabIndex:-1,\"aria-modal\":u?void 0:g===0?!0:void 0,\"aria-labelledby\":v.titleId,\"aria-describedby\":oe,unmount:y},ae=!me(),E=R.None;D&&!u&&(E|=R.RestoreFocus,E|=R.TabLock,T&&(E|=R.AutoFocus),ae&&(E|=R.InitialFocus));let ie=h();return l.createElement(be,null,l.createElement(K,{force:!0},l.createElement(Se,null,l.createElement(w.Provider,{value:re},l.createElement(Ie,{target:f},l.createElement(K,{force:!1},l.createElement(ne,{slot:N},l.createElement(ee,null,l.createElement(Oe,{initialFocus:d,initialFocusFallback:f,containers:M,features:E},l.createElement(Fe,{value:m},ie({ourProps:le,theirProps:S,slot:N,defaultTag:He,features:Ne,visible:g===0,name:\"Dialog\"})))))))))))}),He=\"div\",Ne=X.RenderStrategy|X.Static;function We(e,t){let{transition:o=!1,open:a,...n}=e,i=J(),s=e.hasOwnProperty(\"open\")||i!==null,d=e.hasOwnProperty(\"onClose\");if(!s&&!d)throw new Error(\"You have to provide an `open` and an `onClose` prop to the `Dialog` component.\");if(!s)throw new Error(\"You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.\");if(!d)throw new Error(\"You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.\");if(!i&&typeof e.open!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!=\"function\")throw new Error(`You provided an \\`onClose\\` prop to the \\`Dialog\\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!n.static?l.createElement(Y,null,l.createElement(ke,{show:a,transition:o,unmount:n.unmount},l.createElement(z,{ref:t,...n}))):l.createElement(Y,null,l.createElement(z,{ref:t,open:a,...n}))}let $e=\"div\";function je(e,t){let o=k(),{id:a=`headlessui-dialog-panel-${o}`,transition:n=!1,...i}=e,[{dialogState:s,unmount:d},p]=O(\"Dialog.Panel\"),T=G(t,p.panelRef),u=A(()=>({open:s===0}),[s]),y=_(I=>{I.stopPropagation()}),S={ref:T,id:a,onClick:y},F=n?q:$,c=n?{unmount:d}:{},f=h();return l.createElement(F,{...c},f({ourProps:S,theirProps:i,slot:u,defaultTag:$e,name:\"Dialog.Panel\"}))}let Ye=\"div\";function Je(e,t){let{transition:o=!1,...a}=e,[{dialogState:n,unmount:i}]=O(\"Dialog.Backdrop\"),s=A(()=>({open:n===0}),[n]),d={ref:t,\"aria-hidden\":!0},p=o?q:$,T=o?{unmount:i}:{},u=h();return l.createElement(p,{...T},u({ourProps:d,theirProps:a,slot:s,defaultTag:Ye,name:\"Dialog.Backdrop\"}))}let Ke=\"h2\";function Xe(e,t){let o=k(),{id:a=`headlessui-dialog-title-${o}`,...n}=e,[{dialogState:i,setTitleId:s}]=O(\"Dialog.Title\"),d=G(t);fe(()=>(s(a),()=>s(null)),[a,s]);let p=A(()=>({open:i===0}),[i]),T={ref:d,id:a};return h()({ourProps:T,theirProps:n,slot:p,defaultTag:Ke,name:\"Dialog.Title\"})}let Ve=C(We),qe=C(je),bt=C(Je),ze=C(Xe),vt=V,Lt=Object.assign(Ve,{Panel:qe,Title:ze,Description:V});export{Lt as Dialog,bt as DialogBackdrop,vt as DialogDescription,qe as DialogPanel,ze as DialogTitle};\n", "import{Keys as u}from'../components/keyboard.js';import{useEventListener as i}from'./use-event-listener.js';import{useIsTopLayer as f}from'./use-is-top-layer.js';function a(o,r=typeof document!=\"undefined\"?document.defaultView:null,t){let n=f(o,\"escape\");i(r,\"keydown\",e=>{n&&(e.defaultPrevented||e.key===u.Escape&&t(e))})}export{a as useEscape};\n", "import{useDocumentOverflowLockedEffect as l}from'./document-overflow/use-document-overflow.js';import{useIsTopLayer as m}from'./use-is-top-layer.js';function f(e,c,n=()=>[document.body]){let r=m(e,\"scroll-lock\");l(r,c,t=>{var o;return{containers:[...(o=t.containers)!=null?o:[],n]}})}export{f as useScrollLock};\n", "import{useEffect as l}from\"react\";import{disposables as u}from'../utils/disposables.js';import*as c from'../utils/dom.js';import{useLatestValue as d}from'./use-latest-value.js';function p(s,n,o){let i=d(t=>{let e=t.getBoundingClientRect();e.x===0&&e.y===0&&e.width===0&&e.height===0&&o()});l(()=>{if(!s)return;let t=n===null?null:c.isHTMLElement(n)?n:n.current;if(!t)return;let e=u();if(typeof ResizeObserver!=\"undefined\"){let r=new ResizeObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}if(typeof IntersectionObserver!=\"undefined\"){let r=new IntersectionObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}return()=>e.dispose()},[n,i,s])}export{p as useOnDisappear};\n", "import{useState as i}from\"react\";import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';function f(){var t;let[e]=i(()=>typeof window!=\"undefined\"&&typeof window.matchMedia==\"function\"?window.matchMedia(\"(pointer: coarse)\"):null),[o,c]=i((t=e==null?void 0:e.matches)!=null?t:!1);return s(()=>{if(!e)return;function n(r){c(r.matches)}return e.addEventListener(\"change\",n),()=>e.removeEventListener(\"change\",n)},[e]),o}export{f as useIsTouchDevice};\n"], "names": ["composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "event", "defaultPrevented", "setRef", "ref", "value", "current", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "map", "cleanup", "i", "length", "useComposedRefs", "React.useCallback", "useCallback", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createScope", "scopeContexts", "defaultContext", "React.createContext", "scope", "contexts", "React.useMemo", "useMemo", "rootComponentName", "BaseContext", "createContext", "index", "Provider", "props", "children", "context", "Context", "_a", "Object", "values", "jsx", "displayName", "consumerName", "React.useContext", "useContext", "Error", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "createScope2", "useScope", "overrideScopes", "nextScopes", "reduce", "nextScopes2", "createSlot", "ownerName", "SlotClone", "Slot2", "React.forwardRef", "forwardedRef", "slotProps", "childrenA<PERSON>y", "React.Children", "toArray", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "Children", "count", "only", "React.isValidElement", "isValidElement", "React.cloneElement", "Slot", "createSlotClone", "childrenRef", "element", "getter", "getOwnPropertyDescriptor", "get", "<PERSON><PERSON><PERSON><PERSON>", "isReactWarning", "_b", "getElementRef", "props2", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "test", "args", "result", "filter", "Boolean", "join", "mergeProps", "type", "React.Fragment", "cloneElement", "SLOTTABLE_IDENTIFIER", "Symbol", "createSlottable", "Slottable2", "Fragment2", "__radixId", "Primitive", "primitive", "Node", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "window", "for", "dispatchDiscreteCustomEvent", "target", "ReactDOM.flushSync", "dispatchEvent", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "React.useRef", "useRef", "React.useEffect", "originalBodyPointerEvents", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "DismissableLayerContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "setNode", "React.useState", "ownerDocument", "globalThis", "document", "force", "useState", "composedRefs", "node2", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "handlePointerDownOutside", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "handleAndDispatchPointerDownOutsideEvent2", "handleAndDispatchCustomEvent", "eventDetail", "discrete", "originalEvent", "pointerType", "removeEventListener", "addEventListener", "once", "timerId", "setTimeout", "clearTimeout", "onPointerDownCapture", "usePointerDownOutside", "isPointerDownOnBranch", "some", "branch", "contains", "focusOutside", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "onFocusCapture", "onBlurCapture", "useFocusOutside", "onEscapeKeyDownProp", "handleKeyDown", "key", "capture", "useEscapeKeydown", "preventDefault", "body", "style", "pointerEvents", "add", "dispatchUpdate", "delete", "handleUpdate", "jsxRuntimeExports", "div", "DismissableLayerBranch", "CustomEvent", "name", "handler", "detail", "bubbles", "cancelable", "Root", "Branch", "useLayoutEffect2", "React.useLayoutEffect", "Portal", "container", "containerProp", "portalProps", "mounted", "setMounted", "useLayoutEffect", "ReactDOM", "createPortal", "Presence", "present", "presence", "React2.useState", "stylesRef", "React2.useRef", "prevPresentRef", "prevAnimationNameRef", "initialState", "state", "send", "machine", "React.useReducer", "useReducer", "useStateMachine", "UNMOUNT", "ANIMATION_OUT", "unmountSuspended", "MOUNT", "ANIMATION_END", "unmounted", "React2.useEffect", "currentAnimationName", "getAnimationName", "styles", "wasPresent", "prevAnimationName", "display", "timeoutId", "ownerWindow", "defaultView", "handleAnimationEnd", "isCurrentAnimation", "includes", "animationName", "currentFillMode", "animationFillMode", "handleAnimationStart", "isPresent", "React2.useCallback", "getComputedStyle", "usePresence", "React2.Children", "React2.cloneElement", "useInsertionEffect", "React", "trim", "toString", "useControllableState", "prop", "defaultProp", "onChange", "caller", "uncontrolledProp", "setUncontrolledProp", "onChangeRef", "setValue", "prevValueRef", "call", "useUncontrolledState", "isControlled", "isControlledRef", "wasControlled", "to", "console", "warn", "nextValue", "value2", "isFunction", "VISUALLY_HIDDEN_STYLES", "freeze", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "VisuallyHidden", "span", "r", "e", "t", "f", "n", "isArray", "o", "clsx", "arguments", "useReactId", "useId", "deterministicId", "id", "setId", "reactId", "String", "sides", "min", "Math", "max", "round", "floor", "createCoords", "v", "x", "y", "oppositeSideMap", "left", "right", "bottom", "top", "oppositeAlignmentMap", "start", "end", "clamp", "evaluate", "param", "getSide", "placement", "split", "getAlignment", "getOppositeAxis", "axis", "getAxisLength", "getSideAxis", "getAlignmentAxis", "getOppositeAlignmentPlacement", "replace", "alignment", "getOppositePlacement", "side", "getPaddingObject", "expandPaddingObject", "rectToClientRect", "rect", "computeCoordsFromPlacement", "_ref", "rtl", "reference", "floating", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "isVertical", "commonX", "commonY", "commonAlign", "coords", "async", "detectOverflow", "options", "_await$platform$isEle", "platform", "rects", "elements", "strategy", "boundary", "rootBoundary", "elementContext", "altBoundary", "paddingObject", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "getSideOffsets", "isAnySideFullyClipped", "hasW<PERSON>ow", "getNodeName", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "documentElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "overflowX", "overflowY", "isTableElement", "isTop<PERSON><PERSON>er", "selector", "matches", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "containerType", "<PERSON><PERSON>ilter", "<PERSON><PERSON><PERSON><PERSON>", "contain", "CSS", "supports", "isLastTraversableNode", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "getParentNode", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "concat", "visualViewport", "parent", "getPrototypeOf", "getCssDimensions", "parseFloat", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "dom<PERSON>lement", "getBoundingClientRect", "Number", "isFinite", "noOffsets", "getVisualOffsets", "offsetLeft", "offsetTop", "includeScale", "isFixedStrategy", "clientRect", "scale", "visualOffsets", "isFixed", "floatingOffsetParent", "shouldAddVisualOffsets", "offsetWin", "currentWin", "currentIFrame", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "getWindowScrollBarX", "leftScroll", "getHTMLOffset", "scroll", "ignoreScrollbarX", "htmlRect", "getClientRectFromClippingAncestor", "clippingAncestor", "html", "clientWidth", "clientHeight", "visualViewportBased", "getViewportRect", "scrollWidth", "scrollHeight", "direction", "getDocumentRect", "getInnerBoundingClientRect", "hasFixedPositionAncestor", "stopNode", "getRectRelativeToOffsetParent", "isOffsetParentAnElement", "offsets", "setLeftRTLScrollbarOffset", "offsetRect", "htmlOffset", "isStaticPositioned", "getTrueOffsetParent", "polyfill", "rawOffsetParent", "svgOffsetParent", "currentNode", "getContainingBlock", "topLayer", "clippingAncestors", "cache", "cachedResult", "el", "currentContainingBlockComputedStyle", "elementIsFixed", "computedStyle", "currentNodeIsContaining", "ancestor", "set", "getClippingElementAncestors", "this", "_c", "firstClippingAncestor", "clippingRect", "accRect", "getElementRects", "data", "getOffsetParentFn", "getDimensionsFn", "getDimensions", "floatingDimensions", "getClientRects", "isRTL", "rectsAreEqual", "a", "b", "autoUpdate", "update", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "IntersectionObserver", "animationFrame", "referenceEl", "ancestors", "for<PERSON>ach", "passive", "cleanupIo", "onMove", "io", "root", "_io", "disconnect", "refresh", "skip", "threshold", "elementRectForRootMargin", "rootMargin", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "_e", "observe", "<PERSON><PERSON><PERSON>", "frameId", "reobserveFrame", "resizeObserver", "firstEntry", "unobserve", "cancelAnimationFrame", "requestAnimationFrame", "_resizeObserver", "prevRefRect", "frameLoop", "nextRefRect", "_resizeObserver2", "offset", "fn", "_middlewareData$offse", "_middlewareData$arrow", "middlewareData", "diffCoords", "mainAxisMulti", "crossAxisMulti", "rawValue", "mainAxis", "crossAxis", "convertValueToCoords", "arrow", "alignmentOffset", "shift", "checkMainAxis", "checkCrossAxis", "limiter", "detectOverflowOptions", "mainAxisCoord", "crossAxisCoord", "maxSide", "limitedCoords", "enabled", "flip", "_middlewareData$flip", "initialPlacement", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "flipAlignment", "initialSideAxis", "isBasePlacement", "oppositePlacement", "getExpandedPlacements", "hasFallbackAxisSideDirection", "push", "isStart", "lr", "rl", "tb", "bt", "getSideList", "getOppositeAxisPlacements", "placements", "overflows", "overflowsData", "mainAlignmentSide", "getAlignmentSides", "every", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "nextPlacement", "_overflowsData$", "ignoreCrossAxisOverflow", "hasInitialMainAxisOverflow", "reset", "resetPlacement", "d", "sort", "_overflowsData$filter2", "currentSideAxis", "acc", "_state$middlewareData", "_state$middlewareData2", "apply", "isYAxis", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions", "hide", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "arrowDimensions", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "shouldAddOffset", "centerOffset", "limitShift", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "computePosition", "Map", "mergedOptions", "platformWithCache", "config", "middleware", "validMiddleware", "statefulPlacement", "resetCount", "nextX", "nextY", "computePosition$1", "useEffect", "deepEqual", "keys", "hasOwnProperty", "$$typeof", "getDPR", "devicePixelRatio", "roundByDPR", "dpr", "useLatestRef", "arrow$1", "arrow$2", "deps", "shift$1", "limitShift$1", "flip$1", "size$1", "hide$1", "Arrow", "arrowProps", "svg", "viewBox", "preserveAspectRatio", "points", "POPPER_NAME", "createPopperContext", "createPopperScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "ANCHOR_NAME", "PopperA<PERSON><PERSON>", "__scope<PERSON>opper", "virtualRef", "anchorProps", "onAnchorChange", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sideOffset", "align", "alignOffset", "arrowPadding", "avoidCollisions", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "updatePositionStrategy", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "setArrow", "arrowSize", "setSize", "entry", "borderSizeEntry", "borderSize", "box", "useSize", "arrow<PERSON>idth", "arrowHeight", "desiredPlacement", "hasExplicitBoundaries", "isNotNull", "floatingStyles", "isPositioned", "externalReference", "externalFloating", "transform", "whileElementsMounted", "open", "setData", "latestMiddleware", "setLatestMiddleware", "_reference", "_setReference", "_floating", "_setFloating", "setReference", "referenceRef", "setFloating", "floatingRef", "floatingEl", "dataRef", "hasWhileElementsMounted", "whileElementsMountedRef", "platformRef", "openRef", "then", "fullData", "isMountedRef", "initialStyles", "useFloating", "anchor", "offset$1", "anchorWidth", "anchorHeight", "contentStyle", "setProperty", "floatingUIarrow", "transform<PERSON><PERSON>in", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "arrowX", "arrowY", "cannotCenterArrow", "contentZIndex", "setContentZIndex", "zIndex", "min<PERSON><PERSON><PERSON>", "_d", "_f", "visibility", "dir", "onArrowChange", "shouldHideArrow", "animation", "ARROW_NAME", "OPPOSITE_SIDE", "PopperArrow", "contentContext", "baseSide", "ArrowPrimitive.Root", "isArrowHidden", "noArrowAlign", "arrowXCenter", "arrowYCenter", "<PERSON><PERSON>", "Content", "createTooltipContext", "createTooltipScope", "usePopperScope", "PROVIDER_NAME", "DEFAULT_DELAY_DURATION", "TOOLTIP_OPEN", "TooltipProviderContextProvider", "useTooltipProviderContext", "TooltipProvider", "__scopeTooltip", "delayDuration", "skipDelayDuration", "disableHover<PERSON><PERSON><PERSON>nt", "isOpenDelayedRef", "isPointerInTransitRef", "skip<PERSON>elayTimerRef", "skip<PERSON><PERSON><PERSON>T<PERSON>r", "onOpen", "onClose", "onPointerInTransitChange", "inTransit", "TOOLTIP_NAME", "TooltipContextProvider", "useTooltipContext", "TRIGGER_NAME", "triggerProps", "providerContext", "popperScope", "onTriggerChange", "isPointerDownRef", "hasPointerMoveOpenedRef", "handlePointerUp", "PopperPrimitive.Anchor", "button", "contentId", "stateAttribute", "onPointerMove", "onTriggerEnter", "onPointerLeave", "onTriggerLeave", "onPointerDown", "onFocus", "onBlur", "onClick", "PortalProvider", "usePortalContext", "forceMount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "portalContext", "TooltipContentImpl", "TooltipContentHoverable", "pointerGraceArea", "setPointerGraceArea", "trigger", "handleRemoveGraceArea", "handleCreateGraceArea", "hoverTarget", "currentTarget", "exitPoint", "clientX", "clientY", "paddedExitPoints", "exitSide", "getPaddedExitPoints", "point", "abs", "getExitSideFromRect", "grace<PERSON><PERSON>", "newPoints", "upperHull", "p", "q", "pop", "lowerHull", "getHullPresorted", "getHull", "getPointsFromRect", "handleTriggerLeave", "handleContentLeave", "handleTrackPointerGrace", "pointerPosition", "hasEnteredTarget", "isPointerOutsideGraceArea", "polygon", "inside", "j", "ii", "jj", "xi", "yi", "xj", "yj", "isPointInPolygon", "VisuallyHiddenContentContextProvider", "useVisuallyHiddenContentContext", "isInside", "Slottable", "aria<PERSON><PERSON><PERSON>", "handleScroll", "jsxs", "PopperPrimitive.Content", "VisuallyHiddenPrimitive.Root", "role", "PopperPrimitive.Arrow", "Content2", "defineProperty", "enumerable", "configurable", "writable", "s", "constructor", "detect", "r$3", "handoffState", "currentId", "nextId", "isServer", "isClient", "handoff", "isHandoffComplete", "queueMicrotask", "Promise", "resolve", "catch", "next<PERSON><PERSON><PERSON>", "microTask", "getPropertyValue", "assign", "t2", "group", "dispose", "splice", "s$6", "c", "flatMap", "u", "captureStackTrace", "O", "None", "RenderStrategy", "Static", "A", "Unmount", "Hidden", "L", "k", "U", "ourProps", "theirProps", "slot", "defaultTag", "features", "visible", "l", "mergeRefs", "P", "F", "static", "unmount", "M", "hidden", "C", "as", "refName", "h", "i2", "className", "T", "g", "m", "w", "R", "N", "H", "createElement", "startsWith", "disabled", "Event", "nativeEvent", "K", "S", "E", "version", "Focusable", "borderWidth", "_", "n$6", "register", "D", "I", "Space", "Enter", "Escape", "Backspace", "Delete", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "Home", "End", "PageUp", "PageDown", "Tab", "super", "factory", "has", "TypeError", "WeakSet", "E$1", "c$3", "f$5", "u$3", "disposables", "subscribe", "on", "is", "iterator", "next", "done", "prototype", "WeakMap", "<PERSON><PERSON>", "Pop", "stack", "o$1", "p$1", "isTop", "inStack", "new", "withSelectorModule", "exports", "require$$0", "objectIs", "useSyncExternalStore", "useDebugValue", "useSyncExternalStoreWithSelector_production", "useSyncExternalStoreWithSelector", "getSnapshot", "getServerSnapshot", "isEqual", "instRef", "inst", "hasValue", "memoizedSelector", "nextSnapshot", "hasMemo", "memoizedSnapshot", "currentSelection", "memoizedSelection", "nextSelection", "maybeGetServerSnapshot", "selectors", "actions", "getAttribute", "inert", "setAttribute", "removeAttribute", "First", "Previous", "Next", "Last", "WrapAround", "NoScroll", "AutoFocus", "Overflow", "Success", "Underflow", "Strict", "Loose", "Keyboard", "Mouse", "focus", "preventScroll", "metaKey", "altKey", "ctrl<PERSON>ey", "dataset", "headlessuiFocusVisible", "sorted", "relativeTo", "skipElements", "compareDocumentPosition", "DOCUMENT_POSITION_FOLLOWING", "DOCUMENT_POSITION_PRECEDING", "querySelectorAll", "sign", "tabIndex", "MAX_SAFE_INTEGER", "activeElement", "select", "navigator", "maxTouchPoints", "userAgent", "getRootNode", "isConnected", "composed", "<PERSON><PERSON><PERSON>", "parentElement", "touches", "changedTouches", "d.isHTMLorSVGElement", "d.isHTMLIframeElement", "before", "doc", "innerWidth", "after", "meta", "containers", "scroll<PERSON>eh<PERSON>or", "pageYOffset", "o.isHTMLorSVGElement", "closest", "hash", "URL", "href", "querySelector", "o.hasInlineStyle", "o.isHTMLInputElement", "headless<PERSON><PERSON><PERSON><PERSON>", "scrollTo", "scrollIntoView", "block", "dispatch", "PUSH", "POP", "SCROLL_PREVENT", "SCROLL_ALLOW", "TEARDOWN", "process", "getAnimations", "Closed", "Leave", "hasFlag", "addFlag", "removeFlag", "flags", "setFlag", "toggleFlag", "prepare", "run", "inFlight", "transition", "CSSTransition", "allSettled", "finished", "closed", "enter", "leave", "Open", "Closing", "Opening", "t.useState", "t.useEffect", "readyState", "u.isHTMLorSVGElement", "unshift", "getElementById", "append<PERSON><PERSON><PERSON>", "G", "u$4", "j.is<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "childNodes", "X", "Fragment", "B", "ne", "Group", "head", "f.is<PERSON>lement", "Forwards", "Backwards", "InitialFocus", "TabLock", "FocusLock", "RestoreFocus", "Y", "z", "initialFocus", "initialFocus<PERSON>allback", "J", "ee", "te", "re", "previousActiveElement", "T.isHTMLElement", "stopPropagation", "shift<PERSON>ey", "Q", "relatedTarget", "W", "onKeyDown", "headless<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "T.is<PERSON>MLorSVGElement", "Z", "Re", "ue", "enterFrom", "enterTo", "leaveFrom", "leaveTo", "de", "Visible", "Te", "Ee", "be", "ve", "findIndex", "le", "chains", "all", "wait", "ie", "unregister", "onStart", "onStop", "fe", "xe", "show", "appear", "oe", "l$1", "se", "V", "initial", "beforeEnter", "beforeLeave", "ae", "me", "afterEnter", "afterLeave", "entered", "De", "He", "ce", "ge", "pe", "Ce", "Ne", "Pe", "Se", "he", "ye", "Fe", "ze", "Child", "Ge", "we", "SetTitleId", "Be", "titleId", "Ue", "autoFocus", "__demoMode", "descriptionId", "panelRef", "createRef", "portals", "Me", "Ae", "resolveContainers", "defaultContainers", "mainTreeNode", "allowed", "disallowed", "Le", "blur", "c.isHTMLElement", "dialogState", "close", "setTitleId", "matchMedia", "Ie", "Oe", "Ve", "ke", "qe", "Lt", "Panel", "Title", "Description"], "mappings": "oJACA,SAASA,EAAqBC,EAAsBC,GAAiBC,yBAAEA,GAA2B,GAAS,IAClG,OAAA,SAAqBC,GAE1B,GADuB,MAAAH,GAAAA,EAAAG,IACU,IAA7BD,IAAuCC,EAAMC,iBAC/C,OAAyB,MAAlBH,OAAkB,EAAAA,EAAAE,EAE5B,CACH,CCNA,SAASE,EAAOC,EAAKC,GACf,GAAe,mBAARD,EACT,OAAOA,EAAIC,GACFD,UACTA,EAAIE,QAAUD,EAElB,CACA,SAASE,KAAeC,GACtB,OAAQC,IACN,IAAIC,GAAa,EACjB,MAAMC,EAAWH,EAAKI,KAAKR,IACnB,MAAAS,EAAUV,EAAOC,EAAKK,GAIrB,OAHFC,GAAgC,mBAAXG,IACXH,GAAA,GAERG,CAAA,IAET,GAAIH,EACF,MAAO,KACL,IAAA,IAASI,EAAI,EAAGA,EAAIH,EAASI,OAAQD,IAAK,CAClC,MAAAD,EAAUF,EAASG,GACH,mBAAXD,EACAA,IAEFV,EAAAK,EAAKM,GAAI,KAE5B,EAEA,CAEA,CACA,SAASE,KAAmBR,GAC1B,OAAOS,EAAiBC,YAACX,KAAeC,GAAOA,EACjD,CChBA,SAASW,EAAmBC,EAAWC,EAAyB,IAC9D,IAAIC,EAAkB,GAqBtB,MAAMC,EAAc,KAClB,MAAMC,EAAgBF,EAAgBV,KAAKa,GAClCC,EAAAA,cAAoBD,KAEtB,OAAA,SAAkBE,GACjB,MAAAC,SAAWD,WAAQP,KAAcI,EACvC,OAAOK,EAAaC,SAClB,KAAO,CAAE,CAAC,UAAUV,KAAc,IAAKO,EAAOP,CAACA,GAAYQ,MAC3D,CAACD,EAAOC,GAEX,CAAA,EAGH,OADAL,EAAYH,UAAYA,EACjB,CAjCE,SAAeW,EAAmBN,GACnC,MAAAO,EAAcN,EAAmBO,cAACR,GAClCS,EAAQZ,EAAgBP,OACZO,EAAA,IAAIA,EAAiBG,GACjCU,MAAAA,EAAYC,UAChB,MAAMT,MAAEA,EAAAU,SAAOA,KAAaC,GAAYF,EAClCG,GAAU,OAAAC,EAAA,MAAAb,OAAA,EAAAA,EAAQP,SAAR,EAAAoB,EAAqBN,KAAUF,EACzC3B,EAAQwB,EAAAA,SAAc,IAAMS,GAASG,OAAOC,OAAOJ,IACzD,SAAuBK,IAAIJ,EAAQJ,SAAU,CAAE9B,QAAOgC,YAAU,EAU3D,OARPF,EAASS,YAAcb,EAAoB,WAQpC,CAACI,EAPC,SAAYU,EAAclB,SACjC,MAAMY,GAAU,OAAAC,EAAA,MAAAb,OAAA,EAAAA,EAAQP,SAAR,EAAAoB,EAAqBN,KAAUF,EACzCM,EAAUQ,EAAgBC,WAACR,GACjC,GAAID,EAAgB,OAAAA,EAChB,QAAmB,IAAnBb,EAAkC,OAAAA,EACtC,MAAM,IAAIuB,MAAM,KAAKH,6BAAwCd,MACnE,EAEA,EAc0BkB,EAAqB1B,KAAgBF,GAC/D,CACA,SAAS4B,KAAwBC,GACzB,MAAAC,EAAYD,EAAO,GACrB,GAAkB,IAAlBA,EAAOnC,OAAqB,OAAAoC,EAChC,MAAM5B,EAAc,KAClB,MAAM6B,EAAaF,EAAOtC,KAAKyC,IAAkB,CAC/CC,SAAUD,IACVjC,UAAWiC,EAAajC,cAEnB,OAAA,SAA2BmC,GAC1B,MAAAC,EAAaJ,EAAWK,QAAO,CAACC,GAAeJ,WAAUlC,gBAGtD,IAAKsC,KAFOJ,EAASC,GACI,UAAUnC,QAEzC,IACH,OAAOS,WAAc,KAAO,CAAE,CAAC,UAAUsB,EAAU/B,aAAcoC,KAAe,CAACA,GAClF,CAAA,EAGI,OADPjC,EAAYH,UAAY+B,EAAU/B,UAC3BG,CACT,CCtEA,SAASoC,EAAWC,GACZ,MAAAC,IAA4CD,GAC5CE,EAAQC,EAAAA,YAAiB,CAAC3B,EAAO4B,KACrC,MAAM3B,SAAEA,KAAa4B,GAAc7B,EAC7B8B,EAAgBC,EAAAA,SAAeC,QAAQ/B,GACvCgC,EAAYH,EAAcI,KAAKC,GACrC,GAAIF,EAAW,CACP,MAAAG,EAAaH,EAAUjC,MAAMC,SAC7BoC,EAAcP,EAActD,KAAK8D,GACjCA,IAAUL,EACRF,EAAcQ,SAACC,MAAMJ,GAAc,EAAUL,EAAcQ,SAACE,KAAK,MAC9DC,EAAAA,eAAqBN,GAAcA,EAAWpC,MAAMC,SAAW,KAE/DqC,IAGY/B,OAAAA,EAAAA,IAAIkB,EAAW,IAAKI,EAAW7D,IAAK4D,EAAc3B,SAAUyC,EAAoBC,eAACP,GAAcQ,EAAAA,aAAmBR,OAAY,EAAQC,GAAe,MAClL,CAC2B9B,OAAAA,EAAAA,IAAIkB,EAAW,IAAKI,EAAW7D,IAAK4D,EAAc3B,YAAU,IAG9E,OADDyB,EAAAlB,YAAc,GAAGgB,SAChBE,CACT,CACG,IAACmB,IAAkC,QAEtC,SAASC,EAAgBtB,GACvB,MAAMC,EAAYE,EAAAA,YAAiB,CAAC3B,EAAO4B,KACzC,MAAM3B,SAAEA,KAAa4B,GAAc7B,EAC/B0C,GAAAA,EAAAA,eAAqBzC,GAAW,CAC5B,MAAA8C,EAkDZ,SAAuBC,WACrB,IAAIC,EAAS,OAAA7C,EAAOC,OAAA6C,yBAAyBF,EAAQhD,MAAO,aAAQ,EAAAI,EAAA+C,IAChEC,EAAUH,GAAU,mBAAoBA,GAAUA,EAAOI,eAC7D,GAAID,EACF,OAAOJ,EAAQhF,IAIjB,GAFAiF,EAAS,OAAAK,EAAOjD,OAAA6C,yBAAyBF,EAAS,aAAQ,EAAAM,EAAAH,IAChDC,EAAAH,GAAU,mBAAoBA,GAAUA,EAAOI,eACrDD,EACF,OAAOJ,EAAQhD,MAAMhC,IAEhB,OAAAgF,EAAQhD,MAAMhC,KAAOgF,EAAQhF,GACtC,CA9D0BuF,CAActD,GAC5BuD,EAyBZ,SAAoB3B,EAAW4B,GACvB,MAAAC,EAAgB,IAAKD,GAC3B,IAAA,MAAWE,KAAYF,EAAY,CAC3B,MAAAG,EAAgB/B,EAAU8B,GAC1BE,EAAiBJ,EAAWE,GAChB,WAAWG,KAAKH,GAE5BC,GAAiBC,EACLH,EAAAC,GAAY,IAAII,KACtB,MAAAC,EAASH,KAAkBE,GAE1B,OADPH,KAAiBG,GACVC,CAAA,EAEAJ,IACTF,EAAcC,GAAYC,GAEN,UAAbD,EACTD,EAAcC,GAAY,IAAKC,KAAkBC,GAC3B,cAAbF,IACKD,EAAAC,GAAY,CAACC,EAAeC,GAAgBI,OAAOC,SAASC,KAAK,KAErF,CACE,MAAO,IAAKtC,KAAc6B,EAC5B,CAhDqBU,CAAWvC,EAAW5B,EAASD,OAIvC4C,OAHH3C,EAASoE,OAASC,aACpBd,EAAOxF,IAAM4D,EAAezD,EAAYyD,EAAcmB,GAAeA,GAEhEH,EAAkB2B,aAACtE,EAAUuD,EAC1C,CACWzB,OAAAA,EAAcQ,SAACC,MAAMvC,GAAY,EAAI8B,WAAeU,KAAK,MAAQ,IAAA,IAGnE,OADGhB,EAAAjB,YAAc,GAAGgB,cACpBC,CACT,CACA,IAAI+C,EAAuBC,OAAO,mBAElC,SAASC,EAAgBlD,GACvB,MAAMmD,EAAa,EAAG1E,oBACO2E,EAAAA,SAAW,CAAE3E,aAInC,OAFI0E,EAAAnE,YAAc,GAAGgB,cAC5BmD,EAAWE,UAAYL,EAChBG,CACT,CAEA,SAASxC,EAAYG,GACnB,OAAOI,EAAoBC,eAACL,IAAgC,mBAAfA,EAAM+B,MAAuB,cAAe/B,EAAM+B,MAAQ/B,EAAM+B,KAAKQ,YAAcL,CAClI,CCtDA,IAmBIM,EAnBQ,CACV,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,MAEoBzD,QAAO,CAAC0D,EAAW1G,KACvC,MAAMwE,EAAOtB,EAAW,aAAalD,KAC/B2G,EAAOrD,EAAAA,YAAiB,CAAC3B,EAAO4B,KACpC,MAAMqD,QAAEA,KAAYC,GAAmBlF,EACjCmF,EAAOF,EAAUpC,EAAOxE,EAIPkC,MAHD,oBAAX6E,SACTA,OAAOX,OAAOY,IAAI,cAAe,GAEZ9E,EAAAA,IAAI4E,EAAM,IAAKD,EAAgBlH,IAAK4D,GAAc,IAG3E,OADAoD,EAAKxE,YAAc,aAAanC,IACzB,IAAK0G,EAAW1G,CAACA,GAAO2G,EAAM,GACpC,CAAE,GACL,SAASM,EAA4BC,EAAQ1H,GACvC0H,GAAQC,EAAAA,WAAmB,IAAMD,EAAOE,cAAc5H,IAC5D,CCrCA,SAAS6H,EAAeC,GAChB,MAAAC,EAAcC,EAAYC,OAACH,GAIjC,OAHAI,EAAAA,WAAgB,KACdH,EAAY1H,QAAUyH,CAAA,IAEjBlG,EAAaC,SAAC,IAAM,IAAIqE,WAAqB,OAAZ,OAAY3D,EAAAwF,EAAA1H,4BAAa6F,EAAA,GAAO,GAC1E,CCEA,IAIIiC,EAHAC,EAAiB,0BACjBC,EAAuB,sCACvBC,EAAgB,gCAEhBC,EAA0B9G,EAAAA,cAAoB,CAChD+G,WAA4BC,IAC5BC,2CAA4DD,IAC5DE,aAA8BF,MAE5BG,EAAmB9E,EAAM+E,YAC3B,CAAC1G,EAAO4B,KACA,MAAA+E,4BACJA,GAA8B,EAAAC,gBAC9BA,EAAAC,qBACAA,EAAAC,eACAA,EAAAC,kBACAA,EAAAC,UACAA,KACGC,GACDjH,EACEE,EAAUQ,EAAMC,WAAWyF,IAC1B/H,EAAM6I,GAAWC,EAAAA,SAAe,MACjCC,GAAsB,MAAN/I,OAAM,EAAAA,EAAA+I,iBAA6B,MAAZC,gBAAY,EAAAA,WAAAC,YAChDC,GAASJ,EAAMK,SAAS,IAC3BC,EAAe7I,EAAgBgD,GAAe8F,GAAUR,EAAQQ,KAChErB,EAASsB,MAAMC,KAAK1H,EAAQmG,SAC3BwB,GAAgD,IAAI3H,EAAQqG,wCAAwCuB,OAAQ,GAC7GC,EAAoD1B,EAAO2B,QAAQH,GACnE/H,EAAQzB,EAAOgI,EAAO2B,QAAQ3J,IAAQ,EACtC4J,EAA8B/H,EAAQqG,uCAAuC2B,KAAO,EACpFC,EAAyBrI,GAASiI,EAClCK,EA4FV,SAA+BvB,EAAsBO,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WACzE,MAAAe,EAA2B3C,EAAemB,GAC1CyB,EAA8BzC,EAAMC,QAAO,GAC3CyC,EAAiB1C,EAAAA,QAAa,SAoC7B,OAlCPE,EAAAA,WAAgB,KACR,MAAAyC,EAAqB3K,IACzB,GAAIA,EAAM0H,SAAW+C,EAA4BpK,QAAS,CACxD,IAAIuK,EAA4C,WAC9CC,EACExC,EACAmC,EACAM,EACA,CAAEC,UAAU,GAEhB,EAEM,MAAAD,EAAc,CAAEE,cAAehL,GACX,UAAtBA,EAAMiL,aACM1B,EAAA2B,oBAAoB,QAASR,EAAerK,SAC1DqK,EAAerK,QAAUuK,EACzBrB,EAAc4B,iBAAiB,QAAST,EAAerK,QAAS,CAAE+K,MAAM,KAE9BR,GAC5C,MAEcrB,EAAA2B,oBAAoB,QAASR,EAAerK,SAE5DoK,EAA4BpK,SAAU,CAAA,EAElCgL,EAAU9D,OAAO+D,YAAW,KAClB/B,EAAA4B,iBAAiB,cAAeR,EAAiB,GAC9D,GACH,MAAO,KACLpD,OAAOgE,aAAaF,GACN9B,EAAA2B,oBAAoB,cAAeP,GACnCpB,EAAA2B,oBAAoB,QAASR,EAAerK,QAAO,CACnE,GACC,CAACkJ,EAAeiB,IACZ,CAELgB,qBAAsB,IAAMf,EAA4BpK,SAAU,EAEtE,CAvI+BoL,EAAuBzL,IAChD,MAAM0H,EAAS1H,EAAM0H,OACfgE,EAAwB,IAAIrJ,EAAQsG,UAAUgD,MAAMC,GAAWA,EAAOC,SAASnE,KAChF4C,IAA0BoB,IACR,MAAA1C,GAAAA,EAAAhJ,GACH,MAAAkJ,GAAAA,EAAAlJ,GACfA,EAAMC,kBAA8B,MAAAkJ,GAAAA,IAAA,GACxCI,GACGuC,EAgIV,SAAyB7C,EAAgBM,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WAC7D,MAAAsC,EAAqBlE,EAAeoB,GACpC+C,EAA4BhE,EAAMC,QAAO,GAaxC,OAZPC,EAAAA,WAAgB,KACR,MAAA+D,EAAejM,IACnB,GAAIA,EAAM0H,SAAWsE,EAA0B3L,QAAS,CAEzBwK,EAAAvC,EAAeyD,EADxB,CAAEf,cAAehL,GACwC,CAC3E+K,UAAU,GACX,GAIL,OADcxB,EAAA4B,iBAAiB,UAAWc,GACnC,IAAM1C,EAAc2B,oBAAoB,UAAWe,EAAW,GACpE,CAAC1C,EAAewC,IACZ,CACLG,eAAgB,IAAMF,EAA0B3L,SAAU,EAC1D8L,cAAe,IAAMH,EAA0B3L,SAAU,EAE7D,CAnJyB+L,EAAiBpM,IACpC,MAAM0H,EAAS1H,EAAM0H,OACG,IAAIrF,EAAQsG,UAAUgD,MAAMC,GAAWA,EAAOC,SAASnE,OAE9D,MAAAuB,GAAAA,EAAAjJ,GACG,MAAAkJ,GAAAA,EAAAlJ,GACfA,EAAMC,kBAA8B,MAAAkJ,GAAAA,IAAA,GACxCI,GAwCoB,OC9F3B,SAA0B8C,EAAqB9C,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WACnE,MAAAV,EAAkBlB,EAAewE,GACvCnE,EAAAA,WAAgB,KACR,MAAAoE,EAAiBtM,IACH,WAAdA,EAAMuM,KACRxD,EAAgB/I,EAAK,EAIlB,OADPuJ,EAAc4B,iBAAiB,UAAWmB,EAAe,CAAEE,SAAS,IAC7D,IAAMjD,EAAc2B,oBAAoB,UAAWoB,EAAe,CAAEE,SAAS,GAAM,GACzF,CAACzD,EAAiBQ,GACvB,CD4CIkD,EAAkBzM,IACOiC,IAAUI,EAAQmG,OAAO6B,KAAO,IAErC,MAAAtB,GAAAA,EAAA/I,IACbA,EAAMC,kBAAoBkJ,IAC7BnJ,EAAM0M,iBACIvD,KAAA,GAEXI,GACHrB,EAAAA,WAAgB,KACd,GAAK1H,EAUL,OATIsI,IAC0D,IAAxDzG,EAAQqG,uCAAuC2B,OACrBlC,EAAAoB,EAAcoD,KAAKC,MAAMC,cACvCtD,EAAAoD,KAAKC,MAAMC,cAAgB,QAEnCxK,EAAAqG,uCAAuCoE,IAAItM,IAE7C6B,EAAAmG,OAAOsE,IAAItM,GACJuM,IACR,KACDjE,GAAuF,IAAxDzG,EAAQqG,uCAAuC2B,OAClEd,EAAAoD,KAAKC,MAAMC,cAAgB1E,EAAA,CAE7C,GACC,CAAC3H,EAAM+I,EAAeT,EAA6BzG,IACtD6F,EAAAA,WAAgB,IACP,KACA1H,IACG6B,EAAAmG,OAAOwE,OAAOxM,GACd6B,EAAAqG,uCAAuCsE,OAAOxM,GACvCuM,IAAA,GAEhB,CAACvM,EAAM6B,IACV6F,EAAAA,WAAgB,KACd,MAAM+E,EAAe,IAAMvD,EAAM,IAEjC,OADSD,SAAA0B,iBAAiB/C,EAAgB6E,GACnC,IAAMxD,SAASyB,oBAAoB9C,EAAgB6E,EAAY,GACrE,IACoBC,EAAAxK,IACrBuE,EAAUkG,IACV,IACK/D,EACHjJ,IAAKyJ,EACLgD,MAAO,CACLC,cAAezC,EAA8BE,EAAyB,OAAS,YAAS,KACrFnI,EAAMyK,OAEXV,eAAgBtM,EAAqBuC,EAAM+J,eAAgBJ,EAAaI,gBACxEC,cAAevM,EAAqBuC,EAAMgK,cAAeL,EAAaK,eACtEX,qBAAsB5L,EACpBuC,EAAMqJ,qBACNjB,EAAmBiB,uBAGzB,IAGJ5C,EAAiBjG,YA1GY,mBA2G7B,IACIyK,EAAyBtJ,EAAiB+E,YAAA,CAAC1G,EAAO4B,KAC9C,MAAA1B,EAAUQ,EAAMC,WAAWyF,GAC3BpI,EAAM6H,EAAMC,OAAO,MACnB2B,EAAe7I,EAAgBgD,EAAc5D,GAU5BuC,OATvBwF,EAAAA,WAAgB,KACd,MAAM1H,EAAOL,EAAIE,QACjB,GAAIG,EAEF,OADQ6B,EAAAsG,SAASmE,IAAItM,GACd,KACG6B,EAAAsG,SAASqE,OAAOxM,EAAI,CAC9B,GAED,CAAC6B,EAAQsG,WACWjG,EAAAA,IAAIuE,EAAUkG,IAAK,IAAKhL,EAAOhC,IAAKyJ,GAAc,IAmE3E,SAASmD,IACD,MAAA/M,EAAQ,IAAIqN,YAAYjF,GAC9BqB,SAAS7B,cAAc5H,EACzB,CACA,SAAS6K,EAA6ByC,EAAMC,EAASC,GAAQzC,SAAEA,IACvD,MAAArD,EAAS8F,EAAOxC,cAActD,OAC9B1H,EAAQ,IAAIqN,YAAYC,EAAM,CAAEG,SAAS,EAAOC,YAAY,EAAMF,WACpED,KAAgBpC,iBAAiBmC,EAAMC,EAAS,CAAEnC,MAAM,IACxDL,EACFtD,EAA4BC,EAAQ1H,GAEpC0H,EAAOE,cAAc5H,EAEzB,CA9EAoN,EAAuBzK,YAhBL,yBA+FlB,IAAIgL,EAAO/E,EACPgF,EAASR,EEnNTS,GAAmB,MAAArE,gBAAA,EAAAA,WAAYC,UAAWqE,EAAAA,gBAAwB,OCOlEC,EAASjK,EAAiB+E,YAAA,CAAC1G,EAAO4B,WACpC,MAAQiK,UAAWC,KAAkBC,GAAgB/L,GAC9CgM,EAASC,GAAc9E,EAAAA,UAAe,GAC7C+E,GAAgB,IAAMD,GAAW,IAAO,IACxC,MAAMJ,EAAYC,GAAiBE,IAAW,OAAA5L,EAAA,MAAAiH,gBAAA,EAAAA,WAAYC,eAAU,EAAAlH,EAAAoK,MACpE,OAAOqB,EAAYM,EAASC,aAA6B7L,EAAAA,IAAIuE,EAAUkG,IAAK,IAAKe,EAAa/N,IAAK4D,IAAiBiK,GAAa,IAAA,IAEnID,EAAOpL,YARW,SCSf,IAAC6L,EAAYrM,IACR,MAAAsM,QAAEA,EAASrM,SAAAA,GAAaD,EACxBuM,EAOR,SAAqBD,GACnB,MAAOjO,EAAM6I,GAAWsF,aAClBC,EAAYC,EAAa5G,OAAC,MAC1B6G,EAAiBD,EAAa5G,OAACwG,GAC/BM,EAAuBF,EAAa5G,OAAC,QACrC+G,EAAeP,EAAU,UAAY,aACpCQ,EAAOC,GAvBhB,SAAyBF,EAAcG,GACrC,OAAOC,EAAgBC,YAAC,CAACJ,EAAOjP,IACZmP,EAAQF,GAAOjP,IACbiP,GACnBD,EACL,CAkBwBM,CAAgBN,EAAc,CAClDb,QAAS,CACPoB,QAAS,YACTC,cAAe,oBAEjBC,iBAAkB,CAChBC,MAAO,UACPC,cAAe,aAEjBC,UAAW,CACTF,MAAO,aAmEJ,OAhEPG,EAAAA,WAAiB,KACT,MAAAC,EAAuBC,EAAiBnB,EAAUvO,SACnC0O,EAAA1O,QAAoB,YAAV4O,EAAsBa,EAAuB,MAAA,GAC3E,CAACb,IACJZ,GAAgB,KACd,MAAM2B,EAASpB,EAAUvO,QACnB4P,EAAanB,EAAezO,QAElC,GAD0B4P,IAAexB,EAClB,CACrB,MAAMyB,EAAoBnB,EAAqB1O,QACzCyP,EAAuBC,EAAiBC,GAC9C,GAAIvB,EACFS,EAAK,cACI,GAAyB,SAAzBY,GAAuD,UAApB,MAAAE,OAAA,EAAAA,EAAQG,SACpDjB,EAAK,eACA,CAGHA,EADEe,GADgBC,IAAsBJ,EAEnC,gBAEA,UAEf,CACMhB,EAAezO,QAAUoO,CAC/B,IACK,CAACA,EAASS,IACbb,GAAgB,KACd,GAAI7N,EAAM,CACJ,IAAA4P,EACE,MAAAC,EAAc7P,EAAK+I,cAAc+G,aAAe/I,OAChDgJ,EAAsBvQ,IACpB,MACAwQ,EADuBT,EAAiBnB,EAAUvO,SACRoQ,SAASzQ,EAAM0Q,eAC3D,GAAA1Q,EAAM0H,SAAWlH,GAAQgQ,IAC3BtB,EAAK,kBACAJ,EAAezO,SAAS,CACrB,MAAAsQ,EAAkBnQ,EAAKoM,MAAMgE,kBACnCpQ,EAAKoM,MAAMgE,kBAAoB,WACnBR,EAAAC,EAAY/E,YAAW,KACI,aAAjC9K,EAAKoM,MAAMgE,oBACbpQ,EAAKoM,MAAMgE,kBAAoBD,EAC/C,GAEA,CACA,EAEYE,EAAwB7Q,IACxBA,EAAM0H,SAAWlH,IACEuO,EAAA1O,QAAU0P,EAAiBnB,EAAUvO,SACpE,EAKM,OAHKG,EAAA2K,iBAAiB,iBAAkB0F,GACnCrQ,EAAA2K,iBAAiB,kBAAmBoF,GACpC/P,EAAA2K,iBAAiB,eAAgBoF,GAC/B,KACLF,EAAY9E,aAAa6E,GACpB5P,EAAA0K,oBAAoB,iBAAkB2F,GACtCrQ,EAAA0K,oBAAoB,kBAAmBqF,GACvC/P,EAAA0K,oBAAoB,eAAgBqF,EAAkB,CAEnE,CACMrB,EAAK,gBACX,GACK,CAAC1O,EAAM0O,IACH,CACL4B,UAAW,CAAC,UAAW,oBAAoBL,SAASxB,GACpD9O,IAAK4Q,EAAAA,aAAoBlH,IACvB+E,EAAUvO,QAAUwJ,EAAQmH,iBAAiBnH,GAAS,KACtDR,EAAQQ,EAAK,GACZ,IAEP,CAjGmBoH,CAAYxC,GACvBhK,EAA4B,mBAAbrC,EAA0BA,EAAS,CAAEqM,QAASC,EAASoC,YAAeI,WAAgBtM,KAAKxC,GAC1GjC,EAAMY,EAAgB2N,EAASvO,IAmGvC,SAAuBgF,WACrB,IAAIC,EAAS,OAAA7C,EAAOC,OAAA6C,yBAAyBF,EAAQhD,MAAO,aAAQ,EAAAI,EAAA+C,IAChEC,EAAUH,GAAU,mBAAoBA,GAAUA,EAAOI,eAC7D,GAAID,EACF,OAAOJ,EAAQhF,IAIjB,GAFAiF,EAAS,OAAAK,EAAOjD,OAAA6C,yBAAyBF,EAAS,aAAQ,EAAAM,EAAAH,IAChDC,EAAAH,GAAU,mBAAoBA,GAAUA,EAAOI,eACrDD,EACF,OAAOJ,EAAQhD,MAAMhC,IAEhB,OAAAgF,EAAQhD,MAAMhC,KAAOgF,EAAQhF,GACtC,CA/G4CuF,CAAcjB,IAEjD,MADgC,mBAAbrC,GACLsM,EAASoC,UAAYK,EAAAA,aAAoB1M,EAAO,CAAEtE,QAAS,IAAA,EA8FlF,SAAS4P,EAAiBC,GACxB,aAAOA,WAAQU,gBAAiB,MAClC,CA9FAlC,EAAS7L,YAAc,WCtBvB,IAAIyO,EAAqBC,EAAM,uBAAuBC,OAAOC,aAAelD,EAC5E,SAASmD,GAAqBC,KAC5BA,EAAAC,YACAA,EAAAC,SACAA,EAAW,OACVC,OACDA,IAEA,MAAOC,EAAkBC,EAAqBC,GAmChD,UAA8BL,YAC5BA,EAAAC,SACAA,IAEA,MAAOvR,EAAO4R,GAAY1I,EAAAA,SAAeoI,GACnCO,EAAejK,EAAYC,OAAC7H,GAC5B2R,EAAc/J,EAAYC,OAAC0J,GAU1B,OATPP,GAAmB,KACjBW,EAAY1R,QAAUsR,CAAA,GACrB,CAACA,IACJzJ,EAAAA,WAAgB,WACV+J,EAAa5R,UAAYD,IAC3B,OAAAmC,EAAAwP,EAAY1R,UAAUkC,EAAA2P,KAAAH,EAAA3R,GACtB6R,EAAa5R,QAAUD,EAC7B,GACK,CAACA,EAAO6R,IACJ,CAAC7R,EAAO4R,EAAUD,EAC3B,CApD+DI,CAAqB,CAChFT,cACAC,aAEIS,OAAwB,IAATX,EACfrR,EAAQgS,EAAeX,EAAOI,EAC1B,CACR,MAAMQ,EAAkBrK,EAAAA,YAAsB,IAATyJ,GACrCvJ,EAAAA,WAAgB,KACd,MAAMoK,EAAgBD,EAAgBhS,QACtC,GAAIiS,IAAkBF,EAAc,CAC5B,MAAArI,EAAOuI,EAAgB,aAAe,eACtCC,EAAKH,EAAe,aAAe,eACjCI,QAAAC,KACN,GAAGb,sBAA2B7H,QAAWwI,8KAEnD,CACMF,EAAgBhS,QAAU+R,CAAA,GACzB,CAACA,EAAcR,GACtB,CACE,MAAMI,EAAWhR,EAAiBC,aAC/ByR,UACC,GAAIN,EAAc,CAChB,MAAMO,EA8Bd,SAAoBvS,GAClB,MAAwB,mBAAVA,CAChB,CAhCuBwS,CAAWF,GAAaA,EAAUjB,GAAQiB,EACrDC,IAAWlB,IACb,OAAAlP,EAAAwP,EAAY1R,UAAUkC,EAAA2P,KAAAH,EAAAY,GAEhC,MACQb,EAAoBY,EAC5B,GAEI,CAACN,EAAcX,EAAMK,EAAqBC,IAErC,MAAA,CAAC3R,EAAO4R,EACjB,CCzCA,IAAIa,EAAyBrQ,OAAOsQ,OAAO,CAEzCC,SAAU,WACVC,OAAQ,EACRC,MAAO,EACPC,OAAQ,EACRC,QAAS,EACTC,QAAQ,EACRC,SAAU,SACVC,KAAM,mBACNC,WAAY,SACZC,SAAU,WAGRC,EAAiB3P,EAAgB+E,YACnC,CAAC1G,EAAO4B,IACoBmJ,EAAAxK,IACxBuE,EAAUyM,KACV,IACKvR,EACHhC,IAAK4D,EACL6I,MAAO,IAAKiG,KAA2B1Q,EAAMyK,WAKrD6G,EAAe9Q,YAbJ,iBAcX,IAAIgL,EAAO8F,EC/BX,SAASE,EAAEC,GAAOC,IAAAA,EAAEC,EAAEC,EAAE,GAAG,GAAG,iBAAiBH,GAAG,iBAAiBA,EAAEG,GAAGH,OAAA,GAAU,iBAAiBA,KAAK9J,MAAMkK,QAAQJ,GAAG,CAAC,IAAIK,EAAEL,EAAE9S,OAAO,IAAI+S,EAAE,EAAEA,EAAEI,EAAEJ,IAAID,EAAEC,KAAKC,EAAEH,EAAEC,EAAEC,OAAOE,IAAIA,GAAG,KAAKA,GAAGD,EAAE,MAAUA,IAAAA,KAAKF,EAAEA,EAAEE,KAAKC,IAAIA,GAAG,KAAKA,GAAGD,GAAUC,OAAAA,CAAC,CAAQ,SAASG,IAAeN,IAAAA,IAAAA,EAAEC,EAAEC,EAAE,EAAEC,EAAE,GAAGE,EAAEE,UAAUrT,OAAOgT,EAAEG,EAAEH,KAAKF,EAAEO,UAAUL,MAAMD,EAAEF,EAAEC,MAAMG,IAAIA,GAAG,KAAKA,GAAGF,GAAUE,OAAAA,CAAC,CCG/W,IAAIK,EAAa/C,EAAM,UAAUC,OAAOC,mBAAsB,GAC1D5M,EAAQ,EACZ,SAAS0P,EAAMC,GACb,MAAOC,EAAIC,GAASlL,EAAcK,SAACyK,KAIR,OAH3B/F,GAAgB,KACQmG,GAAOC,GAAYA,GAAWC,OAAO/P,MAAQ,GAClE,CAAC2P,IACuBC,EAAK,SAASA,IAAO,EAClD,CCNA,MAAMI,EAAQ,CAAC,MAAO,QAAS,SAAU,QAGnCC,EAAMC,KAAKD,IACXE,EAAMD,KAAKC,IACXC,EAAQF,KAAKE,MACbC,EAAQH,KAAKG,MACbC,EAAqBC,IAAA,CACzBC,EAAGD,EACHE,EAAGF,IAECG,EAAkB,CACtBC,KAAM,QACNC,MAAO,OACPC,OAAQ,MACRC,IAAK,UAEDC,GAAuB,CAC3BC,MAAO,MACPC,IAAK,SAEP,SAASC,GAAMF,EAAOvV,EAAOwV,GAC3B,OAAOd,EAAIa,EAAOf,EAAIxU,EAAOwV,GAC/B,CACA,SAASE,GAAS1V,EAAO2V,GACvB,MAAwB,mBAAV3V,EAAuBA,EAAM2V,GAAS3V,CACtD,CACA,SAAS4V,GAAQC,GACf,OAAOA,EAAUC,MAAM,KAAK,EAC9B,CACA,SAASC,GAAaF,GACpB,OAAOA,EAAUC,MAAM,KAAK,EAC9B,CACA,SAASE,GAAgBC,GAChB,MAAS,MAATA,EAAe,IAAM,GAC9B,CACA,SAASC,GAAcD,GACd,MAAS,MAATA,EAAe,SAAW,OACnC,CACA,SAASE,GAAYN,GACZ,MAAA,CAAC,MAAO,UAAUxF,SAASuF,GAAQC,IAAc,IAAM,GAChE,CACA,SAASO,GAAiBP,GACjB,OAAAG,GAAgBG,GAAYN,GACrC,CAkBA,SAASQ,GAA8BR,GACrC,OAAOA,EAAUS,QAAQ,cAA2BC,GAAAjB,GAAqBiB,IAC3E,CA6BA,SAASC,GAAqBX,GAC5B,OAAOA,EAAUS,QAAQ,0BAAkCG,GAAAxB,EAAgBwB,IAC7E,CAUA,SAASC,GAAiB3D,GACxB,MAA0B,iBAAZA,EAVhB,SAA6BA,GACpB,MAAA,CACLsC,IAAK,EACLF,MAAO,EACPC,OAAQ,EACRF,KAAM,KACHnC,EAEP,CAEuC4D,CAAoB5D,GAAW,CAClEsC,IAAKtC,EACLoC,MAAOpC,EACPqC,OAAQrC,EACRmC,KAAMnC,EAEV,CACA,SAAS6D,GAAiBC,GAClB,MACJ9B,EAAAA,EACAC,EAAAA,EAAAA,MACAnC,EAAAC,OACAA,GACE+D,EACG,MAAA,CACLhE,QACAC,SACAuC,IAAKL,EACLE,KAAMH,EACNI,MAAOJ,EAAIlC,EACXuC,OAAQJ,EAAIlC,EACZiC,EAAAA,EACAC,EAAAA,EAEJ,CCpIA,SAAS8B,GAA2BC,EAAMlB,EAAWmB,GAC/C,IAAAC,UACFA,EAAAC,SACAA,GACEH,EACE,MAAAI,EAAWhB,GAAYN,GACvBuB,EAAgBhB,GAAiBP,GACjCwB,EAAcnB,GAAckB,GAC5BX,EAAOb,GAAQC,GACfyB,EAA0B,MAAbH,EACbI,EAAUN,EAAUlC,EAAIkC,EAAUpE,MAAQ,EAAIqE,EAASrE,MAAQ,EAC/D2E,EAAUP,EAAUjC,EAAIiC,EAAUnE,OAAS,EAAIoE,EAASpE,OAAS,EACjE2E,EAAcR,EAAUI,GAAe,EAAIH,EAASG,GAAe,EACrE,IAAAK,EACJ,OAAQjB,GACN,IAAK,MACMiB,EAAA,CACP3C,EAAGwC,EACHvC,EAAGiC,EAAUjC,EAAIkC,EAASpE,QAE5B,MACF,IAAK,SACM4E,EAAA,CACP3C,EAAGwC,EACHvC,EAAGiC,EAAUjC,EAAIiC,EAAUnE,QAE7B,MACF,IAAK,QACM4E,EAAA,CACP3C,EAAGkC,EAAUlC,EAAIkC,EAAUpE,MAC3BmC,EAAGwC,GAEL,MACF,IAAK,OACME,EAAA,CACP3C,EAAGkC,EAAUlC,EAAImC,EAASrE,MAC1BmC,EAAGwC,GAEL,MACF,QACWE,EAAA,CACP3C,EAAGkC,EAAUlC,EACbC,EAAGiC,EAAUjC,GAGX,OAAAe,GAAaF,IACnB,IAAK,QACH6B,EAAON,IAAkBK,GAAeT,GAAOM,GAAkB,EAAA,GACjE,MACF,IAAK,MACHI,EAAON,IAAkBK,GAAeT,GAAOM,GAAkB,EAAA,GAG9D,OAAAI,CACT,CAqGAC,eAAeC,GAAe/I,EAAOgJ,GAC/B,IAAAC,OACY,IAAZD,IACFA,EAAU,CAAE,GAER,MACJ9C,EAAAA,EACAC,EAAAA,EACA+C,SAAAA,EAAAA,MACAC,EAAAC,SACAA,EAAAC,SACAA,GACErJ,GACEsJ,SACJA,EAAW,oBAAAC,aACXA,EAAe,WAAAC,eACfA,EAAiB,WAAAC,YACjBA,GAAc,EAAAvF,QACdA,EAAU,GACR2C,GAASmC,EAAShJ,GAChB0J,EAAgB7B,GAAiB3D,GAEjChO,EAAUkT,EAASK,EADa,aAAnBD,EAAgC,YAAc,WACbA,GAC9CG,EAAqB5B,SAAuBmB,EAASU,gBAAgB,CACzE1T,QAAiH,OAAtG+S,QAAqD,MAAtBC,EAASW,eAAoB,EAASX,EAASW,UAAU3T,MAAqB+S,EAAgC/S,EAAUA,EAAQ4T,sBAAyD,MAA/BZ,EAASa,wBAA6B,EAASb,EAASa,mBAAmBX,EAASf,WACxRiB,WACAC,eACAF,cAEIrB,EAA0B,aAAnBwB,EAAgC,CAC3CtD,EAAAA,EACAC,EAAAA,EACAnC,MAAOmF,EAAMd,SAASrE,MACtBC,OAAQkF,EAAMd,SAASpE,QACrBkF,EAAMf,UACJ4B,QAAkD,MAA5Bd,EAASe,qBAA0B,EAASf,EAASe,gBAAgBb,EAASf,WACpG6B,QAA4C,MAAtBhB,EAASW,eAAoB,EAASX,EAASW,UAAUG,WAA+C,MAArBd,EAASiB,cAAmB,EAASjB,EAASiB,SAASH,KAGlK,CACF9D,EAAG,EACHC,EAAG,GAECiE,EAAoBrC,GAAiBmB,EAASmB,4DAA8DnB,EAASmB,sDAAsD,CAC/KjB,WACApB,OACAgC,eACAX,aACGrB,GACE,MAAA,CACLxB,KAAMmD,EAAmBnD,IAAM4D,EAAkB5D,IAAMkD,EAAclD,KAAO0D,EAAY/D,EACxFI,QAAS6D,EAAkB7D,OAASoD,EAAmBpD,OAASmD,EAAcnD,QAAU2D,EAAY/D,EACpGE,MAAOsD,EAAmBtD,KAAO+D,EAAkB/D,KAAOqD,EAAcrD,MAAQ6D,EAAYhE,EAC5FI,OAAQ8D,EAAkB9D,MAAQqD,EAAmBrD,MAAQoD,EAAcpD,OAAS4D,EAAYhE,EAEpG,CA8TA,SAASoE,GAAelG,EAAU4D,GACzB,MAAA,CACLxB,IAAKpC,EAASoC,IAAMwB,EAAK/D,OACzBqC,MAAOlC,EAASkC,MAAQ0B,EAAKhE,MAC7BuC,OAAQnC,EAASmC,OAASyB,EAAK/D,OAC/BoC,KAAMjC,EAASiC,KAAO2B,EAAKhE,MAE/B,CACA,SAASuG,GAAsBnG,GAC7B,OAAOsB,EAAMhJ,MAAKkL,GAAQxD,EAASwD,IAAS,GAC9C,CC7hBA,SAAS4C,KACP,MAAyB,oBAAXlS,MAChB,CACA,SAASmS,GAAYlZ,GACf,OAAAmZ,GAAOnZ,IACDA,EAAKoZ,UAAY,IAAIC,cAKxB,WACT,CACA,SAASC,GAAUtZ,GACb,IAAAuZ,EACI,OAAQ,MAARvZ,GAA8D,OAA7CuZ,EAAsBvZ,EAAK+I,oBAAyB,EAASwQ,EAAoBzJ,cAAgB/I,MAC5H,CACA,SAASyR,GAAmBxY,GACtB,IAAA2W,EACJ,OAA0F,OAAlFA,GAAQwC,GAAOnZ,GAAQA,EAAK+I,cAAgB/I,EAAKiJ,WAAalC,OAAOkC,eAAoB,EAAS0N,EAAK6C,eACjH,CACA,SAASL,GAAOvZ,GACV,QAACqZ,OAGErZ,aAAiB+G,MAAQ/G,aAAiB0Z,GAAU1Z,GAAO+G,KACpE,CACA,SAAS2R,GAAU1Y,GACb,QAACqZ,OAGErZ,aAAiB6Z,SAAW7Z,aAAiB0Z,GAAU1Z,GAAO6Z,QACvE,CACA,SAASC,GAAc9Z,GACjB,QAACqZ,OAGErZ,aAAiB+Z,aAAe/Z,aAAiB0Z,GAAU1Z,GAAO+Z,YAC3E,CACA,SAASC,GAAaha,GACpB,SAAKqZ,MAAqC,oBAAfY,cAGpBja,aAAiBia,YAAcja,aAAiB0Z,GAAU1Z,GAAOia,WAC1E,CACA,SAASC,GAAkBnV,GACnB,MAAAkO,SACJA,EAAAkH,UACAA,EAAAC,UACAA,EAAArK,QACAA,GACEa,GAAiB7L,GACrB,MAAO,kCAAkCc,KAAKoN,EAAWmH,EAAYD,KAAe,CAAC,SAAU,YAAY9J,SAASN,EACtH,CACA,SAASsK,GAAetV,GACf,MAAA,CAAC,QAAS,KAAM,MAAMsL,SAASiJ,GAAYvU,GACpD,CACA,SAASuV,GAAWvV,GAClB,MAAO,CAAC,gBAAiB,UAAUwG,MAAiBgP,IAC9C,IACK,OAAAxV,EAAQyV,QAAQD,EACxB,OAAQ/G,IACA,OAAA,CACb,IAEA,CACA,SAASiH,GAAkBC,GACzB,MAAMC,EAASC,KACTC,EAAMnC,GAAUgC,GAAgB9J,GAAiB8J,GAAgBA,EAIvE,MAAO,CAAC,YAAa,YAAa,QAAS,SAAU,eAAenP,MAAKvL,KAAS6a,EAAI7a,IAAwB,SAAf6a,EAAI7a,QAA+B6a,EAAIC,eAAsC,WAAtBD,EAAIC,gBAAwCH,KAAWE,EAAIE,gBAAwC,SAAvBF,EAAIE,iBAAuCJ,KAAWE,EAAI7U,QAAwB,SAAf6U,EAAI7U,QAA8B,CAAC,YAAa,YAAa,QAAS,SAAU,cAAe,UAAUuF,MAAKvL,IAAU6a,EAAIG,YAAc,IAAI3K,SAASrQ,MAAW,CAAC,QAAS,SAAU,SAAU,WAAWuL,UAAesP,EAAII,SAAW,IAAI5K,SAASrQ,IAC7hB,CAaA,SAAS4a,KACP,QAAmB,oBAARM,MAAwBA,IAAIC,WAChCD,IAAIC,SAAS,0BAA2B,OACjD,CACA,SAASC,GAAsBhb,GACtB,MAAA,CAAC,OAAQ,OAAQ,aAAaiQ,SAASiJ,GAAYlZ,GAC5D,CACA,SAASwQ,GAAiB7L,GACxB,OAAO2U,GAAU3U,GAAS6L,iBAAiB7L,EAC7C,CACA,SAASsW,GAActW,GACjB,OAAA2T,GAAU3T,GACL,CACLuW,WAAYvW,EAAQuW,WACpBC,UAAWxW,EAAQwW,WAGhB,CACLD,WAAYvW,EAAQyW,QACpBD,UAAWxW,EAAQ0W,QAEvB,CACA,SAASC,GAActb,GACjB,GAAsB,SAAtBkZ,GAAYlZ,GACP,OAAAA,EAEH,MAAA2F,EAEN3F,EAAKub,cAELvb,EAAKwb,YAEL5B,GAAa5Z,IAASA,EAAKyb,MAE3BjD,GAAmBxY,GACnB,OAAO4Z,GAAajU,GAAUA,EAAO8V,KAAO9V,CAC9C,CACA,SAAS+V,GAA2B1b,GAC5B,MAAAwb,EAAaF,GAActb,GAC7B,OAAAgb,GAAsBQ,GACjBxb,EAAK+I,cAAgB/I,EAAK+I,cAAcoD,KAAOnM,EAAKmM,KAEzDuN,GAAc8B,IAAe1B,GAAkB0B,GAC1CA,EAEFE,GAA2BF,EACpC,CACA,SAASG,GAAqB3b,EAAM4b,EAAMC,GACpC,IAAAC,OACS,IAATF,IACFA,EAAO,SAEe,IAApBC,IACgBA,GAAA,GAEd,MAAAE,EAAqBL,GAA2B1b,GAChDgc,EAASD,KAAuE,OAA9CD,EAAuB9b,EAAK+I,oBAAyB,EAAS+S,EAAqB3P,MACrH8P,EAAM3C,GAAUyC,GACtB,GAAIC,EAAQ,CACJ,MAAAE,EAAeC,GAAgBF,GACrC,OAAOL,EAAKQ,OAAOH,EAAKA,EAAII,gBAAkB,GAAIvC,GAAkBiC,GAAsBA,EAAqB,GAAIG,GAAgBL,EAAkBF,GAAqBO,GAAgB,GAC9L,CACS,OAAAN,EAAKQ,OAAOL,EAAoBJ,GAAqBI,EAAoB,GAAIF,GACtF,CACA,SAASM,GAAgBF,GAChB,OAAAA,EAAIK,QAAUta,OAAOua,eAAeN,EAAIK,QAAUL,EAAIC,aAAe,IAC9E,CClJA,SAASM,GAAiB7X,GAClB,MAAA8V,EAAMjK,GAAiB7L,GAG7B,IAAI8N,EAAQgK,WAAWhC,EAAIhI,QAAU,EACjCC,EAAS+J,WAAWhC,EAAI/H,SAAW,EACjC,MAAAgK,EAAYhD,GAAc/U,GAC1BgY,EAAcD,EAAY/X,EAAQgY,YAAclK,EAChDmK,EAAeF,EAAY/X,EAAQiY,aAAelK,EAClDmK,EAAiBtI,EAAM9B,KAAWkK,GAAepI,EAAM7B,KAAYkK,EAKlE,OAJHC,IACMpK,EAAAkK,EACCjK,EAAAkK,GAEJ,CACLnK,QACAC,SACAoK,EAAGD,EAEP,CAEA,SAASE,GAAcpY,GACrB,OAAQ2T,GAAU3T,GAAoCA,EAAzBA,EAAQ4T,cACvC,CAEA,SAASK,GAASjU,GACV,MAAAqY,EAAaD,GAAcpY,GAC7B,IAAC+U,GAAcsD,GACjB,OAAOvI,EAAa,GAEhB,MAAAgC,EAAOuG,EAAWC,yBAClBxK,MACJA,EAAAC,OACAA,EACAoK,EAAAA,GACEN,GAAiBQ,GACrB,IAAIrI,GAAKmI,EAAIvI,EAAMkC,EAAKhE,OAASgE,EAAKhE,OAASA,EAC3CmC,GAAKkI,EAAIvI,EAAMkC,EAAK/D,QAAU+D,EAAK/D,QAAUA,EAU1C,OANFiC,GAAMuI,OAAOC,SAASxI,KACzBA,EAAI,GAEDC,GAAMsI,OAAOC,SAASvI,KACzBA,EAAI,GAEC,CACLD,EAAAA,EACAC,EAAAA,EAEJ,CAEA,MAAMwI,KAAsC,GAC5C,SAASC,GAAiB1Y,GAClB,MAAAsX,EAAM3C,GAAU3U,GACtB,OAAK6V,MAAeyB,EAAII,eAGjB,CACL1H,EAAGsH,EAAII,eAAeiB,WACtB1I,EAAGqH,EAAII,eAAekB,WAJfH,EAMX,CAWA,SAASH,GAAsBtY,EAAS6Y,EAAcC,EAAiBhF,QAChD,IAAjB+E,IACaA,GAAA,QAEO,IAApBC,IACgBA,GAAA,GAEd,MAAAC,EAAa/Y,EAAQsY,wBACrBD,EAAaD,GAAcpY,GAC7B,IAAAgZ,EAAQlJ,EAAa,GACrB+I,IACE/E,EACEH,GAAUG,KACZkF,EAAQ/E,GAASH,IAGnBkF,EAAQ/E,GAASjU,IAGf,MAAAiZ,EA7BR,SAAgCjZ,EAASkZ,EAASC,GAIhD,YAHgB,IAAZD,IACQA,GAAA,MAEPC,GAAwBD,GAAWC,IAAyBxE,GAAU3U,KAGpEkZ,CACT,CAqBwBE,CAAuBf,EAAYS,EAAiBhF,GAAgB4E,GAAiBL,GAAcvI,EAAa,GACtI,IAAIE,GAAK+I,EAAW5I,KAAO8I,EAAcjJ,GAAKgJ,EAAMhJ,EAChDC,GAAK8I,EAAWzI,IAAM2I,EAAchJ,GAAK+I,EAAM/I,EAC/CnC,EAAQiL,EAAWjL,MAAQkL,EAAMhJ,EACjCjC,EAASgL,EAAWhL,OAASiL,EAAM/I,EACvC,GAAIoI,EAAY,CACR,MAAAf,EAAM3C,GAAU0D,GAChBgB,EAAYvF,GAAgBH,GAAUG,GAAgBa,GAAUb,GAAgBA,EACtF,IAAIwF,EAAahC,EACbiC,EAAgB/B,GAAgB8B,GAC7B,KAAAC,GAAiBzF,GAAgBuF,IAAcC,GAAY,CAC1D,MAAAE,EAAcvF,GAASsF,GACvBE,EAAaF,EAAcjB,wBAC3BxC,EAAMjK,GAAiB0N,GACvBpJ,EAAOsJ,EAAWtJ,MAAQoJ,EAAcG,WAAa5B,WAAWhC,EAAI6D,cAAgBH,EAAYxJ,EAChGM,EAAMmJ,EAAWnJ,KAAOiJ,EAAcK,UAAY9B,WAAWhC,EAAI+D,aAAeL,EAAYvJ,EAClGD,GAAKwJ,EAAYxJ,EACjBC,GAAKuJ,EAAYvJ,EACjBnC,GAAS0L,EAAYxJ,EACrBjC,GAAUyL,EAAYvJ,EACtBD,GAAKG,EACLF,GAAKK,EACLgJ,EAAa3E,GAAU4E,GACvBA,EAAgB/B,GAAgB8B,EACtC,CACA,CACE,OAAOzH,GAAiB,CACtB/D,QACAC,SACAiC,EAAAA,EACAC,EAAAA,GAEJ,CAIA,SAAS6J,GAAoB9Z,EAAS8R,GAC9B,MAAAiI,EAAazD,GAActW,GAASuW,WAC1C,OAAKzE,EAGEA,EAAK3B,KAAO4J,EAFVzB,GAAsBzE,GAAmB7T,IAAUmQ,KAAO4J,CAGrE,CAEA,SAASC,GAAcnF,EAAiBoF,EAAQC,QACrB,IAArBA,IACiBA,GAAA,GAEf,MAAAC,EAAWtF,EAAgByD,wBAK1B,MAAA,CACLtI,EALQmK,EAAShK,KAAO8J,EAAO1D,YAAc2D,EAAmB,EAElEJ,GAAoBjF,EAAiBsF,IAInClK,EAHQkK,EAAS7J,IAAM2J,EAAOzD,UAKlC,CA6GA,SAAS4D,GAAkCpa,EAASqa,EAAkBlH,GAChE,IAAArB,EACJ,GAAyB,aAArBuI,EACKvI,EA7CX,SAAyB9R,EAASmT,GAC1B,MAAAmE,EAAM3C,GAAU3U,GAChBsa,EAAOzG,GAAmB7T,GAC1B0X,EAAiBJ,EAAII,eAC3B,IAAI5J,EAAQwM,EAAKC,YACbxM,EAASuM,EAAKE,aACdxK,EAAI,EACJC,EAAI,EACR,GAAIyH,EAAgB,CAClB5J,EAAQ4J,EAAe5J,MACvBC,EAAS2J,EAAe3J,OACxB,MAAM0M,EAAsB5E,OACvB4E,GAAuBA,GAAoC,UAAbtH,KACjDnD,EAAI0H,EAAeiB,WACnB1I,EAAIyH,EAAekB,UAEzB,CACS,MAAA,CACL9K,QACAC,SACAiC,EAAAA,EACAC,EAAAA,EAEJ,CAsBWyK,CAAgB1a,EAASmT,QACpC,GAAkC,aAArBkH,EACFvI,EAlEX,SAAyB9R,GACjB,MAAAsa,EAAOzG,GAAmB7T,GAC1Bia,EAAS3D,GAActW,GACvBwH,EAAOxH,EAAQoE,cAAcoD,KAC7BsG,EAAQ6B,EAAI2K,EAAKK,YAAaL,EAAKC,YAAa/S,EAAKmT,YAAanT,EAAK+S,aACvExM,EAAS4B,EAAI2K,EAAKM,aAAcN,EAAKE,aAAchT,EAAKoT,aAAcpT,EAAKgT,cACjF,IAAIxK,GAAKiK,EAAO1D,WAAauD,GAAoB9Z,GAC3CiQ,MAAAA,GAAKgK,EAAOzD,UAIX,MAHkC,QAArC3K,GAAiBrE,GAAMqT,YACzB7K,GAAKL,EAAI2K,EAAKC,YAAa/S,EAAK+S,aAAezM,GAE1C,CACLA,QACAC,SACAiC,EAAAA,EACAC,EAAAA,EAEJ,CAiDW6K,CAAgBjH,GAAmB7T,SAC9C,GAAa2T,GAAU0G,GACZvI,EAvBX,SAAoC9R,EAASmT,GAC3C,MAAM4F,EAAaT,GAAsBtY,GAAS,EAAmB,UAAbmT,GAClD7C,EAAMyI,EAAWzI,IAAMtQ,EAAQ4Z,UAC/BzJ,EAAO4I,EAAW5I,KAAOnQ,EAAQ0Z,WACjCV,EAAQjE,GAAc/U,GAAWiU,GAASjU,GAAW8P,EAAa,GAKjE,MAAA,CACLhC,MALY9N,EAAQua,YAAcvB,EAAMhJ,EAMxCjC,OALa/N,EAAQwa,aAAexB,EAAM/I,EAM1CD,EALQG,EAAO6I,EAAMhJ,EAMrBC,EALQK,EAAM0I,EAAM/I,EAOxB,CAQW8K,CAA2BV,EAAkBlH,OAC/C,CACC,MAAA8F,EAAgBP,GAAiB1Y,GAChC8R,EAAA,CACL9B,EAAGqK,EAAiBrK,EAAIiJ,EAAcjJ,EACtCC,EAAGoK,EAAiBpK,EAAIgJ,EAAchJ,EACtCnC,MAAOuM,EAAiBvM,MACxBC,OAAQsM,EAAiBtM,OAE/B,CACE,OAAO8D,GAAiBC,EAC1B,CACA,SAASkJ,GAAyBhb,EAASib,GACnC,MAAApE,EAAaF,GAAc3W,GAC7B,QAAA6W,IAAeoE,IAAatH,GAAUkD,IAAeR,GAAsBQ,MAG9B,UAA1ChL,GAAiBgL,GAAYjJ,UAAwBoN,GAAyBnE,EAAYoE,GACnG,CA2EA,SAASC,GAA8Blb,EAAS8T,EAAcX,GACtD,MAAAgI,EAA0BpG,GAAcjB,GACxCe,EAAkBhB,GAAmBC,GACrCoF,EAAuB,UAAb/F,EACVrB,EAAOwG,GAAsBtY,GAAS,EAAMkZ,EAASpF,GAC3D,IAAImG,EAAS,CACX1D,WAAY,EACZC,UAAW,GAEP,MAAA4E,EAAUtL,EAAa,GAI7B,SAASuL,IACCD,EAAApL,EAAI8J,GAAoBjF,EACpC,CACE,GAAIsG,IAA4BA,IAA4BjC,EAI1D,IAHkC,SAA9B3E,GAAYT,IAA4BqB,GAAkBN,MAC5DoF,EAAS3D,GAAcxC,IAErBqH,EAAyB,CAC3B,MAAMG,EAAahD,GAAsBxE,GAAc,EAAMoF,EAASpF,GAC9DsH,EAAApL,EAAIsL,EAAWtL,EAAI8D,EAAa4F,WAChC0B,EAAAnL,EAAIqL,EAAWrL,EAAI6D,EAAa8F,SACzC,MAAU/E,GACkBwG,IAG3BnC,IAAYiC,GAA2BtG,GACdwG,IAEvB,MAAAE,GAAa1G,GAAoBsG,GAA4BjC,EAAmDpJ,EAAa,GAAtDkK,GAAcnF,EAAiBoF,GAGrG,MAAA,CACLjK,EAHQ8B,EAAK3B,KAAO8J,EAAO1D,WAAa6E,EAAQpL,EAAIuL,EAAWvL,EAI/DC,EAHQ6B,EAAKxB,IAAM2J,EAAOzD,UAAY4E,EAAQnL,EAAIsL,EAAWtL,EAI7DnC,MAAOgE,EAAKhE,MACZC,OAAQ+D,EAAK/D,OAEjB,CAEA,SAASyN,GAAmBxb,GACnB6L,MAAuC,WAAvCA,GAAiB7L,GAAS4N,QACnC,CAEA,SAAS6N,GAAoBzb,EAAS0b,GAChC,IAAC3G,GAAc/U,IAAmD,UAAvC6L,GAAiB7L,GAAS4N,SAChD,OAAA,KAET,GAAI8N,EACF,OAAOA,EAAS1b,GAElB,IAAI2b,EAAkB3b,EAAQ8T,aASvB,OAHHD,GAAmB7T,KAAa2b,IAClCA,EAAkBA,EAAgBvX,cAAcoD,MAE3CmU,CACT,CAIA,SAAS5H,GAAgB/T,EAAS0b,GAC1B,MAAApE,EAAM3C,GAAU3U,GAClB,GAAAuV,GAAWvV,GACN,OAAAsX,EAEL,IAACvC,GAAc/U,GAAU,CACvB,IAAA4b,EAAkBjF,GAAc3W,GACpC,KAAO4b,IAAoBvF,GAAsBuF,IAAkB,CACjE,GAAIjI,GAAUiI,KAAqBJ,GAAmBI,GAC7C,OAAAA,EAETA,EAAkBjF,GAAciF,EACtC,CACW,OAAAtE,CACX,CACM,IAAAxD,EAAe2H,GAAoBzb,EAAS0b,GAChD,KAAO5H,GAAgBwB,GAAexB,IAAiB0H,GAAmB1H,IACzDA,EAAA2H,GAAoB3H,EAAc4H,GAE/C,OAAA5H,GAAgBuC,GAAsBvC,IAAiB0H,GAAmB1H,KAAkB4B,GAAkB5B,GACzGwD,EAEFxD,GD5XT,SAA4B9T,GACtB,IAAA6b,EAAclF,GAAc3W,GAChC,KAAO+U,GAAc8G,KAAiBxF,GAAsBwF,IAAc,CACpE,GAAAnG,GAAkBmG,GACb,OAAAA,EACb,GAAetG,GAAWsG,GACb,OAAA,KAETA,EAAclF,GAAckF,EAChC,CACS,OAAA,IACT,CCiXyBC,CAAmB9b,IAAYsX,CACxD,CAqBA,MAAMtE,GAAW,CACfmB,sDA/TF,SAA+DnC,GACzD,IAAAkB,SACFA,EAAApB,KACAA,EAAAgC,aACAA,EAAAX,SACAA,GACEnB,EACJ,MAAMkH,EAAuB,UAAb/F,EACV0B,EAAkBhB,GAAmBC,GACrCiI,IAAW7I,GAAWqC,GAAWrC,EAASf,UAC5C,GAAA2B,IAAiBe,GAAmBkH,GAAY7C,EAC3C,OAAApH,EAET,IAAImI,EAAS,CACX1D,WAAY,EACZC,UAAW,GAETwC,EAAQlJ,EAAa,GACnB,MAAAsL,EAAUtL,EAAa,GACvBqL,EAA0BpG,GAAcjB,GAC9C,IAAIqH,IAA4BA,IAA4BjC,MACxB,SAA9B3E,GAAYT,IAA4BqB,GAAkBN,MAC5DoF,EAAS3D,GAAcxC,IAErBiB,GAAcjB,IAAe,CACzB,MAAAwH,EAAahD,GAAsBxE,GACzCkF,EAAQ/E,GAASH,GACTsH,EAAApL,EAAIsL,EAAWtL,EAAI8D,EAAa4F,WAChC0B,EAAAnL,EAAIqL,EAAWrL,EAAI6D,EAAa8F,SAC9C,CAEE,MAAM2B,GAAa1G,GAAoBsG,GAA4BjC,EAAyDpJ,EAAa,GAA5DkK,GAAcnF,EAAiBoF,GAAQ,GAC7G,MAAA,CACLnM,MAAOgE,EAAKhE,MAAQkL,EAAMhJ,EAC1BjC,OAAQ+D,EAAK/D,OAASiL,EAAM/I,EAC5BD,EAAG8B,EAAK9B,EAAIgJ,EAAMhJ,EAAIiK,EAAO1D,WAAayC,EAAMhJ,EAAIoL,EAAQpL,EAAIuL,EAAWvL,EAC3EC,EAAG6B,EAAK7B,EAAI+I,EAAM/I,EAAIgK,EAAOzD,UAAYwC,EAAM/I,EAAImL,EAAQnL,EAAIsL,EAAWtL,EAE9E,EA0RE4D,sBACAH,gBAvJF,SAAyB1B,GACnB,IAAAhS,QACFA,EAAAoT,SACAA,EAAAC,aACAA,EAAAF,SACAA,GACEnB,EACJ,MACMgK,EAAoB,IADoB,sBAAb5I,EAAmCmC,GAAWvV,GAAW,GAxC5F,SAAqCA,EAASic,GACtC,MAAAC,EAAeD,EAAM9b,IAAIH,GAC/B,GAAIkc,EACK,OAAAA,EAET,IAAIlb,EAASgW,GAAqBhX,EAAS,IAAI,GAAOiB,QAAOkb,GAAMxI,GAAUwI,IAA2B,SAApB5H,GAAY4H,KAC5FC,EAAsC,KAC1C,MAAMC,EAAwD,UAAvCxQ,GAAiB7L,GAAS4N,SACjD,IAAIiO,EAAcQ,EAAiB1F,GAAc3W,GAAWA,EAG5D,KAAO2T,GAAUkI,KAAiBxF,GAAsBwF,IAAc,CAC9D,MAAAS,EAAgBzQ,GAAiBgQ,GACjCU,EAA0B7G,GAAkBmG,GAC7CU,GAAsD,UAA3BD,EAAc1O,WACNwO,EAAA,OAEVC,GAAkBE,IAA4BH,GAAuCG,GAAsD,WAA3BD,EAAc1O,UAA2BwO,GAAuC,CAAC,WAAY,SAAS9Q,SAAS8Q,EAAoCxO,WAAauH,GAAkB0G,KAAiBU,GAA2BvB,GAAyBhb,EAAS6b,IAG5Y7a,EAASA,EAAOC,QAAmBub,GAAAA,IAAaX,IAGVO,EAAAE,EAExCT,EAAclF,GAAckF,EAChC,CAES,OADDI,EAAAQ,IAAIzc,EAASgB,GACZA,CACT,CAWiG0b,CAA4B1c,EAAS2c,KAAKC,IAAM,GAAGnF,OAAOrE,GACjGC,GAClDwJ,EAAwBb,EAAkB,GAC1Cc,EAAed,EAAkB3d,QAAO,CAAC0e,EAAS1C,KACtD,MAAMvI,EAAOsI,GAAkCpa,EAASqa,EAAkBlH,GAKnE,OAJP4J,EAAQzM,IAAMX,EAAImC,EAAKxB,IAAKyM,EAAQzM,KACpCyM,EAAQ3M,MAAQX,EAAIqC,EAAK1B,MAAO2M,EAAQ3M,OACxC2M,EAAQ1M,OAASZ,EAAIqC,EAAKzB,OAAQ0M,EAAQ1M,QAC1C0M,EAAQ5M,KAAOR,EAAImC,EAAK3B,KAAM4M,EAAQ5M,MAC/B4M,CAAA,GACN3C,GAAkCpa,EAAS6c,EAAuB1J,IAC9D,MAAA,CACLrF,MAAOgP,EAAa1M,MAAQ0M,EAAa3M,KACzCpC,OAAQ+O,EAAazM,OAASyM,EAAaxM,IAC3CN,EAAG8M,EAAa3M,KAChBF,EAAG6M,EAAaxM,IAEpB,EAgIEyD,mBACAiJ,gBAxBsBpK,eAAgBqK,GAChC,MAAAC,EAAoBP,KAAK5I,iBAAmBA,GAC5CoJ,EAAkBR,KAAKS,cACvBC,QAA2BF,EAAgBF,EAAK9K,UAC/C,MAAA,CACLD,UAAWgJ,GAA8B+B,EAAK/K,gBAAiBgL,EAAkBD,EAAK9K,UAAW8K,EAAK9J,UACtGhB,SAAU,CACRnC,EAAG,EACHC,EAAG,EACHnC,MAAOuP,EAAmBvP,MAC1BC,OAAQsP,EAAmBtP,QAGjC,EAYEuP,eA5RF,SAAwBtd,GACtB,OAAO2E,MAAMC,KAAK5E,EAAQsd,iBAC5B,EA2REF,cAjIF,SAAuBpd,GACf,MAAA8N,MACJA,EAAAC,OACAA,GACE8J,GAAiB7X,GACd,MAAA,CACL8N,QACAC,SAEJ,EAyHEkG,YACAN,aACA4J,MAdF,SAAevd,GACN6L,MAAwC,QAAxCA,GAAiB7L,GAAS6a,SACnC,GAeA,SAAS2C,GAAcC,EAAGC,GACxB,OAAOD,EAAEzN,IAAM0N,EAAE1N,GAAKyN,EAAExN,IAAMyN,EAAEzN,GAAKwN,EAAE3P,QAAU4P,EAAE5P,OAAS2P,EAAE1P,SAAW2P,EAAE3P,MAC7E,CAkGA,SAAS4P,GAAWzL,EAAWC,EAAUyL,EAAQ9K,QAC/B,IAAZA,IACFA,EAAU,CAAE,GAER,MAAA+K,eACJA,GAAiB,EAAAC,eACjBA,GAAiB,EAAAC,cACjBA,EAA0C,mBAAnBC,eAAmBC,YAC1CA,EAA8C,mBAAzBC,qBAAyBC,eAC9CA,GAAiB,GACfrL,EACEsL,EAAchG,GAAclG,GAC5BmM,EAAYR,GAAkBC,EAAiB,IAAKM,EAAcpH,GAAqBoH,GAAe,MAAQpH,GAAqB7E,IAAa,GACtJkM,EAAUC,SAAoB9B,IACVqB,GAAArB,EAASxW,iBAAiB,SAAU4X,EAAQ,CAC5DW,SAAS,IAEOT,GAAAtB,EAASxW,iBAAiB,SAAU4X,EAAM,IAE9D,MAAMY,EAAYJ,GAAeH,EAlHnC,SAAqBje,EAASye,GAC5B,IACIxT,EADAyT,EAAK,KAEH,MAAAC,EAAO9K,GAAmB7T,GAChC,SAASvE,IACH,IAAAmjB,EACJxY,aAAa6E,GACC,OAAb2T,EAAMF,IAAeE,EAAIC,aACrBH,EAAA,IACT,CA2ES,OA1EE,SAAAI,EAAQC,EAAMC,QACR,IAATD,IACKA,GAAA,QAES,IAAdC,IACUA,EAAA,GAELvjB,IACH,MAAAwjB,EAA2Bjf,EAAQsY,yBACnCnI,KACJA,EAAAG,IACAA,EAAAxC,MACAA,EAAAC,OACAA,GACEkR,EAIA,GAHCF,GACKN,KAEL3Q,IAAUC,EACb,OAEI,MAKA+E,EAAU,CACdoM,YANerP,EAAMS,GAIQ,OAHZT,EAAM8O,EAAKpE,aAAepK,EAAOrC,IAGC,OAFjC+B,EAAM8O,EAAKnE,cAAgBlK,EAAMvC,IAEuB,OAD1D8B,EAAMM,GACyE,KAG/F6O,UAAWrP,EAAI,EAAGF,EAAI,EAAGuP,KAAe,GAE1C,IAAIG,GAAgB,EACpB,SAASC,EAAcC,GACf,MAAAC,EAAQD,EAAQ,GAAGE,kBACzB,GAAID,IAAUN,EAAW,CACvB,IAAKG,EACH,OAAOL,IAEJQ,EAOHR,GAAQ,EAAOQ,GAJfrU,EAAY9E,YAAW,KACrB2Y,GAAQ,EAAO,KAAI,GAClB,IAIb,CACoB,IAAVQ,GAAgB9B,GAAcyB,EAA0Bjf,EAAQsY,0BAQzDwG,IAEKK,GAAA,CACtB,CAIQ,IACGT,EAAA,IAAIR,qBAAqBkB,EAAe,IACxCtM,EAEH6L,KAAMA,EAAKva,eAEd,OAAQob,GACFd,EAAA,IAAIR,qBAAqBkB,EAAetM,EACnD,CACI4L,EAAGe,QAAQzf,EACf,CACE8e,EAAQ,GACDrjB,CACT,CA6BiDikB,CAAYtB,EAAaR,GAAU,KAClF,IAsBI+B,EAtBAC,GAAiB,EACjBC,EAAiB,KACjB9B,IACe8B,EAAA,IAAI7B,gBAAuBhM,IACtC,IAAC8N,GAAc9N,EACf8N,GAAcA,EAAWvd,SAAW6b,GAAeyB,IAGrDA,EAAeE,UAAU5N,GACzB6N,qBAAqBJ,GACrBA,EAAiBK,uBAAsB,KACjC,IAAAC,EACkC,OAArCA,EAAkBL,IAA2BK,EAAgBT,QAAQtN,EAAQ,KAG1EyL,GAAA,IAENQ,IAAgBD,GAClB0B,EAAeJ,QAAQrB,GAEzByB,EAAeJ,QAAQtN,IAGzB,IAAIgO,EAAchC,EAAiB7F,GAAsBpG,GAAa,KAatE,OAZIiM,GAGJ,SAASiC,IACD,MAAAC,EAAc/H,GAAsBpG,GACtCiO,IAAgB3C,GAAc2C,EAAaE,IACrCzC,IAEIuC,EAAAE,EACdV,EAAUM,sBAAsBG,EACpC,CATeA,GAULxC,IACD,KACD,IAAA0C,EACJjC,EAAUC,SAAoB9B,IACVqB,GAAArB,EAASzW,oBAAoB,SAAU6X,GACvCE,GAAAtB,EAASzW,oBAAoB,SAAU6X,EAAM,IAEpD,MAAbY,GAAqBA,IACkB,OAAtC8B,EAAmBT,IAA2BS,EAAiBzB,aAC/CgB,EAAA,KACb1B,GACF6B,qBAAqBL,EAC3B,CAEA,CAmBA,MAAMY,GFyGS,SAAUzN,GAIhB,YAHS,IAAZA,IACQA,EAAA,GAEL,CACL3K,KAAM,SACN2K,UACA,QAAM0N,CAAG1W,GACP,IAAI2W,EAAuBC,EACrB,MACJ1Q,EAAAA,EACAC,EAAAA,EAAAA,UACAa,EAAA6P,eACAA,GACE7W,EACE8W,QA9DZhO,eAAoC9I,EAAOgJ,GACnC,MAAAhC,UACJA,EACAkC,SAAAA,EAAAA,SACAE,GACEpJ,EACEmI,QAA+B,MAAlBe,EAASuK,WAAgB,EAASvK,EAASuK,MAAMrK,EAASf,WACvET,EAAOb,GAAQC,GACfU,EAAYR,GAAaF,GACzByB,EAAwC,MAA3BnB,GAAYN,GACzB+P,EAAgB,CAAC,OAAQ,OAAOvV,SAASoG,IAAa,EAAA,EACtDoP,EAAiB7O,GAAOM,GAAkB,EAAA,EAC1CwO,EAAWpQ,GAASmC,EAAShJ,GAG/B,IAAAkX,SACFA,EAAAC,UACAA,EAAA5O,cACAA,GACsB,iBAAb0O,EAAwB,CACjCC,SAAUD,EACVE,UAAW,EACX5O,cAAe,MACb,CACF2O,SAAUD,EAASC,UAAY,EAC/BC,UAAWF,EAASE,WAAa,EACjC5O,cAAe0O,EAAS1O,eAK1B,OAHIb,GAAsC,iBAAlBa,IACV4O,EAAc,QAAdzP,GAA2C,EAArBa,EAAqBA,GAElDE,EAAa,CAClBvC,EAAGiR,EAAYH,EACf7Q,EAAG+Q,EAAWH,GACZ,CACF7Q,EAAGgR,EAAWH,EACd5Q,EAAGgR,EAAYH,EAEnB,CAwB+BI,CAAqBpX,EAAOgJ,GAIrD,OAAIhC,KAAkE,OAAlD2P,EAAwBE,EAAeJ,aAAkB,EAASE,EAAsB3P,YAAgE,OAAjD4P,EAAwBC,EAAeQ,QAAkBT,EAAsBU,gBACjM,CAAE,EAEJ,CACLpR,EAAGA,EAAI4Q,EAAW5Q,EAClBC,EAAGA,EAAI2Q,EAAW3Q,EAClBgN,KAAM,IACD2D,EACH9P,aAGV,EAEA,EE1HMuQ,GFiIQ,SAAUvO,GAIf,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,QACN2K,UACA,QAAM0N,CAAG1W,GACD,MACJkG,EAAAA,EACAC,EAAAA,EAAAA,UACAa,GACEhH,GAEFkX,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,EAAAC,QAC5BA,EAAU,CACRhB,GAAYxO,IACN,IACFhC,EAAAA,EACAC,EAAAA,GACE+B,EACG,MAAA,CACLhC,EAAAA,EACAC,EAAAA,EACD,MAGFwR,GACD9Q,GAASmC,EAAShJ,GAChB6I,EAAS,CACb3C,EAAAA,EACAC,EAAAA,GAEI/B,QAAiB2E,GAAe/I,EAAO2X,GACvCR,EAAY7P,GAAYP,GAAQC,IAChCkQ,EAAW/P,GAAgBgQ,GAC7B,IAAAS,EAAgB/O,EAAOqO,GACvBW,EAAiBhP,EAAOsO,GAC5B,GAAIK,EAAe,CACX,MACAM,EAAuB,MAAbZ,EAAmB,SAAW,QAG9BU,EAAAhR,GAFJgR,EAAgBxT,EAFC,MAAb8S,EAAmB,MAAQ,QAIhBU,EADfA,EAAgBxT,EAAS0T,GAE7C,CACM,GAAIL,EAAgB,CACZ,MACAK,EAAwB,MAAdX,EAAoB,SAAW,QAG9BU,EAAAjR,GAFLiR,EAAiBzT,EAFC,MAAd+S,EAAoB,MAAQ,QAIhBU,EADhBA,EAAiBzT,EAAS0T,GAE9C,CACY,MAAAC,EAAgBL,EAAQhB,GAAG,IAC5B1W,EACHkX,CAACA,GAAWU,EACZT,CAACA,GAAYU,IAER,MAAA,IACFE,EACH5E,KAAM,CACJjN,EAAG6R,EAAc7R,EAAIA,EACrBC,EAAG4R,EAAc5R,EAAIA,EACrB6R,QAAS,CACPd,CAACA,GAAWM,EACZL,CAACA,GAAYM,IAIzB,EAEA,EEhMMQ,GFrSO,SAAUjP,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,OACN2K,UACA,QAAM0N,CAAG1W,GACP,IAAI4W,EAAuBsB,EACrB,MAAAlR,UACJA,EAAA6P,eACAA,EAAA1N,MACAA,EAAAgP,iBACAA,EACAjP,SAAAA,EAAAA,SACAE,GACEpJ,GAEFkX,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,EAC5BW,mBAAoBC,EAAAC,iBACpBA,EAAmB,UAAAC,0BACnBA,EAA4B,OAAAC,cAC5BA,GAAgB,KACbb,GACD9Q,GAASmC,EAAShJ,GAMtB,GAAsD,OAAjD4W,EAAwBC,EAAeQ,QAAkBT,EAAsBU,gBAClF,MAAO,CAAE,EAEL,MAAA1P,EAAOb,GAAQC,GACfyR,EAAkBnR,GAAY6Q,GAC9BO,EAAkB3R,GAAQoR,KAAsBA,EAChDhQ,QAA+B,MAAlBe,EAASuK,WAAgB,EAASvK,EAASuK,MAAMrK,EAASf,WACvE+P,EAAqBC,IAAgCK,IAAoBF,EAAgB,CAAC7Q,GAAqBwQ,ID7X3H,SAA+BnR,GACvB,MAAA2R,EAAoBhR,GAAqBX,GAC/C,MAAO,CAACQ,GAA8BR,GAAY2R,EAAmBnR,GAA8BmR,GACrG,CC0XgJC,CAAsBT,IAC1JU,EAA6D,SAA9BN,GAChCF,GAA+BQ,GAClCT,EAAmBU,QDxW3B,SAAmC9R,EAAWwR,EAAezH,EAAW5I,GAChE,MAAAT,EAAYR,GAAaF,GAC/B,IAAImG,EAnBN,SAAqBvF,EAAMmR,EAAS5Q,GAC5B,MAAA6Q,EAAK,CAAC,OAAQ,SACdC,EAAK,CAAC,QAAS,QACfC,EAAK,CAAC,MAAO,UACbC,EAAK,CAAC,SAAU,OACtB,OAAQvR,GACN,IAAK,MACL,IAAK,SACC,OAAAO,EAAY4Q,EAAUE,EAAKD,EACxBD,EAAUC,EAAKC,EACxB,IAAK,OACL,IAAK,QACH,OAAOF,EAAUG,EAAKC,EACxB,QACE,MAAO,GAEb,CAGaC,CAAYrS,GAAQC,GAA0B,UAAd+J,EAAuB5I,GAO3D,OANHT,IACFyF,EAAOA,EAAKzb,KAAYkW,GAAAA,EAAO,IAAMF,IACjC8Q,IACFrL,EAAOA,EAAKQ,OAAOR,EAAKzb,IAAI8V,OAGzB2F,CACT,CC8VmCkM,CAA0BlB,EAAkBK,EAAeD,EAA2BpQ,IAEnH,MAAMmR,EAAa,CAACnB,KAAqBC,GACnChU,QAAiB2E,GAAe/I,EAAO2X,GACvC4B,EAAY,GACd,IAAAC,GAAiE,OAA/CtB,EAAuBrB,EAAeoB,WAAgB,EAASC,EAAqBqB,YAAc,GAIxH,GAHI/B,GACQ+B,EAAAT,KAAK1U,EAASwD,IAEtB6P,EAAgB,CAClB,MAAM/R,EDvZd,SAA2BsB,EAAWmC,EAAOhB,QAC/B,IAARA,IACIA,GAAA,GAEF,MAAAT,EAAYR,GAAaF,GACzBuB,EAAgBhB,GAAiBP,GACjCnV,EAASwV,GAAckB,GACzB,IAAAkR,EAAsC,MAAlBlR,EAAwBb,KAAeS,EAAM,MAAQ,SAAW,QAAU,OAAuB,UAAdT,EAAwB,SAAW,MAI9I,OAHIyB,EAAMf,UAAUvW,GAAUsX,EAAMd,SAASxW,KAC3C4nB,EAAoB9R,GAAqB8R,IAEpC,CAACA,EAAmB9R,GAAqB8R,GAClD,CC2YsBC,CAAkB1S,EAAWmC,EAAOhB,GACxCoR,EAAAT,KAAK1U,EAASsB,EAAM,IAAKtB,EAASsB,EAAM,IAC1D,CAOM,GANgB8T,EAAA,IAAIA,EAAe,CACjCxS,YACAuS,eAIGA,EAAUI,OAAM/R,GAAQA,GAAQ,IAAI,CACvC,IAAIgS,EAAuBC,EACrB,MAAAC,IAA+D,OAAhDF,EAAwB/C,EAAeoB,WAAgB,EAAS2B,EAAsB5mB,QAAU,GAAK,EACpH+mB,EAAgBT,EAAWQ,GACjC,GAAIC,EAAe,CACb,IAAAC,EACJ,MAAMC,EAA6C,cAAnBxC,GAAiCgB,IAAoBnR,GAAYyS,GAC3FG,GAAsE,OAAvCF,EAAkBR,EAAc,SAAc,EAASQ,EAAgBT,UAAU,IAAM,EACxH,IAACU,GAA2BC,EAEvB,MAAA,CACL/G,KAAM,CACJngB,MAAO8mB,EACPP,UAAWC,GAEbW,MAAO,CACLnT,UAAW+S,GAI3B,CAIQ,IAAIK,EAAgJ,OAA9HP,EAAwBL,EAAcriB,QAAOkjB,GAAKA,EAAEd,UAAU,IAAM,IAAGe,MAAK,CAAC3G,EAAGC,IAAMD,EAAE4F,UAAU,GAAK3F,EAAE2F,UAAU,KAAI,SAAc,EAASM,EAAsB7S,UAG1L,IAAKoT,EACH,OAAQ9B,GACN,IAAK,UACH,CACM,IAAAiC,EACJ,MAAMvT,EASmJ,OATtIuT,EAAyBf,EAAcriB,QAAOkjB,IAC/D,GAAIxB,EAA8B,CAC1B,MAAA2B,EAAkBlT,GAAY+S,EAAErT,WACtC,OAAOwT,IAAoB/B,GAGP,MAApB+B,CACpB,CACyB,OAAA,CAAA,IACN9oB,KAAI2oB,GAAK,CAACA,EAAErT,UAAWqT,EAAEd,UAAUpiB,QAAOiN,GAAYA,EAAW,IAAG7P,QAAO,CAACkmB,EAAKrW,IAAaqW,EAAMrW,GAAU,MAAKkW,MAAK,CAAC3G,EAAGC,IAAMD,EAAE,GAAKC,EAAE,KAAI,SAAc,EAAS2G,EAAuB,GAC5LvT,IACeA,EAAAA,GAEnB,KAChB,CACY,IAAK,mBACcoT,EAAAjC,EAIvB,GAAInR,IAAcoT,EACT,MAAA,CACLD,MAAO,CACLnT,UAAWoT,GAIzB,CACM,MAAO,CAAE,CACf,EAEA,EEkLMhf,GFoQO,SAAU4N,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,OACN2K,UACA,QAAM0N,CAAG1W,GACP,IAAI0a,EAAuBC,EACrB,MAAA3T,UACJA,EAAAmC,MACAA,EACAD,SAAAA,EAAAA,SACAE,GACEpJ,GACE4a,MACJA,EAAQ,UACLjD,GACD9Q,GAASmC,EAAShJ,GAChBoE,QAAiB2E,GAAe/I,EAAO2X,GACvC/P,EAAOb,GAAQC,GACfU,EAAYR,GAAaF,GACzB6T,EAAqC,MAA3BvT,GAAYN,IACtBhD,MACJA,EAAAC,OACAA,GACEkF,EAAMd,SACN,IAAAyS,EACAC,EACS,QAATnT,GAA2B,WAATA,GACPkT,EAAAlT,EACbmT,EAAYrT,WAAyC,MAAlBwB,EAASuK,WAAgB,EAASvK,EAASuK,MAAMrK,EAASf,WAAc,QAAU,OAAS,OAAS,UAE3H0S,EAAAnT,EACCkT,EAAc,QAAdpT,EAAsB,MAAQ,UAE7C,MAAMsT,EAAwB/W,EAASG,EAASoC,IAAMpC,EAASmC,OACzD0U,EAAuBjX,EAAQI,EAASiC,KAAOjC,EAASkC,MACxD4U,EAA0BvV,EAAI1B,EAASG,EAAS0W,GAAaE,GAC7DG,EAAyBxV,EAAI3B,EAAQI,EAAS2W,GAAYE,GAC1DG,GAAWpb,EAAM6W,eAAeU,MACtC,IAAI8D,EAAkBH,EAClBI,EAAiBH,EAOjB,GANwD,OAAvDT,EAAwB1a,EAAM6W,eAAeU,QAAkBmD,EAAsB1C,QAAQ9R,IAC/EoV,EAAAL,GAE0C,OAAxDN,EAAyB3a,EAAM6W,eAAeU,QAAkBoD,EAAuB3C,QAAQ7R,IAChFkV,EAAAL,GAEhBI,IAAY1T,EAAW,CACzB,MAAM6T,EAAO1V,EAAIzB,EAASiC,KAAM,GAC1BmV,EAAO3V,EAAIzB,EAASkC,MAAO,GAC3BmV,EAAO5V,EAAIzB,EAASoC,IAAK,GACzBkV,EAAO7V,EAAIzB,EAASmC,OAAQ,GAC9BsU,EACFS,EAAiBtX,EAAQ,GAAc,IAATuX,GAAuB,IAATC,EAAaD,EAAOC,EAAO3V,EAAIzB,EAASiC,KAAMjC,EAASkC,QAEnG+U,EAAkBpX,EAAS,GAAc,IAATwX,GAAuB,IAATC,EAAaD,EAAOC,EAAO7V,EAAIzB,EAASoC,IAAKpC,EAASmC,QAE9G,OACYqU,EAAM,IACP5a,EACHsb,iBACAD,oBAEF,MAAMM,QAAuBzS,EAASoK,cAAclK,EAASf,UAC7D,OAAIrE,IAAU2X,EAAe3X,OAASC,IAAW0X,EAAe1X,OACvD,CACLkW,MAAO,CACLhR,OAAO,IAIN,CAAE,CACf,EAEA,EEzUMyS,GFvKO,SAAU5S,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,OACN2K,UACA,QAAM0N,CAAG1W,GACD,MAAAmJ,MACJA,GACEnJ,GACEqJ,SACJA,EAAW,qBACRsO,GACD9Q,GAASmC,EAAShJ,GACtB,OAAQqJ,GACN,IAAK,kBACH,CACQ,MAIAiI,EAAUhH,SAJOvB,GAAe/I,EAAO,IACxC2X,EACHnO,eAAgB,cAEuBL,EAAMf,WACxC,MAAA,CACL+K,KAAM,CACJ0I,uBAAwBvK,EACxBwK,gBAAiBvR,GAAsB+G,IAGvD,CACQ,IAAK,UACH,CACQ,MAIAA,EAAUhH,SAJOvB,GAAe/I,EAAO,IACxC2X,EACHlO,aAAa,IAE0BN,EAAMd,UACxC,MAAA,CACL8K,KAAM,CACJ4I,eAAgBzK,EAChB0K,QAASzR,GAAsB+G,IAG/C,CACQ,QAEI,MAAO,CAAE,EAGrB,EAEA,EE2HM+F,GFrfoBrO,IAAA,CACxB3K,KAAM,QACN2K,UACA,QAAM0N,CAAG1W,GACD,MACJkG,EAAAA,EACAC,EAAAA,EAAAA,UACAa,EAAAmC,MACAA,EACAD,SAAAA,EAAAA,SACAE,EAAAyN,eACAA,GACE7W,GAEE9J,QACJA,EAAAgO,QACAA,EAAU,GACR2C,GAASmC,EAAShJ,IAAU,CAAE,EAClC,GAAe,MAAX9J,EACF,MAAO,CAAE,EAEL,MAAAwT,EAAgB7B,GAAiB3D,GACjC2E,EAAS,CACb3C,EAAAA,EACAC,EAAAA,GAEIiB,EAAOG,GAAiBP,GACxBnV,EAASwV,GAAcD,GACvB6U,QAAwB/S,EAASoK,cAAcpd,GAC/C2kB,EAAmB,MAATzT,EACV8U,EAAUrB,EAAU,MAAQ,OAC5BsB,EAAUtB,EAAU,SAAW,QAC/BuB,EAAavB,EAAU,eAAiB,cACxCwB,EAAUlT,EAAMf,UAAUvW,GAAUsX,EAAMf,UAAUhB,GAAQyB,EAAOzB,GAAQ+B,EAAMd,SAASxW,GAC1FyqB,EAAYzT,EAAOzB,GAAQ+B,EAAMf,UAAUhB,GAC3CmV,QAAuD,MAA5BrT,EAASe,qBAA0B,EAASf,EAASe,gBAAgB/T,IACtG,IAAIsmB,EAAaD,EAAoBA,EAAkBH,GAAc,EAGhEI,SAA6C,MAAtBtT,EAASW,eAAoB,EAASX,EAASW,UAAU0S,MACnFC,EAAapT,EAASf,SAAS+T,IAAejT,EAAMd,SAASxW,IAEzD,MAAA4qB,EAAoBJ,EAAU,EAAIC,EAAY,EAI9CI,EAAyBF,EAAa,EAAIP,EAAgBpqB,GAAU,EAAI,EACxE8qB,EAAahX,EAAI+D,EAAcwS,GAAUQ,GACzCE,EAAajX,EAAI+D,EAAcyS,GAAUO,GAIzCG,EAAQF,EACR9W,EAAM2W,EAAaP,EAAgBpqB,GAAU+qB,EAC7CE,EAASN,EAAa,EAAIP,EAAgBpqB,GAAU,EAAI4qB,EACxDhG,EAAS7P,GAAMiW,EAAOC,EAAQjX,GAM9BkX,GAAmBlG,EAAeQ,OAAoC,MAA3BnQ,GAAaF,IAAsB8V,IAAWrG,GAAUtN,EAAMf,UAAUvW,GAAU,GAAKirB,EAASD,EAAQF,EAAaC,GAAcX,EAAgBpqB,GAAU,EAAI,EAC5MylB,EAAkByF,EAAkBD,EAASD,EAAQC,EAASD,EAAQC,EAASjX,EAAM,EACpF,MAAA,CACLuB,CAACA,GAAOyB,EAAOzB,GAAQkQ,EACvBnE,KAAM,CACJ/L,CAACA,GAAOqP,EACRuG,aAAcF,EAASrG,EAASa,KAC5ByF,GAAmB,CACrBzF,oBAGJ6C,MAAO4C,EAEb,IEubME,GFkKa,SAAUjU,GAIpB,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACLA,UACA,EAAA0N,CAAG1W,GACK,MACJkG,EAAAA,EACAC,EAAAA,EAAAA,UACAa,EAAAmC,MACAA,EAAA0N,eACAA,GACE7W,GAEFyW,OAAAA,EAAS,EACTS,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,GAC1B5Q,GAASmC,EAAShJ,GAChB6I,EAAS,CACb3C,EAAAA,EACAC,EAAAA,GAEIgR,EAAY7P,GAAYN,GACxBkQ,EAAW/P,GAAgBgQ,GAC7B,IAAAS,EAAgB/O,EAAOqO,GACvBW,EAAiBhP,EAAOsO,GACtB,MAAA+F,EAAYrW,GAAS4P,EAAQzW,GAC7Bmd,EAAsC,iBAAdD,EAAyB,CACrDhG,SAAUgG,EACV/F,UAAW,GACT,CACFD,SAAU,EACVC,UAAW,KACR+F,GAEL,GAAI1F,EAAe,CACX,MAAA4F,EAAmB,MAAblG,EAAmB,SAAW,QACpCmG,EAAWlU,EAAMf,UAAU8O,GAAY/N,EAAMd,SAAS+U,GAAOD,EAAejG,SAC5EoG,EAAWnU,EAAMf,UAAU8O,GAAY/N,EAAMf,UAAUgV,GAAOD,EAAejG,SAC/EU,EAAgByF,EACFzF,EAAAyF,EACPzF,EAAgB0F,IACT1F,EAAA0F,EAE1B,CACM,GAAI7F,EAAgB,CAClB,IAAId,EAAuB4G,EACrB,MAAAH,EAAmB,MAAblG,EAAmB,QAAU,SACnCsG,EAAe,CAAC,MAAO,QAAQhc,SAASuF,GAAQC,IAChDqW,EAAWlU,EAAMf,UAAU+O,GAAahO,EAAMd,SAAS+U,IAAQI,IAAmE,OAAlD7G,EAAwBE,EAAeJ,aAAkB,EAASE,EAAsBQ,KAAmB,IAAMqG,EAAe,EAAIL,EAAehG,WACnOmG,EAAWnU,EAAMf,UAAU+O,GAAahO,EAAMf,UAAUgV,IAAQI,EAAe,GAAyD,OAAnDD,EAAyB1G,EAAeJ,aAAkB,EAAS8G,EAAuBpG,KAAe,IAAMqG,EAAeL,EAAehG,UAAY,GAChPU,EAAiBwF,EACFxF,EAAAwF,EACRxF,EAAiByF,IACTzF,EAAAyF,EAE3B,CACa,MAAA,CACLpG,CAACA,GAAWU,EACZT,CAACA,GAAYU,EAErB,EAEA,EE5NM4F,GAAkB,CAACrV,EAAWC,EAAUW,KAItC,MAAAmJ,MAAYuL,IACZC,EAAgB,CACpBzU,eACGF,GAEC4U,EAAoB,IACrBD,EAAczU,SACjB4J,GAAIX,GAEC,MF9qBerJ,OAAOV,EAAWC,EAAUwV,KAC5C,MAAA7W,UACJA,EAAY,SAAAqC,SACZA,EAAW,WAAAyU,WACXA,EAAa,GACb5U,SAAAA,GACE2U,EACEE,EAAkBD,EAAW3mB,OAAOC,SACpC+Q,QAA+B,MAAlBe,EAASuK,WAAgB,EAASvK,EAASuK,MAAMpL,IAChE,IAAAc,QAAcD,EAASgK,gBAAgB,CACzC9K,YACAC,WACAgB,cAGAnD,EAAAA,EACAC,EAAAA,GACE8B,GAA2BkB,EAAOnC,EAAWmB,GAC7C6V,EAAoBhX,EACpB6P,EAAiB,CAAE,EACnBoH,EAAa,EACjB,IAAA,IAASrsB,EAAI,EAAGA,EAAImsB,EAAgBlsB,OAAQD,IAAK,CACzC,MAAAyM,KACJA,EAAAqY,GACAA,GACEqH,EAAgBnsB,IAElBsU,EAAGgY,EACH/X,EAAGgY,EAAAhL,KACHA,EAAAgH,MACAA,SACQzD,EAAG,CACXxQ,EAAAA,EACAC,EAAAA,EACAgS,iBAAkBnR,EAClBA,UAAWgX,EACX3U,WACAwN,iBACA1N,QACAD,SAAAA,EACAE,SAAU,CACRhB,YACAC,cAGJnC,EAAa,MAATgY,EAAgBA,EAAQhY,EAC5BC,EAAa,MAATgY,EAAgBA,EAAQhY,EACX0Q,EAAA,IACZA,EACHxY,CAACA,GAAO,IACHwY,EAAexY,MACf8U,IAGHgH,GAAS8D,GAAc,KACzBA,IACqB,iBAAV9D,IACLA,EAAMnT,YACRgX,EAAoB7D,EAAMnT,WAExBmT,EAAMhR,QACRA,GAAwB,IAAhBgR,EAAMhR,YAAuBD,EAASgK,gBAAgB,CAC5D9K,YACAC,WACAgB,aACG8Q,EAAMhR,SAGXjD,EAAAA,EACAC,EAAAA,GACE8B,GAA2BkB,EAAO6U,EAAmB7V,KAE3DvW,GAAI,EAEV,CACS,MAAA,CACLsU,EAAAA,EACAC,EAAAA,EACAa,UAAWgX,EACX3U,WACAwN,iBACD,EE6lBMuH,CAAkBhW,EAAWC,EAAU,IACzCsV,EACHzU,SAAU0U,GACX,EC7uBH,IAAI5qB,GAA4B,oBAAbwH,SAA2B4E,EAAeA,gBAAGif,EAASA,UAIzE,SAASC,GAAU3K,EAAGC,GACpB,GAAID,IAAMC,EACD,OAAA,EAEL,UAAOD,UAAaC,EACf,OAAA,EAEL,GAAa,mBAAND,GAAoBA,EAAErR,aAAesR,EAAEtR,WACzC,OAAA,EAEL,IAAAzQ,EACAD,EACA2sB,EACJ,GAAI5K,GAAKC,GAAkB,iBAAND,EAAgB,CAC/B,GAAA9Y,MAAMkK,QAAQ4O,GAAI,CAEhB,GADJ9hB,EAAS8hB,EAAE9hB,OACPA,IAAW+hB,EAAE/hB,OAAe,OAAA,EAC3BD,IAAAA,EAAIC,EAAgB,IAARD,KACX,IAAC0sB,GAAU3K,EAAE/hB,GAAIgiB,EAAEhiB,IACd,OAAA,EAGJ,OAAA,CACb,CAGI,GAFO2sB,EAAAhrB,OAAOgrB,KAAK5K,GACnB9hB,EAAS0sB,EAAK1sB,OACVA,IAAW0B,OAAOgrB,KAAK3K,GAAG/hB,OACrB,OAAA,EAEJD,IAAAA,EAAIC,EAAgB,IAARD,KACX,IAAC,CAAE,EAAC4sB,eAAevb,KAAK2Q,EAAG2K,EAAK3sB,IAC3B,OAAA,EAGNA,IAAAA,EAAIC,EAAgB,IAARD,KAAY,CACrB,MAAA0L,EAAMihB,EAAK3sB,GACb,IAAQ,WAAR0L,IAAoBqW,EAAE8K,YAGrBH,GAAU3K,EAAErW,GAAMsW,EAAEtW,IAChB,OAAA,CAEf,CACW,OAAA,CACX,CACSqW,OAAAA,GAAMA,GAAKC,GAAMA,CAC1B,CAEA,SAAS8K,GAAOxoB,GACV,GAAkB,oBAAXoC,OACF,OAAA,EAGT,OADYpC,EAAQoE,cAAc+G,aAAe/I,QACtCqmB,kBAAoB,CACjC,CAEA,SAASC,GAAW1oB,EAAS/E,GACrB,MAAA0tB,EAAMH,GAAOxoB,GACnB,OAAO0P,KAAKE,MAAM3U,EAAQ0tB,GAAOA,CACnC,CAEA,SAASC,GAAa3tB,GACd,MAAAD,EAAM6H,EAAYC,OAAC7H,GAIlB,OAHP6B,IAAM,KACJ9B,EAAIE,QAAUD,CAAA,IAETD,CACT,CAoKA,MAAM6tB,GAAqB/V,IAIlB,CACL3K,KAAM,QACN2K,UACA,EAAA0N,CAAG1W,GACK,MAAA9J,QACJA,EAAAgO,QACAA,GACqB,mBAAZ8E,EAAyBA,EAAQhJ,GAASgJ,EACjD,OAAA9S,IAXO/E,EAWU+E,EAVhB,CAAE,EAACsoB,eAAevb,KAAK9R,EAAO,YAWV,MAAnB+E,EAAQ9E,QACH4tB,GAAQ,CACb9oB,QAASA,EAAQ9E,QACjB8S,YACCwS,GAAG1W,GAED,CAAE,EAEP9J,EACK8oB,GAAQ,CACb9oB,UACAgO,YACCwS,GAAG1W,GAED,CAAE,EA1Bb,IAAe7O,CA2BjB,IAqBMomB,GAAQ,CAACvO,EAASiW,KAAU,IAC7BC,GAAQlW,GACXA,QAAS,CAACA,EAASiW,KAMfhC,GAAa,CAACjU,EAASiW,KAAU,IAClCE,GAAanW,GAChBA,QAAS,CAACA,EAASiW,KASfhH,GAAO,CAACjP,EAASiW,KAAU,IAC5BG,GAAOpW,GACVA,QAAS,CAACA,EAASiW,KASf7jB,GAAO,CAAC4N,EAASiW,KAAU,IAC5BI,GAAOrW,GACVA,QAAS,CAACA,EAASiW,KAmBfrD,GAAO,CAAC5S,EAASiW,KAAU,IAC5BK,GAAOtW,GACVA,QAAS,CAACA,EAASiW,KAmBf5H,GAAQ,CAACrO,EAASiW,KAAU,IAC7BF,GAAQ/V,GACXA,QAAS,CAACA,EAASiW,KCxWrB,IACIM,GAAQ1qB,EAAgB+E,YAAC,CAAC1G,EAAO4B,KAC7B,MAAA3B,SAAEA,QAAU6Q,EAAQ,GAAAC,OAAIA,EAAS,KAAMub,GAAetsB,EAC5D,OAA0B+K,EAAAxK,IACxBuE,EAAUynB,IACV,IACKD,EACHtuB,IAAK4D,EACLkP,QACAC,SACAyb,QAAS,YACTC,oBAAqB,OACrBxsB,SAAUD,EAAMiF,QAAUhF,EAA2BM,EAAGA,IAAC,UAAW,CAAEmsB,OAAQ,oBAEjF,IAEHL,GAAM7rB,YAhBK,QAiBX,IAAIgL,GAAO6gB,GCIX,IAAIM,GAAc,UACbC,GAAqBC,IAAqB9tB,EAAmB4tB,KAC7DG,GAAgBC,IAAoBH,GAAoBD,IAOzDK,GAAc,eACdC,GAAetrB,EAAgB+E,YACjC,CAAC1G,EAAO4B,KACN,MAAMsrB,cAAEA,EAAAC,WAAeA,KAAeC,GAAgBptB,EAChDE,EAAU6sB,GAAiBC,GAAaE,GACxClvB,EAAM6H,EAAYC,OAAC,MACnB2B,EAAe7I,EAAgBgD,EAAc5D,GAI5C,OAHP+H,EAAAA,WAAgB,KACd7F,EAAQmtB,gBAAe,MAAAF,OAAA,EAAAA,EAAYjvB,UAAWF,EAAIE,QAAO,IAEpDivB,EAAa,KAAuB5sB,EAAAA,IAAIuE,EAAUkG,IAAK,IAAKoiB,EAAapvB,IAAKyJ,GAAc,IAGvGwlB,GAAazsB,YAAcwsB,GAC3B,IAAIM,GAAe,iBACdC,GAAuBC,IAAqBZ,GAAoBU,IACjEG,GAAgB9rB,EAAgB+E,YAClC,CAAC1G,EAAO4B,qBACA,MAAAsrB,cACJA,EAAAxY,KACAA,EAAO,SAAAgZ,WACPA,EAAa,EAAAC,MACbA,EAAQ,SAAAC,YACRA,EAAc,EAAAC,aACdA,EAAe,EAAAC,gBACfA,GAAkB,EAAAC,kBAClBA,EAAoB,GACpBC,iBAAkBC,EAAuB,EAAAC,OACzCA,EAAS,UAAAC,iBACTA,GAAmB,EAAAC,uBACnBA,EAAyB,YAAAC,SACzBA,KACGC,GACDtuB,EACEE,EAAU6sB,GAAiBO,GAAcJ,IACxCqB,EAASC,GAAcrnB,EAAAA,SAAe,MACvCM,EAAe7I,EAAgBgD,GAAevD,GAASmwB,EAAWnwB,MACjE8lB,EAAOsK,GAAYtnB,EAAAA,SAAe,MACnCunB,ECrEV,SAAiB1rB,GACf,MAAOkF,EAAMymB,GAAWxnB,EAAAA,cAAe,GA+BhCe,OA9BPgE,GAAgB,KACd,GAAIlJ,EAAS,CACX2rB,EAAQ,CAAE7d,MAAO9N,EAAQgY,YAAajK,OAAQ/N,EAAQiY,eACtD,MAAM4H,EAAiB,IAAI7B,gBAAgBqB,IACzC,IAAK1a,MAAMkK,QAAQwQ,GACjB,OAEE,IAACA,EAAQ1jB,OACX,OAEI,MAAAiwB,EAAQvM,EAAQ,GAClB,IAAAvR,EACAC,EACJ,GAAI,kBAAmB6d,EAAO,CACtB,MAAAC,EAAkBD,EAAqB,cACvCE,EAAannB,MAAMkK,QAAQgd,GAAmBA,EAAgB,GAAKA,EACzE/d,EAAQge,EAAuB,WAC/B/d,EAAS+d,EAAsB,SACzC,MACUhe,EAAQ9N,EAAQgY,YAChBjK,EAAS/N,EAAQiY,aAEX0T,EAAA,CAAE7d,QAAOC,UAAQ,IAGpB,OADP8R,EAAeJ,QAAQzf,EAAS,CAAE+rB,IAAK,eAChC,IAAMlM,EAAeE,UAAU/f,EAC5C,CACM2rB,OAAQ,EACd,GACK,CAAC3rB,IACGkF,CACT,CDoCsB8mB,CAAQ7K,GACpB8K,SAAaP,WAAW5d,QAAS,EACjCoe,SAAcR,WAAW3d,SAAU,EACnCoe,EAAmBza,GAAkB,WAAViZ,EAAqB,IAAMA,EAAQ,IAC9DK,EAAmD,iBAAzBC,EAAoCA,EAAuB,CAAE3a,IAAK,EAAGF,MAAO,EAAGC,OAAQ,EAAGF,KAAM,KAAM8a,GAChI7X,EAAWzO,MAAMkK,QAAQkc,GAAqBA,EAAoB,CAACA,GACnEqB,EAAwBhZ,EAASzX,OAAS,EAC1C8lB,EAAwB,CAC5BzT,QAASgd,EACT5X,SAAUA,EAASnS,OAAOorB,IAE1B9Y,YAAa6Y,IAEThxB,KAAEA,EAAMkxB,eAAAA,EAAAxb,UAAgBA,eAAWyb,EAAc5L,eAAAA,GFF3D,SAAqB7N,QACH,IAAZA,IACFA,EAAU,CAAE,GAER,MAAAhC,UACJA,EAAY,SAAAqC,SACZA,EAAW,WAAAyU,WACXA,EAAa,GACb5U,SAAAA,EACAE,UACEhB,UAAWsa,EACXra,SAAUsa,GACR,CAAE,EAAAC,UACNA,GAAY,EAAAC,qBACZA,EAAAC,KACAA,GACE9Z,GACGmK,EAAM4P,GAAW1oB,WAAe,CACrC6L,EAAG,EACHC,EAAG,EACHkD,WACArC,YACA6P,eAAgB,CAAE,EAClB4L,cAAc,KAETO,EAAkBC,GAAuB5oB,EAAAA,SAAeyjB,GAC1DQ,GAAU0E,EAAkBlF,IAC/BmF,EAAoBnF,GAEtB,MAAOoF,EAAYC,GAAiB9oB,EAAAA,SAAe,OAC5C+oB,EAAWC,GAAgBhpB,EAAAA,SAAe,MAC3CipB,EAAevxB,EAAiBC,aAAST,IACzCA,IAASgyB,EAAanyB,UACxBmyB,EAAanyB,QAAUG,EACvB4xB,EAAc5xB,GACpB,GACK,IACGiyB,EAAczxB,EAAiBC,aAAST,IACxCA,IAASkyB,EAAYryB,UACvBqyB,EAAYryB,QAAUG,EACtB8xB,EAAa9xB,GACnB,GACK,IACG+iB,EAAcoO,GAAqBQ,EACnCQ,EAAaf,GAAoBS,EACjCG,EAAexqB,EAAYC,OAAC,MAC5ByqB,EAAc1qB,EAAYC,OAAC,MAC3B2qB,EAAU5qB,EAAYC,OAACma,GACvByQ,EAAkD,MAAxBf,EAC1BgB,EAA0B/E,GAAa+D,GACvCiB,EAAchF,GAAa5V,GAC3B6a,EAAUjF,GAAagE,GACvBhP,EAAS/hB,EAAAA,aAAkB,KAC/B,IAAKwxB,EAAanyB,UAAYqyB,EAAYryB,QACxC,OAEF,MAAMysB,EAAS,CACb7W,YACAqC,WACAyU,WAAYkF,GAEVc,EAAY1yB,UACdysB,EAAO3U,SAAW4a,EAAY1yB,SAEhBqsB,GAAA8F,EAAanyB,QAASqyB,EAAYryB,QAASysB,GAAQmG,MAAK7Q,IACtE,MAAM8Q,EAAW,IACZ9Q,EAKHsP,cAAkC,IAApBsB,EAAQ3yB,SAEpB8yB,EAAa9yB,UAAYktB,GAAUqF,EAAQvyB,QAAS6yB,KACtDN,EAAQvyB,QAAU6yB,EAClBvrB,EAAAA,WAAmB,KACjBqqB,EAAQkB,EAAQ,IAE1B,GACK,GACA,CAACjB,EAAkBhc,EAAWqC,EAAUya,EAAaC,IACxD/wB,IAAM,MACS,IAAT8vB,GAAkBa,EAAQvyB,QAAQqxB,eACpCkB,EAAQvyB,QAAQqxB,cAAe,EAC/BM,GAAQ5P,IAAS,IACZA,EACHsP,cAAc,MAEtB,GACK,CAACK,IACE,MAAAoB,EAAenrB,EAAYC,QAAC,GAClChG,IAAM,KACJkxB,EAAa9yB,SAAU,EAChB,KACL8yB,EAAa9yB,SAAU,CAAA,IAExB,IACH4B,IAAM,KAGJ,GAFIshB,MAA0BljB,QAAUkjB,GACpCoP,MAAwBtyB,QAAUsyB,GAClCpP,GAAeoP,EAAY,CAC7B,GAAIG,EAAwBzyB,QAC1B,OAAOyyB,EAAwBzyB,QAAQkjB,EAAaoP,EAAY5P,GAE1DA,GACd,IACK,CAACQ,EAAaoP,EAAY5P,EAAQ+P,EAAyBD,IACxD,MAAAtyB,EAAOqB,EAAAA,SAAc,KAAO,CAChCyV,UAAWmb,EACXlb,SAAUob,EACVH,eACAE,iBACE,CAACF,EAAcE,IACbpa,EAAWzW,EAAAA,SAAc,KAAO,CACpCyV,UAAWkM,EACXjM,SAAUqb,KACR,CAACpP,EAAaoP,IACZlB,EAAiB7vB,EAAAA,SAAc,KACnC,MAAMwxB,EAAgB,CACpBrgB,SAAUuF,EACVhD,KAAM,EACNG,IAAK,GAEH,IAAC4C,EAASf,SACL,OAAA8b,EAET,MAAMje,EAAI0Y,GAAWxV,EAASf,SAAU8K,EAAKjN,GACvCC,EAAIyY,GAAWxV,EAASf,SAAU8K,EAAKhN,GAC7C,OAAIyc,EACK,IACFuB,EACHvB,UAAW,aAAe1c,EAAI,OAASC,EAAI,SACvCuY,GAAOtV,EAASf,WAAa,KAAO,CACtC8D,WAAY,cAIX,CACLrI,SAAUuF,EACVhD,KAAMH,EACNM,IAAKL,EACN,GACA,CAACkD,EAAUuZ,EAAWxZ,EAASf,SAAU8K,EAAKjN,EAAGiN,EAAKhN,IAClDxT,OAAAA,EAAaC,SAAC,KAAO,IACvBugB,EACHW,SACAxiB,OACA8X,WACAoZ,oBACE,CAACrP,EAAMW,EAAQxiB,EAAM8X,EAAUoZ,GACrC,CEpJ8E4B,CAAY,CAEpF/a,SAAU,QACVrC,UAAWqb,EACXQ,qBAAsB,IAAI5rB,IACR4c,MAAc5c,EAAM,CAClCod,eAA2C,WAA3BiN,IAIpBlY,SAAU,CACRhB,UAAWhV,EAAQixB,QAErBvG,WAAY,EFsLF9U,EErLD,CAAEkO,SAAU0J,EAAawB,EAAa7Z,cAAeuY,GFqLjC,IAC9BwD,GAAStb,GACZA,QAAS,CAACA,EAASiW,KEtLb+B,GAAmBzJ,GAAM,CACvBL,UAAU,EACVC,WAAW,EACXO,QAAoB,YAAX0J,EAAuBnE,UAAe,KAC5CtF,IAELqJ,GAAmB/I,GAAK,IAAKN,IAC7Bvc,GAAK,IACAuc,EACHiD,MAAO,EAAGxR,WAAUD,QAAOmS,iBAAgBD,sBACzC,MAAQrX,MAAOugB,EAAatgB,OAAQugB,GAAiBrb,EAAMf,UACrDqc,EAAerb,EAASf,SAAS1K,MACvC8mB,EAAaC,YAAY,iCAAkC,GAAGpJ,OAC9DmJ,EAAaC,YAAY,kCAAmC,GAAGrJ,OAC/DoJ,EAAaC,YAAY,8BAA+B,GAAGH,OAC3DE,EAAaC,YAAY,+BAAgC,GAAGF,MAAgB,IAGhFnN,GAASsN,GAAgB,CAAEzuB,QAASmhB,EAAOnT,QAAS6c,IACpD6D,GAAgB,CAAEzC,aAAYC,gBAC9Bf,GAAoBzF,GAAK,CAAEvS,SAAU,qBAAsBsO,OFgKpD,IAAC3O,EAASiW,EE7JrB,MAAO4F,EAAYC,GAAeC,GAA6B/d,GACzDge,EAAepsB,EAAe2oB,GACpCniB,GAAgB,KACVqjB,IACF,MAAAuC,GAAAA,IACR,GACO,CAACvC,EAAcuC,IACZ,MAAAC,EAAS,OAAA3xB,EAAeujB,EAAAQ,YAAO,EAAA/jB,EAAA4S,EAC/Bgf,GAAS,OAAA1uB,EAAeqgB,EAAAQ,YAAO,EAAA7gB,EAAA2P,EAC/Bgf,GAA2D,KAAvC,OAAArS,EAAA+D,EAAeQ,YAAf,EAAAvE,EAAsBkK,eACzCoI,GAAeC,IAAoBhrB,aAI1C,OAHA+E,GAAgB,KACVqiB,GAA0B4D,GAAA/sB,OAAOyJ,iBAAiB0f,GAAS6D,OAAM,GACpE,CAAC7D,IACsBxjB,EAAAxK,IACxB,MACA,CACEvC,IAAKI,EAAKkyB,YACV,oCAAqC,GACrC7lB,MAAO,IACF6kB,EACHI,UAAWH,EAAeD,EAAeI,UAAY,sBAErD2C,SAAU,cACVD,OAAQF,GACR,kCAAqC,CACnC,OAAAI,EAAA3O,EAAe+N,sBAAiB,EAAAY,EAAAtf,EAChC,OAAAwP,EAAAmB,EAAe+N,sBAAf,EAAAlP,EAAgCvP,GAChC9O,KAAK,SAIJ,OAAAouB,EAAA5O,EAAe+E,WAAf,EAAA6J,EAAqB3J,kBAAmB,CACzC4J,WAAY,SACZ9nB,cAAe,SAGnB+nB,IAAKzyB,EAAMyyB,IACXxyB,SAA6B8K,EAAAxK,IAC3BgtB,GACA,CACEhuB,MAAO2tB,EACPyE,aACAe,cAAejE,EACfsD,SACAC,UACAW,gBAAiBV,GACjBhyB,SAA6B8K,EAAAxK,IAC3BuE,EAAUkG,IACV,CACE,YAAa2mB,EACb,aAAcC,KACXtD,EACHtwB,IAAKyJ,EACLgD,MAAO,IACF6jB,EAAa7jB,MAGhBmoB,UAAYrD,OAAwB,EAAT,aAOxC,IAGL9B,GAAcjtB,YAAc8sB,GAC5B,IAAIuF,GAAa,cACbC,GAAgB,CAClBxf,IAAK,SACLF,MAAO,OACPC,OAAQ,MACRF,KAAM,SAEJ4f,GAAcpxB,EAAAA,YAAiB,SAAsB3B,EAAO4B,GAC9D,MAAMsrB,cAAEA,KAAkBZ,GAAetsB,EACnCgzB,EAAiBxF,GAAkBqF,GAAY3F,GAC/C+F,EAAWH,GAAcE,EAAerB,YAC9C,OAIqB5mB,EAAAxK,IACjB,OACA,CACEvC,IAAKg1B,EAAeN,cACpBjoB,MAAO,CACLmG,SAAU,WACVuC,KAAM6f,EAAejB,OACrBze,IAAK0f,EAAehB,OACpBiB,CAACA,GAAW,EACZvB,gBAAiB,CACfpe,IAAK,GACLF,MAAO,MACPC,OAAQ,WACRF,KAAM,UACN6f,EAAerB,YACjBjC,UAAW,CACTpc,IAAK,mBACLF,MAAO,iDACPC,OAAQ,iBACRF,KAAM,kDACN6f,EAAerB,YACjBa,WAAYQ,EAAeL,gBAAkB,cAAW,GAE1D1yB,SAA6B8K,EAAAxK,IAC3B2yB,GACA,IACK5G,EACHtuB,IAAK4D,EACL6I,MAAO,IACF6hB,EAAW7hB,MAEduD,QAAS,YAOvB,IAEA,SAASqhB,GAAUpxB,GACjB,OAAiB,OAAVA,CACT,CAHA80B,GAAYvyB,YAAcqyB,GAI1B,IAAInB,GAAmB5b,IAAa,CAClC3K,KAAM,kBACN2K,UACA,EAAA0N,CAAGvD,aACD,MAAMnM,UAAEA,EAAAmC,MAAWA,EAAO0N,eAAAA,GAAmB1D,EAEvCkT,EAD2D,KAAvC,OAAA/yB,EAAAujB,EAAeQ,YAAf,EAAA/jB,EAAsB0pB,cAE1CmF,EAAakE,EAAgB,EAAIrd,EAAQmZ,WACzCC,EAAciE,EAAgB,EAAIrd,EAAQoZ,aACzCyC,EAAYC,GAAeC,GAA6B/d,GACzDsf,EAAe,CAAE5f,MAAO,KAAMoW,OAAQ,MAAOnW,IAAK,QAASme,GAC3DyB,IAAgB,OAAA/vB,EAAeqgB,EAAAQ,YAAO,EAAA7gB,EAAA0P,IAAK,GAAKic,EAAa,EAC7DqE,IAAgB,OAAA1T,EAAe+D,EAAAQ,YAAO,EAAAvE,EAAA3M,IAAK,GAAKic,EAAc,EACpE,IAAIlc,EAAI,GACJC,EAAI,GAcR,MAbmB,WAAf0e,GACF3e,EAAImgB,EAAgBC,EAAe,GAAGC,MACtCpgB,GAAQic,EAAJ,MACoB,QAAfyC,GACT3e,EAAImgB,EAAgBC,EAAe,GAAGC,MACtCpgB,EAAI,GAAGgD,EAAMd,SAASpE,OAASme,OACP,UAAfyC,GACT3e,GAAQkc,EAAJ,KACJjc,EAAIkgB,EAAgBC,EAAe,GAAGE,OACd,SAAf3B,IACT3e,EAAI,GAAGiD,EAAMd,SAASrE,MAAQoe,MAC9Bjc,EAAIkgB,EAAgBC,EAAe,GAAGE,OAEjC,CAAErT,KAAM,CAAEjN,EAAAA,EAAGC,EAAAA,GACxB,IAEA,SAAS4e,GAA6B/d,GACpC,MAAOY,EAAMiZ,EAAQ,UAAY7Z,EAAUC,MAAM,KAC1C,MAAA,CAACW,EAAMiZ,EAChB,CAEA,IAAI4F,GAAStG,GACTuG,GAAU/F,GACVpB,GAAQ0G,IE9QPU,GAAsBC,IAAsB30B,EAAmB,UAAW,CAC7E8tB,KAEE8G,GAAiB9G,KACjB+G,GAAgB,kBAChBC,GAAyB,IACzBC,GAAe,gBACdC,GAAgCC,IAA6BP,GAAqBG,IACnFK,GAAmBj0B,IACf,MAAAk0B,eACJA,EAAAC,cACAA,EAAgBN,GAAAO,kBAChBA,EAAoB,IAAAC,wBACpBA,GAA0B,EAAAp0B,SAC1BA,GACED,EACEs0B,EAAmBzuB,EAAYC,QAAC,GAChCyuB,EAAwB1uB,EAAYC,QAAC,GACrC0uB,EAAoB3uB,EAAYC,OAAC,GAKvC,OAJAC,EAAAA,WAAgB,KACd,MAAM0uB,EAAiBD,EAAkBt2B,QAClC,MAAA,IAAMkH,OAAOgE,aAAaqrB,EAAc,GAC9C,IACuB1pB,EAAAxK,IACxBwzB,GACA,CACEx0B,MAAO20B,EACPI,mBACAH,gBACAO,OAAQ71B,EAAiBC,aAAC,KACjBsG,OAAAgE,aAAaorB,EAAkBt2B,SACtCo2B,EAAiBp2B,SAAU,CAAA,GAC1B,IACHy2B,QAAS91B,EAAiBC,aAAC,KAClBsG,OAAAgE,aAAaorB,EAAkBt2B,SACtCs2B,EAAkBt2B,QAAUkH,OAAO+D,YACjC,IAAMmrB,EAAiBp2B,SAAU,GACjCk2B,EACD,GACA,CAACA,IACJG,wBACAK,yBAA0B/1B,EAAAA,aAAmBg2B,IAC3CN,EAAsBr2B,QAAU22B,CAAA,GAC/B,IACHR,0BACAp0B,YAEH,EAEHg0B,GAAgBzzB,YAAcozB,GAC9B,IAAIkB,GAAe,WACdC,GAAwBC,IAAqBvB,GAAqBqB,IA4FnEG,GAAe,iBACEtzB,EAAgB+E,YACnC,CAAC1G,EAAO4B,KACN,MAAMsyB,eAAEA,KAAmBgB,GAAiBl1B,EACtCE,EAAU80B,GAAkBC,GAAcf,GAC1CiB,EAAkBnB,GAA0BiB,GAAcf,GAC1DkB,EAAczB,GAAeO,GAE7BzsB,EAAe7I,EAAgBgD,EADzBiE,EAAYC,OAAC,MAC+B5F,EAAQm1B,iBAC1DC,EAAmBzvB,EAAYC,QAAC,GAChCyvB,EAA0B1vB,EAAYC,QAAC,GACvC0vB,EAAkB32B,EAAAA,aAAkB,IAAMy2B,EAAiBp3B,SAAU,GAAO,IAI3DqC,OAHvBwF,EAAAA,WAAgB,IACP,IAAMuB,SAASyB,oBAAoB,YAAaysB,IACtD,CAACA,IACmBj1B,EAAAA,IAAIk1B,GAAwB,CAAExwB,SAAS,KAASmwB,EAAan1B,SAA6B8K,EAAAxK,IAC/GuE,EAAU4wB,OACV,CACE,mBAAoBx1B,EAAQ0vB,KAAO1vB,EAAQy1B,eAAY,EACvD,aAAcz1B,EAAQ01B,kBACnBV,EACHl3B,IAAKyJ,EACLouB,cAAep4B,EAAqBuC,EAAM61B,eAAgBh4B,IAC9B,UAAtBA,EAAMiL,cACLysB,EAAwBr3B,SAAYi3B,EAAgBZ,sBAAsBr2B,UAC7EgC,EAAQ41B,iBACRP,EAAwBr3B,SAAU,GAC9C,IAEQ63B,eAAgBt4B,EAAqBuC,EAAM+1B,gBAAgB,KACzD71B,EAAQ81B,iBACRT,EAAwBr3B,SAAU,CAAA,IAEpC+3B,cAAex4B,EAAqBuC,EAAMi2B,eAAe,KACnD/1B,EAAQ0vB,MACV1vB,EAAQy0B,UAEVW,EAAiBp3B,SAAU,EAC3BoJ,SAAS0B,iBAAiB,YAAawsB,EAAiB,CAAEvsB,MAAM,GAAM,IAExEitB,QAASz4B,EAAqBuC,EAAMk2B,SAAS,KACtCZ,EAAiBp3B,SAASgC,EAAQw0B,QAAQ,IAEjDyB,OAAQ14B,EAAqBuC,EAAMm2B,OAAQj2B,EAAQy0B,SACnDyB,QAAS34B,EAAqBuC,EAAMo2B,QAASl2B,EAAQy0B,YAEtD,IAGQn0B,YAAcy0B,GAC7B,IACKoB,GAAgBC,IAAoB7C,GADvB,gBACyD,CACzE8C,gBAAY,IAQVjJ,GAAe,iBACfkJ,GAAiB70B,EAAgB+E,YACnC,CAAC1G,EAAO4B,KACN,MAAM60B,EAAgBH,GAAiBhJ,GAActtB,EAAMk0B,iBACrDqC,WAAEA,EAAaE,EAAcF,WAAA7hB,KAAYA,EAAO,SAAU4Z,GAAiBtuB,EAC3EE,EAAU80B,GAAkB1H,GAActtB,EAAMk0B,gBACtD,OAA0BnpB,EAAAxK,IAAC8L,EAAU,CAAEC,QAASiqB,GAAcr2B,EAAQ0vB,KAAM3vB,SAAUC,EAAQm0B,wBAA6CtpB,EAAAxK,IAACm2B,GAAoB,CAAEhiB,UAAS4Z,EAActwB,IAAK4D,MAAqCrB,IAACo2B,GAAyB,CAAEjiB,UAAS4Z,EAActwB,IAAK4D,KAAiB,IAG5S+0B,GAA0Bh1B,EAAgB+E,YAAC,CAAC1G,EAAO4B,KACrD,MAAM1B,EAAU80B,GAAkB1H,GAActtB,EAAMk0B,gBAChDiB,EAAkBnB,GAA0B1G,GAActtB,EAAMk0B,gBAChEl2B,EAAM6H,EAAYC,OAAC,MACnB2B,EAAe7I,EAAgBgD,EAAc5D,IAC5C44B,EAAkBC,GAAuB1vB,EAAAA,SAAe,OACzD2vB,QAAEA,EAASnC,QAAAA,GAAYz0B,EACvBquB,EAAUvwB,EAAIE,SACd02B,yBAAEA,GAA6BO,EAC/B4B,EAAwBl4B,EAAAA,aAAkB,KAC9Cg4B,EAAoB,MACpBjC,GAAyB,EAAK,GAC7B,CAACA,IACEoC,EAAwBn4B,EAAiBC,aAC7C,CAACjB,EAAOo5B,KACN,MAAMC,EAAgBr5B,EAAMq5B,cACtBC,EAAY,CAAEnkB,EAAGnV,EAAMu5B,QAASnkB,EAAGpV,EAAMw5B,SAEzCC,EA6IZ,SAA6BH,EAAWI,EAAUvmB,EAAU,GAC1D,MAAMsmB,EAAmB,GACzB,OAAQC,GACN,IAAK,MACcD,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAE/C,MACF,IAAK,SACcsmB,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAE/C,MACF,IAAK,OACcsmB,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAE/C,MACF,IAAK,QACcsmB,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAI5C,OAAAsmB,CACT,CA1K+BE,CAAoBL,EA2HnD,SAA6BM,EAAO3iB,GAClC,MAAMxB,EAAMZ,KAAKglB,IAAI5iB,EAAKxB,IAAMmkB,EAAMxkB,GAChCI,EAASX,KAAKglB,IAAI5iB,EAAKzB,OAASokB,EAAMxkB,GACtCG,EAAQV,KAAKglB,IAAI5iB,EAAK1B,MAAQqkB,EAAMzkB,GACpCG,EAAOT,KAAKglB,IAAI5iB,EAAK3B,KAAOskB,EAAMzkB,GACxC,OAAQN,KAAKD,IAAIa,EAAKD,EAAQD,EAAOD,IACnC,KAAKA,EACI,MAAA,OACT,KAAKC,EACI,MAAA,QACT,KAAKE,EACI,MAAA,MACT,KAAKD,EACI,MAAA,SACT,QACQ,MAAA,IAAIzS,MAAM,eAEtB,CA7IuB+2B,CAAoBR,EAAWD,EAAc5b,0BAGxDsc,EAiMZ,SAAiBlL,GACT,MAAAmL,EAAYnL,EAAO5kB,QAQzB,OAPU+vB,EAAAzQ,MAAK,CAAC3G,EAAGC,IACbD,EAAEzN,EAAI0N,EAAE1N,GAAU,EACbyN,EAAEzN,EAAI0N,EAAE1N,EAAU,EAClByN,EAAExN,EAAIyN,EAAEzN,GAAU,EAClBwN,EAAExN,EAAIyN,EAAEzN,EAAU,EACf,IAIhB,SAA0ByZ,GACxB,GAAIA,EAAO/tB,QAAU,EAAG,OAAO+tB,EAAO5kB,QACtC,MAAMgwB,EAAY,GAClB,IAAA,IAASp5B,EAAI,EAAGA,EAAIguB,EAAO/tB,OAAQD,IAAK,CAChCq5B,MAAAA,EAAIrL,EAAOhuB,GACV,KAAAo5B,EAAUn5B,QAAU,GAAG,CAC5B,MAAMq5B,EAAIF,EAAUA,EAAUn5B,OAAS,GACjC6S,EAAIsmB,EAAUA,EAAUn5B,OAAS,GACvC,MAAKq5B,EAAEhlB,EAAIxB,EAAEwB,IAAM+kB,EAAE9kB,EAAIzB,EAAEyB,KAAO+kB,EAAE/kB,EAAIzB,EAAEyB,IAAM8kB,EAAE/kB,EAAIxB,EAAEwB,IACnD,QADiEilB,KAE5E,CACIH,EAAUlS,KAAKmS,EACnB,CACED,EAAUG,MACV,MAAMC,EAAY,GAClB,IAAA,IAASx5B,EAAIguB,EAAO/tB,OAAS,EAAGD,GAAK,EAAGA,IAAK,CACrCq5B,MAAAA,EAAIrL,EAAOhuB,GACV,KAAAw5B,EAAUv5B,QAAU,GAAG,CAC5B,MAAMq5B,EAAIE,EAAUA,EAAUv5B,OAAS,GACjC6S,EAAI0mB,EAAUA,EAAUv5B,OAAS,GACvC,MAAKq5B,EAAEhlB,EAAIxB,EAAEwB,IAAM+kB,EAAE9kB,EAAIzB,EAAEyB,KAAO+kB,EAAE/kB,EAAIzB,EAAEyB,IAAM8kB,EAAE/kB,EAAIxB,EAAEwB,IACnD,QADiEilB,KAE5E,CACIC,EAAUtS,KAAKmS,EACnB,CAEM,OADJG,EAAUD,MACe,IAArBH,EAAUn5B,QAAqC,IAArBu5B,EAAUv5B,QAAgBm5B,EAAU,GAAG9kB,IAAMklB,EAAU,GAAGllB,GAAK8kB,EAAU,GAAG7kB,IAAMilB,EAAU,GAAGjlB,EACpH6kB,EAEAA,EAAUrd,OAAOyd,EAE5B,CAjCSC,CAAiBN,EAC1B,CA3MwBO,CAAQ,IAAId,KAyKpC,SAA2BxiB,GACzB,MAAMxB,IAAEA,EAAAF,MAAKA,EAAOC,OAAAA,EAAAF,KAAQA,GAAS2B,EAC9B,MAAA,CACL,CAAE9B,EAAGG,EAAMF,EAAGK,GACd,CAAEN,EAAGI,EAAOH,EAAGK,GACf,CAAEN,EAAGI,EAAOH,EAAGI,GACf,CAAEL,EAAGG,EAAMF,EAAGI,GAElB,CAlLgCglB,CAAkBpB,EAAY3b,2BAExDub,EAAoBe,GACpBhD,GAAyB,EAAI,GAE/B,CAACA,IAmCoBr0B,OAjCvBwF,EAAAA,WAAgB,IACP,IAAMgxB,KACZ,CAACA,IACJhxB,EAAAA,WAAgB,KACd,GAAI+wB,GAAWvI,EAAS,CACtB,MAAM+J,EAAsBz6B,GAAUm5B,EAAsBn5B,EAAO0wB,GAC7DgK,EAAsB16B,GAAUm5B,EAAsBn5B,EAAOi5B,GAGnE,OAFQA,EAAA9tB,iBAAiB,eAAgBsvB,GACjC/J,EAAAvlB,iBAAiB,eAAgBuvB,GAClC,KACGzB,EAAA/tB,oBAAoB,eAAgBuvB,GACpC/J,EAAAxlB,oBAAoB,eAAgBwvB,EAAkB,CAEtE,IACK,CAACzB,EAASvI,EAASyI,EAAuBD,IAC7ChxB,EAAAA,WAAgB,KACd,GAAI6wB,EAAkB,CACd,MAAA4B,EAA2B36B,IAC/B,MAAM0H,EAAS1H,EAAM0H,OACfkzB,EAAkB,CAAEzlB,EAAGnV,EAAMu5B,QAASnkB,EAAGpV,EAAMw5B,SAC/CqB,GAAmB,MAAA5B,OAAA,EAAAA,EAASptB,SAASnE,YAAWgpB,WAAS7kB,SAASnE,IAClEozB,GAuJd,SAA0BlB,EAAOmB,GAC/B,MAAQ5lB,EAAAA,EAAGC,EAAAA,GAAMwkB,EACjB,IAAIoB,GAAS,EACJn6B,IAAAA,IAAAA,EAAI,EAAGo6B,EAAIF,EAAQj6B,OAAS,EAAGD,EAAIk6B,EAAQj6B,OAAQm6B,EAAIp6B,IAAK,CAC7D,MAAAq6B,EAAKH,EAAQl6B,GACbs6B,EAAKJ,EAAQE,GACbG,EAAKF,EAAG/lB,EACRkmB,EAAKH,EAAG9lB,EACRkmB,EAAKH,EAAGhmB,EACRomB,EAAKJ,EAAG/lB,EACIimB,EAAKjmB,GAAMmmB,EAAKnmB,GAAKD,GAAKmmB,EAAKF,IAAOhmB,EAAIimB,IAAOE,EAAKF,GAAMD,OACrDJ,EAC7B,CACS,OAAAA,CACT,CArK2CQ,CAAiBZ,EAAiB7B,GACjE8B,EACqB3B,IACd4B,IACc5B,IACdpC,IACnB,EAGM,OADSrtB,SAAA0B,iBAAiB,cAAewvB,GAClC,IAAMlxB,SAASyB,oBAAoB,cAAeyvB,EAC/D,IACK,CAAC1B,EAASvI,EAASqI,EAAkBjC,EAASoC,IAC1Bx2B,EAAAA,IAAIm2B,GAAoB,IAAK12B,EAAOhC,IAAKyJ,GAAc,KAE3E6xB,GAAsCC,IAAmC9F,GAAqBqB,GAAc,CAAE0E,UAAU,IACzHC,KAA4B,kBAC5B/C,GAAqB/0B,EAAgB+E,YACvC,CAAC1G,EAAO4B,KACA,MAAAsyB,eACJA,EAAAj0B,SACAA,EACA,aAAcy5B,EAAA9yB,gBACdA,EAAAC,qBACAA,KACGynB,GACDtuB,EACEE,EAAU80B,GAAkB1H,GAAc4G,GAC1CkB,EAAczB,GAAeO,IAC7BS,QAAEA,GAAYz0B,EAepB,OAdA6F,EAAAA,WAAgB,KACLuB,SAAA0B,iBAAiB8qB,GAAca,GACjC,IAAMrtB,SAASyB,oBAAoB+qB,GAAca,KACvD,CAACA,IACJ5uB,EAAAA,WAAgB,KACd,GAAI7F,EAAQ42B,QAAS,CACb,MAAA6C,EAAgB97B,IACpB,MAAM0H,EAAS1H,EAAM0H,QACT,MAARA,OAAQ,EAAAA,EAAAmE,SAASxJ,EAAQ42B,WAAmBnC,GAAA,EAG3C,OADPvvB,OAAO4D,iBAAiB,SAAU2wB,EAAc,CAAEtvB,SAAS,IACpD,IAAMjF,OAAO2D,oBAAoB,SAAU4wB,EAAc,CAAEtvB,SAAS,GACnF,IACO,CAACnK,EAAQ42B,QAASnC,IACK5pB,EAAAxK,IACxBkG,EACA,CACExB,SAAS,EACT0B,6BAA6B,EAC7BC,kBACAC,uBACAC,eAAiBjJ,GAAUA,EAAM0M,iBACjCvD,UAAW2tB,EACX10B,SAA8B8K,EAAA6uB,KAC5BC,GACA,CACE,aAAc35B,EAAQ01B,kBACnBR,KACA9G,EACHtwB,IAAK4D,EACL6I,MAAO,IACF6jB,EAAa7jB,MAGd,2CAA4C,uCAC5C,0CAA2C,sCAC3C,2CAA4C,uCAC5C,gCAAiC,mCACjC,iCAAkC,qCAGtCxK,SAAU,OACYw5B,GAAW,CAAEx5B,aACjBM,MAAI+4B,GAAsC,CAAE/5B,MAAO20B,EAAgBsF,UAAU,EAAMv5B,eAA8B65B,EAA8B,CAAE1nB,GAAIlS,EAAQy1B,UAAWoE,KAAM,UAAW95B,SAAUy5B,GAAaz5B,UAKzO,IAGLu2B,GAAeh2B,YAAc8sB,GAC7B,IAAIuF,GAAa,eACElxB,EAAgB+E,YACjC,CAAC1G,EAAO4B,KACN,MAAMsyB,eAAEA,KAAmB5H,GAAetsB,EACpCo1B,EAAczB,GAAeO,GAKnC,OAJqCqF,GACnC1G,GACAqB,GAEkCsF,SAAW,KAAuBj5B,EAAGA,IAACy5B,GAAuB,IAAK5E,KAAgB9I,EAAYtuB,IAAK4D,GAAc,IAG5IpB,YAAcqyB,GAoHxB,IAAC9yB,GAAWk0B,GAIXgG,GAAWzD,GC1eX93B,GAAE2B,OAAO65B,eAA2G1oB,GAAE,CAACE,EAAED,EAAEG,KAA7F,EAACF,EAAED,EAAEG,KAAIH,KAAKC,EAAEhT,GAAEgT,EAAED,EAAE,CAAC0oB,YAAW,EAAGC,cAAa,EAAGC,UAAS,EAAGp8B,MAAM2T,IAAIF,EAAED,GAAGG,CAAAA,EAAkBuV,CAAEzV,EAAY,iBAAHD,EAAYA,EAAE,GAAGA,EAAEG,GAAGA,GAAG,IAAskB0oB,GAAE,IAAxkB,MAAQ,WAAAC,GAAc/oB,GAAEmO,KAAK,UAAUA,KAAK6a,UAAYC,GAAA9a,KAAK,eAAe,WAAa8a,GAAA9a,KAAK,YAAY,EAAE,CAAC,GAAAF,CAAIhO,GAAQkO,KAAAzhB,UAAUuT,IAAIkO,KAAK+a,aAAa,UAAU/a,KAAKgb,UAAU,EAAEhb,KAAKzhB,QAAQuT,EAAE,CAAC,KAAAwV,GAAatH,KAAAF,IAAIE,KAAK6a,SAAS,CAAC,MAAAI,GAAS,QAAQjb,KAAKgb,SAAS,CAAC,YAAIE,GAAW,MAAsB,WAAflb,KAAKzhB,OAAkB,CAAC,YAAI48B,GAAW,MAAsB,WAAfnb,KAAKzhB,OAAkB,CAAC,MAAAs8B,GAAS,MAAsB,oBAARp1B,QAAsC,oBAAVkC,SAAsB,SAAS,QAAQ,CAAC,OAAAyzB,GAA8B,YAAfpb,KAAA+a,eAA2B/a,KAAK+a,aAAa,WAAW,CAAC,qBAAIM,GAAoB,MAA2B,aAApBrb,KAAK+a,YAAyB,GCAzsB,SAAS5oB,GAAEF,GAAG,IAAIH,EAAED,EAASE,OAAAA,GAAEmpB,SAAS,KAAKjpB,EAAE,kBAAkBA,EAAEA,EAAExK,cAAc,YAAYwK,EAAkD,OAA/CJ,EAAiB,OAAdC,EAAEG,EAAE1T,cAAe,EAAOuT,EAAErK,eAAqBoK,EAAElK,SAAS,KAAKA,QAAQ,CCA7M,SAASoK,GAAED,GAA0B,mBAAhBwpB,eAA2BA,eAAexpB,GAAGypB,QAAQC,UAAUrK,KAAKrf,GAAG2pB,OAAMtpB,GAAG3I,YAAW,KAAW2I,MAAAA,CAAAA,KAAI,CCAnF,SAASA,KAAQwoB,IAAAA,EAAE,GAAG9oB,EAAE,CAACxI,iBAAiByI,CAAAA,EAAEC,EAAEE,EAAElT,KAAU+S,EAAEzI,iBAAiB0I,EAAEE,EAAElT,GAAG8S,EAAE7G,KAAI,IAAI8G,EAAE1I,oBAAoB2I,EAAEE,EAAElT,MAAK,qBAAAukB,IAAyBxR,GAAOC,IAAAA,EAAEuR,yBAAyBxR,GAAG,OAAOD,EAAE7G,KAAI,IAAIqY,qBAAqBtR,IAAG,EAAE2pB,cAAa5pB,IAAUD,EAAEyR,uBAAsB,IAAIzR,EAAEyR,yBAAyBxR,KAAK,UAAAtI,IAAcsI,GAAOC,IAAAA,EAAEvI,cAAcsI,GAAG,OAAOD,EAAE7G,KAAI,IAAIvB,aAAasI,IAAG,EAAE,SAAA4pB,IAAa7pB,GAAOC,IAAAA,EAAE,CAACxT,SAAQ,GAAI,OAAOuiB,IAAE,KAAK/O,EAAExT,SAASuT,EAAE,QAAOD,EAAE7G,KAAI,KAAK+G,EAAExT,SAAQ,CAAA,GAAI,EAAE,KAAAuM,CAAMgH,EAAEC,EAAEE,GAAG,IAAIlT,EAAE+S,EAAEhH,MAAM8wB,iBAAiB7pB,GAAG,OAAOrR,OAAOm7B,OAAO/pB,EAAEhH,MAAM,CAACgxB,CAAC/pB,GAAGE,IAAI+N,KAAKhV,KAAI,KAAYtK,OAAAm7B,OAAO/pB,EAAEhH,MAAM,CAACgxB,CAAC/pB,GAAGhT,GAAE,GAAG,EAAE,KAAAg9B,CAAMjqB,GAAG,IAAIC,EAAEI,KAAWL,OAAAA,EAAEC,GAAGiO,KAAKhV,KAAI,IAAI+G,EAAEiqB,WAAU,EAAEhxB,IAAI8G,IAAU6oB,EAAEhsB,SAASmD,IAAI6oB,EAAE1U,KAAKnU,GAAG,KAASC,IAAAA,EAAE4oB,EAAEtyB,QAAQyJ,GAAMC,GAAAA,GAAG,EAAE,IAAA,IAAQE,KAAK0oB,EAAEsB,OAAOlqB,EAAE,GAAGE,GAAC,GAAK,OAAA+pB,GAAU,IAAA,IAAQlqB,KAAK6oB,EAAEsB,OAAO,GAAGnqB,GAAG,GAAUD,OAAAA,CAAC,CCAlwB,SAASumB,KAAI,IAAItmB,GAAGK,EAACtK,SAACkK,IAAU4oB,OAAAA,EAACnP,WAAC,IAAI,IAAI1Z,EAAEkqB,WAAU,CAAClqB,IAAIA,CAAC,CCArE,IAAIG,GAAE,CAACH,EAAEC,KAAOmqB,GAAAhB,SAASlpB,YAAEF,EAAEC,GAAGoqB,EAAC5vB,gBAACuF,EAAEC,EAAC,ECAjC,SAAS4oB,GAAE7oB,GAAOD,IAAAA,EAAEE,EAAC5L,OAAC2L,GAAG,OAAOK,IAAE,KAAKN,EAAEtT,QAAQuT,CAAAA,GAAG,CAACA,IAAID,CAAC,CCA/E,IAAIM,GAAE,SAASJ,GAAOD,IAAAA,EAAEG,GAAEF,GAAU+O,OAAAA,EAAE3hB,aAAY,IAAI0S,IAAIC,EAAEvT,WAAWsT,IAAG,CAACC,GAAG,ECA7FA,GAAED,EAAC3R,mBAAC,GCAjE,SAAS6R,MAAKF,GAAU,OAAA7J,MAAMC,KAAK,IAAItB,IAAIkL,EAAEuqB,SAAQnqB,GAAa,iBAAHA,EAAYA,EAAEmC,MAAM,KAAK,OAAM9P,OAAOC,SAASC,KAAK,IAAI,CCAvH,SAAS63B,GAAExqB,EAAEI,KAAK6O,GAAG,GAAGjP,KAAKI,EAAE,CAAKH,IAAAA,EAAEG,EAAEJ,GAAG,MAAiB,mBAAHC,EAAcA,KAAKgP,GAAGhP,CAAC,CAAKC,IAAAA,EAAE,IAAI9Q,MAAM,oBAAoB4Q,kEAAkEnR,OAAOgrB,KAAKzZ,GAAGpT,KAAIiT,GAAG,IAAIA,OAAMtN,KAAK,UAAU,MAAMvD,MAAMq7B,mBAAmBr7B,MAAMq7B,kBAAkBvqB,EAAEsqB,IAAGtqB,CAAC,CCAxE,IAA0GD,GAAnGgP,GAAHyb,KAAGzb,GAAyFyb,IAAG,IAAtFzb,GAAE0b,KAAK,GAAG,OAAO1b,GAAEA,GAAE2b,eAAe,GAAG,iBAAiB3b,GAAEA,GAAE4b,OAAO,GAAG,SAAS5b,IAAW6b,KAAG7qB,GAAwD6qB,IAAG,IAArD7qB,GAAE8qB,QAAQ,GAAG,UAAU9qB,GAAEA,GAAE+qB,OAAO,GAAG,SAAS/qB,IAAW,SAASgrB,KAAI,IAAI7qB,EAG5E,WAAiBA,IAAAA,EAAE8qB,EAAC52B,OAAC,IAAI0L,EAAEwB,EAAAA,aAAEvB,IAAI,IAAA,IAAQgP,KAAK7O,EAAE1T,QAAW,MAAHuiB,IAAoB,mBAAHA,EAAcA,EAAEhP,GAAGgP,EAAEviB,QAAQuT,EAAAA,GAAI,IAAI,MAAM,IAAIA,KAAQ,IAACA,EAAEgV,OAAMhG,GAAM,MAAHA,IAAS,OAAO7O,EAAE1T,QAAQuT,EAAED,CAAAA,CAAE,CAH5FmrB,GAAI,OAAO3pB,EAAClU,aAAC0S,GAA8B,UAAYorB,SAAShrB,EAAEirB,WAAWrrB,EAAEsrB,KAAKrrB,EAAEsrB,WAAWtc,EAAEuc,SAAS1C,EAAE2C,QAAQvrB,GAAE,EAAGvG,KAAK+xB,EAAEC,UAAUz+B,IAAIA,EAAK,MAAHA,EAAQA,EAAEyc,GAAMrJ,IAAAA,EAAEsrB,GAAE5rB,EAAEI,GAAG,GAAGF,EAAS2rB,OAAAA,GAAEvrB,EAAEL,EAAEgP,EAAEyc,EAAEx+B,GAAOuU,IAAAA,EAAK,MAAHqnB,EAAQA,EAAE,EAAE,GAAK,EAAFrnB,EAAI,CAAC,IAAIqqB,OAAO3rB,GAAE,KAAMqqB,GAAGlqB,EAAE,GAAGH,EAAS0rB,OAAAA,GAAErB,EAAEvqB,EAAEgP,EAAEyc,EAAEx+B,EAAE,CAAC,GAAK,EAAFuU,EAAI,CAAC,IAAIsqB,QAAQ5rB,GAAE,KAAMqqB,GAAGlqB,EAAE,OAAO0rB,GAAE7rB,EAAE,EAAE,EAAE,CAAC,EAAK,IAAQ,KAAM,EAAK,IAAQ0rB,GAAE,IAAIrB,EAAEyB,QAAO,EAAGhzB,MAAM,CAACuD,QAAQ,SAASyD,EAAEgP,EAAEyc,EAAEx+B,IAAK,CAAC,OAAO2+B,GAAEvrB,EAAEL,EAAEgP,EAAEyc,EAAEx+B,EAAE,CAAzZg/B,CAAE,CAACP,UAAUvrB,KAAKJ,KAAI,CAACI,GAAG,CAAgY,SAASyrB,GAAEzrB,EAAEJ,EAAE,CAAA,EAAGC,EAAEgP,EAAE6Z,GAAG,IAAIqD,GAAGjsB,EAAED,EAAExR,SAASi9B,EAAEU,QAAQl/B,EAAE,SAASoT,GAAG+rB,GAAEjsB,EAAE,CAAC,UAAU,WAAWqB,OAAU,IAARrB,EAAE5T,IAAa,CAAC8/B,CAACp/B,GAAGkT,EAAE5T,KAAK,CAAA,EAAG2T,EAAY,mBAAHurB,EAAcA,EAAE1rB,GAAG0rB,EAAgBprB,cAAAA,GAAGA,EAAEisB,WAA+B,mBAAbjsB,EAAEisB,YAAwBjsB,EAAEisB,UAAUjsB,EAAEisB,UAAUvsB,IAAIM,EAAE,oBAAoBA,EAAE,qBAAqBA,EAAEM,KAAKN,EAAE,wBAAmB,GAAQ,IAAIkqB,EAAE,CAAA,EAAG,GAAGxqB,EAAE,CAAK2V,IAAAA,GAAE,EAAG4Q,EAAE,GAAU,IAAA,IAAC+D,EAAEkC,KAAK39B,OAAOgiB,QAAQ7Q,GAAa,kBAAHwsB,IAAe7W,GAAE,IAAQ,IAAJ6W,GAAQjG,EAAEnS,KAAKkW,EAAEvnB,QAAQ,YAAW0pB,GAAG,IAAIA,EAAEvmB,mBAAkB,GAAGyP,EAAE,CAAC6U,EAAE,yBAAyBjE,EAAE5zB,KAAK,KAAK,IAAA,IAAQ23B,KAAK/D,EAAEiE,EAAE,QAAQF,KAAK,EAAE,CAAC,CAAC,GAAGpqB,IAAIgP,EAAAA,WAAIrgB,OAAOgrB,KAAK6S,GAAEpsB,IAAInT,OAAO,GAAG0B,OAAOgrB,KAAK6S,GAAElC,IAAIr9B,OAAO,GAAM,IAACw/B,EAAAA,eAAExsB,MAAIhK,MAAMkK,QAAQF,IAAIA,EAAEhT,OAAO,GAGl9C,CAAKwoB,IAAAA,EAAExV,EAAE3R,MAAM+3B,EAAK,MAAH5Q,OAAQ,EAAOA,EAAE4W,UAAUjC,EAAY,mBAAH/D,EAAc,IAAIqG,IAAIC,GAAEtG,KAAKqG,GAAGtsB,EAAEisB,WAAWM,GAAEtG,EAAEjmB,EAAEisB,WAAWC,EAAElC,EAAE,CAACiC,UAAUjC,GAAG,CAAE,EAACmC,EAAEb,GAAEzrB,EAAE3R,MAAMk+B,GAAEL,GAAE/rB,EAAE,CAAC,UAAU,IAAA,IAAQssB,KAAKpC,EAAEoC,KAAKH,UAAUjC,EAAEoC,GAAUtF,OAAAA,EAAAA,aAAEnnB,EAAEtR,OAAOm7B,OAAO,CAAA,EAAGyC,EAAEjC,EAAE/oB,EAAE,CAACjV,IAAIs8B,EAAEgE,GAAE3sB,GAAGsB,EAAEjV,MAAMggC,GAAG,CAHotC,GAAG39B,OAAOgrB,KAAK6S,GAAEpsB,IAAInT,OAAO,EAAQ,MAAA,IAAIiC,MAAM,CAAC,+BAA+B,GAAG,0BAA0B6f,kCAAkC,sDAAsDpgB,OAAOgrB,KAAK6S,GAAEpsB,IAAI2I,OAAOpa,OAAOgrB,KAAK6S,GAAElC,KAAKx9B,KAAI2oB,GAAG,OAAOA,MAAKhjB,KAAK,MACjuD,GAAG,iCAAiC,CAAC,8FAA8F,4FAA4F3F,KAAI2oB,GAAG,OAAOA,MAAKhjB,KAAK,OACtPA,KAAK,MACgQ,CAAQ4O,OAAAA,EAACwrB,cAAC7sB,EAAErR,OAAOm7B,OAAO,CAAE,EAACqC,GAAE/rB,EAAE,CAAC,QAAQJ,IAAIgP,EAAAA,UAAGzN,EAAEvB,IAAIgP,EAAAA,UAAGsb,GAAGrqB,EAAE,CAA4K,SAASwJ,MAAKvJ,GAAUA,OAAAA,EAAE6U,OAAMjV,GAAM,MAAHA,SAAS,EAAOA,IAAI,IAAA,IAAQC,KAAKG,EAAK,MAAHH,IAAoB,mBAAHA,EAAcA,EAAED,GAAGC,EAAEvT,QAAQsT,EAAAA,CAAG,CAAC,SAAS4rB,MAAKxrB,GAAS,GAAc,IAAXA,EAAEjT,OAAW,MAAM,GAAG,GAAc,IAAXiT,EAAEjT,OAAW,OAAOiT,EAAE,GAAG,IAAIJ,EAAE,CAAE,EAACC,EAAE,CAAA,EAAG,IAAA,IAAQ6oB,KAAK1oB,EAAE,IAAA,IAAQF,KAAK4oB,EAAE5oB,EAAE8sB,WAAW,OAAoB,mBAANlE,EAAE5oB,IAA0B,MAAPD,EAAEC,KAAYD,EAAEC,GAAG,IAAID,EAAEC,GAAGkU,KAAK0U,EAAE5oB,KAAKF,EAAEE,GAAG4oB,EAAE5oB,GAAG,GAAGF,EAAEitB,UAAUjtB,EAAE,yBAAyB8oB,KAAK7oB,EAAwD,sDAAA3N,KAAKw2B,KAAK7oB,EAAE6oB,GAAG,CAAC5oB,IAAQwrB,IAAAA,EAASA,OAAoC,OAApCA,EAAK,MAAHxrB,OAAQ,EAAOA,EAAEnH,qBAAsB,EAAO2yB,EAAEntB,KAAK2B,EAAC,IAAa4oB,IAAAA,IAAAA,KAAK7oB,EAAEpR,OAAOm7B,OAAOhqB,EAAE,CAAC,CAAC8oB,GAAG5oB,KAAKwrB,GAAOx+B,IAAAA,EAAE+S,EAAE6oB,GAAG,IAAA,IAAQxoB,KAAKpT,EAAE,CAAKgT,IAAAA,aAAagtB,QAAW,MAAHhtB,OAAQ,EAAOA,EAAEitB,uBAAuBD,QAAQhtB,EAAE5T,iBAAiB,OAAOgU,EAAEJ,KAAKwrB,EAAE,CAAC,IAAW1rB,OAAAA,CAAC,CAA4T,SAASotB,GAAEhtB,GAAOJ,IAAAA,EAAE,OAAOnR,OAAOm7B,OAAOqD,aAAEjtB,GAAG,CAACpR,YAA+B,OAAlBgR,EAAEI,EAAEpR,aAAmBgR,EAAEI,EAAEzG,MAAM,CAAC,SAAS+yB,GAAEtsB,GAAG,IAAIJ,EAAEnR,OAAOm7B,OAAO,CAAA,EAAG5pB,GAAWH,IAAAA,IAAAA,KAAKD,OAAS,IAAPA,EAAEC,WAAoBD,EAAEC,GAAUD,OAAAA,CAAC,CAAC,SAASqsB,GAAEjsB,EAAEJ,EAAE,IAAI,IAAIC,EAAEpR,OAAOm7B,OAAO,CAAA,EAAG5pB,GAAG,IAAA,IAAQ6O,KAAKjP,EAAEiP,KAAKhP,UAAUA,EAAEgP,GAAUhP,OAAAA,CAAC,CAAC,SAAS6sB,GAAE1sB,GAAUktB,OAAAA,EAAEC,QAAQhrB,MAAM,KAAK,IAAI,KAAKnC,EAAE5R,MAAMhC,IAAI4T,EAAE5T,GAAG,CCH3wD,IAAIs8B,GAAAA,CAAG7oB,IAAIA,EAAEA,EAAE0qB,KAAK,GAAG,OAAO1qB,EAAEA,EAAEutB,UAAU,GAAG,YAAYvtB,EAAEA,EAAE+qB,OAAO,GAAG,SAAS/qB,GAA9E6oB,CAAkFA,IAAG,CAAA,GAAma,IAAI3oB,GAAEjT,IAAra,SAAWgT,EAAEF,GAAOI,IAAAA,EAAE,IAAIorB,SAAS7V,EAAE,KAAK1V,GAAGC,EAAEI,EAAE,CAAC9T,IAAIwT,EAAE,gBAAsB,GAAP2V,KAAkC,OAArBvV,EAAEH,EAAE,gBAAsBG,OAAE,GAAO6rB,SAAe,GAAPtW,SAAY,EAAO1c,MAAM,CAACmG,SAAS,QAAQ0C,IAAI,EAAEH,KAAK,EAAErC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAO,EAAGC,SAAS,SAASC,KAAK,mBAAmBC,WAAW,SAAS6tB,YAAY,SAAe,GAAP9X,OAAkB,GAAPA,IAAU,CAACnZ,QAAQ,UAAU,OAAO+pB,KAAI,CAAC6E,SAAS9qB,EAAE+qB,WAAWprB,EAAEqrB,KAAK,CAAA,EAAGC,WAAte,OAAmf5xB,KAAK,UAAU,ICA7kB,SAAS2G,GAAEL,GAAG,MAAiB,iBAAHA,GAAiB,OAAJA,GAAY,aAAaA,CAAC,CAAC,SAASC,GAAED,GAAUK,OAAAA,GAAEL,IAAI,YAAYA,CAAC,CAAC,SAASG,GAAEH,GAAUC,OAAAA,GAAED,IAAI,cAAcA,CAAC,CAAC,SAAS/S,GAAE+S,GAAUC,OAAAA,GAAED,IAAI,aAAaA,CAAC,CCAzG,IAAIuqB,GAAEv3B,SAA4D,SAASwO,MAAKvB,GAAOE,IAAAA,EAAElT,EAACoH,OAAC4L,GAAGwrB,EAAAA,WAAE,KAAKtrB,EAAE1T,QAAQwT,CAAAA,GAAG,CAACA,IAAQoqB,IAAAA,EAAEtqB,IAAEC,IAAI,IAAA,IAAQK,KAAKF,EAAE1T,QAAW,MAAH4T,IAAoB,mBAAHA,EAAcA,EAAEL,GAAGK,EAAE5T,QAAQuT,EAAAA,IAAK,OAAOC,EAAE+U,OAAMhV,GAAM,MAAHA,IAAa,MAAHA,OAAQ,EAAOA,EAAEuqB,YAAK,EAAOF,CAAC,CCA4G,IAAIrb,GAAEud,EAACn+B,cAAC,MAAyC,SAAS8R,KAAQH,IAAAA,EAAEwqB,EAACr7B,WAAC8f,IAAG,GAAO,OAAJjP,EAAS,CAAKC,IAAAA,EAAE,IAAI7Q,MAAM,iFAAiF,MAAMA,MAAMq7B,mBAAmBr7B,MAAMq7B,kBAAkBxqB,EAAEE,IAAGF,CAAC,CAAQD,OAAAA,CAAC,CAA/OiP,GAAEjgB,YAAY,qBAAo8B,IAAI0+B,GAAEd,IAA3S,SAAW5sB,EAAEC,GAAO0V,IAAAA,EAAEnU,EAACd,QAAGR,EPAnkChT,EAACiC,WAAC8Q,KOAwkCW,GAAG1T,EAAE,0BAA0ByoB,OAAO+V,GAAG1rB,EAAEI,EAAED,KAAI2oB,EAAEwE,GAAErtB,GAAK0tB,IAAA,IAAIvtB,EAAEwtB,SAAS1gC,IAAG,CAACA,EAAEkT,EAAEwtB,WAAW,IAAIttB,EAAEJ,IAAG,EAAGqmB,EAAE+D,EAACp8B,SAAC,KAAK,IAAIkS,EAAEkrB,KAAK2B,SAAS3sB,KAAI,CAACF,EAAEkrB,KAAKhrB,IAAIutB,EAAE,CAACrhC,IAAIs8B,KAAK1oB,EAAE5R,MAAMoS,GAAG1T,GAAG,OAAO4gC,KAAI,CAAC1C,SAASyC,EAAExC,WAAWK,EAAEJ,KAAK/E,EAAEgF,WAA/P,IAA4Q5xB,KAAKyG,EAAEzG,MAAM,eAAe,IAAYmzB,GAAEj+B,OAAOm7B,OAAO0D,GAAE,CAAA,GCAx8C,IAAO1tB,GAAHM,KAAGN,GAA4QM,IAAG,CAAA,GAAzQytB,MAAM,IAAI/tB,GAAEguB,MAAM,QAAQhuB,GAAEiuB,OAAO,SAASjuB,GAAEkuB,UAAU,YAAYluB,GAAEmuB,OAAO,SAASnuB,GAAEouB,UAAU,YAAYpuB,GAAEquB,QAAQ,UAAUruB,GAAEsuB,WAAW,aAAatuB,GAAEuuB,UAAU,YAAYvuB,GAAEwuB,KAAK,OAAOxuB,GAAEyuB,IAAI,MAAMzuB,GAAE0uB,OAAO,SAAS1uB,GAAE2uB,SAAS,WAAW3uB,GAAE4uB,IAAI,MAAM5uB,ICAzM,IAAIC,GAAEG,EAAAA,eAAE,SAAiC,SAAS8rB,IAAGz/B,MAAMyT,EAAEzR,SAAS6R,IAAWN,OAAAA,EAAE+sB,cAAc9sB,GAAE1R,SAAS,CAAC9B,MAAMyT,GAAGI,EAAE,QCA9L,cAAgB0Y,IAAI,WAAA+P,CAAY7oB,GAAQ2uB,QAAG1gB,KAAK2gB,QAAQ5uB,CAAC,CAAC,GAAAvO,CAAIuO,GAAOD,IAAAA,EAAE4uB,MAAMl9B,IAAIuO,GAAUD,YAAI,IAAJA,IAAaA,EAAEkO,KAAK2gB,QAAQ5uB,GAAGiO,KAAKF,IAAI/N,EAAED,IAAIA,CAAC,GCA1I,IAA2f/S,GAAE+hB,GAAE3O,GAA3fimB,GAAE13B,OAAO65B,eAA2GvoB,GAAE,CAACD,EAAED,EAAED,KAA7F,EAACE,EAAED,EAAED,KAAIC,KAAKC,EAAEqmB,GAAErmB,EAAED,EAAE,CAAC0oB,YAAW,EAAGC,cAAa,EAAGC,UAAS,EAAGp8B,MAAMuT,IAAIE,EAAED,GAAGD,CAAAA,EAAkBqsB,CAAEnsB,EAAqBD,EAAE,GAAKD,GAAGA,GAAGkP,GAAE,CAAChP,EAAED,EAAED,KAAQ,IAACC,EAAE8uB,IAAI7uB,GAAS,MAAA8uB,UAAU,UAAUhvB,EAAC,EAAOI,GAAE,CAACF,EAAED,EAAED,KAAKkP,GAAEhP,EAAED,EAAE,2BAA2BD,EAAEA,EAAEzB,KAAK2B,GAAGD,EAAEtO,IAAIuO,IAAIoqB,GAAE,CAACpqB,EAAED,EAAED,KAAK,GAAGC,EAAE8uB,IAAI7uB,GAAG,MAAM8uB,UAAU,qDAAqD/uB,aAAagvB,QAAQhvB,EAAE9G,IAAI+G,GAAGD,EAAEgO,IAAI/N,EAAEF,EAAC,EAAGwqB,GAAE,CAACtqB,EAAED,EAAED,EAAE8oB,KAAK5Z,GAAEhP,EAAED,EAAE,0BAAwCA,EAAEgO,IAAI/N,EAAEF,GAAGA,GAAsH,IAAAkvB,GAAA,MAAQ,WAAAnG,CAAY9oB,GAAKkvB,GAAAhhB,KAAKjhB,GAAE,IAAIo9B,GAAEnc,KAAKc,GAAE,IAAI1N,IAAE,IAAI,IAAIzM,OAAQq6B,GAAAhhB,KAAK7N,GAAE,IAAIxL,KAAOs6B,GAAAjhB,KAAK,cAAckf,MAAOgC,GAAAlhB,KAAKjhB,GAAE+S,EAAE,CAAC,OAAAkqB,GAAUhc,KAAKmhB,YAAYnF,SAAS,CAAC,SAAI7uB,GAAe8E,OAAAA,GAAE+N,KAAKjhB,GAAE,CAAC,SAAAqiC,CAAUtvB,EAAED,GAAG,IAAI8oB,EAAE,CAAC9hB,SAAS/G,EAAE9L,SAAS6L,EAAEtT,QAAQuT,EAAEG,GAAE+N,KAAKjhB,MAAYkT,OAAAA,GAAE+N,KAAK7N,IAAGnH,IAAI2vB,GAAG3a,KAAKmhB,YAAYn2B,KAAI,KAAKiH,GAAE+N,KAAK7N,IAAGjH,OAAOyvB,EAAC,GAAG,CAAC,EAAA0G,CAAGvvB,EAAED,GAAG,OAAOI,GAAE+N,KAAKc,IAAGtd,IAAIsO,GAAG9G,IAAI6G,GAAGmO,KAAKmhB,YAAYn2B,KAAI,KAAKiH,GAAE+N,KAAKc,IAAGtd,IAAIsO,GAAG5G,OAAO2G,EAAC,GAAG,CAAC,IAAAzE,CAAK0E,GAAG,IAAID,EAAEmO,KAAKte,OAAOuQ,GAAE+N,KAAKjhB,IAAG+S,GAAG,GAAGD,IAAII,GAAE+N,KAAKjhB,IAAG,CAAGmiC,GAAAlhB,KAAKjhB,GAAE8S,GAAG,IAAA,IAAQ8oB,KAAK1oB,GAAE+N,KAAK7N,IAAG,CAAC,IAAIorB,EAAE5C,EAAE9hB,SAAS5G,GAAE+N,KAAKjhB,KAAM47B,GAAAA,EAAEp8B,QAAQg/B,KAAK5C,EAAEp8B,QAAQg/B,EAAE5C,EAAE30B,SAASu3B,GAAG,CAAC,IAAA,IAAQ5C,KAAK1oB,GAAE+N,KAAKc,IAAGtd,IAAIsO,EAAEpN,MAAMi2B,EAAE1oB,GAAE+N,KAAKjhB,IAAG+S,EAAE,CAAC,GAA4C,SAASqnB,GAAEpnB,EAAED,GAAG,QAAOpR,OAAO4gC,GAAGvvB,EAAED,IAAgB,iBAAHC,GAAiB,OAAJA,GAAoB,iBAAHD,GAAiB,OAAJA,IAAY9J,MAAMkK,QAAQH,IAAI/J,MAAMkK,QAAQJ,GAAGC,EAAE/S,SAAS8S,EAAE9S,QAAUwoB,GAAEzV,EAAEjN,OAAOy8B,YAAYzvB,EAAEhN,OAAOy8B,aAAaxvB,aAAa8Y,KAAK/Y,aAAa+Y,KAAK9Y,aAAapL,KAAKmL,aAAanL,IAAIoL,EAAExJ,OAAOuJ,EAAEvJ,MAAQif,GAAEzV,EAAE2Q,UAAU5Q,EAAE4Q,cAAWpP,GAAEvB,KAAIuB,GAAExB,KAAG0V,GAAE9mB,OAAOgiB,QAAQ3Q,GAAGjN,OAAOy8B,YAAY7gC,OAAOgiB,QAAQ5Q,GAAGhN,OAAOy8B,aAAe,CAAC,SAAS/Z,GAAEzV,EAAED,GAAK,OAAA,CAAC,IAAID,EAAEE,EAAEyvB,OAAO7G,EAAE7oB,EAAE0vB,OAAO,GAAG3vB,EAAE4vB,MAAM9G,EAAE8G,KAAa,OAAA,EAAC,GAAG5vB,EAAE4vB,MAAM9G,EAAE8G,OAAO/gC,OAAO4gC,GAAGzvB,EAAEvT,MAAMq8B,EAAEr8B,eAAe,CAAU,CAAC,SAASgV,GAAEvB,GAAG,GAAuC,oBAApCrR,OAAOghC,UAAUjyB,SAASW,KAAK2B,GAA+B,OAAA,EAAKD,IAAAA,EAAEpR,OAAOua,eAAelJ,GAAG,OAAW,OAAJD,GAAqC,OAA3BpR,OAAOua,eAAenJ,EAAS,CAAjvB/S,OAAM4iC,QAAQ7gB,GAAM,IAAA6gB,QAAQxvB,GAAM,IAAAwvB,QCAxwC,IAAsT5vB,GAAlT+O,GAAEpgB,OAAO65B,eAA2GnC,GAAE,CAACtmB,EAAEqqB,EAAEpqB,KAA7F,EAACD,EAAEqqB,EAAEpqB,KAAIoqB,KAAKrqB,EAAEgP,GAAEhP,EAAEqqB,EAAE,CAAC3B,YAAW,EAAGC,cAAa,EAAGC,UAAS,EAAGp8B,MAAMyT,IAAID,EAAEqqB,GAAGpqB,CAAAA,EAAkBF,CAAEC,EAAY,iBAAHqqB,EAAYA,EAAE,GAAGA,EAAEpqB,GAAGA,GAA8IgrB,KAAGhrB,GAA4CgrB,IAAG,IAAzChrB,GAAE6vB,KAAK,GAAG,OAAO7vB,GAAEA,GAAE8vB,IAAI,GAAG,MAAM9vB,IAAW,IAAIuB,GAAE,CAAC,CAAC,CAAGxB,EAAEqqB,GAAOpqB,IAAAA,EAAEoqB,EAAE1pB,GAAGkoB,EAAE7oB,EAAEgwB,MAAM/iC,EAAE+S,EAAEgwB,MAAMz5B,QAAQ0J,GAAG,IAAU,IAAPhT,EAAO,CAAKkT,IAAAA,EAAEH,EAAEgwB,MAAM35B,QAAQ,OAAO8J,EAAEgqB,OAAOl9B,EAAE,GAAGkT,EAAEgU,KAAKlU,GAAG4oB,EAAE1oB,EAAE,IAAIH,EAAEgwB,MAAMnH,EAAE,CAAO,MAAA,IAAI7oB,EAAEgwB,MAAM,IAAIhwB,EAAEgwB,MAAM/vB,GAAG,EAAE,CAAC,CAAGD,EAAEqqB,GAAG,IAAIpqB,EAAEoqB,EAAE1pB,GAAGkoB,EAAE7oB,EAAEgwB,MAAMz5B,QAAQ0J,GAAM4oB,QAAAA,EAAc7oB,OAAAA,EAAM/S,IAAAA,EAAE+S,EAAEgwB,MAAM35B,QAAepJ,OAAAA,EAAEk9B,OAAOtB,EAAE,GAAG,IAAI7oB,EAAEgwB,MAAM/iC,EAAE,GAAEgjC,GAAC,MAAM5vB,UAAUqV,GAAE,WAAAoT,GAAc8F,SAASruB,WAAa2vB,GAAAhiB,KAAK,UAAU,CAACiG,KAAKlU,GAAGiO,KAAK5S,KAAK,CAAC1I,KAAK,EAAE+N,GAAGV,IAAIumB,IAAIvmB,GAAGiO,KAAK5S,KAAK,CAAC1I,KAAK,EAAE+N,GAAGV,MAAQiwB,GAAAhiB,KAAK,YAAY,CAACiiB,MAAM,CAAClwB,EAAE4oB,IAAI5oB,EAAE+vB,MAAM/vB,EAAE+vB,MAAM9iC,OAAO,KAAK27B,EAAEuH,QAAQ,CAACnwB,EAAE4oB,IAAI5oB,EAAE+vB,MAAMnzB,SAASgsB,IAAI,CAAC,UAAO,GAAM,OAAO,IAAIxoB,EAAE,CAAC2vB,MAAM,IAAI,CAAC,MAAApgC,CAAOqQ,EAAE4oB,GAAG,OAAO0B,GAAE1B,EAAEj2B,KAAK4O,GAAEvB,EAAE4oB,EAAE,GAAE,MAAMtnB,GAAE,IAAIkqB,IAAE,IAAIprB,GAAEgwB,0DCGn9BC,GAAAC,wCCQT,IAAI9yB,EAAQ+yB,IAIRC,EAAW,mBAAsB7hC,OAAO4gC,GAAK5gC,OAAO4gC,GAH/C,SAAGjuB,EAAGC,GACLD,OAAAA,IAAMC,IAAM,IAAMD,GAAK,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,CACxE,EAEEkvB,EAAuBjzB,EAAMizB,qBAC7Br8B,EAASoJ,EAAMpJ,OACfqlB,EAAYjc,EAAMic,UAClBzrB,EAAUwP,EAAMxP,QAChB0iC,EAAgBlzB,EAAMkzB,qBACxBC,GAAAC,iCAA2C,SACzCvB,EACAwB,EACAC,EACAhqB,EACAiqB,GAEI,IAAAC,EAAU58B,EAAO,MACjB,GAAA,OAAS48B,EAAQxkC,QAAS,CAC5B,IAAIykC,EAAO,CAAEC,UAAU,EAAI3kC,MAAO,MAClCykC,EAAQxkC,QAAUykC,CACtB,QAAgBD,EAAQxkC,QACZwkC,EAAAhjC,GACR,WACE,SAASmjC,EAAiBC,GACxB,IAAKC,EAAS,CAIR,GAHMA,GAAA,EACSC,EAAAF,EACnBA,EAAetqB,EAASsqB,QACpB,IAAWL,GAAWE,EAAKC,SAAU,CACvC,IAAIK,EAAmBN,EAAK1kC,MACxB,GAAAwkC,EAAQQ,EAAkBH,GAC5B,OAAQI,EAAoBD,CAC1C,CACU,OAAQC,EAAoBJ,CACtC,CAEQ,GADmBG,EAAAC,EACfhB,EAASc,EAAkBF,GAAsB,OAAAG,EACjD,IAAAE,EAAgB3qB,EAASsqB,GAC7B,YAAI,IAAWL,GAAWA,EAAQQ,EAAkBE,IAC1CH,EAAmBF,EAAeG,IACzBD,EAAAF,EACXI,EAAoBC,EACpC,CACM,IACEH,EACAE,EAFEH,GAAU,EAGZK,OACE,IAAWZ,EAAoB,KAAOA,EACnC,MAAA,CACL,WACS,OAAAK,EAAiBN,IACzB,EACD,OAASa,OACL,EACA,WACS,OAAAP,EAAiBO,IACtC,EAEK,GACD,CAACb,EAAaC,EAAmBhqB,EAAUiqB,IAEzC,IAAAxkC,EAAQkkC,EAAqBpB,EAAW2B,EAAQ,GAAIA,EAAQ,IASzD,OARPvX,GACE,WACEwX,EAAKC,UAAW,EAChBD,EAAK1kC,MAAQA,CACd,GACD,CAACA,IAEHmkC,EAAcnkC,GACPA,CACR,KDhFkBgkC,gBEHkK,SAASpD,GAAEptB,EAAEG,EAAEJ,EAAEM,IAAU2O,OAAAA,GAAAA,iCAAE/O,IAAEhT,GAAG+S,EAAEsvB,UAAUzG,GAAE57B,KAAIgT,IAAE,IAAID,EAAE3E,QAAO4E,IAAE,IAAID,EAAE3E,QAAO4E,GAAEE,GAAGJ,EAAE,CAAC,SAAS8oB,GAAE7oB,GAAUA,OAAAA,CAAC,CCA3E,SAAS6tB,GAAExtB,EAAEwoB,GAAG,IAAI5oB,EAAEsqB,EAAC9pB,QAAGV,EAAEumB,GAAE50B,IAAIm3B,IAAI57B,EAAEo9B,GAAGnqB,GAAEH,EAAEI,EAAC9S,aAAC2S,GAAG,CAACD,EAAE6xB,UAAUzB,MAAMnwB,EAAEC,GAAGF,EAAE6xB,UAAUxB,QAAQpwB,EAAEC,KAAI,CAACF,EAAEE,KAAK,OAAO+O,IAAE,KAAQ3O,GAAAA,EAASN,OAAAA,EAAE8xB,QAAQ1d,KAAKlU,GAAG,IAAIF,EAAE8xB,QAAQrL,IAAIvmB,EAAC,GAAG,CAACF,EAAEM,EAAEJ,MAAII,KAAEgqB,GAAEp9B,EAAO,CCA3L,IAAIiT,GAAE,IAAI6Y,IAAIwR,GAAM,IAAAxR,IAAI,SAASqT,GAAEnsB,GAAOD,IAAAA,EAAE,IAAID,EAAgB,OAAbC,EAAEuqB,GAAE74B,IAAIuO,IAAUD,EAAE,EAAE,OAAOuqB,GAAEvc,IAAI/N,EAAEF,EAAE,GAAO,IAAJA,IAAgBG,GAAE8N,IAAI/N,EAAE,CAAC,cAAcA,EAAE6xB,aAAa,eAAeC,MAAM9xB,EAAE8xB,QAAQ9xB,EAAE+xB,aAAa,cAAc,QAAQ/xB,EAAE8xB,OAAM,GAA5H,IAAItF,GAAExsB,EAAkI,CAAC,SAASwsB,GAAExsB,GAAOhT,IAAAA,EAAE,IAAI8S,EAAgB,OAAb9S,EAAEs9B,GAAE74B,IAAIuO,IAAUhT,EAAE,EAAE,GAAO,IAAJ8S,EAAMwqB,GAAEnxB,OAAO6G,GAAGsqB,GAAEvc,IAAI/N,EAAEF,EAAE,GAAO,IAAJA,EAAM,OAAWC,IAAAA,EAAEE,GAAExO,IAAIuO,GAAGD,IAAuB,OAAnBA,EAAE,eAAsBC,EAAEgyB,gBAAgB,eAAehyB,EAAE+xB,aAAa,cAAchyB,EAAE,gBAAgBC,EAAE8xB,MAAM/xB,EAAE+xB,MAAM7xB,GAAE9G,OAAO6G,GAAG,CCA3iB,IAAIC,GAAE,CAAC,yBAAyB,aAAa,UAAU,aAAa,yBAAyB,SAAS,wBAAwB,yBAAyB,4BAA4BnT,KAAIiT,GAAG,GAAGA,2BAA0BtN,KAAK,KAAKk5B,GAAE,CAAC,oBAAoB7+B,KAAIiT,GAAG,GAAGA,2BAA0BtN,KAAK,KAAK,IAAyM2N,GAAlMF,GAAHosB,KAAGpsB,GAAwLosB,IAAG,CAAE,GAAvLpsB,GAAE+xB,MAAM,GAAG,QAAQ/xB,GAAEA,GAAEgyB,SAAS,GAAG,WAAWhyB,GAAEA,GAAEiyB,KAAK,GAAG,OAAOjyB,GAAEA,GAAEkyB,KAAK,GAAG,OAAOlyB,GAAEA,GAAEmyB,WAAW,IAAI,aAAanyB,GAAEA,GAAEoyB,SAAS,IAAI,WAAWpyB,GAAEA,GAAEqyB,UAAU,IAAI,YAAYryB,IAAWqB,KAAGnB,GAA8GmB,IAAG,IAA3GnB,GAAElR,MAAM,GAAG,QAAQkR,GAAEA,GAAEoyB,SAAS,GAAG,WAAWpyB,GAAEA,GAAEqyB,QAAQ,GAAG,UAAUryB,GAAEA,GAAEsyB,UAAU,GAAG,YAAYtyB,IAAW+sB,IAAGntB,IAAIA,EAAEA,EAAEkyB,UAAS,GAAI,WAAWlyB,EAAEA,EAAEmyB,KAAK,GAAG,OAAOnyB,IAAImtB,IAAG,CAAE,GAA0W,IAAIhB,IAAGnsB,IAAIA,EAAEA,EAAE2yB,OAAO,GAAG,SAAS3yB,EAAEA,EAAE4yB,MAAM,GAAG,QAAQ5yB,IAAImsB,IAAG,CAAE,GAAgT,IAAIS,IAAG5sB,IAAIA,EAAEA,EAAE6yB,SAAS,GAAG,WAAW7yB,EAAEA,EAAE8yB,MAAM,GAAG,QAAQ9yB,IAAI4sB,IAAG,CAAE,GAA0Y,SAASgB,GAAE7tB,GAAM,MAAHA,GAASA,EAAEgzB,MAAM,CAACC,eAAc,GAAI,CAA3a,oBAARt/B,QAAsC,oBAAVkC,WAAwBA,SAAS0B,iBAAiB,WAAUyI,IAAIA,EAAEkzB,SAASlzB,EAAEmzB,QAAQnzB,EAAEozB,UAAUv9B,SAASuQ,gBAAgBitB,QAAQC,uBAAuB,GAAA,IAAK,GAAIz9B,SAAS0B,iBAAiB,SAAQyI,IAAe,IAAXA,EAAEpG,cAAkB/D,SAASuQ,gBAAgBitB,QAAQC,uBAAkC,IAAXtzB,EAAEpG,SAAa/D,SAASuQ,gBAAgBitB,QAAQC,uBAAuB,GAAA,IAAK,IAAwD,IAAI5G,GAAE,CAAC,WAAW,SAASh6B,KAAK,KAAkX,SAAS85B,GAAExsB,EAAED,GAAGwzB,OAAOtzB,GAAE,EAAGuzB,WAAW/H,EAAE,KAAKgI,aAAapzB,EAAE,IAAI,CAAA,GAAI,IAAIgqB,EAAEn0B,MAAMkK,QAAQJ,GAAGA,EAAE9S,OAAO,EAAE8S,EAAE,GAAGrK,cAAcE,SAASmK,EAAErK,cAAc40B,EAAEr0B,MAAMkK,QAAQJ,GAAGC,EAAlb,SAAWD,EAAED,EAAEE,GAAGA,GAAG,OAAOD,EAAE3J,QAAQsf,MAAK,CAAC1V,EAAEwrB,KAAK,IAAIprB,EAAEN,EAAEE,GAAGoqB,EAAEtqB,EAAE0rB,GAAG,GAAO,OAAJprB,GAAc,OAAJgqB,EAAgB,OAAA,EAAME,IAAAA,EAAElqB,EAAEqzB,wBAAwBrJ,GAAG,OAAOE,EAAEh3B,KAAKogC,+BAA+BpJ,EAAEh3B,KAAKqgC,4BAA4B,EAAE,CAAA,GAAG,CAAwNjI,CAAE3rB,GAAGA,EAAI,GAAFD,EAAtmD,SAAWC,EAAEnK,SAASkD,MAAaiH,OAAG,MAAHA,EAAQ,GAAG9J,MAAMC,KAAK6J,EAAE6zB,iBAAiBjI,KAAIjW,MAAK,CAAC5V,EAAEE,IAAIgB,KAAK6yB,MAAM/zB,EAAEg0B,UAAUjqB,OAAOkqB,mBAAmB/zB,EAAE8zB,UAAUjqB,OAAOkqB,oBAAmB,CAAw7CvJ,CAAEzqB,GAAjyD,SAAWA,EAAEnK,SAASkD,MAAaiH,OAAG,MAAHA,EAAQ,GAAG9J,MAAMC,KAAK6J,EAAE6zB,iBAAiB3zB,KAAIyV,MAAK,CAAC5V,EAAEE,IAAIgB,KAAK6yB,MAAM/zB,EAAEg0B,UAAUjqB,OAAOkqB,mBAAmB/zB,EAAE8zB,UAAUjqB,OAAOkqB,oBAAmB,CAAinD/kB,CAAEjP,GAAGK,EAAEnT,OAAO,GAAGq9B,EAAEr9B,OAAO,IAAIq9B,EAAEA,EAAE/3B,QAAOq2B,IAAIxoB,EAAEtI,MAAKiX,GAAM,MAAHA,GAAS,YAAYA,GAAM,MAAHA,OAAQ,EAAOA,EAAEviB,WAAWo8B,EAAE7Z,IAAI6Z,OAAK4C,EAAK,MAAHA,EAAQA,EAAEpB,EAAE4J,cAAc,IAAsYhnC,EAAlYkT,QAAWJ,GAAE,EAAFA,EAAW,OAAA,EAAKA,GAAE,GAAFA,WAAoB,MAAA,IAAI5Q,MAAM,gEAAgE,KAAKoS,EAAAA,MAAWxB,GAAE,EAAFA,EAAW,OAAA,EAAKA,GAAE,EAAFA,EAAI,OAAOkB,KAAKC,IAAI,EAAEqpB,EAAEh0B,QAAQk1B,IAAI,EAAK1rB,GAAE,EAAFA,EAAI,OAAOkB,KAAKC,IAAI,EAAEqpB,EAAEh0B,QAAQk1B,IAAI,EAAE,GAAK,EAAF1rB,EAAWwqB,OAAAA,EAAEr9B,OAAO,EAAQ,MAAA,IAAIiC,MAAM,gEAAiE,EAApNoS,GAAwNwqB,EAAI,GAAFhsB,EAAK,CAACkzB,eAAc,GAAI,CAAA,EAAGxG,EAAE,EAAE/W,EAAE6U,EAAEr9B,OAAW,EAAA,CAAC,GAAGu/B,GAAG/W,GAAG+W,EAAE/W,GAAG,EAAS,OAAA,EAAE,IAAImT,EAAEtnB,EAAEkrB,EAAE,GAAK,GAAF1sB,EAAK8oB,GAAGA,EAAEnT,GAAGA,MAAM,CAAImT,GAAAA,EAAE,EAAS,OAAA,EAAKA,GAAAA,GAAGnT,EAAS,OAAA,CAAC,CAACzoB,EAAEs9B,EAAE1B,GAAM,MAAH57B,GAASA,EAAE+lC,MAAMjH,GAAGU,GAAGtsB,CAAC,OAAOlT,IAAIo9B,EAAE4J,eAAe,OAAS,EAAFl0B,GAAhuC,SAAWC,GAAG,IAAID,EAAEE,EAAE,OAAiE,OAA1DA,EAAgC,OAA7BF,EAAK,MAAHC,OAAQ,EAAOA,EAAEgH,cAAe,EAAOjH,EAAEzB,KAAK0B,EAAE0sB,MAAUzsB,CAAI,CAAqoCwtB,CAAExgC,IAAIA,EAAEinC,SAAS,CAAC,CCArzG,SAASj0B,KAAI,MAAM,WAAW5N,KAAKsB,OAAOwgC,UAAU5vB,WAAW,QAAQlS,KAAKsB,OAAOwgC,UAAU5vB,WAAW5Q,OAAOwgC,UAAUC,eAAe,CAAC,CAAiE,SAASj0B,KAAWF,OAAAA,MAAjE,YAAY5N,KAAKsB,OAAOwgC,UAAUE,UAAuC,CCA7I,SAASpnC,GAAEgT,EAAED,EAAEK,EAAEF,GAAOoqB,IAAAA,EAAEvb,GAAE3O,GAAGgqB,EAAC3Q,WAAC,KAAK,GAAIzZ,EAA2C,OAAApK,SAAS0B,iBAAiByI,EAAED,EAAEI,GAAG,IAAItK,SAASyB,oBAAoB0I,EAAED,EAAEI,GAAxG,SAASJ,EAAE0sB,GAAGlC,EAAE99B,QAAQggC,EAAE,CAA+E,GAAG,CAACxsB,EAAED,EAAEG,GAAG,CCAvK,SAAS0oB,GAAE5oB,EAAED,EAAEK,EAAEF,GAAOlT,IAAAA,EAAEiT,GAAEG,GAAG2O,EAAC0K,WAAC,KAAK,GAAIzZ,EAA2C,OAAAtM,OAAO4D,iBAAiByI,EAAED,EAAEI,GAAG,IAAIxM,OAAO2D,oBAAoB0I,EAAED,EAAEI,GAApG,SAASJ,EAAE2V,GAAGzoB,EAAER,QAAQipB,EAAE,CAA2E,GAAG,CAACzV,EAAED,EAAEG,GAAG,CCA+I,SAAS8qB,GAAE5qB,EAAEH,EAAEksB,GAAOK,IAAAA,EAAEzB,GAAEoB,GAAGvD,EAAE0D,EAACl/B,aAAC,SAAS2S,EAAEqqB,GAAG,GAAGrqB,EAAE3T,iBAAiB,OAAW0T,IAAAA,EAAEsqB,EAAErqB,GAAMD,GAAI,OAAJA,IAAWA,EAAEu0B,cAAcr8B,SAAS8H,KAAKA,EAAEw0B,YAAY,OAAWxI,IAAAA,EAAE,SAASxB,EAAEpqB,GAAG,MAAiB,mBAAHA,EAAcoqB,EAAEpqB,KAAKjK,MAAMkK,QAAQD,IAAIA,aAAatL,IAAIsL,EAAE,CAACA,EAAE,CAAzF,CAA2FD,GAAG,IAAA,IAAQqqB,KAAKwB,EAAE,GAAO,OAAJxB,IAAWA,EAAEtyB,SAAS8H,IAAIC,EAAEw0B,UAAUx0B,EAAEy0B,eAAe53B,SAAS0tB,IAAI,OAAO,OJAsgB,SAAWvqB,EAAED,EAAE,GAAOE,IAAAA,EAAE,OAAOD,KAAe,OAATC,EAAEotB,GAAErtB,SAAU,EAAOC,EAAElH,OAASiyB,GAAEjrB,EAAE,CAAC,EAAK,IAAQC,EAAEgH,QAAQ9G,IAAI,CAAC,GAAK,IAAIurB,EAAEzrB,EAAE,KAAS,OAAJyrB,GAAU,CAAC,GAAGA,EAAEzkB,QAAQ9G,IAAS,OAAA,EAAGurB,EAAEA,EAAEiJ,aAAa,CAAO,OAAA,CAAE,GAAG,CIAxrBlzB,CAAEzB,EAAEysB,GAAEqG,SAAqB,IAAb9yB,EAAEg0B,UAAe/zB,EAAElH,iBAAiB2zB,EAAEhgC,QAAQuT,EAAED,EAAE,GAAE,CAAC0sB,EAAEvsB,IAAIjT,EAAEogC,EAAAA,OAAE,MAAQhtB,GAAAA,EAAE,eAAcJ,IAAI,IAAID,EAAEqqB,EAAE/D,OAAMr5B,EAAER,SAAwD,OAA9C49B,EAAsB,OAAnBrqB,EAAEC,EAAEw0B,mBAAoB,EAAOz0B,EAAE1B,KAAK2B,SAAU,EAAOoqB,EAAE,KAAKpqB,EAAEnM,OAAA,IAAS,GAAIkb,GAAE3O,EAAE,aAAYJ,IAAI,GAAGqmB,OAAMr5B,EAAER,QAAQ,OAAO,IAAIuT,EAAE/S,EAAER,QAAQ,OAAOQ,EAAER,QAAQ,KAAKo8B,EAAE5oB,GAAE,IAAID,GAAC,IAAG,GAAQyrB,IAAAA,EAAE4B,EAACh5B,OAAC,CAACkN,EAAE,EAAEC,EAAE,IAAMnB,GAAAA,EAAE,cAAaJ,IAAIwrB,EAAEh/B,QAAQ8U,EAAEtB,EAAE00B,QAAQ,GAAGhP,QAAQ8F,EAAEh/B,QAAQ+U,EAAEvB,EAAE00B,QAAQ,GAAG/O,OAAA,IAAS,GAAI5W,GAAE3O,EAAE,YAAWJ,IAAI,IAAID,EAAKC,EAAE20B,eAAe,GAAGjP,QAAzB3lB,EAAmCC,EAAE20B,eAAe,GAAGhP,QAAS,KAAK3kB,KAAKglB,IAAIjmB,EAAIyrB,EAAEh/B,QAAQ8U,IAA53B,IAAm4BN,KAAKglB,IAAIjmB,EAAIyrB,EAAEh/B,QAAQ+U,IAA15B,IAAi6B,OAAOqnB,EAAE5oB,GAAE,IAAI40B,GAAqB50B,EAAEnM,QAAQmM,EAAEnM,OAAO,MAAI,IAAG,GAAIyN,GAAElB,EAAE,QAAOJ,GAAG4oB,EAAE5oB,GAAE,IjBAppC,SAAWD,GAAG,OAAOG,GAAEH,IAAiB,WAAbA,EAAEgG,QAAmB,CiBAwmC8uB,CAAsBnhC,OAAOkC,SAASo+B,eAAetgC,OAAOkC,SAASo+B,cAAc,SAAM,EAAG,CCAx4C,SAAS9zB,MAAKH,GAAUC,OAAAA,WAAE,IAAII,MAAKL,IAAG,IAAIA,GAAG,CCAzC,SAASqtB,GAAEltB,EAAEH,EAAEgP,EAAE/O,GAAOhT,IAAAA,EAAE47B,GAAE7Z,GAAG0G,EAAAA,WAAE,KAAwB,SAAS3V,EAAEM,GAAGpT,EAAER,QAAQ4T,EAAE,CAAQF,OAArDA,EAAK,MAAHA,EAAQA,EAAExM,QAA2C4D,iBAAiByI,EAAED,EAAEE,GAAG,IAAIE,EAAE7I,oBAAoB0I,EAAED,EAAEE,EAAC,GAAG,CAACE,EAAEH,EAAEC,GAAG,CCAxP,SAASyV,KAAQ3V,IAAAA,EAAE,MAAM,CAAC,MAAAg1B,EAAQC,IAAIh1B,IAAQyrB,IAAAA,EAAMprB,IAAAA,EAAEL,EAAEoG,gBAAgBnG,EAAqB,OAAlBwrB,EAAEzrB,EAAEtD,aAAmB+uB,EAAE93B,OAAOoM,EAAEkB,KAAKC,IAAI,EAAEjB,EAAEg1B,WAAW50B,EAAEyL,YAAY,EAAE,KAAAopB,EAAOF,IAAIh1B,EAAE0V,EAAErV,IAAI,IAAIJ,EAAED,EAAEoG,gBAAgBqlB,EAAExqB,KAAKC,IAAI,EAAEjB,EAAE6L,YAAY7L,EAAEsJ,aAAapJ,EAAEc,KAAKC,IAAI,EAAEnB,EAAE0rB,GAAGprB,EAAErH,MAAMiH,EAAE,eAAe,GAAGE,MAAM,EAAE,CCAlJ,SAASusB,KAAWpG,OAAAA,KAAI,CAAC,MAAAyO,EAAQC,IAAI70B,EAAEuV,EAAE+V,EAAE0J,KAAKj1B,IAAI,SAASjT,EAAE+hB,GAAG,OAAO9O,EAAEk1B,WAAW9K,SAAQvqB,GAAGA,MAAKhI,MAAKgI,GAAGA,EAAE9H,SAAS+W,IAAG,CAACyc,EAAE5B,WAAU,KAASQ,IAAAA,EAAE,GAA+D,SAA5D12B,OAAOyJ,iBAAiB+C,EAAEiG,iBAAiBivB,eAAwB,CAAC,IAAIp1B,EAAEsqB,KAAItqB,EAAEjH,MAAMmH,EAAEiG,gBAAgB,iBAAiB,QAAQqlB,EAAEvyB,KAAI,IAAIuyB,EAAE5B,WAAU,IAAI5pB,EAAEiqB,aAAW,CAAKlb,IAAAA,EAAsB,OAAnBqb,EAAE12B,OAAOsU,SAAeoiB,EAAE12B,OAAO2hC,YAAYv1B,EAAE,KAAK0rB,EAAEl0B,iBAAiB4I,EAAE,SAAQF,IAAI,GAAGs1B,GAAqBt1B,EAAEnM,QAAW,IAAC,IAAIkM,EAAEC,EAAEnM,OAAO0hC,QAAQ,KAAK,IAAIx1B,EAAE,OAAO,IAAIy1B,KAAKhJ,GAAG,IAAIiJ,IAAI11B,EAAE21B,MAAM9M,EAAE1oB,EAAEy1B,cAAcnJ,GAAG8I,GAAqB1M,KAAK57B,EAAE47B,KAAK9oB,EAAE8oB,EAAE,CAAM,MAAA,KAAI,GAAI4C,EAAEl0B,iBAAiB4I,EAAE,cAAaF,IAAI,GAAGs1B,GAAqBt1B,EAAEnM,SrBA3lB,SAAWkM,GAAUC,OAAAA,GAAED,IAAI,UAAUA,CAAC,CqBA8jB61B,CAAiB51B,EAAEnM,QAAW7G,GAAAA,EAAEgT,EAAEnM,QAAQ,CAAC,IAAIkM,EAAEC,EAAEnM,OAAYkM,KAAAA,EAAE00B,eAAeznC,EAAE+S,EAAE00B,gBAAgB10B,EAAEA,EAAE00B,cAAcjJ,EAAEzyB,MAAMgH,EAAE,qBAAqB,UAAU,MAAMyrB,EAAEzyB,MAAMiH,EAAEnM,OAAO,cAAc,OAAM,IAAI23B,EAAEl0B,iBAAiB4I,EAAE,aAAYF,IAAOs1B,GAAAA,GAAqBt1B,EAAEnM,QAAQ,CAAIgiC,GrBAryB,SAAW91B,GAAG,OAAOG,GAAEH,IAAiB,UAAbA,EAAEgG,QAAkB,CqBAsvB8vB,CAAqB71B,EAAEnM,QAAQ,OAAU7G,GAAAA,EAAEgT,EAAEnM,QAAQ,CAAC,IAAIkM,EAAEC,EAAEnM,OAAO,KAAKkM,EAAE00B,eAA4C,KAA7B10B,EAAEqzB,QAAQ0C,oBAAyB/1B,EAAEmM,aAAanM,EAAE+L,cAAc/L,EAAEkM,YAAYlM,EAAE8L,cAAc9L,EAAEA,EAAE00B,cAA2C,KAA7B10B,EAAEqzB,QAAQ0C,kBAAuB91B,EAAEnH,gBAAgB,MAAMmH,EAAEnH,gBAAgB,IAAG,CAACgX,SAAQ,IAAK2b,EAAEvyB,KAAI,KAAS8G,IAAAA,EAAE,IAAIC,EAAsB,OAAnBD,EAAErM,OAAOsU,SAAejI,EAAErM,OAAO2hC,YAAYtmB,IAAI/O,GAAGtM,OAAOqiC,SAAS,EAAEhnB,GAAGjP,GAAGA,EAAEw0B,cAAcx0B,EAAEk2B,eAAe,CAACC,MAAM,YAAYn2B,EAAE,KAAA,GAAM,GAAG,GAAG,CAAE,CAAA,CCA9uC,SAAS0sB,GAAEzsB,GAAG,IAAIG,EAAE,CAAE,EAAC,IAAA,IAAQF,KAAKD,EAAEpR,OAAOm7B,OAAO5pB,EAAEF,EAAEE,IAAWA,OAAAA,CAAC,CAAC,IAAI6O,GCA3W,SAAW3O,EAAEN,GAAG,IAAIE,EAAEI,IAAIF,EAAM,IAAAtL,IAAI,MAAM,CAACi8B,YAAa,IAAQ7wB,EAAGqvB,UAAUtvB,IAAUG,EAAEjH,IAAI8G,GAAG,IAAIG,EAAE/G,OAAO4G,IAAI,QAAAm2B,CAASn2B,KAAK6oB,GAAG,IAAI57B,EAAE8S,EAAEC,GAAG1B,KAAK2B,KAAK4oB,GAAG57B,IAAIgT,EAAEhT,EAAEkT,EAAE0P,SAAQwa,GAAGA,MAAK,EAAE,CDAuLp9B,EAAE,IAAI,IAAI8rB,KAAI,CAAC,IAAAqd,CAAKp2B,EAAEG,GAAOE,IAAAA,EAAE,IAAIJ,EAAmB,OAAhBI,EAAE6N,KAAKxc,IAAIsO,IAAUK,EAAE,CAAC20B,IAAIh1B,EAAEjP,MAAM,EAAE2kB,EAAEmT,KAAIsM,SAAStgC,KAAYoL,OAAAA,EAAElP,QAAQkP,EAAEk1B,KAAKj8B,IAAIiH,GAAG+N,KAAKF,IAAIhO,EAAEC,GAAGiO,IAAI,EAAE,GAAAmoB,CAAIr2B,EAAEG,GAAOF,IAAAA,EAAEiO,KAAKxc,IAAIsO,GAAG,OAAOC,IAAIA,EAAElP,QAAQkP,EAAEk1B,KAAK/7B,OAAO+G,IAAI+N,IAAI,EAAE,cAAAooB,EAAgBtB,IAAIh1B,EAAE0V,EAAEvV,EAAEg1B,KAAKl1B,IAAI,IAAII,EAAE,CAAC20B,IAAIh1B,EAAE0V,EAAEvV,EAAEg1B,KAAK1I,GAAExsB,IAAIoqB,EAAE,CAAC3U,KAAI+V,KEA3nB,CAAC,MAAAsJ,EAAQC,IAAIh1B,EAAE0V,EAAErV,IAAIA,EAAErH,MAAMgH,EAAEoG,gBAAgB,WAAW,SAAS,IFAikBikB,EAAExa,SAAQ,EAAEklB,OAAOh1B,KAAQ,MAAHA,OAAQ,EAAOA,EAAEM,KAAIgqB,EAAExa,SAAQ,EAAEqlB,MAAMn1B,KAAQ,MAAHA,OAAQ,EAAOA,EAAEM,IAAG,EAAE,YAAAk2B,EAAc7gB,EAAE1V,IAAIA,EAAEkqB,SAAS,EAAE,QAAAsM,EAAUxB,IAAIh1B,IAAIkO,KAAK9U,OAAO4G,EAAE,IGA3oB,SAASgP,GAAEjP,EAAEC,EAAEG,EAAE,MAAMi1B,WAAW,MAAM,IAAIl1B,ECAtK,SAAWD,GAAG,OAAOD,uBAAEC,EAAEqvB,UAAUrvB,EAAE6wB,YAAY7wB,EAAE6wB,YAAY,CDAyGjI,CAAE5oB,IAAGI,EAAEL,EAAEE,EAAExO,IAAIsO,QAAG,EAAO/S,IAAEoT,GAAEA,EAAEtP,MAAM,EAAK,OAAOw5B,IAAE,KAAK,GAAMvqB,GAAID,SAAUE,GAAEk2B,SAAS,OAAOn2B,EAAEG,GAAG,IAAIF,GAAEk2B,SAAS,MAAMn2B,EAAEG,EAAC,GAAG,CAACJ,EAAEC,IAAI/S,CAAC,CHA2d+hB,GAAEsgB,WAAU,KAAK,IAAItvB,EAAEgP,GAAE8hB,cAAc3wB,EAAM,IAAA4Y,IAAW,IAAA,IAAC9Y,KAAKD,EAAEG,EAAE6N,IAAI/N,EAAEA,EAAEmG,gBAAgBpN,MAAMyG,UAAkBQ,IAAAA,IAAAA,KAAKD,EAAEnR,SAAS,CAAKwR,IAAAA,EAAiB,WAAfF,EAAEzO,IAAIuO,EAAE+0B,KAAgB3K,EAAY,IAAVpqB,EAAElP,OAAWs5B,IAAIhqB,IAAIgqB,GAAGhqB,IAAI2O,GAAEmnB,SAASl2B,EAAElP,MAAM,EAAE,iBAAiB,eAAekP,GAAa,IAAVA,EAAElP,OAAWie,GAAEmnB,SAAS,WAAWl2B,EAAE,SKA9kCssB,GAAEtd,GAAuR,oBAATwnB,SAAyC,oBAAZ7gC,YAAyC,oBAATyQ,SAA0F,UAA7B,OAArCkmB,GAAW,MAATkK,aAAc,WAA0B,EAAOlK,GAAY,gBAA8F,KAA9B,OAA3Ctd,GAAW,MAAT5I,aAAc,EAAOA,QAAQupB,gBAAiB,EAAO3gB,GAAEynB,iBAA8BrwB,QAAQupB,UAAU8G,cAAc,WAAW,OAAO93B,QAAQC,KAAK,CAAC,+EAA+E,0FAA0F,GAAG,iBAAiB,QAAQ,0DAA0D,sBAAsB,OAAOnM,KAAK,OAC/2B,EAAE,GAAG,IAAIs4B,GAAG,CAAAjrB,IAAIA,EAAEA,EAAE2qB,KAAK,GAAG,OAAO3qB,EAAEA,EAAE42B,OAAO,GAAG,SAAS52B,EAAEA,EAAEguB,MAAM,GAAG,QAAQhuB,EAAEA,EAAE62B,MAAM,GAAG,QAAQ72B,GAAxF,CAA4FirB,IAAG,CAAA,GAAI,SAAS2B,GAAE1sB,GAAG,IAAIE,EAAE,CAAC,EAAUH,IAAAA,IAAAA,KAAKC,GAAS,IAAPA,EAAED,KAAUG,EAAE,QAAQH,KAAK,IAAWG,OAAAA,CAAC,CAAC,SAASoB,GAAEtB,EAAEE,EAAEH,EAAE/S,GAAG,IAAI8S,EAAEM,GAAG+sB,EAAAA,SAAEptB,IAAI62B,QAAQhO,EAAEiO,QAAQ9nB,EAAE+nB,WAAWtL,GCDjN,SAAWlB,EAAE,GAAG,IAAItqB,EAAEwrB,GAAGxc,EAAAA,SAAEsb,GAAGiC,EAAEzsB,eAAEC,GAAGyrB,EAAEzrB,IAAG,CAACC,IAAI4oB,EAAE9oB,EAAC1S,aAAC2S,GAAGyrB,GAAEzc,GAAGA,EAAEhP,KAAG,CAACC,IAAIwsB,EAAE1sB,eAAEC,IAAIC,EAAED,KAAKA,GAAE,CAACC,IAAIE,EAAEJ,eAAEC,GAAGyrB,GAAEzc,GAAGA,GAAGhP,KAAG,CAACyrB,IAAIG,EAAE7rB,EAAC1S,aAAC2S,GAAGyrB,GAAEzc,GAAGA,EAAEhP,KAAG,CAACyrB,IAAI,MAAM,CAACuL,MAAM/2B,EAAEg3B,QAAQzK,EAAEsK,QAAQjO,EAAEgO,QAAQpK,EAAEsK,WAAW52B,EAAE+2B,WAAWtL,EAAE,CDCKpqB,CAAEvB,GAAGF,EAAE,EAAE,GAAGwqB,EAAEF,EAAAh2B,QAAE,GAAI6L,EAAEmqB,EAAAh2B,QAAE,GAAIg5B,EAAEb,KAAI,OAAO3B,IAAE,KAASnV,IAAAA,EAAE,GAAGzV,EAAG,OAAGD,GAAGK,GAAE,GAAKF,GAAoD,OAA3BuV,EAAK,MAAHzoB,OAAQ,EAAOA,EAAE8U,QAAc2T,EAAEpX,KAAKrR,EAAE+S,GAAse,SAAWC,GAAGk3B,QAAQh3B,EAAEi3B,IAAIp3B,EAAE2vB,KAAK1iC,EAAEoqC,SAASt3B,IAAI,IAAIM,EAAEosB,KAAW,OAAgZ,SAAWxsB,GAAGo3B,SAASl3B,EAAEg3B,QAAQn3B,IAAOG,GAAG,MAAHA,GAASA,EAAE1T,QAAa,YAAFuT,IAAa/S,IAAAA,EAAEgT,EAAEjH,MAAMs+B,WAAar3B,EAAAjH,MAAMs+B,WAAW,OAAOt3B,IAAmBC,EAAEjH,MAAMs+B,WAAWrqC,CAAC,CAAjjBo6B,CAAEpnB,EAAE,CAACk3B,QAAQh3B,EAAEk3B,SAASt3B,IAAIM,EAAEupB,WAAU,KAAO5pB,IAAEK,EAAEmR,uBAAsB,KAAKnR,EAAEnH,IAA0B,SAAW+G,EAAEE,GAAG,IAAIE,EAAEwoB,EAAE,IAAI7oB,EAAEysB,KAAO,IAACxsB,EAAE,OAAOD,EAAEkqB,QAAQ,IAAIj9B,GAAE,EAAG+S,EAAE9G,KAAI,KAAOjM,GAAA,CAAA,IAAK,IAAI8S,EAAwF,OAArF8oB,EAAuB,OAApBxoB,EAAEJ,EAAEy2B,oBAAqB,EAAOr2B,EAAE/B,KAAK2B,GAAGzN,QAAUwc,GAAAA,aAAauoB,iBAAsB1O,EAAE,GAAG,OAAkB,IAAX9oB,EAAE7S,QAAYiT,IAAIH,EAAEkqB,UAAUT,QAAQ+N,WAAWz3B,EAAEhT,KAAOiiB,GAAAA,EAAEyoB,YAAWpY,MAAK,KAAKpyB,GAAGkT,GAAE,IAAIH,EAAEkqB,QAAQ,CAA3T6B,CAAE9rB,EAAEhT,GAAE,GAAE,IAAIoT,EAAE6pB,OAAO,CAA/oB+B,CAAE9rB,EAAE,CAACk3B,SAAS9M,EAAE,OAAA4M,GAAYj3B,EAAAzT,QAAQyT,EAAEzT,SAAQ,EAAGyT,EAAEzT,QAAQ89B,EAAE99B,QAAQ89B,EAAE99B,SAAQ,GAAIyT,EAAEzT,UAAUuT,GAAGgP,EAAE,GAAGyc,EAAE,KAAKzc,EAAE,GAAGyc,EAAE,MAAM,GAAA2L,GAAMl3B,EAAEzT,QAAQuT,GAAGyrB,EAAE,GAAGzc,EAAE,KAAKyc,EAAE,GAAGzc,EAAE,IAAIhP,EAAEyrB,EAAE,GAAGzc,EAAE,IAAI,IAAA2gB,GAAWrJ,IAAAA,EAAEpmB,EAAEzT,SAAiC,mBAAjB0T,EAAEu2B,eAA2Bv2B,EAAEu2B,gBAAgBxpC,OAAO,IAAIq9B,EAAE99B,SAAQ,EAAGg/B,EAAE,GAAGzrB,GAAGK,GAAE,GAA8B,OAAzBimB,EAAK,MAAHr5B,OAAQ,EAAOA,EAAE+U,MAAYskB,EAAEhoB,KAAKrR,EAAE+S,GAAC,UAAnZA,GAAGgP,EAAE,GAAkZ,GAAI,CAAC/O,EAAED,EAAEG,EAAEktB,IAAIptB,EAAE,CAACF,EAAE,CAAC23B,OAAO7O,EAAE,GAAG8O,MAAM9O,EAAE,GAAG+O,MAAM/O,EAAE,GAAGyO,WAAWzO,EAAE,IAAIA,EAAE,KAAK,CAAC7oB,EAAE,CAAC03B,YAAO,EAAOC,WAAM,EAAOC,WAAM,EAAON,gBAAW,GAAQ,CED/xB,SAAS7K,GAAElC,EAAEtqB,GAAOD,IAAAA,EAAE6oB,EAAAA,OAAE,IAAI9oB,EAAE9S,GAAEs9B,GAAGrqB,aAAE,KAAK,IAAIG,EAAE,IAAIL,EAAEvT,SAAS,IAAA,IAAQuiB,EAAEyc,KAAKxrB,EAAE2Q,UAAU,GAAG5Q,EAAEvT,QAAQuiB,KAAKyc,EAAE,CAAKtrB,IAAAA,EAAEJ,EAAEE,EAAEI,GAAUL,OAAAA,EAAEvT,QAAQwT,EAAEE,CAAC,IAAG,CAACJ,KAAKE,GAAG,CCA3L,IAAIE,GAAEsrB,EAACr9B,cAAC,MAAM+R,GAAEpR,YAAY,oBAAoB,IAAI9B,GAAG,CAAA+S,IAAIA,EAAEA,EAAE63B,KAAK,GAAG,OAAO73B,EAAEA,EAAE22B,OAAO,GAAG,SAAS32B,EAAEA,EAAE83B,QAAQ,GAAG,UAAU93B,EAAEA,EAAE+3B,QAAQ,GAAG,UAAU/3B,GAAhG,CAAoG/S,IAAG,CAAA,GAAI,SAASs9B,KAAW7U,OAAAA,EAACxmB,WAACiR,GAAE,CAAC,SAASkqB,IAAG79B,MAAM6T,EAAE7R,SAASyR,IAAWF,OAAAA,EAAE+sB,cAAc3sB,GAAE7R,SAAS,CAAC9B,MAAM6T,GAAGJ,EAAE,CAAC,SAAS4oB,IAAGr6B,SAAS6R,IAAWN,OAAAA,EAAE+sB,cAAc3sB,GAAE7R,SAAS,CAAC9B,MAAM,MAAM6T,EAAE,CCAnQ,IAAIF,GAAE,GCAN,SAASkqB,GAAEpqB,GAAG,IAAIF,EAAEG,GAAED,GAAGD,EAAEG,EAAC9L,QAAC,GAAIk2B,EAAAA,WAAE,KAAKvqB,EAAEvT,SAAQ,EAAG,KAAKuT,EAAEvT,SAAQ,EAAG4T,IAAE,KAAKL,EAAEvT,SAASsT,GAAC,GAAG,IAAI,CAACA,GAAG,CCArC,SAAS0rB,KAAQ1rB,IAAAA,EAA9J,WAAiBA,IAAAA,EAAmB,oBAAVlK,SAA4B,MAAA,yBAAyBoK,GAA8BA,EAAtBywB,sBAAyB,IAAI,SAAO,KAAI,IAAG,KAAK3wB,GAAK,CAAoB8oB,IAAK7oB,EAAEG,GAAG63B,EAAUjiC,SAACmK,GAAEqpB,mBAA0BvpB,OAAAA,IAAyB,IAAtBE,GAAEqpB,mBAAwBppB,GAAE,GAAI83B,EAAAA,WAAY,MAAS,IAAJj4B,GAAQG,GAAE,EAAE,GAAG,CAACH,IAAIi4B,EAAWve,WAAC,IAAIxZ,GAAEopB,WAAU,KAAIvpB,GAAKC,CAAC,ECA7X,SAAWG,GAAG,SAASH,IAA0B,YAAtBnK,SAASqiC,aAAyB/3B,IAAItK,SAASyB,oBAAoB,mBAAmB0I,GAAG,CAAgB,oBAARrM,QAAsC,oBAAVkC,WAAwBA,SAAS0B,iBAAiB,mBAAmByI,GAAGA,IAAI,CHAhF0V,EAAE,KAAK,SAAS1V,EAAEC,GAAG,IAAIk4B,GAAqBl4B,EAAEnM,SAASmM,EAAEnM,SAAS+B,SAASkD,MAAMoH,GAAE,KAAKF,EAAEnM,OAAO,OAAO,IAAIiM,EAAEE,EAAEnM,OAAOiM,EAAEA,EAAEy1B,QAAQvoC,IAAGkT,GAAEi4B,QAAW,MAAHr4B,EAAQA,EAAEE,EAAEnM,QAAQqM,GAAEA,GAAE3N,QAAO6N,GAAM,MAAHA,GAASA,EAAEk0B,cAAap0B,GAAEgqB,OAAO,GAAG,CAAQx2B,OAAA4D,iBAAiB,QAAQyI,EAAE,CAACpH,SAAQ,IAAKjF,OAAO4D,iBAAiB,YAAYyI,EAAE,CAACpH,SAAQ,IAAKjF,OAAO4D,iBAAiB,QAAQyI,EAAE,CAACpH,SAAQ,IAAK/C,SAASkD,KAAKxB,iBAAiB,QAAQyI,EAAE,CAACpH,SAAQ,IAAK/C,SAASkD,KAAKxB,iBAAiB,YAAYyI,EAAE,CAACpH,SAAQ,IAAK/C,SAASkD,KAAKxB,iBAAiB,QAAQyI,EAAE,CAACpH,SAAQ,GAAG,IIA/mB,IAAIoH,GAAED,EAAAA,eAAE,GAA6B,SAAS0rB,GAAEprB,GAAUJ,OAAAA,EAAE6sB,cAAc9sB,GAAE1R,SAAS,CAAC9B,MAAM6T,EAAEvK,OAAOuK,EAAE7R,SAAS,CCA+nB,SAASq/B,GAAE7tB,GAAG,IAAIyrB,EDAjuBpB,aAAErqB,ICAquBK,EAAEqV,EAAAA,WAAEmX,KAAI9sB,EAAEwqB,GAAGF,EAAAA,UAAE,KAASp9B,IAAAA,EAAK,IAACw+B,GAAO,OAAJprB,SAA8B,OAAdpT,EAAEoT,EAAE5T,SAAeQ,EAAE,KAAQqU,GAAAA,GAAE8nB,SAAgB,OAAA,KAAK,IAAInpB,EAAK,MAAHD,OAAQ,EAAOA,EAAEq4B,eAAe,0BAA0B,GAAGp4B,EAASA,OAAAA,EAAKD,GAAI,OAAJA,EAAgB,OAAA,KAASgP,IAAAA,EAAEhP,EAAE8sB,cAAc,OAAc9d,OAAAA,EAAEgjB,aAAa,KAAK,0BAA0BhyB,EAAEjH,KAAKu/B,YAAYtpB,EAAC,IAAWupB,OAAAA,EAAC7e,WAAC,KAAS,OAAJ3Z,IAAc,MAAHC,GAASA,EAAEjH,KAAKd,SAAS8H,IAAO,MAAHC,GAASA,EAAEjH,KAAKu/B,YAAYv4B,GAAC,GAAI,CAACA,EAAEC,IAAIu4B,EAAAA,WAAE,KAAK9M,GAAO,OAAJprB,GAAUkqB,EAAElqB,EAAE5T,QAAO,GAAG,CAAC4T,EAAEkqB,EAAEkB,IAAI1rB,CAAC,CAAC,IAAIgsB,GAAEsB,EAAAA,SAAEO,GAAEpsB,IAAE,SAASiqB,EAAEprB,GAAG,IAAI1K,cAAcoK,EAAE,QAAQwqB,GAAGkB,EAAExrB,EAAE+qB,SAAE,MAAMhc,EAAEyd,GnCAhuC,SAAWxsB,EAAEE,GAAE,GAAW,OAAAvR,OAAOm7B,OAAO9pB,EAAE,CAACu4B,CAACjO,IAAGpqB,GAAG,CmCAgrCitB,EAAEvE,IAAI5oB,EAAExT,QAAQo8B,CAAAA,IAAIxoB,GAAGpT,EAAEi+B,GAAEjrB,GAAGC,EAAK,MAAHH,EAAQA,EAAE9S,EAAEq5B,EAAEuH,GAAE3tB,IAAIC,GAAGkqB,EAAAA,UAAE,KAASxB,IAAAA,EAAE,OAAOvnB,GAAE8nB,SAAS,KAAgD,OAA1CP,EAAK,MAAH3oB,OAAQ,EAAOA,EAAE4sB,cAAc,QAAcjE,EAAE,IAAA,IAAO8C,EAAEjW,EAAAA,WAAE8W,IAAG/B,EAAEmC,KAAIX,IAAE,MAAM3F,IAAInmB,GAAGmmB,EAAEruB,SAASkI,KAAKA,EAAE6xB,aAAa,yBAAyB,IAAI1L,EAAEgS,YAAYn4B,GAAC,GAAI,CAACmmB,EAAEnmB,IAAI8rB,IAAE,KAAK,GAAG9rB,GAAGwrB,EAASA,OAAAA,EAAEgC,SAASxtB,EAAC,GAAG,CAACwrB,EAAExrB,IAAIyrB,IAAE,KAAS/C,IAAAA,GAAGvC,IAAInmB,IAAIs4B,GAASt4B,IAAImmB,EAAEruB,SAASkI,IAAImmB,EAAEoS,YAAYv4B,GAAGmmB,EAAEqS,WAAWzrC,QAAQ,IAAyB,OAApB27B,EAAEvC,EAAEoO,gBAAsB7L,EAAE6P,YAAYpS,IAAC,IAAM,IAAIrX,EAAE0d,KAAWlC,OAAAA,GAAGnE,GAAInmB,EAAOisB,EAACzxB,aAACsU,EAAE,CAACkc,SAAS,CAAC5+B,IAAIyiB,GAAGoc,WAAWb,EAAEc,KAAK,CAAE,EAACC,WAAWS,GAAEryB,KAAK,WAAWyG,GAAG,IAAI,IAAyM,IAAIy4B,GAAEvL,EAACwL,SAAChM,GAAEhC,EAACz8B,cAAC,MAA0K,IAAIo+B,GAAE3B,EAACz8B,cAAC,MAAsU,IAAI0qC,GAAEt3B,IAAhtB,SAAWxB,EAAEyrB,GAAG,IAAIprB,EAAEosB,GAAEhB,IAAIpY,QAAQtT,GAAE,EAAGpK,cAAc40B,KAAKtqB,GAAGD,EAAEgP,EAAE2d,KAAI,OAAO5sB,EAAEwsB,EAAEO,cAAcc,GAAE,IAAI3tB,EAAEtK,cAAc40B,EAAEh+B,IAAI8T,IAAI2O,EAAE,CAACmc,SAAS,CAAC5+B,IAAI8T,GAAG+qB,WAAWnrB,EAAEorB,KAAK,GAAGC,WAAWS,GAAEryB,KAAK,UAAU,IAAghB6sB,GAAE/kB,IAA/f,SAAWxB,EAAEyrB,GAAG,IAAI33B,OAAOuM,KAAKN,GAAGC,EAAEC,EAAE,CAAC1T,IAAIkgC,GAAEhB,IAAIzc,EAAE2d,KAAI,OAAOJ,EAAEO,cAAcD,GAAEv+B,SAAS,CAAC9B,MAAM6T,GAAG2O,EAAE,CAACmc,SAASlrB,EAAEmrB,WAAWrrB,EAAEurB,WAAWsN,GAAEl/B,KAAK,kBAAkB,IAAiWq/B,GAAGnqC,OAAOm7B,OAAO+O,GAAE,CAACE,MAAMzS,KCA9qD,IAAIvX,GAAEqe,EAACj/B,cAAC,MAAM,SAASu9B,IAAGn9B,SAASuR,EAAEnT,KAAKuT,IAAI,IAAIE,EAAEorB,GAAGnF,EAACvwB,SAAC,MAAMw0B,EAAE/oB,GAAK,MAAHrB,EAAQA,EAAEE,GAAUwoB,OAAAA,EAAEiE,cAAc9d,GAAE1gB,SAAS,CAAC9B,MAAM+9B,GAAGxqB,EAAM,OAAJwqB,GAAU1B,EAAEiE,cAAc7d,GAAE,CAACsc,SAASQ,GAAEhB,OAAOx+B,IAAI0T,IAAI,IAAIhT,EAAEo9B,EAAE,GAAGpqB,EAAG,IAAA,IAAQD,KAAwE,OAAnEqqB,EAAY,OAATp9B,EAAEqU,GAAErB,SAAU,EAAOhT,EAAE4mC,iBAAiB,uBAA6BxJ,EAAE,GAAG,GAAGrqB,IAAInK,SAASkD,MAAMiH,IAAInK,SAASojC,MAAMC,GAAYl5B,IAAO,MAAHA,GAASA,EAAE/H,SAASgI,GAAG,CAACwrB,EAAEzrB,GAAG,KAAK,CAAC,IAAK,CAAC,SAASwB,GAAEzB,EAAE,MAAUI,IAAAA,EAAE,OAAgB,OAATA,EAAEisB,EAAAA,WAAEpd,KAAU7O,EAAEJ,CAAC,CCA/wC,SAASG,KAAQF,IAAAA,EAAED,EAAAA,QAAE,GAAI,OAAOE,IAAE,KAAKD,EAAEvT,SAAQ,EAAG,KAAKuT,EAAEvT,SAAQ,CAAA,IAAK,IAAIuT,CAAC,CCAxF,IAAIgP,IAAGjP,IAAIA,EAAEA,EAAEo5B,SAAS,GAAG,WAAWp5B,EAAEA,EAAEq5B,UAAU,GAAG,YAAYr5B,IAAIiP,IAAG,CAAE,GCAihC,SAASzN,GAAEsnB,GAAM,IAACA,EAAE,OAAW,IAAAh0B,IAAI,GAAa,mBAAHg0B,SAAqB,IAAIh0B,IAAIg0B,KAAK,IAAI7oB,EAAM,IAAAnL,IAAYoL,IAAAA,IAAAA,KAAK4oB,EAAEp8B,QAAoBwT,GAAAA,EAAExT,UAAUuT,EAAE9G,IAAI+G,EAAExT,SAAgBuT,OAAAA,CAAC,CAAa,IAAIu4B,IAAGp4B,IAAIA,EAAEA,EAAEuqB,KAAK,GAAG,OAAOvqB,EAAEA,EAAEk5B,aAAa,GAAG,eAAel5B,EAAEA,EAAEm5B,QAAQ,GAAG,UAAUn5B,EAAEA,EAAEo5B,UAAU,GAAG,YAAYp5B,EAAEA,EAAEq5B,aAAa,GAAG,eAAer5B,EAAEA,EAAEqyB,UAAU,IAAI,YAAYryB,IAAIo4B,IAAG,CAAE,GAA02C,IAAI7L,GAAE+M,IAA92C,SAAW5Q,EAAE7oB,GAAOC,IAAAA,EAAE8rB,EAAC13B,OAAC,MAAM0L,EAAE25B,GAAEz5B,EAAED,IAAI25B,aAAat5B,EAAEu5B,qBAAqB5qB,EAAEomB,WAAWj1B,EAAEorB,SAAShB,EAAE,MAAMrqB,GAAG2oB,EAAE+P,OAAMrO,EAAE,GAAOkB,IAAAA,EAAEoO,GAAE55B,IAAu/C,SAAY4oB,GAAGlzB,cAAcqK,IAAI,IAAIC,KAAO,EAAF4oB,GAAK9oB,EAAtR,SAAY8oB,GAAE,GAAI,IAAI7oB,EAAE+rB,SAAEc,GAAEx2B,SAAS,OAAOs2B,IAAE,EAAE1sB,IAAIF,OAAU,IAAJA,IAAY,IAAJE,GAAQirB,IAAE,KAAKlrB,EAAEvT,QAAQ09B,OAAO,EAAC,KAAQ,IAAJpqB,IAAY,IAAJE,IAASD,EAAEvT,QAAQogC,GAAEx2B,QAAO,GAAG,CAACwyB,EAAEgE,GAAE7sB,IAAIyqB,IAAE,KAASxqB,IAAAA,EAAE,OAAqD,OAA9CA,EAAED,EAAEvT,QAAQgE,MAAKsP,GAAM,MAAHA,GAASA,EAAEw0B,eAAoBt0B,EAAE,IAAA,GAAM,CAAkD65B,CAAG75B,GAAG0sB,IAAE,KAAK1sB,IAAO,MAAHD,OAAQ,EAAOA,EAAEi0B,kBAAqB,MAAHj0B,OAAQ,EAAOA,EAAEjH,OAAOutB,GAAEvmB,IAAG,GAAG,CAACE,IAAIsmB,IAAE,KAAKtmB,GAAGqmB,GAAEvmB,IAAG,GAAG,CAAhpDg6B,CAAGxP,EAAE,CAAC50B,cAAc81B,IAAI,IAAIgB,EAAqnD,SAAY5D,GAAGlzB,cAAcqK,EAAE5F,UAAU6F,EAAE05B,aAAa55B,EAAE65B,qBAAqBv5B,IAAI,IAAI2O,EAAE+c,SAAE,MAAM5rB,EAAE8rB,MAAO,EAAFpD,GAAK,4BAA4B0B,EAAEoB,KAAI,OAAOgB,IAAE,KAAK,GAAO,IAAJ9D,EAAM,OAAO,IAAI1oB,EAAoC,YAA9B,MAAHE,GAASA,EAAE5T,SAAS65B,GAAEjmB,EAAE5T,UAAgB,IAAIyT,EAAED,EAAExT,QAAQyT,GAAGgrB,IAAE,KAAQ,IAACX,EAAE99B,QAAQ,OAAO,IAAIg/B,EAAK,MAAHzrB,OAAQ,EAAOA,EAAEi0B,cAAiBl0B,GAAG,MAAHA,GAASA,EAAEtT,SAAS,IAAO,MAAHsT,OAAQ,EAAOA,EAAEtT,WAAWg/B,EAAe,YAAZzc,EAAEviB,QAAQg/B,QAAkBvrB,GAAAA,EAAEjI,SAASwzB,GAAgB,YAAZzc,EAAEviB,QAAQg/B,GAAS,GAAM,MAAH1rB,GAASA,EAAEtT,QAAQ65B,GAAEvmB,EAAEtT,aAAa,CAAC,GAAK,GAAFo8B,GAASnT,GAAAA,GAAExV,EAAEjT,GAAEilC,MAAMjlC,GAAEulC,aAAapG,GAAEj9B,MAAM,eAAeumB,GAAExV,EAAEjT,GAAEilC,SAAS9F,GAAEj9B,MAAM,OAAO,GAAM,MAAHkR,GAASA,EAAE5T,UAAU65B,GAAEjmB,EAAE5T,UAAa,MAAHuT,OAAQ,EAAOA,EAAEi0B,iBAAiB5zB,EAAE5T,SAAS,OAAOmS,QAAQC,KAAK,2DAA2D,CAACmQ,EAAEviB,QAAW,MAAHuT,OAAQ,EAAOA,EAAEi0B,aAAA,GAAc,GAAG,CAAC5zB,EAAEF,EAAE0oB,IAAI7Z,CAAC,CAAx3EgrB,CAAGzP,EAAE,CAAC50B,cAAc81B,EAAErxB,UAAU6F,EAAE05B,aAAat5B,EAAEu5B,qBAAqB5qB,KAAmzE,SAAY6Z,GAAGlzB,cAAcqK,EAAE5F,UAAU6F,EAAEm1B,WAAWr1B,EAAEk6B,sBAAsB55B,IAAI,IAAI2O,EAAE2c,KAAIxrB,KAAO,EAAF0oB,GAAKsE,GAAK,MAAHntB,OAAQ,EAAOA,EAAEtD,YAAY,SAAQ6tB,IAAI,IAAIpqB,IAAI6O,EAAEviB,QAAQ,OAAWyT,IAAAA,EAAEqB,GAAExB,GAAGm6B,GAAgBj6B,EAAExT,UAAUyT,EAAEhH,IAAI+G,EAAExT,SAAS,IAAIg/B,EAAEprB,EAAE5T,QAAQ,IAAIg/B,EAAE,OAAO,IAAIgB,EAAElC,EAAEz2B,OAAuB24B,GAAAA,GAAGoB,GAAE3tB,EAAEusB,IAAIpsB,EAAE5T,QAAQggC,EAAEnG,GAAEmG,KAAKlC,EAAEzxB,iBAAiByxB,EAAE4P,kBAAkB7T,GAAEmF,IAAInF,GAAEjmB,EAAE5T,QAAO,IAAG,EAAG,CAAtqF89B,CAAAA,EAAE,CAAC50B,cAAc81B,EAAErxB,UAAU6F,EAAEm1B,WAAWj1B,EAAE85B,sBAAsBxN,IAAI,IAAID,EDAhsD,WAAiBxsB,IAAAA,EAAEK,SAAE,GAAG,OAAOJ,IAAE,EAAG,WAAUF,IAAY,QAARA,EAAEpH,MAAcqH,EAAEvT,QAAQsT,EAAEq6B,SAAS,EAAE,EAAA,IAAI,GAAIp6B,CAAC,CCAgmDq6B,GAAI/4B,EAAEmpB,IAAEJ,IAAI,IAAI6P,GAAgBj6B,EAAExT,SAAS,OAAO,IAAI4gC,EAAEptB,EAAExT,QAAsB46B,GAAEmF,EAAE//B,QAAQ,CAAC,CAAC+U,GAAE23B,UAAU,KAAO9L,GAAAA,EAAEpgC,GAAEilC,MAAM,CAACuB,aAAa,CAACpJ,EAAEiQ,cAActrB,IAAG,EAAG,CAACxN,GAAE43B,WAAW,KAAO/L,GAAAA,EAAEpgC,GAAEolC,KAAK,CAACoB,aAAa,CAACpJ,EAAEiQ,cAActrB,IAAG,GAAK,IAAI6b,EAAEoB,MAAO,EAAF1B,GAAK,uBAAuBqC,EAAE2N,KAAItrB,EAAE8c,EAAAA,QAAE,GAAId,EAAE,CAAC1+B,IAAIwT,EAAE,SAAAy6B,CAAUnQ,GAAU,OAAPA,EAAE1xB,MAAasW,EAAExiB,SAAQ,EAAGmgC,EAAEpb,uBAAsB,KAAKvC,EAAExiB,SAAQ,CAAA,IAAK,EAAE,MAAAi4B,CAAO2F,GAAM,KAAI,EAAFE,GAAK,OAAW8C,IAAAA,EAAE9rB,GAAEpB,GAAG+5B,GAAgBj6B,EAAExT,UAAU4gC,EAAEn0B,IAAI+G,EAAExT,SAAS,IAAIu+B,EAAEX,EAAEiQ,cAAmCtP,GAAAA,IAAqC,SAAjCA,EAAEqI,QAAQoH,uBAAgC5M,GAAER,EAAErC,KAAK/b,EAAExiB,QAAQipB,GAAEzV,EAAExT,QAAQ46B,GAAEmF,EAAE//B,QAAQ,CAAC,CAAC+U,GAAE23B,UAAU,IAAIlsC,GAAEmlC,KAAK,CAAC5wB,GAAE43B,WAAW,IAAInsC,GAAEklC,WAAWllC,GAAEqlC,WAAW,CAACkB,WAAWnJ,EAAEv2B,SAAS4mC,GAAqBrQ,EAAEv2B,SAASwyB,GAAE+D,EAAEv2B,SAAS,GAAGglC,EAAE6B,KAAW/O,OAAAA,EAAEkB,cAAclB,EAAEiN,SAAS,KAAKhO,GAAGe,EAAEkB,cAAcW,GAAE,CAACvB,GAAG,SAASt5B,KAAK,SAAS,+BAA8B,EAAG6xB,QAAQnjB,EAAEiqB,SAAS6B,GAAEG,YAAYuL,EAAE,CAAC3N,SAASF,EAAEG,WAAWlrB,EAAEorB,WAA95C,MAA26C5xB,KAAK,cAAcmxB,GAAGe,EAAEkB,cAAcW,GAAE,CAACvB,GAAG,SAASt5B,KAAK,SAAS,+BAA8B,EAAG6xB,QAAQnjB,EAAEiqB,SAAS6B,GAAEG,YAAY,IAAYqN,GAAGhsC,OAAOm7B,OAAO2C,GAAE,CAACnB,SAASgN,KAAygD,SAAS1K,GAAEhF,EAAE7oB,GAAG,IAAA,IAAQC,KAAK4oB,EAAE,GAAG5oB,EAAEhI,SAAS+H,GAAW,OAAA,EAAS,OAAA,CAAA,CCAz/G,SAAS66B,GAAG76B,GAAOC,IAAAA,EAAQ,SAAGD,EAAE23B,OAAO33B,EAAE86B,WAAW96B,EAAE+6B,SAAS/6B,EAAE43B,OAAO53B,EAAEg7B,WAAWh7B,EAAEi7B,WAAqB,OAATh7B,EAAED,EAAEksB,IAAUjsB,EAAEi7B,MAAMzQ,EAAAA,UAAkC,IAA/BJ,EAAEv5B,SAASC,MAAMiP,EAAExR,SAAa,CAAC,IAAIk+B,GAAEqM,EAAAA,cAAG,MAAMrM,GAAE39B,YAAY,oBAAoB,IAAIgiB,GAAI,CAAA5Q,IAAIA,EAAEg7B,QAAQ,UAAUh7B,EAAE4qB,OAAO,SAAS5qB,GAA1C,CAA8C4Q,IAAI,CAAA,GAAwU,IAAIgb,GAAEgN,EAAAA,cAAG,MAAqC,SAAS7N,GAAElrB,GAAS,MAAA,aAAaA,EAAEkrB,GAAElrB,EAAExR,UAAUwR,EAAEvT,QAAQ+F,QAAO,EAAEkb,GAAGzN,KAAiB,OAAZA,EAAExT,UAAgB+F,QAAO,EAAE6I,MAAM4E,KAAS,YAAJA,IAAe/S,OAAO,CAAC,CAAC,SAASkuC,GAAGp7B,EAAEC,GAAOE,IAAAA,EAAEk7B,GAAGr7B,GAAGyrB,EAAExc,EAAC5a,OAAC,IAAI+4B,EAAEkO,KAAK3O,EAAE4O,KAAK7lB,EAAE2X,IAAE,CAAChtB,EAAEpT,EAAE0+B,GAAEZ,UAAc/b,IAAAA,EAAEyc,EAAEh/B,QAAQ+uC,WAAU,EAAE9tB,GAAGmb,KAAKA,IAAIxoB,SAAG2O,IAASysB,GAAGxuC,EAAE,CAAC,CAAC0+B,GAAEb,WAAWW,EAAEh/B,QAAQ09B,OAAOnb,EAAE,EAAE,EAAE,CAAC2c,GAAEZ,UAAUU,EAAEh/B,QAAQuiB,GAAG3T,MAAM,QAAQ,IAAIsxB,EAAE9C,WAAU,KAAShB,IAAAA,GAAGqC,GAAEO,IAAI2B,EAAE3gC,UAAyB,OAAdo8B,EAAE1oB,EAAE1T,UAAgBo8B,EAAEvqB,KAAK6B,GAAC,IAAG,IAAKqB,EAAE6rB,IAAEhtB,IAAQpT,IAAAA,EAAEw+B,EAAEh/B,QAAQgE,MAAK,EAAEid,GAAGsB,KAAKA,IAAI3O,IAAUpT,OAAAA,EAAY,YAAVA,EAAEoO,QAAoBpO,EAAEoO,MAAM,WAAWowB,EAAEh/B,QAAQ0nB,KAAK,CAACzG,GAAGrN,EAAEhF,MAAM,YAAY,IAAIqa,EAAErV,EAAEsrB,GAAEb,QAAO,IAAImB,EAAEhd,EAAAA,OAAE,IAAIqX,EAAErX,SAAEwa,QAAQC,WAAW0C,EAAEnd,EAAC5a,OAAC,CAACsjC,MAAM,GAAGC,MAAM,KAAKpL,EAAEa,IAAE,CAAChtB,EAAEpT,EAAE+hB,KAAKid,EAAEx/B,QAAQ09B,OAAO,GAAGlqB,IAAIA,EAAEy7B,OAAOjvC,QAAQQ,GAAGgT,EAAEy7B,OAAOjvC,QAAQQ,GAAGuF,QAAO,EAAEq2B,KAAKA,IAAIxoB,KAAO,MAAHJ,GAASA,EAAEy7B,OAAOjvC,QAAQQ,GAAGknB,KAAK,CAAC9T,EAAE,IAAIopB,SAAQZ,IAAIoD,EAAEx/B,QAAQ0nB,KAAK0U,EAAC,MAAS,MAAH5oB,GAASA,EAAEy7B,OAAOjvC,QAAQQ,GAAGknB,KAAK,CAAC9T,EAAE,IAAIopB,SAAQZ,IAAIY,QAAQkS,IAAIvP,EAAE3/B,QAAQQ,GAAGF,KAAI,EAAEgT,EAAEG,KAAKA,KAAImf,MAAK,IAAIwJ,KAAG,MAAU,UAAJ57B,EAAYq5B,EAAE75B,QAAQ65B,EAAE75B,QAAQ4yB,MAAK,IAAO,MAAHpf,OAAQ,EAAOA,EAAE27B,KAAKnvC,UAAS4yB,MAAK,IAAIrQ,EAAE/hB,KAAI+hB,EAAE/hB,EAAC,IAAIqU,EAAE+rB,IAAE,CAAChtB,EAAEpT,EAAE+hB,KAAKya,QAAQkS,IAAIvP,EAAE3/B,QAAQQ,GAAGk9B,OAAO,GAAGp9B,KAAI,EAAE87B,EAAE9oB,KAAKA,KAAIsf,MAAK,KAASwJ,IAAAA,EAAyB,OAAtBA,EAAEoD,EAAEx/B,QAAQmmB,UAAgBiW,GAAG,IAAGxJ,MAAK,IAAIrQ,EAAE/hB,IAAE,IAAW4uC,OAAAA,EAAAA,SAAG,KAAA,CAAMrtC,SAASi9B,EAAEkC,SAASnsB,EAAEs6B,WAAWpmB,EAAEqmB,QAAQvP,EAAEwP,OAAO16B,EAAEs6B,KAAKtV,EAAEoV,OAAOtP,KAAI,CAAC5qB,EAAEkU,EAAE+V,EAAEe,EAAElrB,EAAE8qB,EAAE9F,GAAG,CAA3wCyF,GAAEh9B,YAAY,iBAA8vC,IAAImsC,GAAGzQ,EAACoO,SAACoD,GAAGC,GAAGvR,eAAuyF,IAAIiO,GAAEiB,IAApnC,SAAY75B,EAAEC,GAAG,IAAIk8B,KAAKh8B,EAAEi8B,OAAO3Q,GAAE,EAAGK,QAAQsB,GAAE,KAAMT,GAAG3sB,EAAE0V,EAAEzG,EAAC5a,OAAC,MAAc43B,EAAEoQ,MAARxB,GAAG76B,GAAa,CAAC0V,EAAEzV,GAAO,OAAJA,EAAS,GAAG,CAACA,IAAMq8B,KAAG,IAAIhW,EAAEiW,KAAK,QAAO,IAAJp8B,GAAgB,OAAJmmB,IAAWnmB,GAAGmmB,EAAE/kB,GAAEs2B,QAAQt2B,GAAEs2B,WAAU,IAAJ13B,EAAiB,MAAA,IAAIhR,MAAM,4EAA4E,IAAIi9B,EAAEI,GAAGgQ,EAACzmC,SAACoK,EAAE,UAAU,UAAUmB,EAAE85B,IAAG,KAAKj7B,GAAGqsB,EAAE,SAAQ,KAAKnsB,EAAEpT,GAAGuvC,EAACzmC,UAAC,GAAIiZ,EAAEC,EAAC5a,OAAC,CAAC8L,IAAIytB,IAAE,MAAS,IAAJvtB,GAAQ2O,EAAEviB,QAAQuiB,EAAEviB,QAAQS,OAAO,KAAKiT,IAAI6O,EAAEviB,QAAQ0nB,KAAKhU,GAAGlT,GAAE,GAAE,GAAI,CAAC+hB,EAAE7O,IAAI,IAAI0oB,EAAEgT,EAAE5tC,SAAC,KAAK,CAACkuC,KAAKh8B,EAAEi8B,OAAO3Q,EAAEgR,QAAQp8B,KAAI,CAACF,EAAEsrB,EAAEprB,IAAIutB,IAAE,KAAKztB,EAAEqsB,EAAE,YAAYtB,GAAE5pB,IAAgB,OAAZoU,EAAEjpB,SAAgB+/B,EAAE,SAAQ,GAAG,CAACrsB,EAAEmB,IAAI,IAAIvB,EAAE,CAAC+rB,QAAQsB,GAAGltB,EAAEmtB,IAAE,KAAS9C,IAAAA,EAAElqB,GAAGpT,GAAE,GAAuB,OAAlBs9B,EAAEvqB,EAAE08B,cAAoBnS,EAAEjsB,KAAK0B,EAAC,IAAIqnB,EAAEgG,IAAE,KAAS9C,IAAAA,EAAElqB,GAAGpT,GAAE,GAAuB,OAAlBs9B,EAAEvqB,EAAE28B,cAAoBpS,EAAEjsB,KAAK0B,EAAC,IAAI6sB,EAAE+P,KAAYvS,OAAAA,EAAEyC,cAAcf,GAAEz9B,SAAS,CAAC9B,MAAM8U,GAAG+oB,EAAEyC,cAAcJ,GAAEp+B,SAAS,CAAC9B,MAAMq8B,GAAGgE,EAAE,CAAC1B,SAAS,IAAIprB,EAAEmsB,GAAGzB,EAAAA,SAAEj8B,SAAS67B,EAAEyC,cAAc+P,GAAG,CAACtwC,IAAI0/B,KAAKlsB,KAAK4sB,EAAE+P,YAAYx8B,EAAEy8B,YAAYtV,KAAK+D,WAAW,CAAE,EAACE,WAAWb,EAACoO,SAACtN,SAAS0Q,GAAGzQ,QAAY,YAAJY,EAAc1yB,KAAK,gBAAgB,IAA6KmjC,GAAGhD,IAAvyF,SAAY75B,EAAEC,GAAG,IAAI65B,EAAGC,EAAM,IAACzC,WAAWn3B,GAAE,EAAGu8B,YAAYjR,EAAEqR,WAAW1P,EAAEuP,YAAYhQ,EAAEoQ,WAAWrnB,EAAEiiB,MAAMn2B,EAAEs5B,UAAU7O,EAAE8O,QAAQzU,EAAE0W,QAAQ5Q,EAAEwL,MAAMpL,EAAEwO,UAAU15B,EAAE25B,QAAQ56B,KAAKpT,GAAG+S,GAAGgP,EAAE6Z,GAAG2T,EAAAA,SAAE,MAAMz8B,EAAEkP,EAAC5a,OAAC,MAAM6L,EAAE26B,GAAG76B,GAAGqnB,EAAEgV,MAAMn8B,EAAE,CAACH,EAAEE,EAAE4oB,GAAO,OAAJ5oB,EAAS,GAAG,CAACA,IAAI4sB,EAAkB,OAAfiN,EAAG7sC,EAAE6+B,UAAgBgO,EAAGnO,GAAEb,QAAQa,GAAEZ,QAAQoR,KAAK5R,EAAE6R,OAAO1C,EAAE+C,QAAQtP,GAA17D,WAAkBntB,IAAAA,EAAEumB,EAAAA,WAAEmG,IAAG,GAAO,OAAJ1sB,EAAe,MAAA,IAAI7Q,MAAM,oGAA2G6Q,OAAAA,CAAC,CAA4xDi9B,IAAMxQ,EAAE8L,GAAGiE,EAACzmC,SAACw0B,EAAE,UAAU,UAAU8P,EAA9zD,WAAkBr6B,IAAAA,EAAEumB,EAAAA,WAAEwF,IAAG,GAAO,OAAJ/rB,EAAe,MAAA,IAAI7Q,MAAM,oGAA2G6Q,OAAAA,CAAC,CAA+pDk9B,IAAMvP,SAAS9C,EAAEiR,WAAWjO,GAAGwM,EAAI3M,IAAA,IAAI7C,EAAE9qB,IAAG,CAAC8qB,EAAE9qB,IAAI6tB,IAAE,KAAK,GAAGf,IAAIlB,GAAEZ,QAAQhrB,EAAEtT,QAAY89B,OAAAA,GAAO,YAAJkC,OAAe8L,EAAE,WAAyBkD,GAAGhP,EAAE,CAACT,OAAW,IAAI6B,EAAE9tB,GAAGyrB,QAAY,IAAIX,EAAE9qB,IAAI,GAAG,CAAC0sB,EAAE1sB,EAAE8qB,EAAEgD,EAAEtD,EAAEsC,IAAI,IAAIiM,EAAEkB,KAAKpM,IAAE,KAAQ1tB,GAAAA,GAAG44B,GAAO,YAAJrM,GAA2B,OAAZ1sB,EAAEtT,QAAe,MAAM,IAAI0C,MAAM,kEAAiE,GAAG,CAAC4Q,EAAE0sB,EAAEqM,EAAE54B,IAAI,IAAIi9B,EAAGhQ,IAAIuM,EAAED,EAAEC,GAAGnP,GAAG4C,EAAEoN,EAAEtrB,EAAAA,QAAE,GAAI+b,EAAEoQ,IAAG,KAAKb,EAAE9tC,UAAU8rC,EAAE,UAAU1K,EAAE9tB,GAAC,GAAIs6B,GAAGM,EAAEtN,IAAEpC,IAAIsP,EAAE9tC,SAAQ,EAAOm/B,IAAAA,EAAEX,EAAE,QAAQ,QAAQD,EAAE+Q,QAAQh8B,EAAE6rB,GAAE6B,IAAQ,UAAJA,EAAe,MAAHhC,GAASA,IAAQ,UAAJgC,IAAiB,MAAHd,GAASA,IAAG,GAAE,IAAIjjB,EAAE2jB,IAAEpC,IAAQW,IAAAA,EAAEX,EAAE,QAAQ,QAAQsP,EAAE9tC,SAAQ,EAAGu+B,EAAEgR,OAAOj8B,EAAE6rB,GAAE6B,IAAQ,UAAJA,EAAe,MAAHL,GAASA,IAAQ,UAAJK,IAAiB,MAAH/X,GAASA,IAAC,IAAU,UAAJkW,IAAcV,GAAEF,KAAKuN,EAAE,UAAU1K,EAAE9tB,GAAC,IAAKq9B,EAAE1jB,WAAC,KAAKxZ,GAAGC,IAAIw6B,EAAEpQ,GAAG7gB,EAAE6gB,GAAC,GAAI,CAACA,EAAErqB,EAAEC,IAAI,IAAIk9B,EAAQ,QAAGl9B,IAAID,IAAI44B,GAAGqE,GAAd,IAAuB,CAAA5Q,GAAGqO,GAAGyC,EAAGruB,EAAEub,EAAE,CAACxoB,MAAM44B,EAAE34B,IAAI0H,IAAI4zB,EAAGC,GAAG,CAAChxC,IAAI86B,EAAEiF,WAA2K,OAA/JyN,EAAGyD,GAAGvwC,EAAEq/B,UAAUmN,GAAGj4B,EAAEi4B,GAAGxN,EAAEM,EAAEoL,OAAOn2B,EAAE+qB,EAAEoL,OAAOpL,EAAEmL,QAAQzL,EAAEM,EAAEoL,QAAQpL,EAAEmL,QAAQpR,EAAEiG,EAAEqL,OAAOpL,EAAED,EAAEqL,QAAQrL,EAAEmL,QAAQp2B,EAAEirB,EAAEqL,OAAOrL,EAAEmL,QAAQr3B,GAAGksB,EAAE+K,YAAY/M,GAAG6B,SAAU,EAAO2N,EAAGr8B,cAAS,KAAU+/B,GAAGlR,KAAKK,EAAE,EAAM,YAAJH,IAAgBG,GAAGrrB,GAAEs2B,MAAU,WAAJpL,IAAeG,GAAGrrB,GAAEo1B,QAAQpM,GAAO,WAAJkC,IAAeG,GAAGrrB,GAAEw2B,UAAUxN,GAAO,YAAJkC,IAAgBG,GAAGrrB,GAAEu2B,SAAS,IAAI4F,EAAGd,KAAK,OAAOvS,EAAEyC,cAAcf,GAAEz9B,SAAS,CAAC9B,MAAMw+B,GAAGX,EAAEyC,cAAc6Q,GAAG,CAACnxC,MAAMogC,GAAG8Q,EAAG,CAACvS,SAASmS,EAAGlS,WAAWn+B,EAAEq+B,WAAW4P,GAAG3P,SAAS0Q,GAAGzQ,QAAY,YAAJiB,EAAc/yB,KAAK,sBAAsB,IAAooCkkC,GAAG/D,IAAxL,SAAY75B,EAAEC,GAAOE,IAAAA,EAAS,OAAPomB,EAAAA,WAAEmG,IAAUjB,EAAS,OAAP8Q,KAAmBlS,OAAAA,EAAEyC,cAAczC,EAAEwO,SAAS,MAAM14B,GAAGsrB,EAAEpB,EAAEyC,cAAc8L,GAAE,CAACrsC,IAAI0T,KAAKD,IAAIqqB,EAAEyC,cAAc+P,GAAG,CAACtwC,IAAI0T,KAAKD,IAAI,IAA+B69B,GAAGjvC,OAAOm7B,OAAO6O,GAAE,CAACkF,MAAMF,GAAG7jC,KAAK6+B,KCA94H,IAAImF,GAAI,CAAA19B,IAAIA,EAAEA,EAAEw3B,KAAK,GAAG,OAAOx3B,EAAEA,EAAEs2B,OAAO,GAAG,SAASt2B,GAA9C,CAAkD09B,IAAI,CAAE,GAAEC,GAAI,CAAA/9B,IAAIA,EAAEA,EAAEg+B,WAAW,GAAG,aAAah+B,GAAnC,CAAuC+9B,IAAI,CAAA,GAAI,IAAIE,GAAG,CAAC,EAAE,CAAEl+B,EAAEC,IAAUD,EAAEm+B,UAAUl+B,EAAEU,GAAGX,EAAE,IAAIA,EAAEm+B,QAAQl+B,EAAEU,KAAM+rB,GAAE6P,EAAAA,cAAG,MAAoC,SAAS9R,GAAEzqB,GAAOC,IAAAA,EAAE46B,EAAAA,WAAGnO,IAAG,GAAO,OAAJzsB,EAAS,CAAC,IAAII,EAAE,IAAIlR,MAAM,IAAI6Q,kDAAkD,MAAM7Q,MAAMq7B,mBAAmBr7B,MAAMq7B,kBAAkBnqB,EAAEoqB,IAAGpqB,CAAC,CAAQJ,OAAAA,CAAC,CAAC,SAASm+B,GAAGp+B,EAAEC,GAAG,OAAOi8B,GAAGj8B,EAAErN,KAAKsrC,GAAGl+B,EAAEC,EAAE,CAA3PysB,GAAE39B,YAAY,gBAA8O,IAAI2qC,GAAEzN,IAAE,SAAShsB,EAAEI,GAAG,IAAI2O,EAAEic,EAAAA,SAAKtqB,GAAGR,EAAE,qBAAqB6O,IAAImP,KAAKlxB,EAAEi2B,QAAQ2F,EAAE8Q,aAAajkB,EAAE4S,KAAKhC,EAAE,SAAS+X,UAAU9R,GAAE,EAAG+R,WAAW/T,GAAE,EAAGuB,QAAQtqB,GAAE,KAAM4rB,GAAGntB,EAAE2rB,EAAEvE,EAAAA,QAAE,GAAIf,EAAwB,WAAJA,GAAkB,gBAAJA,EAAkBA,GAAGsF,EAAEn/B,UAAUm/B,EAAEn/B,SAAQ,EAAGmS,QAAQC,KAAK,iBAAiBynB,8GAA8G,UAAa,IAAI+D,EAAEwP,UAAQ,IAAJ5sC,GAAgB,OAAJo9B,IAAWp9B,GAAGo9B,EAAE9oB,GAAEs2B,QAAQt2B,GAAEs2B,MAAM,IAAI33B,EAAEmnB,EAAChzB,OAAC,MAAMw5B,EAAE0K,GAAEr4B,EAAEG,GAAG4O,EAAEosB,GAAGn7B,GAAGssB,EAAEv/B,EAAE,EAAE,GAAGqU,EAAE+4B,GAAGe,EAAE3/B,WAAC2iC,GAAG,CAACD,QAAQ,KAAKI,cAAc,KAAKC,SAASnB,EAAEoB,cAAKhS,EAAEgB,IAAE,IAAI5E,GAAE,KAAKiQ,EAAErL,IAAE1tB,GAAGs6B,EAAE,CAACznC,KAAK,EAAE+N,GAAGZ,MAAK6tB,IAAEgN,MAAS,IAAJpO,GAAUmO,EAAEb,GNAloB,WAAc,IAAI95B,EAAE0V,EAACxmB,WAACs9B,IAAGf,EAAET,EAAC32B,OAAC,IAAIgM,EAAEotB,IAAExtB,IAAIwrB,EAAEh/B,QAAQ0nB,KAAKlU,GAAGD,GAAGA,EAAE2tB,SAAS1tB,GAAG,IAAIF,EAAEE,MAAKF,EAAE0tB,IAAExtB,IAAI,IAAI+O,EAAEyc,EAAEh/B,QAAQ8J,QAAQ0J,IAAWwrB,IAARzc,GAAQyc,EAAEh/B,QAAQ09B,OAAOnb,EAAE,GAAGhP,GAAGA,EAAE87B,WAAW77B,EAAC,IAAIsqB,EAAEhpB,EAACtT,SAAC,KAAA,CAAM0/B,SAASttB,EAAEy7B,WAAW/7B,EAAE2+B,QAAQjT,KAAI,CAACprB,EAAEN,EAAE0rB,IAAU,MAAA,CAACA,EAAElqB,EAAAA,SAAE,IAAI,UAAU/S,SAASwgB,IAAWud,OAAAA,EAAEO,cAAcN,GAAEl+B,SAAS,CAAC9B,MAAM+9B,GAAGvb,EAAE,GAAE,CAACub,IAAI,CMAuUoU,GAAK5E,EAAG,CAAC,WAAIttC,GAAcsT,IAAAA,EAAE,OAA8B,OAAvBA,EAAEuB,EAAEk9B,SAAS/xC,SAAesT,EAAEG,EAAEzT,OAAO,GAAGu+B,EAAE4T,MAAMC,kBAAkB9S,GLAnrF,UAAY+S,kBAAkB/+B,EAAE,GAAG2+B,QAAQv+B,EAAE4+B,aAAa1+B,GAAG,CAAA,GAAI,IAAIorB,EAAElqB,GAAElB,GAAGkqB,EAAEkC,IAAE,KAAK,IAAIx/B,EAAEo9B,EAAE,IAAIpqB,EAAE,GAAWD,IAAAA,IAAAA,KAAKD,EAAM,OAAJC,IAAWk5B,GAAYl5B,GAAGC,EAAEkU,KAAKnU,GAAG,YAAYA,GAAGk5B,GAAYl5B,EAAEvT,UAAUwT,EAAEkU,KAAKnU,EAAEvT,UAAa0T,GAAG,MAAHA,GAASA,EAAE1T,QAAQ,IAAA,IAAQuT,KAAKG,EAAE1T,QAAQwT,EAAEkU,KAAKnU,GAAWA,IAAAA,IAAAA,KAAiE,OAA5D/S,EAAK,MAAHw+B,OAAQ,EAAOA,EAAEoI,iBAAiB,uBAA6B5mC,EAAE,GAAG+S,IAAInK,SAASkD,MAAMiH,IAAInK,SAASojC,MAAMC,GAAYl5B,IAAW,2BAAPA,EAAEW,KAAgCN,IAAIL,EAAE/H,SAASoI,IAAIL,EAAE/H,SAA6C,OAAnCoyB,EAAK,MAAHhqB,OAAQ,EAAOA,EAAEi0B,oBAAqB,EAAOjK,EAAEhiB,QAAQpI,EAAElI,MAAK2d,GAAG1V,EAAE/H,SAASyd,MAAKzV,EAAEkU,KAAKnU,IAAWC,OAAAA,CAAAA,IAAI,MAAM,CAAC4+B,kBAAkBtU,EAAEtyB,SAASw0B,IAAExsB,GAAGsqB,IAAIxyB,MAAK9K,GAAGA,EAAEgL,SAASgI,OAAK,CKAojE8Q,CAAG,CAACguB,aAAa/T,EAAE0T,QAAQ/D,EAAEmE,kBAAkB,CAAC/E,KAAM7O,EAAM,OAAJb,IAAUA,EAAE9oB,GAAEu2B,WAAWv2B,GAAEu2B,S9BA/3E,SAAW73B,GAAG++B,QAAQj/B,EAAEk/B,WAAWj/B,GAAG,CAAA,GAAQ/S,IAAAA,EAAE+9B,GAAE/qB,EAAE,gBAAgBssB,IAAE,KAAK,IAAI7W,EAAE2U,EAAE,IAAIp9B,EAAE,OAAO,IAAI+hB,EAAE+c,KAAI,IAAA,IAAQ5rB,KAA4B,OAAvBuV,EAAK,MAAH1V,OAAQ,EAAOA,KAAW0V,EAAE,GAAGvV,GAAG6O,EAAE9V,IAAIkzB,GAAEjsB,IAAQ0oB,IAAAA,EAA0B,OAAvBwB,EAAK,MAAHtqB,OAAQ,EAAOA,KAAWsqB,EAAE,GAAG,IAAA,IAAQlqB,KAAK0oB,EAAE,CAAC,IAAI1oB,EAAE,SAAasrB,IAAAA,EAAExc,GAAE9O,GAAG,IAAIsrB,EAAE,SAAS,IAAIprB,EAAEF,EAAEu0B,cAAmBr0B,KAAAA,GAAGA,IAAIorB,EAAE1yB,MAAM,CAAC,IAAA,IAAQutB,KAAKjmB,EAAE7R,SAASq6B,EAAE9wB,MAAKs1B,GAAG/G,EAAEruB,SAASo1B,MAAKre,EAAE9V,IAAIkzB,GAAE9F,IAAIjmB,EAAEA,EAAEq0B,aAAa,CAAC,CAAC,OAAO1lB,EAAEkb,OAAA,GAAS,CAACj9B,EAAE8S,EAAEC,GAAG,C8BA4+Dm9B,EAAG5S,IAAGW,GAAK0C,EAAE,CAACoR,QAAQvR,IAAE,KAAK,IAAI1tB,EAAEw6B,EAAE,MAAM,CAAuE,OAArEA,EAAiB,OAAdx6B,EAAEG,EAAEzT,cAAe,EAAOsT,EAAEy1B,QAAQ,6BAAmC+E,EAAE,KAAI,IAAI0E,WAAWxR,IAAE,KAAS1tB,IAAAA,EAAQ,MAAA,CAAwE,OAAtEA,EAAK,MAAHirB,OAAQ,EAAOA,EAAEwK,QAAQ,0CAAgDz1B,EAAE,KAAI,MAAU4rB,IAAAA,EAAE4P,GAAG7pC,IAAI,MAAMurC,IAAG,KAAQrP,GAAAA,EAASjC,OAAAA,EAAEkG,QAAQ1d,KAAKhU,GAAG,IAAIwrB,EAAEkG,QAAQrL,IAAIrmB,EAAC,GAAG,CAACwrB,EAAExrB,EAAEytB,IAAI,IAAIf,EAAEqS,GAAGvT,EAAEuP,EAAAA,aAAGn7B,GAAG4rB,EAAEiG,UAAUzB,MAAMpwB,EAAEI,IAAG,CAACwrB,EAAExrB,KAAQ0sB,GAAAA,EAAEd,GAAEhsB,IAAIA,EAAEjH,iBAAiB2zB,GAAG,ICAh1G,SAAWpsB,EAAEN,GAAmB,oBAAVlK,SAAsBA,SAAS6G,YAAY,MAAKuD,GAAOE,IAAAA,EAAED,GAAEG,EAAE,UAAUpT,GAAE8S,EAAE,WAAUC,IAAIG,IAAIH,EAAE3T,kBAAkB2T,EAAErH,MAAM4xB,GAAEyD,QAAQ/tB,EAAED,GAAC,GAAI,CDAmrGo9B,CAAGvQ,EAAK,MAAH5d,OAAQ,EAAOA,EAAEvS,aAAYqD,IAAIA,EAAEjH,iBAAiBiH,EAAEo6B,kBAAkBtkC,SAASo+B,eAAe,SAASp+B,SAASo+B,eAAmD,mBAA7Bp+B,SAASo+B,cAAckL,MAAkBtpC,SAASo+B,cAAckL,OAAO1S,OEApjH,SAAWzsB,EAAEqqB,EAAElqB,EAAE,IAAI,CAACtK,SAASkD,OAAkCgH,GAArB0sB,GAAEzsB,EAAE,eAAmBqqB,GAAEpqB,IAAQI,IAAAA,EAAE,MAAM,CAAC+0B,WAAW,IAAsB,OAAjB/0B,EAAEJ,EAAEm1B,YAAkB/0B,EAAE,GAAGF,GAAE,GAAG,CFAo7Gm9B,EAAG/S,IAAGW,GAAK0C,EAAE3e,EAAE8c,GGA7iH,SAAWlD,EAAE1oB,EAAEE,GAAOpT,IAAAA,EAAEyoB,IAAEzV,IAAQD,IAAAA,EAAEC,EAAE4J,wBAA8B,IAAN7J,EAAEuB,GAAa,IAANvB,EAAEwB,GAAiB,IAAVxB,EAAEX,OAAsB,IAAXW,EAAEV,QAAYe,GAAG,IAAGorB,EAAAA,WAAE,KAAK,IAAI5C,EAAE,OAAW5oB,IAAAA,EAAM,OAAJE,EAAS,KAAKi/B,GAAgBj/B,GAAGA,EAAEA,EAAE1T,QAAQ,IAAIwT,EAAE,OAAO,IAAID,EAAEuqB,KAAO,GAAuB,oBAAhBhb,eAA4B,CAAC,IAAIxP,EAAE,IAAIwP,gBAAe,IAAItiB,EAAER,QAAQwT,KAAIF,EAAEiR,QAAQ/Q,GAAGD,EAAE9G,KAAI,IAAI6G,EAAEqQ,cAAa,CAAI,GAA6B,oBAAtBX,qBAAkC,CAAC,IAAI1P,EAAE,IAAI0P,sBAAqB,IAAIxiB,EAAER,QAAQwT,KAAIF,EAAEiR,QAAQ/Q,GAAGD,EAAE9G,KAAI,IAAI6G,EAAEqQ,cAAa,CAAO,MAAA,IAAIpQ,EAAEkqB,SAAS,GAAE,CAAC/pB,EAAElT,EAAE47B,GAAG,CHAokG2U,CAAG5P,EAAE1tB,EAAEusB,GAAG,IAAI4P,EAAGtD,GxCA79F,WAAa,IAAIh5B,EAAEC,GAAG2rB,EAAC51B,SAAC,IAAI,MAAM,CAACgK,EAAE7S,OAAO,EAAE6S,EAAErN,KAAK,UAAK,EAAO23B,EAACp8B,SAAC,IAAI,SAASgS,GAAG,IAAIhT,EAAEu/B,IAAErsB,IAAIH,GAAE6oB,GAAG,IAAIA,EAAE1oB,KAAI,IAAIH,GAAE6oB,IAAI,IAAIxoB,EAAEwoB,EAAExyB,QAAQiwB,EAAEjmB,EAAE9J,QAAQ4J,GAAG,OAAeE,IAARimB,GAAQjmB,EAAE8pB,OAAO7D,EAAE,GAAGjmB,CAAAA,OAAMorB,EAAEpB,EAAAA,SAAE,KAAA,CAAMsD,SAAS1gC,EAAEo+B,KAAKprB,EAAEorB,KAAK3xB,KAAKuG,EAAEvG,KAAKnL,MAAM0R,EAAE1R,MAAM/B,MAAMyT,EAAEzT,SAAQ,CAACS,EAAEgT,EAAEorB,KAAKprB,EAAEvG,KAAKuG,EAAE1R,MAAM0R,EAAEzT,QAAeigC,OAAAA,EAAEK,cAAc9d,GAAE1gB,SAAS,CAAC9B,MAAMi/B,GAAGxrB,EAAEzR,SAAS,GAAE,CAACwR,IAAI,CwCA2nF09B,GAAK1D,EAAGnP,EAAC58B,SAAC,IAAI,CAAC,CAACoxC,YAAY7S,EAAE8S,MAAM7S,EAAE8S,WAAWzG,EAAEhN,QAAQtqB,GAAGF,IAAG,CAACkrB,EAAElrB,EAAEmrB,EAAEqM,EAAEt3B,IAAIorB,EAAE/B,EAAC58B,SAAC,MAAMkwB,KAAS,IAAJqO,KAAQ,CAACA,IAAIiP,EAAG,CAAClvC,IAAIshC,EAAEltB,GAAGR,EAAEmoB,KAAKhC,EAAEyN,YAAY,aAAaxJ,OAAE,EAAW,IAAJiC,QAAS,EAAO,kBAAkBlrB,EAAE68B,QAAQ,mBAAmB9B,EAAGvQ,QAAQtqB,GAAGo7B,GIAl4H,WAAiB38B,IAAAA,EAAE,IAAID,GAAG/S,EAAAA,UAAE,IAAmB,oBAAR0G,QAA+C,mBAAnBA,OAAO6rC,WAAuB7rC,OAAO6rC,WAAW,qBAAqB,QAAOn/B,EAAEgqB,GAAGp9B,EAAC8I,SAA+B,OAA7BkK,EAAK,MAAHD,OAAQ,EAAOA,EAAEgH,UAAe/G,GAAM,OAAO4oB,IAAE,KAAK,GAAI7oB,EAA2CA,OAAAA,EAAEzI,iBAAiB,SAAS4I,GAAG,IAAIH,EAAE1I,oBAAoB,SAAS6I,GAApG,SAASA,EAAEJ,GAAGsqB,EAAEtqB,EAAEiH,QAAQ,CAA2E,GAAG,CAAChH,IAAIK,CAAC,CJA8jHw8B,GAAKxP,EAAEV,GAAEjC,KAAKkD,IAAIrD,IAAI8C,GAAGV,GAAE6M,aAAanM,GAAGV,GAAE2M,QAAQ/M,IAAIc,GAAGV,GAAE6F,WAAWoK,IAAKvP,GAAGV,GAAE0M,eAAe,IAAIwC,EAAGzP,KAAWX,OAAAA,EAAEqB,cAAcwO,GAAG,KAAK7P,EAAEqB,cAAcK,GAAE,CAACr3B,OAAM,GAAI21B,EAAEqB,cAAc2Q,GAAG,KAAKhS,EAAEqB,cAAcJ,GAAEp+B,SAAS,CAAC9B,MAAMwtC,GAAIvO,EAAEqB,cAAc2S,GAAG,CAAC3rC,OAAOoM,GAAGurB,EAAEqB,cAAcK,GAAE,CAACr3B,OAAM,GAAI21B,EAAEqB,cAAciM,EAAG,CAAC1N,KAAKuB,GAAGnB,EAAEqB,cAAcgN,EAAG,KAAKrO,EAAEqB,cAAc4S,GAAG,CAAC/F,aAAajkB,EAAEkkB,qBAAqB15B,EAAEk1B,WAAWrJ,EAAER,SAAS8B,GAAG5B,EAAEqB,cAAc8Q,GAAG,CAACpxC,MAAMigC,GAAGoP,EAAG,CAAC1Q,SAASsQ,EAAGrQ,WAAWgC,EAAE/B,KAAKuB,EAAEtB,WAAW4R,GAAG3R,SAASgS,GAAG/R,QAAY,IAAJgB,EAAM9yB,KAAK,oBAAoB,IAAGwjC,GAAG,MAAMK,GAAG3E,GAAEjO,eAAeiO,GAAEhO,OAAg6D,IAAC+U,GAAG1T,IAA75D,SAAYjsB,EAAEC,GAAM,IAACq3B,WAAWj3B,GAAE,EAAG8d,KAAKnP,KAAK7O,GAAGH,EAAE/S,EAAE4sC,KAAIhR,EAAE7oB,EAAE6Z,eAAe,SAAa,OAAJ5sB,EAASyoB,EAAE1V,EAAE6Z,eAAe,WAAW,IAAIgP,IAAInT,EAAQ,MAAA,IAAIvmB,MAAM,kFAAkF,IAAI05B,EAAQ,MAAA,IAAI15B,MAAM,8EAA8E,IAAIumB,EAAQ,MAAA,IAAIvmB,MAAM,8EAA8E,IAAIlC,GAAkB,kBAAR+S,EAAEme,KAAsB,MAAA,IAAIhvB,MAAM,8FAA8F6Q,EAAEme,QAAW,GAAkB,mBAAXne,EAAEkjB,QAAoB,MAAM,IAAI/zB,MAAM,kGAAkG6Q,EAAEkjB,WAAkBlU,YAAI,IAAJA,IAAY3O,GAAKF,EAAE0rB,OAA2HJ,EAAEqB,cAAc2M,GAAE,KAAKhO,EAAEqB,cAAc4M,GAAE,CAACntC,IAAI0T,EAAEke,KAAKnP,KAAK7O,KAA9KsrB,EAAEqB,cAAc2M,GAAE,KAAKhO,EAAEqB,cAAc8S,GAAG,CAACzD,KAAKntB,EAAEsoB,WAAWj3B,EAAEyrB,QAAQ3rB,EAAE2rB,SAASL,EAAEqB,cAAc4M,GAAE,CAACntC,IAAI0T,KAAKE,KAAoE,IAAw+B0/B,GAAG5T,IAA79B,SAAYjsB,EAAEC,GAAOI,IAAAA,EAAE4qB,EAACxqB,SAAIE,GAAGqO,EAAE,2BAA2B3O,IAAIi3B,WAAWn3B,GAAE,KAAMlT,GAAG+S,IAAIq/B,YAAYxW,EAAEiD,QAAQpW,GAAG4Q,GAAGmE,GAAE,gBAAgB8B,EAAEgM,GAAEt4B,EAAEqmB,EAAEkY,UAAUjU,EAAEM,EAAC58B,SAAC,KAAA,CAAMkwB,KAAS,IAAJ0K,KAAQ,CAACA,IAAkCuE,EAAE,CAAC7gC,IAAIggC,EAAE5rB,GAAGqO,EAAE2V,QAA1C8I,IAAEI,IAAIA,EAAEsM,iBAAe,KAA8BvO,EAAEzrB,EAAEomB,GAAE7c,EAAAA,SAAE2gB,EAAElqB,EAAE,CAAC2rB,QAAQpW,GAAG,CAAE,EAACxV,EAAEksB,KAAWX,OAAAA,EAAEqB,cAAclB,EAAE,IAAIvB,GAAGnqB,EAAE,CAACirB,SAASiC,EAAEhC,WAAWn+B,EAAEo+B,KAAKd,EAAEe,WAAtV,MAAoW5xB,KAAK,iBAAiB,IAAknBuyB,IAApmB,SAAYjsB,EAAEC,GAAM,IAACq3B,WAAWj3B,GAAE,KAAM2O,GAAGhP,IAAIq/B,YAAYl/B,EAAE2rB,QAAQ7+B,IAAIw9B,GAAE,mBAAmB5B,EAAEgC,EAAAA,SAAE,KAAK,CAAC1M,KAAS,IAAJhe,KAAQ,CAACA,IAAIuV,EAAE,CAACnpB,IAAI0T,EAAE,eAAc,GAAIqmB,EAAEjmB,EAAEkmB,GAAE7c,EAAAA,SAAE6iB,EAAElsB,EAAE,CAACyrB,QAAQ7+B,GAAG,CAAE,EAACs9B,EAAE6B,KAAWX,OAAAA,EAAEqB,cAAcxG,EAAE,IAAIiG,GAAGhC,EAAE,CAACY,SAASzV,EAAE0V,WAAWpc,EAAEqc,KAAKxC,EAAEyC,WAA9P,MAA4Q5xB,KAAK,oBAAoB,QAA2UmkC,GAAG5R,IAAjU,SAAYjsB,EAAEC,GAAOI,IAAAA,EAAE4qB,EAACxqB,SAAIE,GAAGqO,EAAE,2BAA2B3O,OAAOF,GAAGH,IAAIq/B,YAAYpyC,EAAEsyC,WAAW1W,IAAI4B,GAAE,gBAAgB/U,EAAE6iB,GAAEt4B,GAAGg8B,EAAAA,WAAG,KAAKpT,EAAE7Z,GAAG,IAAI6Z,EAAE,QAAO,CAAC7Z,EAAE6Z,IAAI,IAAIvC,EAAEuE,WAAE,MAAM1M,KAAS,IAAJlxB,KAAQ,CAACA,IAAIs/B,EAAE,CAAChgC,IAAImpB,EAAE/U,GAAGqO,GAAG,OAAOod,KAAI,CAACjB,SAASoB,EAAEnB,WAAWjrB,EAAEkrB,KAAK/E,EAAEgF,WAAhQ,KAA8Q5xB,KAAK,gBAAgB,IAA8ComC,GAAGlxC,OAAOm7B,OAAO4V,GAAG,CAACI,MAAMF,GAAGG,MAAMnC,GAAGoC,YAAYzD", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]}
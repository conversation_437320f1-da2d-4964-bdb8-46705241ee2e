export default {
  title: "Feedback",
  subtitle: "Tu opinión importa",
  description: "Comparte tu experiencia y sugerencias",
  typeQuestion: "¿Qué tipo de feedback te gustaría compartir?",
  close: "Cerra<PERSON>",
  back: "Volver",
  send: "Enviar feedback",
  sending: "Enviando...",
  includeEmail: "Incluir mi email para respuesta",
  privacyPolicy: "Política de Privacidad",

  // Tipos de feedback
  problem: "Reportar Problema",
  idea: "Compartir Idea",
  praise: "Dar Elogio",

  // Títulos específicos por tipo
  problemTitle: "Reportar Problema",
  ideaTitle: "Compartir Idea",
  praiseTitle: "Dar Elogio",
  defaultTitle: "Enviar Feedback",

  // Instrucciones específicas por tipo
  problemInstruction: "Describe el problema que encontraste en detalle",
  ideaInstruction: "Comparte tu idea o sugerencia de mejora",
  praiseInstruction: "Cuéntanos qué te gustó",
  defaultInstruction: "Comparte tu feedback con nosotros",

  // Placeholders específicos por tipo
  problemPlaceholder: "Describe el problema que encontraste...",
  ideaPlaceholder: "Comparte tu idea o sugerencia...",
  praisePlaceholder: "Cuéntanos qué te gustó...",
  defaultPlaceholder: "Comparte tu feedback...",

  // Validación
  validation: {
    messageRequired: "El mensaje es obligatorio",
    messageMinLength: "Mínimo 5 caracteres",
    emailInvalid: "Email inválido"
  },

  // Formulario
  form: {
    type: "Tipo de feedback",
    message: "Tu mensaje",
    email: "Tu email (opcional)",
    send: "Enviar feedback",
    sending: "Enviando...",
    success: "✅ ¡Gracias por tu feedback! Tu opinión es muy importante para nosotros.",
    error: "❌ ¡Ups! No pudimos enviar tu feedback. Intenta de nuevo o contacta directamente.",
    messageRequired: "El mensaje es obligatorio"
  },

  // Mensajes de estado
  status: {
    success: "¡Gracias por tu feedback!",
    error: "Error al enviar feedback. Inténtalo de nuevo.",
    sending: "Enviando feedback..."
  },

  // Tipos legados (mantener compatibilidad)
  types: {
    bug: "Reportar bug",
    suggestion: "Sugerencia",
    compliment: "Elogio",
    other: "Otro"
  }
};

export default {
  title: "Feedback",
  subtitle: "Sua opinião importa",
  description: "Compartilhe sua experiência e sugestões",
  typeQuestion: "Que tipo de feedback você gostaria de compartilhar?",
  close: "<PERSON><PERSON><PERSON>",
  back: "Voltar",
  send: "Enviar feedback",
  sending: "Enviando...",
  includeEmail: "Incluir meu e-mail para resposta",
  privacyPolicy: "Política de Privacidade",

  // Tipos de feedback
  problem: "Reportar Problema",
  idea: "Compartilhar Ideia",
  praise: "Dar Elogio",

  // Títulos específicos por tipo
  problemTitle: "Reportar Problema",
  ideaTitle: "Compartilhar Ideia",
  praiseTitle: "Dar Elogio",
  defaultTitle: "Enviar Feedback",

  // Instruções específicas por tipo
  problemInstruction: "Descreva o problema que você encontrou em detalhes",
  ideaInstruction: "Compartilhe sua ideia ou sugestão de melhoria",
  praiseInstruction: "Conte-nos o que você gostou",
  defaultInstruction: "Compartilhe seu feedback conosco",

  // Placeholders específicos por tipo
  problemPlaceholder: "Descreva o problema que você encontrou...",
  ideaPlaceholder: "Compartilhe sua ideia ou sugestão...",
  praisePlaceholder: "Conte-nos o que você gostou...",
  defaultPlaceholder: "Compartilhe seu feedback...",

  // Validação
  validation: {
    messageRequired: "Mensagem é obrigatória",
    messageMinLength: "Mínimo 5 caracteres",
    emailInvalid: "E-mail inválido"
  },

  // Formulário
  form: {
    type: "Tipo de feedback",
    message: "Sua mensagem",
    email: "Seu e-mail (opcional)",
    send: "Enviar feedback",
    sending: "Enviando...",
    success: "✅ Obrigado pelo seu feedback! Sua opinião é muito importante para nós.",
    error: "❌ Ops! Não conseguimos enviar seu feedback. Tente novamente ou entre em contato diretamente.",
    messageRequired: "A mensagem é obrigatória"
  },

  // Status messages
  status: {
    success: "Obrigado pelo seu feedback!",
    error: "Erro ao enviar feedback. Tente novamente.",
    sending: "Enviando feedback..."
  },

  // Tipos legados (manter compatibilidade)
  types: {
    bug: "Reportar bug",
    suggestion: "Sugestão",
    compliment: "Elogio",
    other: "Outro"
  }
};

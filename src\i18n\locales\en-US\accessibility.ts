export default {
  title: "Accessibility",
  subtitle: "Customize your browsing experience",
  description: "Configure accessibility options to improve your experience.",
  instructions: "Use the options below to customize the interface.",
  close: "Close",
  open: "Open",
  menuLabel: "Accessibility Menu",
  menuTooltip: "Accessibility settings (Shift + A)",
  fontSize: {
    label: "Font size",
    increase: "Increase",
    decrease: "Decrease",
    reset: "Reset",
    increaseLabel: "Increase font size",
    decreaseLabel: "Decrease font size",
    resetLabel: "Reset font size"
  },
  contrast: {
    label: "High contrast",
    enable: "Enable high contrast",
    disable: "Disable high contrast",
    enabled: "High contrast enabled",
    disabled: "High contrast disabled"
  },
  readingMode: {
    label: "Reading mode",
    enable: "Enable reading mode",
    disable: "Disable reading mode",
    enabled: "Reading mode enabled",
    disabled: "Reading mode disabled"
  },
  screenReader: {
    label: "Screen reader",
    enable: "Enable screen reader",
    disable: "Disable screen reader",
    enabled: "Screen reader enabled",
    disabled: "Screen reader disabled"
  },
  reset: {
    label: "Reset settings",
    action: "Accessibility settings reset",
    success: "Settings reset successfully"
  },
  features: {
    fontSize: "Font size",
    fontSizeIncrease: "Increase font",
    fontSizeDecrease: "Decrease font",
    fontSizeReset: "Reset font",
    contrast: "High contrast",
    contrastEnable: "Enable contrast",
    contrastDisable: "Disable contrast",
    readingMode: "Reading mode",
    readingModeEnable: "Enable reading",
    readingModeDisable: "Disable reading"
  },
  skipLinks: {
    skipToContent: "Skip to main content",
    skipToNavigation: "Skip to navigation"
  },
  status: {
    enabled: "Enabled",
    disabled: "Disabled",
    fontIncreased: "Font increased",
    fontDecreased: "Font decreased",
    fontReset: "Font reset",
    contrastEnabled: "High contrast enabled",
    contrastDisabled: "High contrast disabled",
    readingEnabled: "Reading mode enabled",
    readingDisabled: "Reading mode disabled"
  }
};

import{r as e}from"./chunk-D3Ns84uO.js";const t={},s=(e,s,i,o)=>{a(i)&&t[i]||(a(i)&&(t[i]=new Date),((e,t,s,i)=>{var o,n,r,l;const u=[s,{code:t,...i||{}}];if(null==(n=null==(o=null==e?void 0:e.services)?void 0:o.logger)?void 0:n.forward)return e.services.logger.forward(u,"warn","react-i18next::",!0);a(u[0])&&(u[0]=`react-i18next:: ${u[0]}`),(null==(l=null==(r=null==e?void 0:e.services)?void 0:r.logger)?void 0:l.warn)?e.services.logger.warn(...u):(null==console?void 0:console.warn)&&console.warn(...u)})(e,s,i,o))},i=(e,t)=>()=>{if(e.isInitialized)t();else{const s=()=>{setTimeout((()=>{e.off("initialized",s)}),0),t()};e.on("initialized",s)}},o=(e,t,s)=>{e.loadNamespaces(t,i(e,s))},n=(e,t,s,n)=>{if(a(s)&&(s=[s]),e.options.preload&&e.options.preload.indexOf(t)>-1)return o(e,s,n);s.forEach((t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)})),e.loadLanguages(t,i(e,n))},a=e=>"string"==typeof e,r=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,l={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},u=e=>l[e];let c={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(r,u)};let h;const p={type:"3rdParty",init(e){((e={})=>{c={...c,...e}})(e.options.react),(e=>{h=e})(e)}},d=e.createContext();class g{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const f=(e,t,s,i)=>e.getFixedT(t,s,i),m=(t,i={})=>{var r,l,u,p;const{i18n:m}=i,{i18n:v,defaultNS:y}=e.useContext(d)||{},x=m||v||h;if(x&&!x.reportNamespaces&&(x.reportNamespaces=new g),!x){s(x,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>{return a(t)?t:"object"==typeof(s=t)&&null!==s&&a(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e;var s},t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}(null==(r=x.options.react)?void 0:r.wait)&&s(x,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const b={...c,...x.options.react,...i},{useSuspense:S,keyPrefix:k}=b;let w=y||(null==(l=x.options)?void 0:l.defaultNS);w=a(w)?[w]:w||["translation"],null==(p=(u=x.reportNamespaces).addUsedNamespaces)||p.call(u,w);const O=(x.isInitialized||x.initializedStoreOnce)&&w.every((e=>((e,t,i={})=>t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:i.lng,precheck:(t,s)=>{var o;if((null==(o=i.bindI18n)?void 0:o.indexOf("languageChanging"))>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!s(t.isLanguageChangingTo,e))return!1}}):(s(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0))(e,x,b))),L=((t,s,i,o)=>e.useCallback(f(t,s,i,o),[t,s,i,o]))(x,i.lng||null,"fallback"===b.nsMode?w:w[0],k),$=()=>L,C=()=>f(x,i.lng||null,"fallback"===b.nsMode?w:w[0],k),[N,R]=e.useState($);let P=w.join();i.lng&&(P=`${i.lng}${P}`);const E=((t,s)=>{const i=e.useRef();return e.useEffect((()=>{i.current=t}),[t,s]),i.current})(P),j=e.useRef(!0);e.useEffect((()=>{const{bindI18n:e,bindI18nStore:t}=b;j.current=!0,O||S||(i.lng?n(x,i.lng,w,(()=>{j.current&&R(C)})):o(x,w,(()=>{j.current&&R(C)}))),O&&E&&E!==P&&j.current&&R(C);const s=()=>{j.current&&R(C)};return e&&(null==x||x.on(e,s)),t&&(null==x||x.store.on(t,s)),()=>{j.current=!1,x&&(null==e||e.split(" ").forEach((e=>x.off(e,s)))),t&&x&&t.split(" ").forEach((e=>x.store.off(e,s)))}}),[x,P]),e.useEffect((()=>{j.current&&O&&R($)}),[x,k,O]);const I=[N,x,O];if(I.t=N,I.i18n=x,I.ready=O,O)return I;if(!O&&!S)return I;throw new Promise((e=>{i.lng?n(x,i.lng,w,(()=>e())):o(x,w,(()=>e()))}))},v=e=>"string"==typeof e,y=()=>{let e,t;const s=new Promise(((s,i)=>{e=s,t=i}));return s.resolve=e,s.reject=t,s},x=e=>null==e?"":""+e,b=/###/g,S=e=>e&&e.indexOf("###")>-1?e.replace(b,"."):e,k=e=>!e||v(e),w=(e,t,s)=>{const i=v(t)?t.split("."):t;let o=0;for(;o<i.length-1;){if(k(e))return{};const t=S(i[o]);!e[t]&&s&&(e[t]=new s),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++o}return k(e)?{}:{obj:e,k:S(i[o])}},O=(e,t,s)=>{const{obj:i,k:o}=w(e,t,Object);if(void 0!==i||1===t.length)return void(i[o]=s);let n=t[t.length-1],a=t.slice(0,t.length-1),r=w(e,a,Object);for(;void 0===r.obj&&a.length;)n=`${a[a.length-1]}.${n}`,a=a.slice(0,a.length-1),r=w(e,a,Object),(null==r?void 0:r.obj)&&void 0!==r.obj[`${r.k}.${n}`]&&(r.obj=void 0);r.obj[`${r.k}.${n}`]=s},L=(e,t)=>{const{obj:s,k:i}=w(e,t);if(s&&Object.prototype.hasOwnProperty.call(s,i))return s[i]},$=(e,t,s)=>{for(const i in t)"__proto__"!==i&&"constructor"!==i&&(i in e?v(e[i])||e[i]instanceof String||v(t[i])||t[i]instanceof String?s&&(e[i]=t[i]):$(e[i],t[i],s):e[i]=t[i]);return e},C=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var N={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const R=e=>v(e)?e.replace(/[&<>"'\/]/g,(e=>N[e])):e;const P=[" ",",","?","!",";"],E=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}(20),j=(e,t,s=".")=>{if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}const i=t.split(s);let o=e;for(let n=0;n<i.length;){if(!o||"object"!=typeof o)return;let e,t="";for(let a=n;a<i.length;++a)if(a!==n&&(t+=s),t+=i[a],e=o[t],void 0!==e){if(["string","number","boolean"].indexOf(typeof e)>-1&&a<i.length-1)continue;n+=a-n+1;break}o=e}return o},I=e=>null==e?void 0:e.replace("_","-"),F={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){var s,i;null==(i=null==(s=null==console?void 0:console[e])?void 0:s.apply)||i.call(s,console,t)}};class D{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||F,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,i){return i&&!this.debug?null:(v(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new D(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new D(this.logger,e)}}var T=new D;class V{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach((e=>{this.observers[e]||(this.observers[e]=new Map);const s=this.observers[e].get(t)||0;this.observers[e].set(t,s+1)})),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e,...t){if(this.observers[e]){Array.from(this.observers[e].entries()).forEach((([e,s])=>{for(let i=0;i<s;i++)e(...t)}))}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach((([s,i])=>{for(let o=0;o<i;o++)s.apply(s,[e,...t])}))}}}class U extends V{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s,i={}){var o,n;const a=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,r=void 0!==i.ignoreJSONStructure?i.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;e.indexOf(".")>-1?l=e.split("."):(l=[e,t],s&&(Array.isArray(s)?l.push(...s):v(s)&&a?l.push(...s.split(a)):l.push(s)));const u=L(this.data,l);return!u&&!t&&!s&&e.indexOf(".")>-1&&(e=l[0],t=l[1],s=l.slice(2).join(".")),!u&&r&&v(s)?j(null==(n=null==(o=this.data)?void 0:o[e])?void 0:n[t],s,a):u}addResource(e,t,s,i,o={silent:!1}){const n=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator;let a=[e,t];s&&(a=a.concat(n?s.split(n):s)),e.indexOf(".")>-1&&(a=e.split("."),i=t,t=a[1]),this.addNamespaces(t),O(this.data,a,i),o.silent||this.emit("added",e,t,s,i)}addResources(e,t,s,i={silent:!1}){for(const o in s)(v(s[o])||Array.isArray(s[o]))&&this.addResource(e,t,o,s[o],{silent:!0});i.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,i,o,n={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),i=s,s=t,t=a[1]),this.addNamespaces(t);let r=L(this.data,a)||{};n.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?$(r,s,o):r={...r,...s},O(this.data,a,r),n.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find((e=>t[e]&&Object.keys(t[e]).length>0))}toJSON(){return this.data}}var A={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,s,i,o){return e.forEach((e=>{var n;t=(null==(n=this.processors[e])?void 0:n.process(t,s,i,o))??t})),t}};const M={},K=e=>!v(e)&&"boolean"!=typeof e&&"number"!=typeof e;class z extends V{constructor(e,t={}){var s,i;super(),s=e,i=this,["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"].forEach((e=>{s[e]&&(i[e]=s[e])})),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=T.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const s={...t};if(null==e)return!1;const i=this.resolve(e,s);return void 0!==(null==i?void 0:i.res)}extractFromKey(e,t){let s=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===s&&(s=":");const i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator;let o=t.ns||this.options.defaultNS||[];const n=s&&e.indexOf(s)>-1,a=!(this.options.userDefinedKeySeparator||t.keySeparator||this.options.userDefinedNsSeparator||t.nsSeparator||((e,t,s)=>{t=t||"",s=s||"";const i=P.filter((e=>t.indexOf(e)<0&&s.indexOf(e)<0));if(0===i.length)return!0;const o=E.getRegExp(`(${i.map((e=>"?"===e?"\\?":e)).join("|")})`);let n=!o.test(e);if(!n){const t=e.indexOf(s);t>0&&!o.test(e.substring(0,t))&&(n=!0)}return n})(e,s,i));if(n&&!a){const t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:v(o)?[o]:o};const n=e.split(s);(s!==i||s===i&&this.options.ns.indexOf(n[0])>-1)&&(o=n.shift()),e=n.join(i)}return{key:e,namespaces:v(o)?[o]:o}}translate(e,t,s){let i="object"==typeof t?{...t}:t;if("object"!=typeof i&&this.options.overloadTranslationOptionHandler&&(i=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(i={...i}),i||(i={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);const o=void 0!==i.returnDetails?i.returnDetails:this.options.returnDetails,n=void 0!==i.keySeparator?i.keySeparator:this.options.keySeparator,{key:a,namespaces:r}=this.extractFromKey(e[e.length-1],i),l=r[r.length-1];let u=void 0!==i.nsSeparator?i.nsSeparator:this.options.nsSeparator;void 0===u&&(u=":");const c=i.lng||this.language,h=i.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if("cimode"===(null==c?void 0:c.toLowerCase()))return h?o?{res:`${l}${u}${a}`,usedKey:a,exactUsedKey:a,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(i)}:`${l}${u}${a}`:o?{res:a,usedKey:a,exactUsedKey:a,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(i)}:a;const p=this.resolve(e,i);let d=null==p?void 0:p.res;const g=(null==p?void 0:p.usedKey)||a,f=(null==p?void 0:p.exactUsedKey)||a,m=void 0!==i.joinArrays?i.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,x=void 0!==i.count&&!v(i.count),b=z.hasDefaultValue(i),S=x?this.pluralResolver.getSuffix(c,i.count,i):"",k=i.ordinal&&x?this.pluralResolver.getSuffix(c,i.count,{ordinal:!1}):"",w=x&&!i.ordinal&&0===i.count,O=w&&i[`defaultValue${this.options.pluralSeparator}zero`]||i[`defaultValue${S}`]||i[`defaultValue${k}`]||i.defaultValue;let L=d;y&&!d&&b&&(L=O);const $=K(L),C=Object.prototype.toString.apply(L);if(!(y&&L&&$&&["[object Number]","[object Function]","[object RegExp]"].indexOf(C)<0)||v(m)&&Array.isArray(L))if(y&&v(m)&&Array.isArray(d))d=d.join(m),d&&(d=this.extendTranslation(d,e,i,s));else{let t=!1,o=!1;!this.isValidLookup(d)&&b&&(t=!0,d=O),this.isValidLookup(d)||(o=!0,d=a);const r=(i.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&o?void 0:d,h=b&&O!==d&&this.options.updateMissing;if(o||t||h){if(this.logger.log(h?"updateKey":"missingKey",c,l,a,h?O:d),n){const e=this.resolve(a,{...i,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,i.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let i=0;i<t.length;i++)e.push(t[i]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(i.lng||this.language):e.push(i.lng||this.language);const s=(e,t,s)=>{var o;const n=b&&s!==d?s:r;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,t,n,h,i):(null==(o=this.backendConnector)?void 0:o.saveMissing)&&this.backendConnector.saveMissing(e,l,t,n,h,i),this.emit("missingKey",e,l,t,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&x?e.forEach((e=>{const t=this.pluralResolver.getSuffixes(e,i);w&&i[`defaultValue${this.options.pluralSeparator}zero`]&&t.indexOf(`${this.options.pluralSeparator}zero`)<0&&t.push(`${this.options.pluralSeparator}zero`),t.forEach((t=>{s([e],a+t,i[`defaultValue${t}`]||O)}))})):s(e,a,O))}d=this.extendTranslation(d,e,i,p,s),o&&d===a&&this.options.appendNamespaceToMissingKey&&(d=`${l}${u}${a}`),(o||t)&&this.options.parseMissingKeyHandler&&(d=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}${u}${a}`:a,t?d:void 0,i))}else{if(!i.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,L,{...i,ns:r}):`key '${a} (${this.language})' returned an object instead of string.`;return o?(p.res=e,p.usedParams=this.getUsedParamsDetails(i),p):e}if(n){const e=Array.isArray(L),t=e?[]:{},s=e?f:g;for(const o in L)if(Object.prototype.hasOwnProperty.call(L,o)){const e=`${s}${n}${o}`;t[o]=b&&!d?this.translate(e,{...i,defaultValue:K(O)?O[o]:void 0,joinArrays:!1,ns:r}):this.translate(e,{...i,joinArrays:!1,ns:r}),t[o]===e&&(t[o]=L[o])}d=t}}return o?(p.res=d,p.usedParams=this.getUsedParamsDetails(i),p):d}extendTranslation(e,t,s,i,o){var n,a;if(null==(n=this.i18nFormat)?void 0:n.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const n=v(e)&&(void 0!==(null==(a=null==s?void 0:s.interpolation)?void 0:a.skipOnVariables)?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let r;if(n){const t=e.match(this.interpolator.nestingRegexp);r=t&&t.length}let l=s.replace&&!v(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),e=this.interpolator.interpolate(e,l,s.lng||this.language||i.usedLng,s),n){const t=e.match(this.interpolator.nestingRegexp);r<(t&&t.length)&&(s.nest=!1)}!s.lng&&i&&i.res&&(s.lng=this.language||i.usedLng),!1!==s.nest&&(e=this.interpolator.nest(e,((...e)=>(null==o?void 0:o[0])!==e[0]||s.context?this.translate(...e,t):(this.logger.warn(`It seems you are nesting recursively key: ${e[0]} in key: ${t[0]}`),null)),s)),s.interpolation&&this.interpolator.reset()}const r=s.postProcess||this.options.postProcess,l=v(r)?[r]:r;return null!=e&&(null==l?void 0:l.length)&&!1!==s.applyPostProcessor&&(e=A.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e,t={}){let s,i,o,n,a;return v(e)&&(e=[e]),e.forEach((e=>{if(this.isValidLookup(s))return;const r=this.extractFromKey(e,t),l=r.key;i=l;let u=r.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const c=void 0!==t.count&&!v(t.count),h=c&&!t.ordinal&&0===t.count,p=void 0!==t.context&&(v(t.context)||"number"==typeof t.context)&&""!==t.context,d=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);u.forEach((e=>{var r,u;this.isValidLookup(s)||(a=e,M[`${d[0]}-${e}`]||!(null==(r=this.utils)?void 0:r.hasLoadedNamespace)||(null==(u=this.utils)?void 0:u.hasLoadedNamespace(a))||(M[`${d[0]}-${e}`]=!0,this.logger.warn(`key "${i}" for languages "${d.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),d.forEach((i=>{var a;if(this.isValidLookup(s))return;n=i;const r=[l];if(null==(a=this.i18nFormat)?void 0:a.addLookupKeys)this.i18nFormat.addLookupKeys(r,l,i,e,t);else{let e;c&&(e=this.pluralResolver.getSuffix(i,t.count,t));const s=`${this.options.pluralSeparator}zero`,o=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(r.push(l+e),t.ordinal&&0===e.indexOf(o)&&r.push(l+e.replace(o,this.options.pluralSeparator)),h&&r.push(l+s)),p){const i=`${l}${this.options.contextSeparator}${t.context}`;r.push(i),c&&(r.push(i+e),t.ordinal&&0===e.indexOf(o)&&r.push(i+e.replace(o,this.options.pluralSeparator)),h&&r.push(i+s))}}let u;for(;u=r.pop();)this.isValidLookup(s)||(o=u,s=this.getResource(i,e,u,t))})))}))})),{res:s,usedKey:i,exactUsedKey:o,usedLng:n,usedNS:a}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,t,s,i={}){var o;return(null==(o=this.i18nFormat)?void 0:o.getResource)?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=e.replace&&!v(e.replace);let i=s?e.replace:e;if(s&&void 0!==e.count&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!s){i={...i};for(const e of t)delete i[e]}return i}static hasDefaultValue(e){const t="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,12)&&void 0!==e[s])return!0;return!1}}class H{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=T.create("languageUtils")}getScriptPartFromCode(e){if(!(e=I(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=I(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(v(e)&&e.indexOf("-")>-1){let s;try{s=Intl.getCanonicalLocales(e)[0]}catch(t){}return s&&this.options.lowerCaseLng&&(s=s.toLowerCase()),s||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach((e=>{if(t)return;const s=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(s)||(t=s)})),!t&&this.options.supportedLngs&&e.forEach((e=>{if(t)return;const s=this.getScriptPartFromCode(e);if(this.isSupportedCode(s))return t=s;const i=this.getLanguagePartFromCode(e);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find((e=>e===i?e:e.indexOf("-")<0&&i.indexOf("-")<0?void 0:e.indexOf("-")>0&&i.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===i||0===e.indexOf(i)&&i.length>1?e:void 0))})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),v(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){const s=this.getFallbackCodes(t||this.options.fallbackLng||[],e),i=[],o=e=>{e&&(this.isSupportedCode(e)?i.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return v(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&o(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&o(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&o(this.getLanguagePartFromCode(e))):v(e)&&o(this.formatLanguageCode(e)),s.forEach((e=>{i.indexOf(e)<0&&o(this.formatLanguageCode(e))})),i}}const B={zero:0,one:1,two:2,few:3,many:4,other:5},q={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class J{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=T.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const s=I("dev"===e?"en":e),i=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:s,type:i});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let n;try{n=new Intl.PluralRules(s,{type:i})}catch(a){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),q;if(!e.match(/-|_/))return q;const s=this.languageUtils.getLanguagePartFromCode(e);n=this.getRule(s,t)}return this.pluralRulesCache[o]=n,n}needsPlural(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),(null==s?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t,s={}){return this.getSuffixes(e,s).map((e=>`${t}${e}`))}getSuffixes(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?s.resolvedOptions().pluralCategories.sort(((e,t)=>B[e]-B[t])).map((e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`)):[]}getSuffix(e,t,s={}){const i=this.getRule(e,s);return i?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}const _=(e,t,s,i=".",o=!0)=>{let n=((e,t,s)=>{const i=L(e,s);return void 0!==i?i:L(t,s)})(e,t,s);return!n&&o&&v(s)&&(n=j(e,s,i),void 0===n&&(n=j(t,s,i))),n},W=e=>e.replace(/\$/g,"$$$$");class Q{constructor(e={}){var t;this.logger=T.create("interpolator"),this.options=e,this.format=(null==(t=null==e?void 0:e.interpolation)?void 0:t.format)||(e=>e),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:o,prefixEscaped:n,suffix:a,suffixEscaped:r,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:h,nestingPrefixEscaped:p,nestingSuffix:d,nestingSuffixEscaped:g,nestingOptionsSeparator:f,maxReplaces:m,alwaysFormat:v}=e.interpolation;this.escape=void 0!==t?t:R,this.escapeValue=void 0===s||s,this.useRawValueToEscape=void 0!==i&&i,this.prefix=o?C(o):n||"{{",this.suffix=a?C(a):r||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=h?C(h):p||C("$t("),this.nestingSuffix=d?C(d):g||C(")"),this.nestingOptionsSeparator=f||",",this.maxReplaces=m||1e3,this.alwaysFormat=void 0!==v&&v,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>(null==e?void 0:e.source)===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,i){var o;let n,a,r;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=e=>{if(e.indexOf(this.formatSeparator)<0){const o=_(t,l,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(o,void 0,s,{...i,...t,interpolationkey:e}):o}const o=e.split(this.formatSeparator),n=o.shift().trim(),a=o.join(this.formatSeparator).trim();return this.format(_(t,l,n,this.options.keySeparator,this.options.ignoreJSONStructure),a,s,{...i,...t,interpolationkey:n})};this.resetRegExp();const c=(null==i?void 0:i.missingInterpolationHandler)||this.options.missingInterpolationHandler,h=void 0!==(null==(o=null==i?void 0:i.interpolation)?void 0:o.skipOnVariables)?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>W(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?W(this.escape(e)):W(e)}].forEach((t=>{for(r=0;n=t.regex.exec(e);){const s=n[1].trim();if(a=u(s),void 0===a)if("function"==typeof c){const t=c(e,n,i);a=v(t)?t:""}else if(i&&Object.prototype.hasOwnProperty.call(i,s))a="";else{if(h){a=n[0];continue}this.logger.warn(`missed to pass in variable ${s} for interpolating ${e}`),a=""}else v(a)||this.useRawValueToEscape||(a=x(a));const o=t.safeValue(a);if(e=e.replace(n[0],o),h?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=n[0].length):t.regex.lastIndex=0,r++,r>=this.maxReplaces)break}})),e}nest(e,t,s={}){let i,o,n;const a=(e,t)=>{const s=this.nestingOptionsSeparator;if(e.indexOf(s)<0)return e;const i=e.split(new RegExp(`${s}[ ]*{`));let o=`{${i[1]}`;e=i[0],o=this.interpolate(o,n);const a=o.match(/'/g),r=o.match(/"/g);(((null==a?void 0:a.length)??0)%2==0&&!r||r.length%2!=0)&&(o=o.replace(/'/g,'"'));try{n=JSON.parse(o),t&&(n={...t,...n})}catch(l){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,l),`${e}${s}${o}`}return n.defaultValue&&n.defaultValue.indexOf(this.prefix)>-1&&delete n.defaultValue,e};for(;i=this.nestingRegexp.exec(e);){let r=[];n={...s},n=n.replace&&!v(n.replace)?n.replace:n,n.applyPostProcessor=!1,delete n.defaultValue;let l=!1;if(-1!==i[0].indexOf(this.formatSeparator)&&!/{.*}/.test(i[1])){const e=i[1].split(this.formatSeparator).map((e=>e.trim()));i[1]=e.shift(),r=e,l=!0}if(o=t(a.call(this,i[1].trim(),n),n),o&&i[0]===e&&!v(o))return o;v(o)||(o=x(o)),o||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),o=""),l&&(o=r.reduce(((e,t)=>this.format(e,t,s.lng,{...s,interpolationkey:i[1].trim()})),o.trim())),e=e.replace(i[0],o),this.regexp.lastIndex=0}return e}}const Y=e=>{const t={};return(s,i,o)=>{let n=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(n={...n,[o.interpolationkey]:void 0});const a=i+JSON.stringify(n);let r=t[a];return r||(r=e(I(i),o),t[a]=r),r(s)}},G=e=>(t,s,i)=>e(I(s),i)(t);class X{constructor(e={}){this.logger=T.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const s=t.cacheInBuiltFormats?Y:G;this.formats={number:s(((e,t)=>{const s=new Intl.NumberFormat(e,{...t});return e=>s.format(e)})),currency:s(((e,t)=>{const s=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>s.format(e)})),datetime:s(((e,t)=>{const s=new Intl.DateTimeFormat(e,{...t});return e=>s.format(e)})),relativetime:s(((e,t)=>{const s=new Intl.RelativeTimeFormat(e,{...t});return e=>s.format(e,t.range||"day")})),list:s(((e,t)=>{const s=new Intl.ListFormat(e,{...t});return e=>s.format(e)}))}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=Y(t)}format(e,t,s,i={}){const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find((e=>e.indexOf(")")>-1))){const e=o.findIndex((e=>e.indexOf(")")>-1));o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}return o.reduce(((e,t)=>{var o;const{formatName:n,formatOptions:a}=(e=>{let t=e.toLowerCase().trim();const s={};if(e.indexOf("(")>-1){const i=e.split("(");t=i[0].toLowerCase().trim();const o=i[1].substring(0,i[1].length-1);"currency"===t&&o.indexOf(":")<0?s.currency||(s.currency=o.trim()):"relativetime"===t&&o.indexOf(":")<0?s.range||(s.range=o.trim()):o.split(";").forEach((e=>{if(e){const[t,...i]=e.split(":"),o=i.join(":").trim().replace(/^'+|'+$/g,""),n=t.trim();s[n]||(s[n]=o),"false"===o&&(s[n]=!1),"true"===o&&(s[n]=!0),isNaN(o)||(s[n]=parseInt(o,10))}}))}return{formatName:t,formatOptions:s}})(t);if(this.formats[n]){let t=e;try{const r=(null==(o=null==i?void 0:i.formatParams)?void 0:o[i.interpolationkey])||{},l=r.locale||r.lng||i.locale||i.lng||s;t=this.formats[n](e,l,{...a,...i,...r})}catch(r){this.logger.warn(r)}return t}return this.logger.warn(`there was no format function for ${n}`),e}),e)}}class Z extends V{constructor(e,t,s,i={}){var o,n;super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=T.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],null==(n=null==(o=this.backend)?void 0:o.init)||n.call(o,s,i.backend,i)}queueLoad(e,t,s,i){const o={},n={},a={},r={};return e.forEach((e=>{let i=!0;t.forEach((t=>{const a=`${e}|${t}`;!s.reload&&this.store.hasResourceBundle(e,t)?this.state[a]=2:this.state[a]<0||(1===this.state[a]?void 0===n[a]&&(n[a]=!0):(this.state[a]=1,i=!1,void 0===n[a]&&(n[a]=!0),void 0===o[a]&&(o[a]=!0),void 0===r[t]&&(r[t]=!0)))})),i||(a[e]=!0)})),(Object.keys(o).length||Object.keys(n).length)&&this.queue.push({pending:n,pendingCount:Object.keys(n).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(o),pending:Object.keys(n),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(r)}}loaded(e,t,s){const i=e.split("|"),o=i[0],n=i[1];t&&this.emit("failedLoading",o,n,t),!t&&s&&this.store.addResourceBundle(o,n,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);const a={};this.queue.forEach((s=>{((e,t,s)=>{const{obj:i,k:o}=w(e,t,Object);i[o]=i[o]||[],i[o].push(s)})(s.loaded,[o],n),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(s,e),t&&s.errors.push(t),0!==s.pendingCount||s.done||(Object.keys(s.loaded).forEach((e=>{a[e]||(a[e]={});const t=s.loaded[e];t.length&&t.forEach((t=>{void 0===a[e][t]&&(a[e][t]=!0)}))})),s.done=!0,s.errors.length?s.callback(s.errors):s.callback())})),this.emit("loaded",a),this.queue=this.queue.filter((e=>!e.done))}read(e,t,s,i=0,o=this.retryTimeout,n){if(!e.length)return n(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:o,callback:n});this.readingCalls++;const a=(a,r)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}a&&r&&i<this.maxRetries?setTimeout((()=>{this.read.call(this,e,t,s,i+1,2*o,n)}),o):n(a,r)},r=this.backend[s].bind(this.backend);if(2!==r.length)return r(e,t,a);try{const s=r(e,t);s&&"function"==typeof s.then?s.then((e=>a(null,e))).catch(a):a(null,s)}catch(l){a(l)}}prepareLoading(e,t,s={},i){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();v(e)&&(e=this.languageUtils.toResolveHierarchy(e)),v(t)&&(t=[t]);const o=this.queueLoad(e,t,s,i);if(!o.toLoad.length)return o.pending.length||i(),null;o.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e,t=""){const s=e.split("|"),i=s[0],o=s[1];this.read(i,o,"read",void 0,void 0,((s,n)=>{s&&this.logger.warn(`${t}loading namespace ${o} for language ${i} failed`,s),!s&&n&&this.logger.log(`${t}loaded namespace ${o} for language ${i}`,n),this.loaded(e,s,n)}))}saveMissing(e,t,s,i,o,n={},a=()=>{}){var r,l,u,c,h;if(!(null==(l=null==(r=this.services)?void 0:r.utils)?void 0:l.hasLoadedNamespace)||(null==(c=null==(u=this.services)?void 0:u.utils)?void 0:c.hasLoadedNamespace(t))){if(null!=s&&""!==s){if(null==(h=this.backend)?void 0:h.create){const r={...n,isUpdate:o},l=this.backend.create.bind(this.backend);if(l.length<6)try{let o;o=5===l.length?l(e,t,s,i,r):l(e,t,s,i),o&&"function"==typeof o.then?o.then((e=>a(null,e))).catch(a):a(null,o)}catch(p){a(p)}else l(e,t,s,i,a,r)}e&&e[0]&&this.store.addResource(e[0],t,s,i)}}else this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")}}const ee=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),v(e[1])&&(t.defaultValue=e[1]),v(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){const s=e[3]||e[2];Object.keys(s).forEach((e=>{t[e]=s[e]}))}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),te=e=>{var t,s;return v(e.ns)&&(e.ns=[e.ns]),v(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),v(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),(null==(s=null==(t=e.supportedLngs)?void 0:t.indexOf)?void 0:s.call(t,"cimode"))<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"==typeof e.initImmediate&&(e.initAsync=e.initImmediate),e},se=()=>{};class ie extends V{constructor(e={},t){var s;if(super(),this.options=te(e),this.services={},this.logger=T,this.modules={external:[]},s=this,Object.getOwnPropertyNames(Object.getPrototypeOf(s)).forEach((e=>{"function"==typeof s[e]&&(s[e]=s[e].bind(s))})),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout((()=>{this.init(e,t)}),0)}}init(e={},t){this.isInitializing=!0,"function"==typeof e&&(t=e,e={}),null==e.defaultNS&&e.ns&&(v(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const s=ee();this.options={...s,...this.options,...te(e)},this.options.interpolation={...s.interpolation,...this.options.interpolation},void 0!==e.keySeparator&&(this.options.userDefinedKeySeparator=e.keySeparator),void 0!==e.nsSeparator&&(this.options.userDefinedNsSeparator=e.nsSeparator);const i=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let e;this.modules.logger?T.init(i(this.modules.logger),this.options):T.init(null,this.options),e=this.modules.formatter?this.modules.formatter:X;const t=new H(this.options);this.store=new U(this.options.resources,this.options);const o=this.services;o.logger=T,o.resourceStore=this.store,o.languageUtils=t,o.pluralResolver=new J(t,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!e||this.options.interpolation.format&&this.options.interpolation.format!==s.interpolation.format||(o.formatter=i(e),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new Q(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new Z(i(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",((e,...t)=>{this.emit(e,...t)})),this.modules.languageDetector&&(o.languageDetector=i(this.modules.languageDetector),o.languageDetector.init&&o.languageDetector.init(o,this.options.detection,this.options)),this.modules.i18nFormat&&(o.i18nFormat=i(this.modules.i18nFormat),o.i18nFormat.init&&o.i18nFormat.init(this)),this.translator=new z(this.services,this.options),this.translator.on("*",((e,...t)=>{this.emit(e,...t)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,t||(t=se),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((e=>{this[e]=(...t)=>this.store[e](...t)}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((e=>{this[e]=(...t)=>(this.store[e](...t),this)}));const o=y(),n=()=>{const e=(e,s)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(s),t(e,s)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?n():setTimeout(n,0),o}loadResources(e,t=se){var s,i;let o=t;const n=v(e)?e:this.language;if("function"==typeof e&&(o=e),!this.options.resources||this.options.partialBundledLanguages){if("cimode"===(null==n?void 0:n.toLowerCase())&&(!this.options.preload||0===this.options.preload.length))return o();const e=[],t=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach((t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)}))};if(n)t(n);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>t(e)))}null==(i=null==(s=this.options.preload)?void 0:s.forEach)||i.call(s,(e=>t(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),o(e)}))}else o(null)}reloadResources(e,t,s){const i=y();return"function"==typeof e&&(s=e,e=void 0),"function"==typeof t&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=se),this.services.backendConnector.reload(e,t,(e=>{i.resolve(),s(e)})),i}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&A.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){const t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const s=y();this.emit("languageChanging",e);const i=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},o=(o,n)=>{n?this.isLanguageChangingTo===e&&(i(n),this.translator.changeLanguage(n),this.isLanguageChangingTo=void 0,this.emit("languageChanged",n),this.logger.log("languageChanged",n)):this.isLanguageChangingTo=void 0,s.resolve(((...e)=>this.t(...e))),t&&t(o,((...e)=>this.t(...e)))},n=t=>{var s,n;e||t||!this.services.languageDetector||(t=[]);const a=v(t)?t:t&&t[0],r=this.store.hasLanguageSomeTranslations(a)?a:this.services.languageUtils.getBestMatchFromCodes(v(t)?[t]:t);r&&(this.language||i(r),this.translator.language||this.translator.changeLanguage(r),null==(n=null==(s=this.services.languageDetector)?void 0:s.cacheUserLanguage)||n.call(s,r)),this.loadResources(r,(e=>{o(e,r)}))};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(n):this.services.languageDetector.detect(n):n(e):n(this.services.languageDetector.detect()),s}getFixedT(e,t,s){const i=(e,t,...o)=>{let n;n="object"!=typeof t?this.options.overloadTranslationOptionHandler([e,t].concat(o)):{...t},n.lng=n.lng||i.lng,n.lngs=n.lngs||i.lngs,n.ns=n.ns||i.ns,""!==n.keyPrefix&&(n.keyPrefix=n.keyPrefix||s||i.keyPrefix);const a=this.options.keySeparator||".";let r;return r=n.keyPrefix&&Array.isArray(e)?e.map((e=>`${n.keyPrefix}${a}${e}`)):n.keyPrefix?`${n.keyPrefix}${a}${e}`:e,this.t(r,n)};return v(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=s,i}t(...e){var t;return null==(t=this.translator)?void 0:t.translate(...e)}exists(...e){var t;return null==(t=this.translator)?void 0:t.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===s.toLowerCase())return!0;const n=(e,t)=>{const s=this.services.backendConnector.state[`${e}|${t}`];return-1===s||0===s||2===s};if(t.precheck){const e=t.precheck(this,n);if(void 0!==e)return e}return!!this.hasResourceBundle(s,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!n(s,e)||i&&!n(o,e)))}loadNamespaces(e,t){const s=y();return this.options.ns?(v(e)&&(e=[e]),e.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{s.resolve(),t&&t(e)})),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){const s=y();v(e)&&(e=[e]);const i=this.options.preload||[],o=e.filter((e=>i.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return o.length?(this.options.preload=i.concat(o),this.loadResources((e=>{s.resolve(),t&&t(e)})),s):(t&&t(),Promise.resolve())}dir(e){var t,s;if(e||(e=this.resolvedLanguage||((null==(t=this.languages)?void 0:t.length)>0?this.languages[0]:this.language)),!e)return"rtl";const i=(null==(s=this.services)?void 0:s.languageUtils)||new H(ee());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(i.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new ie(e,t)}cloneInstance(e={},t=se){const s=e.forkResourceStore;s&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},o=new ie(i);void 0===e.debug&&void 0===e.prefix||(o.logger=o.logger.clone(e));if(["store","services","language"].forEach((e=>{o[e]=this[e]})),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},s){const e=Object.keys(this.store.data).reduce(((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce(((s,i)=>(s[i]={...e[t][i]},s)),e[t]),e)),{});o.store=new U(e,i),o.services.resourceStore=o.store}return o.translator=new z(o.services,i),o.translator.on("*",((e,...t)=>{o.emit(e,...t)})),o.init(i,t),o.translator.options=i,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const oe=ie.createInstance();oe.createInstance=ie.createInstance;const{slice:ne,forEach:ae}=[];const re=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,le={create(e,t,s,i){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};s&&(o.expires=new Date,o.expires.setTime(o.expires.getTime()+60*s*1e3)),i&&(o.domain=i),document.cookie=function(e,t){const s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{path:"/"};let i=`${e}=${encodeURIComponent(t)}`;if(s.maxAge>0){const e=s.maxAge-0;if(Number.isNaN(e))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(e)}`}if(s.domain){if(!re.test(s.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${s.domain}`}if(s.path){if(!re.test(s.path))throw new TypeError("option path is invalid");i+=`; Path=${s.path}`}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw new TypeError("option expires is invalid");i+=`; Expires=${s.expires.toUTCString()}`}if(s.httpOnly&&(i+="; HttpOnly"),s.secure&&(i+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s.partitioned&&(i+="; Partitioned"),i}(e,encodeURIComponent(t),o)},read(e){const t=`${e}=`,s=document.cookie.split(";");for(let i=0;i<s.length;i++){let e=s[i];for(;" "===e.charAt(0);)e=e.substring(1,e.length);if(0===e.indexOf(t))return e.substring(t.length,e.length)}return null},remove(e){this.create(e,"",-1)}};var ue={name:"cookie",lookup(e){let{lookupCookie:t}=e;if(t&&"undefined"!=typeof document)return le.read(t)||void 0},cacheUserLanguage(e,t){let{lookupCookie:s,cookieMinutes:i,cookieDomain:o,cookieOptions:n}=t;s&&"undefined"!=typeof document&&le.create(s,e,i,o,n)}},ce={name:"querystring",lookup(e){var t;let s,{lookupQuerystring:i}=e;if("undefined"!=typeof window){let{search:e}=window.location;!window.location.search&&(null==(t=window.location.hash)?void 0:t.indexOf("?"))>-1&&(e=window.location.hash.substring(window.location.hash.indexOf("?")));const o=e.substring(1).split("&");for(let t=0;t<o.length;t++){const e=o[t].indexOf("=");if(e>0){o[t].substring(0,e)===i&&(s=o[t].substring(e+1))}}}return s}};let he=null;const pe=()=>{if(null!==he)return he;try{if(he="undefined"!=typeof window&&null!==window.localStorage,!he)return!1;const e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch(e){he=!1}return he};var de={name:"localStorage",lookup(e){let{lookupLocalStorage:t}=e;if(t&&pe())return window.localStorage.getItem(t)||void 0},cacheUserLanguage(e,t){let{lookupLocalStorage:s}=t;s&&pe()&&window.localStorage.setItem(s,e)}};let ge=null;const fe=()=>{if(null!==ge)return ge;try{if(ge="undefined"!=typeof window&&null!==window.sessionStorage,!ge)return!1;const e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch(e){ge=!1}return ge};var me={name:"sessionStorage",lookup(e){let{lookupSessionStorage:t}=e;if(t&&fe())return window.sessionStorage.getItem(t)||void 0},cacheUserLanguage(e,t){let{lookupSessionStorage:s}=t;s&&fe()&&window.sessionStorage.setItem(s,e)}},ve={name:"navigator",lookup(e){const t=[];if("undefined"!=typeof navigator){const{languages:e,userLanguage:s,language:i}=navigator;if(e)for(let o=0;o<e.length;o++)t.push(e[o]);s&&t.push(s),i&&t.push(i)}return t.length>0?t:void 0}},ye={name:"htmlTag",lookup(e){let t,{htmlTag:s}=e;const i=s||("undefined"!=typeof document?document.documentElement:null);return i&&"function"==typeof i.getAttribute&&(t=i.getAttribute("lang")),t}},xe={name:"path",lookup(e){var t;let{lookupFromPathIndex:s}=e;if("undefined"==typeof window)return;const i=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(!Array.isArray(i))return;return null==(t=i["number"==typeof s?s:0])?void 0:t.replace("/","")}},be={name:"subdomain",lookup(e){var t,s;let{lookupFromSubdomainIndex:i}=e;const o="number"==typeof i?i+1:1,n="undefined"!=typeof window&&(null==(s=null==(t=window.location)?void 0:t.hostname)?void 0:s.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(n)return n[o]}};let Se=!1;try{document.cookie,Se=!0}catch(Oe){}const ke=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Se||ke.splice(1,1);class we{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{languageUtils:{}},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=e,this.options=function(e){return ae.call(ne.call(arguments,1),(t=>{if(t)for(const s in t)void 0===e[s]&&(e[s]=t[s])})),e}(t,this.options||{},{order:ke,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:e=>e}),"string"==typeof this.options.convertDetectedLanguage&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=e=>e.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(ue),this.addDetector(ce),this.addDetector(de),this.addDetector(me),this.addDetector(ve),this.addDetector(ye),this.addDetector(xe),this.addDetector(be)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.order,t=[];return e.forEach((e=>{if(this.detectors[e]){let s=this.detectors[e].lookup(this.options);s&&"string"==typeof s&&(s=[s]),s&&(t=t.concat(s))}})),t=t.filter((e=>{return null!=e&&!("string"==typeof(t=e)&&[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some((e=>e.test(t))));var t})).map((e=>this.options.convertDetectedLanguage(e))),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}cacheUserLanguage(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.caches;t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach((t=>{this.detectors[t]&&this.detectors[t].cacheUserLanguage(e,this.options)})))}}we.type="languageDetector";export{we as B,p as a,oe as i,m as u};
//# sourceMappingURL=chunk-CS50z45r.js.map

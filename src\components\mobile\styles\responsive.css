/**
 * 🎯 CSS RESPONSIVO MOBILE
 */

@media (max-width: 479px) {
  .mobile-xs-only { display: block !important; }
  .mobile-xs-hidden { display: none !important; }
}

@media (min-width: 480px) and (max-width: 767px) {
  .mobile-sm-only { display: block !important; }
  .mobile-sm-hidden { display: none !important; }
}

@media (max-width: 767px) {
  .mobile-only { display: block !important; }
  .mobile-hidden { display: none !important; }
  
  .mobile-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 768px) {
  .mobile-only { display: none !important; }
  .desktop-only { display: block !important; }
}

/* ===== NAVIGATION BUTTON STYLES ===== */
/* Estilos para MobileMenuButton.tsx, MobileConfigButton.tsx */

/* ===== MOBILE MENU BUTTON ===== */
.button-mobile-menu {
  /* Dimensions */
  width: 44px;
  height: 44px;
  
  /* Appearance */
  border-radius: var(--button-border-radius);
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  
  /* Effects */
  box-shadow: var(--button-shadow);
  transition: var(--button-transition);
}

.button-mobile-menu:hover {
  background: var(--color-accent);
  border-color: var(--color-primary);
  transform: scale(1.05);
  box-shadow: var(--button-shadow-hover);
}

.button-mobile-menu:active {
  transform: scale(0.95);
  box-shadow: var(--button-shadow-active);
}

/* Hamburger Icon Styles */
.button-mobile-menu .hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--color-text);
  border-radius: 1px;
  transition: var(--button-transition-fast);
}

.button-mobile-menu[aria-expanded="true"] .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translateY(6px);
}

.button-mobile-menu[aria-expanded="true"] .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: translateX(-10px);
}

.button-mobile-menu[aria-expanded="true"] .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translateY(-6px);
}

/* ===== MOBILE CONFIG BUTTON ===== */
.button-mobile-config {
  /* Dimensions */
  width: 44px;
  height: 44px;
  
  /* Appearance */
  border-radius: var(--button-border-radius);
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  
  /* Effects */
  box-shadow: var(--button-shadow);
  transition: var(--button-transition);
}

.button-mobile-config:hover {
  background: var(--color-accent);
  border-color: var(--color-primary);
  transform: scale(1.05);
  box-shadow: var(--button-shadow-hover);
}

.button-mobile-config:active {
  transform: scale(0.95);
  box-shadow: var(--button-shadow-active);
}

/* Settings Icon Animation */
.button-mobile-config .settings-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-mobile-config[aria-expanded="true"] .settings-icon {
  transform: rotate(180deg);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .button-mobile-menu,
  .button-mobile-config {
    /* Ensure touch targets are adequate */
    min-width: 44px;
    min-height: 44px;
  }
}

/* ===== DARK MODE ===== */
.dark .button-mobile-menu,
.dark .button-mobile-config {
  border-color: var(--color-border);
  background: var(--color-surface);
}

.dark .button-mobile-menu:hover,
.dark .button-mobile-config:hover {
  background: var(--color-accent);
  border-color: var(--color-primary);
}

.dark .hamburger-line {
  background: var(--color-text);
}

/**
 * 🎯 ANIMAÇÕES PARA MODAIS
 * 
 * Animações CSS para modais
 * Otimizadas para performance e acessibilidade
 */

/* ===== KEYFRAMES ===== */
@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(1rem) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modal-slide-down {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(1rem) scale(0.95);
  }
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes modal-scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modal-scale-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes modal-slide-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes modal-slide-left {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes modal-bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modal-bounce-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* ===== OVERLAY ANIMATIONS ===== */
@keyframes overlay-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes overlay-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* ===== ANIMATION CLASSES ===== */
.modal-enter {
  animation: modal-slide-up 0.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.modal-exit {
  animation: modal-slide-down 0.15s ease-out forwards;
}

.modal-fade-enter {
  animation: modal-fade-in 0.15s ease-out forwards;
}

.modal-fade-exit {
  animation: modal-fade-out 0.1s ease-out forwards;
}

.modal-scale-enter {
  animation: modal-scale-in 0.25s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.modal-scale-exit {
  animation: modal-scale-out 0.2s ease-out forwards;
}

.modal-slide-right-enter {
  animation: modal-slide-right 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.modal-slide-right-exit {
  animation: modal-slide-left 0.25s ease-out forwards;
}

.modal-bounce-enter {
  animation: modal-bounce-in 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.modal-bounce-exit {
  animation: modal-bounce-out 0.2s ease-out forwards;
}

/* ===== OVERLAY ANIMATION CLASSES ===== */
.overlay-enter {
  animation: overlay-fade-in 0.2s ease-out forwards;
}

.overlay-exit {
  animation: overlay-fade-out 0.15s ease-out forwards;
}

/* ===== RESPONSIVE ANIMATIONS ===== */
@media (max-width: 768px) {
  .modal-enter {
    animation: modal-slide-right 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }
  
  .modal-exit {
    animation: modal-slide-left 0.25s ease-out forwards;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .modal-enter,
  .modal-exit,
  .modal-fade-enter,
  .modal-fade-exit,
  .modal-scale-enter,
  .modal-scale-exit,
  .modal-slide-right-enter,
  .modal-slide-right-exit,
  .modal-bounce-enter,
  .modal-bounce-exit {
    animation: modal-fade-in 0.1s ease forwards;
  }
  
  .modal-exit,
  .modal-fade-exit,
  .modal-scale-exit,
  .modal-slide-right-exit,
  .modal-bounce-exit {
    animation: modal-fade-out 0.1s ease forwards;
  }
  
  .overlay-enter,
  .overlay-exit {
    animation: overlay-fade-in 0.1s ease forwards;
  }
  
  .overlay-exit {
    animation: overlay-fade-out 0.1s ease forwards;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.modal-content,
.modal-overlay {
  will-change: transform, opacity;
}

.modal-content.animating,
.modal-overlay.animating {
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== FOCUS ANIMATIONS ===== */
@keyframes focus-ring {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
}

.modal-focus-ring {
  animation: focus-ring 0.2s ease-out forwards;
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes modal-loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.modal-loading {
  animation: modal-loading-pulse 1.5s ease-in-out infinite;
}

@keyframes modal-loading-spinner {
  to {
    transform: rotate(360deg);
  }
}

.modal-spinner {
  animation: modal-loading-spinner 1s linear infinite;
}

/* ===== SUCCESS/ERROR ANIMATIONS ===== */
@keyframes modal-success-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes modal-error-shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.modal-success-animation {
  animation: modal-success-bounce 0.3s ease-out;
}

.modal-error-animation {
  animation: modal-error-shake 0.3s ease-out;
}

/* ===== STAGGER ANIMATIONS ===== */
.modal-stagger-children > * {
  opacity: 0;
  transform: translateY(10px);
  animation: modal-stagger-item 0.3s ease-out forwards;
}

.modal-stagger-children > *:nth-child(1) {
  animation-delay: 0.05s;
}

.modal-stagger-children > *:nth-child(2) {
  animation-delay: 0.1s;
}

.modal-stagger-children > *:nth-child(3) {
  animation-delay: 0.15s;
}

.modal-stagger-children > *:nth-child(4) {
  animation-delay: 0.2s;
}

@keyframes modal-stagger-item {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

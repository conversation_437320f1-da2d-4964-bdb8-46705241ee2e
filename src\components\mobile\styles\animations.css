/**
 * 🎯 CSS ANIMAÇÕES MOBILE
 */

@keyframes mobileSlideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes mobileSlideDown {
  from { transform: translateY(0); }
  to { transform: translateY(100%); }
}

@keyframes mobileFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes mobileFadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.mobile-animate-slide-up {
  animation: mobileSlideUp var(--mobile-transition);
}

.mobile-animate-fade-in {
  animation: mobileFadeIn var(--mobile-transition);
}

/* ===== ACCESSIBILITY BUTTON STYLES ===== */
/* Estilos para AccessibilityButton.tsx */

.button-accessibility {
  /* Dimensions */
  width: 40px;
  height: 40px;
  
  /* Appearance */
  border-radius: 50%;
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  color: var(--color-primary);
  
  /* Effects */
  box-shadow: var(--button-shadow);
  transition: var(--button-transition);
}

.button-accessibility:hover {
  background: var(--color-accent);
  border-color: var(--color-primary);
  transform: scale(1.1);
  box-shadow: var(--button-shadow-hover);
}

.button-accessibility:active {
  transform: scale(0.95);
}

.button-accessibility[aria-expanded="true"] {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* Accessibility Menu */
.accessibility-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 200px;
  
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--button-border-radius);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  
  z-index: 50;
  overflow: hidden;
}

.accessibility-option {
  width: 100%;
  padding: 12px 16px;
  
  background: transparent;
  border: none;
  color: var(--color-text);
  
  font-size: 14px;
  text-align: left;
  
  transition: var(--button-transition-fast);
  cursor: pointer;
}

.accessibility-option:hover {
  background: var(--color-accent);
  color: var(--color-primary);
}

.accessibility-option.active {
  background: var(--color-primary);
  color: white;
}

import{j as e,A as t,m as r}from"./chunk-L3uak9dD.js";import{r as s}from"./chunk-D3Ns84uO.js";import{u as a}from"./chunk-CS50z45r.js";import{h as o,X as i,S as c,i as l,E as n}from"./chunk-Bc9oLI49.js";import"./chunk-DJqUFuPP.js";const d=()=>{const{t:d}=a(),[g,x]=s.useState(!1),[m,y]=s.useState(!1),[h,k]=s.useState({necessary:!0,analytics:!1,marketing:!1});s.useEffect((()=>{const e=localStorage.getItem("cookie-consent");if(e)try{const t=JSON.parse(e);k(t),t.analytics&&f()}catch(t){console.error("Error parsing cookie consent:",t)}else setTimeout((()=>x(!0)),2e3)}),[]);const f=()=>{"undefined"!=typeof window&&window.gtag&&window.gtag("consent","update",{analytics_storage:"granted"})},p=()=>{const e={necessary:!0,analytics:!0,marketing:!0};k(e),localStorage.setItem("cookie-consent",JSON.stringify(e)),localStorage.setItem("cookie-consent-date",(new Date).toISOString()),f(),x(!1)},u=()=>{const e={necessary:!0,analytics:!1,marketing:!1};k(e),localStorage.setItem("cookie-consent",JSON.stringify(e)),localStorage.setItem("cookie-consent-date",(new Date).toISOString()),x(!1)},b=e=>{"necessary"!==e&&k((t=>({...t,[e]:!t[e]})))};return g?e.jsx(t,{children:e.jsx(r.div,{initial:{y:100,opacity:0},animate:{y:0,opacity:1},exit:{y:100,opacity:0},transition:{duration:.4,ease:[.16,1,.3,1]},className:"fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg",role:"dialog","aria-labelledby":"cookie-consent-title","aria-describedby":"cookie-consent-description",children:e.jsx("div",{className:"max-w-7xl mx-auto p-4",children:m?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:d("cookies.preferences.title")}),e.jsx("button",{onClick:()=>y(!1),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200","aria-label":d("common.close"),children:e.jsx(i,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"grid gap-4",children:[e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx(c,{className:"w-5 h-5 text-green-600 dark:text-green-400 mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:d("cookies.types.necessary.title")}),e.jsx("span",{className:"text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded",children:d("cookies.required")})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:d("cookies.types.necessary.description")})]})]}),e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx(l,{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:d("cookies.types.analytics.title")}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:h.analytics,onChange:()=>b("analytics"),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:d("cookies.types.analytics.description")}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[d("cookies.types.analytics.providers"),": Google Analytics, LogRocket"]})]})]}),e.jsxs("div",{className:"flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsx(n,{className:"w-5 h-5 text-purple-600 dark:text-purple-400 mt-1"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:d("cookies.types.marketing.title")}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:h.marketing,onChange:()=>b("marketing"),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:d("cookies.types.marketing.description")})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{onClick:u,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors",children:d("cookies.acceptNecessary")}),e.jsx("button",{onClick:()=>{localStorage.setItem("cookie-consent",JSON.stringify(h)),localStorage.setItem("cookie-consent-date",(new Date).toISOString()),h.analytics&&f(),x(!1),y(!1)},className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",children:d("cookies.savePreferences")}),e.jsx("button",{onClick:p,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors",children:d("cookies.acceptAll")})]})]}):e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-start gap-3 flex-1",children:[e.jsx(o,{className:"w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{id:"cookie-consent-title",className:"font-semibold text-gray-900 dark:text-white mb-1",children:d("cookies.title")}),e.jsxs("p",{id:"cookie-consent-description",className:"text-sm text-gray-600 dark:text-gray-300",children:[d("cookies.description")," ",e.jsx("button",{onClick:()=>y(!0),className:"text-blue-600 dark:text-blue-400 hover:underline font-medium",children:d("cookies.learnMore")})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto",children:[e.jsx("button",{onClick:u,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors",children:d("cookies.acceptNecessary")}),e.jsx("button",{onClick:()=>y(!0),className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors",children:d("cookies.customize")}),e.jsx("button",{onClick:p,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",children:d("cookies.acceptAll")})]})]})})})}):null};export{d as default};
//# sourceMappingURL=CookieConsent-DDWHkk1W.js.map

{"version": 3, "file": "LazyScripts-Chw9cytQ.js", "sources": ["../../src/components/LazyScripts.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\n\n/**\n * LazyScripts Component\n * Loads third-party scripts only when needed to reduce initial bundle size\n */\n\ninterface LazyScriptsProps {\n  enableAnalytics?: boolean;\n  enableGTM?: boolean;\n  enableClarity?: boolean;\n  delay?: number;\n}\n\nconst LazyScripts: React.FC<LazyScriptsProps> = ({\n  enableAnalytics = true,\n  enableGTM = true,\n  enableClarity = true,\n  delay = 3000 // 3 seconds delay\n}) => {\n  const [scriptsLoaded, setScriptsLoaded] = useState(false);\n\n  useEffect(() => {\n    // Only load scripts in production\n    if (!import.meta.env.PROD) {\n      return;\n    }\n\n    // Delay script loading to improve initial page load\n    const timer = setTimeout(() => {\n      loadScripts();\n    }, delay);\n\n    return () => clearTimeout(timer);\n  }, [delay]);\n\n  const loadScripts = async () => {\n    try {\n      // Load scripts in sequence to avoid blocking\n      if (enableAnalytics) {\n        await loadGoogleAnalytics();\n      }\n\n      if (enableGTM) {\n        await loadGoogleTagManager();\n      }\n\n      if (enableClarity) {\n        await loadMicrosoftClarity();\n      }\n\n      setScriptsLoaded(true);\n      console.log('Third-party scripts loaded successfully');\n    } catch (error) {\n      console.warn('Failed to load some third-party scripts:', error);\n    }\n  };\n\n  const loadGoogleAnalytics = (): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Check if already loaded\n      if (window.gtag) {\n        resolve();\n        return;\n      }\n\n      const script = document.createElement('script');\n      script.async = true;\n      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-3QCW5SKK73';\n      script.onload = () => {\n        // Initialize Google Analytics\n        window.dataLayer = window.dataLayer || [];\n        function gtag(...args: any[]) {\n          window.dataLayer.push(args);\n        }\n        window.gtag = gtag;\n        gtag('js', new Date());\n        gtag('config', 'G-3QCW5SKK73', {\n          page_title: document.title,\n          page_location: window.location.href\n        });\n        resolve();\n      };\n      script.onerror = reject;\n      document.head.appendChild(script);\n    });\n  };\n\n  const loadGoogleTagManager = (): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Check if already loaded\n      if (window.google_tag_manager) {\n        resolve();\n        return;\n      }\n\n      // GTM script\n      const script = document.createElement('script');\n      script.async = true;\n      script.src = 'https://www.googletagmanager.com/gtm.js?id=GTM-M2NFRBD9';\n      script.onload = () => {\n        // Initialize dataLayer\n        window.dataLayer = window.dataLayer || [];\n        window.dataLayer.push({\n          'gtm.start': new Date().getTime(),\n          event: 'gtm.js'\n        });\n        resolve();\n      };\n      script.onerror = reject;\n      document.head.appendChild(script);\n\n      // GTM noscript fallback\n      const noscript = document.createElement('noscript');\n      const iframe = document.createElement('iframe');\n      iframe.src = 'https://www.googletagmanager.com/ns.html?id=GTM-M2NFRBD9';\n      iframe.height = '0';\n      iframe.width = '0';\n      iframe.style.display = 'none';\n      iframe.style.visibility = 'hidden';\n      noscript.appendChild(iframe);\n      document.body.appendChild(noscript);\n    });\n  };\n\n  const loadMicrosoftClarity = (): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Check if already loaded\n      if (window.clarity) {\n        resolve();\n        return;\n      }\n\n      const script = document.createElement('script');\n      script.async = true;\n      script.src = 'https://www.clarity.ms/tag/rp64ayubme';\n      script.onload = () => {\n        // Initialize Clarity\n        (function(c: any, l: any, a: any, r: any, i: any, t: any, y: any) {\n          c[a] = c[a] || function() { (c[a].q = c[a].q || []).push(arguments) };\n          t = l.createElement(r); t.async = 1; t.src = \"https://www.clarity.ms/tag/\" + i;\n          y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);\n        })(window, document, \"clarity\", \"script\", \"rp64ayubme\");\n        resolve();\n      };\n      script.onerror = reject;\n      document.head.appendChild(script);\n    });\n  };\n\n  // Don't render anything visible\n  return null;\n};\n\n// Extend window interface for TypeScript\ndeclare global {\n  interface Window {\n    gtag: (...args: any[]) => void;\n    dataLayer: any[];\n    google_tag_manager: any;\n    clarity: (...args: any[]) => void;\n  }\n}\n\nexport default LazyScripts;\n"], "names": ["LazyScripts", "enableAnalytics", "enableGTM", "enableClarity", "delay", "scriptsLoaded", "setScriptsLoaded", "useState", "useEffect", "timer", "setTimeout", "loadScripts", "clearTimeout", "async", "loadGoogleAnalytics", "loadGoogleTagManager", "loadMicrosoftClarity", "error", "warn", "Promise", "resolve", "reject", "window", "gtag", "script", "document", "createElement", "src", "onload", "args", "dataLayer", "push", "Date", "page_title", "title", "page_location", "location", "href", "onerror", "head", "append<PERSON><PERSON><PERSON>", "google_tag_manager", "getTime", "event", "noscript", "iframe", "height", "width", "style", "display", "visibility", "body", "clarity", "c", "l", "a", "r", "t", "y", "q", "arguments", "getElementsByTagName", "parentNode", "insertBefore"], "mappings": "oEAcA,MAAMA,EAA0C,EAC9CC,mBAAkB,EAClBC,aAAY,EACZC,iBAAgB,EAChBC,QAAQ,QAER,MAAOC,EAAeC,GAAoBC,EAAAA,UAAS,GAEnDC,EAAAA,WAAU,KAOFC,MAAAA,EAAQC,YAAW,KACvBC,GAAAA,GACCP,GAEI,MAAA,IAAMQ,aAAaH,EAAAA,GACzB,CAACL,IAEJ,MAAMO,EAAcE,UACd,IAEEZ,SACIa,IAGJZ,SACIa,IAGJZ,SACIa,IAGRV,GAAiB,SAEVW,GACCC,QAAAA,KAAK,2CAA4CD,EAAAA,GAIvDH,EAAsB,IACnB,IAAIK,SAAQ,CAACC,EAASC,KAE3B,GAAIC,OAAOC,KAET,YADAH,IAIII,MAAAA,EAASC,SAASC,cAAc,UACtCF,EAAOX,OAAQ,EACfW,EAAOG,IAAM,2DACbH,EAAOI,OAAS,KAGd,SAASL,KAAQM,GACRC,OAAAA,UAAUC,KAAKF,EAAAA,CAFjBC,OAAAA,UAAYR,OAAOQ,WAAa,GAIvCR,OAAOC,KAAOA,EACTA,EAAA,KAAUS,IAAAA,MACfT,EAAK,SAAU,eAAgB,CAC7BU,WAAYR,SAASS,MACrBC,cAAeb,OAAOc,SAASC,OAEjCjB,GAAAA,EAEFI,EAAOc,QAAUjB,EACRkB,SAAAA,KAAKC,YAAYhB,EAAAA,IAIxBT,EAAuB,IACpB,IAAII,SAAQ,CAACC,EAASC,KAE3B,GAAIC,OAAOmB,mBAET,YADArB,IAKII,MAAAA,EAASC,SAASC,cAAc,UACtCF,EAAOX,OAAQ,EACfW,EAAOG,IAAM,0DACbH,EAAOI,OAAS,KAEPE,OAAAA,UAAYR,OAAOQ,WAAa,GACvCR,OAAOQ,UAAUC,KAAK,CACpB,aAAa,IAAIC,MAAOU,UACxBC,MAAO,WAETvB,GAAAA,EAEFI,EAAOc,QAAUjB,EACRkB,SAAAA,KAAKC,YAAYhB,GAGpBoB,MAAAA,EAAWnB,SAASC,cAAc,YAClCmB,EAASpB,SAASC,cAAc,UACtCmB,EAAOlB,IAAM,2DACbkB,EAAOC,OAAS,IAChBD,EAAOE,MAAQ,IACfF,EAAOG,MAAMC,QAAU,OACvBJ,EAAOG,MAAME,WAAa,SAC1BN,EAASJ,YAAYK,GACZM,SAAAA,KAAKX,YAAYI,EAAAA,IAIxB5B,EAAuB,IACpB,IAAIG,SAAQ,CAACC,EAASC,KAE3B,GAAIC,OAAO8B,QAET,YADAhC,IAIII,MAAAA,EAASC,SAASC,cAAc,UACtCF,EAAOX,OAAQ,EACfW,EAAOG,IAAM,wCACbH,EAAOI,OAAS,KAEb,IAASyB,EAAQC,EAAQC,EAAQC,EAAgBC,EAAQC,EAAhDL,EAIP/B,OAJegC,EAIP7B,SAJuB+B,EAIF,SAH9BH,EADwBE,EAIL,WAHZF,EAAEE,IAAM,YAAcF,EAAEE,GAAGI,EAAIN,EAAEE,GAAGI,GAAK,IAAI5B,KAAK6B,UAAW,GAChEN,EAAAA,EAAE5B,cAAc8B,IAAM3C,MAAQ,EAAG4C,EAAE9B,IAAM,yCAC7C+B,EAAIJ,EAAEO,qBAAqBL,GAAG,IAAMM,WAAWC,aAAaN,EAAGC,GAEjEtC,GAAAA,EAEFI,EAAOc,QAAUjB,EACRkB,SAAAA,KAAKC,YAAYhB,EAAAA,IAKvB,OAAA,IAAA"}
/* ===== FORM BUTTON STYLES ===== */
/* Estilos para FormButton.tsx, SubmitButton.tsx */

.button-form {
  /* Dimensions */
  min-height: var(--button-height-lg);
  padding: var(--button-padding-lg);
  gap: 12px;
  
  /* Typography */
  font-size: 16px;
  font-weight: 600;
  
  /* Appearance */
  border-radius: var(--button-border-radius);
  border: 2px solid var(--color-primary);
  background: var(--color-primary);
  color: white;
  
  /* Effects */
  box-shadow: var(--button-shadow-hover);
  transition: var(--button-transition);
}

.button-form:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(var(--primary), 0.3);
}

.button-form:active {
  transform: translateY(0);
  box-shadow: var(--button-shadow);
}

/* Form Button Variants */
.button-form.variant-secondary {
  background: var(--color-surface);
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.button-form.variant-secondary:hover {
  background: var(--color-primary);
  color: white;
}

.button-form.variant-danger {
  background: var(--color-error);
  border-color: var(--color-error);
}

.button-form.variant-danger:hover {
  background: var(--color-error);
  filter: brightness(0.9);
}

/* ===== SUBMIT BUTTON ===== */
.button-submit {
  /* Extends form button */
  min-height: var(--button-height-lg);
  padding: var(--button-padding-lg);
  gap: 12px;
  
  /* Typography */
  font-size: 16px;
  font-weight: 600;
  
  /* Appearance */
  border-radius: var(--button-border-radius);
  border: 2px solid var(--color-primary);
  background: var(--color-primary);
  color: white;
  
  /* Effects */
  box-shadow: var(--button-shadow-hover);
  transition: var(--button-transition);
}

.button-submit:hover:not(:disabled) {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(var(--primary), 0.3);
}

.button-submit:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--button-shadow);
}

/* Submit Button States */
.button-submit.loading {
  background: var(--color-primary);
  pointer-events: none;
}

.button-submit.success {
  background: var(--color-success);
  border-color: var(--color-success);
  animation: submit-success 0.3s ease-out;
}

.button-submit.error {
  background: var(--color-error);
  border-color: var(--color-error);
  animation: submit-error 0.3s ease-out;
}

@keyframes submit-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes submit-error {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* Circle Animation for Success */
.button-submit.success::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  transform: translate(-50%, -50%) scale(0);
  animation: submit-circle 0.6s ease-out forwards;
}

@keyframes submit-circle {
  0% { transform: translate(-50%, -50%) scale(0); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

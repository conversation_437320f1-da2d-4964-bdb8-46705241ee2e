export default {
  title: "Feedback",
  subtitle: "Your opinion matters",
  description: "Share your experience and suggestions",
  typeQuestion: "What type of feedback would you like to share?",
  close: "Close",
  back: "Back",
  send: "Send feedback",
  sending: "Sending...",
  includeEmail: "Include my email for response",
  privacyPolicy: "Privacy Policy",

  // Feedback types
  problem: "Report Problem",
  idea: "Share Idea",
  praise: "Give Praise",

  // Specific titles by type
  problemTitle: "Report Problem",
  ideaTitle: "Share Idea",
  praiseTitle: "Give Praise",
  defaultTitle: "Send Feedback",

  // Specific instructions by type
  problemInstruction: "Describe the problem you found in detail",
  ideaInstruction: "Share your idea or improvement suggestion",
  praiseInstruction: "Tell us what you liked",
  defaultInstruction: "Share your feedback with us",

  // Specific placeholders by type
  problemPlaceholder: "Describe the problem you found...",
  ideaPlaceholder: "Share your idea or suggestion...",
  praisePlaceholder: "Tell us what you liked...",
  defaultPlaceholder: "Share your feedback...",

  // Validation
  validation: {
    messageRequired: "Message is required",
    messageMinLength: "Minimum 5 characters",
    emailInvalid: "Invalid email"
  },

  // Form
  form: {
    type: "Feedback type",
    message: "Your message",
    email: "Your email (optional)",
    send: "Send feedback",
    sending: "Sending...",
    success: "✅ Thank you for your feedback! Your opinion is very important to us.",
    error: "❌ Oops! We couldn't send your feedback. Try again or contact directly.",
    messageRequired: "Message is required"
  },

  // Status messages
  status: {
    success: "Thank you for your feedback!",
    error: "Error sending feedback. Please try again.",
    sending: "Sending feedback..."
  },

  // Legacy types (maintain compatibility)
  types: {
    bug: "Report bug",
    suggestion: "Suggestion",
    compliment: "Compliment",
    other: "Other"
  }
};

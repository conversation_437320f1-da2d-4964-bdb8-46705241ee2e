import{r as e,a as t,v as n,R as r,b as o}from"./chunk-D3Ns84uO.js";import{j as i}from"./chunk-L3uak9dD.js";import{r as l}from"./chunk-DJqUFuPP.js";function a(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1;const r=e.map((e=>{const r=s(e,t);return n||"function"!=typeof r||(n=!0),r}));if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():s(e[t],null)}}}}function c(...t){return e.useCallback(u(...t),t)}function d(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const l=e.createContext(o),a=r.length;r=[...r,o];const s=n=>{var r;const{scope:o,children:s,...u}=n,c=(null==(r=null==o?void 0:o[t])?void 0:r[a])||l,d=e.useMemo((()=>u),Object.values(u));return i.jsx(c.Provider,{value:d,children:s})};return s.displayName=n+"Provider",[s,function(r,i){var s;const u=(null==(s=null==i?void 0:i[t])?void 0:s[a])||l,c=e.useContext(u);if(c)return c;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},f(o,...n)]}function f(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function p(t){const n=h(t),r=e.forwardRef(((t,r)=>{const{children:o,...l}=t,a=e.Children.toArray(o),s=a.find(y);if(s){const t=s.props.children,o=a.map((n=>n===s?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n));return i.jsx(n,{...l,ref:r,children:e.isValidElement(t)?e.cloneElement(t,void 0,o):null})}return i.jsx(n,{...l,ref:r,children:o})}));return r.displayName=`${t}.Slot`,r}var m=p("Slot");function h(t){const n=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(r),i=function(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{const t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,r.props);return r.type!==e.Fragment&&(i.ref=n?u(n,t):t),e.cloneElement(r,i)}return e.Children.count(r)>1?e.Children.only(null):null}));return n.displayName=`${t}.SlotClone`,n}var v=Symbol("radix.slottable");function g(e){const t=({children:e})=>i.jsx(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=v,t}function y(t){return e.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===v}var w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce(((t,n)=>{const r=p(`Primitive.${n}`),o=e.forwardRef(((e,t)=>{const{asChild:o,...l}=e,a=o?r:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),i.jsx(a,{...l,ref:t})}));return o.displayName=`Primitive.${n}`,{...t,[n]:o}}),{});function b(e,n){e&&t.flushSync((()=>e.dispatchEvent(n)))}function x(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>{var t;return null==(t=n.current)?void 0:t.call(n,...e)}),[])}var E,P="dismissableLayer.update",C="dismissableLayer.pointerDownOutside",S="dismissableLayer.focusOutside",T=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),R=e.forwardRef(((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:s,onInteractOutside:u,onDismiss:d,...f}=t,p=e.useContext(T),[m,h]=e.useState(null),v=(null==m?void 0:m.ownerDocument)??(null==globalThis?void 0:globalThis.document),[,g]=e.useState({}),y=c(n,(e=>h(e))),b=Array.from(p.layers),[R]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),O=b.indexOf(R),F=m?b.indexOf(m):-1,L=p.layersWithOutsidePointerEventsDisabled.size>0,D=F>=O,N=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=x(t),o=e.useRef(!1),i=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){k(C,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...p.branches].some((e=>e.contains(t)));D&&!n&&(null==l||l(e),null==u||u(e),e.defaultPrevented||null==d||d())}),v),j=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=x(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){k(S,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...p.branches].some((e=>e.contains(t)))||(null==s||s(e),null==u||u(e),e.defaultPrevented||null==d||d())}),v);return function(t,n=(null==globalThis?void 0:globalThis.document)){const r=x(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{F===p.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))}),v),e.useEffect((()=>{if(m)return r&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(E=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(m)),p.layers.add(m),A(),()=>{r&&1===p.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=E)}}),[m,v,r,p]),e.useEffect((()=>()=>{m&&(p.layers.delete(m),p.layersWithOutsidePointerEventsDisabled.delete(m),A())}),[m,p]),e.useEffect((()=>{const e=()=>g({});return document.addEventListener(P,e),()=>document.removeEventListener(P,e)}),[]),i.jsx(w.div,{...f,ref:y,style:{pointerEvents:L?D?"auto":"none":void 0,...t.style},onFocusCapture:a(t.onFocusCapture,j.onFocusCapture),onBlurCapture:a(t.onBlurCapture,j.onBlurCapture),onPointerDownCapture:a(t.onPointerDownCapture,N.onPointerDownCapture)})}));R.displayName="DismissableLayer";var O=e.forwardRef(((t,n)=>{const r=e.useContext(T),o=e.useRef(null),l=c(n,o);return e.useEffect((()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}}),[r.branches]),i.jsx(w.div,{...t,ref:l})}));function A(){const e=new CustomEvent(P);document.dispatchEvent(e)}function k(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?b(o,i):o.dispatchEvent(i)}O.displayName="DismissableLayerBranch";var F=R,L=O,D=(null==globalThis?void 0:globalThis.document)?e.useLayoutEffect:()=>{},N=e.forwardRef(((t,r)=>{var o;const{container:l,...a}=t,[s,u]=e.useState(!1);D((()=>u(!0)),[]);const c=l||s&&(null==(o=null==globalThis?void 0:globalThis.document)?void 0:o.body);return c?n.createPortal(i.jsx(w.div,{...a,ref:r}),c):null}));N.displayName="Portal";var j=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef(null),i=e.useRef(t),l=e.useRef("none"),a=t?"mounted":"unmounted",[s,u]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=M(o.current);l.current="mounted"===s?e:"none"}),[s]),D((()=>{const e=o.current,n=i.current;if(n!==t){const r=l.current,o=M(e);if(t)u("MOUNT");else if("none"===o||"none"===(null==e?void 0:e.display))u("UNMOUNT");else{u(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}}),[t,u]),D((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const l=M(o.current).includes(r.animationName);if(r.target===n&&l&&(u("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},a=e=>{e.target===n&&(l.current=M(o.current))};return n.addEventListener("animationstart",a),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",a),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}u("ANIMATION_END")}),[n,u]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:e.useCallback((e=>{o.current=e?getComputedStyle(e):null,r(e)}),[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),l=c(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:l}):null};function M(e){return(null==e?void 0:e.animationName)||"none"}j.displayName="Presence";var I=r[" useInsertionEffect ".trim().toString()]||D;function _({prop:t,defaultProp:n,onChange:r=()=>{},caller:o}){const[i,l,a]=function({defaultProp:t,onChange:n}){const[r,o]=e.useState(t),i=e.useRef(r),l=e.useRef(n);return I((()=>{l.current=n}),[n]),e.useEffect((()=>{var e;i.current!==r&&(null==(e=l.current)||e.call(l,r),i.current=r)}),[r,i]),[r,o,l]}({defaultProp:n,onChange:r}),s=void 0!==t,u=s?t:i;{const n=e.useRef(void 0!==t);e.useEffect((()=>{const e=n.current;if(e!==s){const t=e?"controlled":"uncontrolled",n=s?"controlled":"uncontrolled";console.warn(`${o} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=s}),[s,o])}const c=e.useCallback((e=>{var n;if(s){const r=function(e){return"function"==typeof e}(e)?e(t):e;r!==t&&(null==(n=a.current)||n.call(a,r))}else l(e)}),[s,t,l,a]);return[u,c]}var W=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),H=e.forwardRef(((e,t)=>i.jsx(w.span,{...e,ref:t,style:{...W,...e.style}})));H.displayName="VisuallyHidden";var $=H;function B(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=B(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function V(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=B(e))&&(r&&(r+=" "),r+=t);return r}var U=r[" useId ".trim().toString()]||(()=>{}),z=0;function Y(t){const[n,r]=e.useState(U());return D((()=>{r((e=>e??String(z++)))}),[t]),n?`radix-${n}`:""}const q=["top","right","bottom","left"],X=Math.min,K=Math.max,G=Math.round,Z=Math.floor,J=e=>({x:e,y:e}),Q={left:"right",right:"left",bottom:"top",top:"bottom"},ee={start:"end",end:"start"};function te(e,t,n){return K(e,X(t,n))}function ne(e,t){return"function"==typeof e?e(t):e}function re(e){return e.split("-")[0]}function oe(e){return e.split("-")[1]}function ie(e){return"x"===e?"y":"x"}function le(e){return"y"===e?"height":"width"}function ae(e){return["top","bottom"].includes(re(e))?"y":"x"}function se(e){return ie(ae(e))}function ue(e){return e.replace(/start|end/g,(e=>ee[e]))}function ce(e){return e.replace(/left|right|bottom|top/g,(e=>Q[e]))}function de(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function fe(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function pe(e,t,n){let{reference:r,floating:o}=e;const i=ae(t),l=se(t),a=le(l),s=re(t),u="y"===i,c=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[a]/2-o[a]/2;let p;switch(s){case"top":p={x:c,y:r.y-o.height};break;case"bottom":p={x:c,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(oe(t)){case"start":p[l]-=f*(n&&u?-1:1);break;case"end":p[l]+=f*(n&&u?-1:1)}return p}async function me(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=ne(t,e),m=de(p),h=a[f?"floating"===d?"reference":"floating":d],v=fe(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=fe(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-b.top+m.top)/w.y,bottom:(b.bottom-v.bottom+m.bottom)/w.y,left:(v.left-b.left+m.left)/w.x,right:(b.right-v.right+m.right)/w.x}}function he(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ve(e){return q.some((t=>e[t]>=0))}function ge(){return"undefined"!=typeof window}function ye(e){return xe(e)?(e.nodeName||"").toLowerCase():"#document"}function we(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function be(e){var t;return null==(t=(xe(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function xe(e){return!!ge()&&(e instanceof Node||e instanceof we(e).Node)}function Ee(e){return!!ge()&&(e instanceof Element||e instanceof we(e).Element)}function Pe(e){return!!ge()&&(e instanceof HTMLElement||e instanceof we(e).HTMLElement)}function Ce(e){return!(!ge()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof we(e).ShadowRoot)}function Se(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Fe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Te(e){return["table","td","th"].includes(ye(e))}function Re(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(Rn){return!1}}))}function Oe(e){const t=Ae(),n=Ee(e)?Fe(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Ae(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function ke(e){return["html","body","#document"].includes(ye(e))}function Fe(e){return we(e).getComputedStyle(e)}function Le(e){return Ee(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function De(e){if("html"===ye(e))return e;const t=e.assignedSlot||e.parentNode||Ce(e)&&e.host||be(e);return Ce(t)?t.host:t}function Ne(e){const t=De(e);return ke(t)?e.ownerDocument?e.ownerDocument.body:e.body:Pe(t)&&Se(t)?t:Ne(t)}function je(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Ne(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=we(o);if(i){const e=Me(l);return t.concat(l,l.visualViewport||[],Se(o)?o:[],e&&n?je(e):[])}return t.concat(o,je(o,[],n))}function Me(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ie(e){const t=Fe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Pe(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=G(n)!==i||G(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function _e(e){return Ee(e)?e:e.contextElement}function We(e){const t=_e(e);if(!Pe(t))return J(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Ie(t);let l=(i?G(n.width):n.width)/r,a=(i?G(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}const He=J(0);function $e(e){const t=we(e);return Ae()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:He}function Be(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=_e(e);let l=J(1);t&&(r?Ee(r)&&(l=We(r)):l=We(e));const a=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==we(e))&&t}(i,n,r)?$e(i):J(0);let s=(o.left+a.x)/l.x,u=(o.top+a.y)/l.y,c=o.width/l.x,d=o.height/l.y;if(i){const e=we(i),t=r&&Ee(r)?we(r):r;let n=e,o=Me(n);for(;o&&r&&t!==n;){const e=We(o),t=o.getBoundingClientRect(),r=Fe(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,u*=e.y,c*=e.x,d*=e.y,s+=i,u+=l,n=we(o),o=Me(n)}}return fe({width:c,height:d,x:s,y:u})}function Ve(e,t){const n=Le(e).scrollLeft;return t?t.left+n:Be(be(e)).left+n}function Ue(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Ve(e,r)),y:r.top+t.scrollTop}}function ze(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=we(e),r=be(e),o=n.visualViewport;let i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;const e=Ae();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){const t=be(e),n=Le(e),r=e.ownerDocument.body,o=K(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=K(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+Ve(e);const a=-n.scrollTop;return"rtl"===Fe(r).direction&&(l+=K(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(be(e));else if(Ee(t))r=function(e,t){const n=Be(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Pe(e)?We(e):J(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=$e(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return fe(r)}function Ye(e,t){const n=De(e);return!(n===t||!Ee(n)||ke(n))&&("fixed"===Fe(n).position||Ye(n,t))}function qe(e,t,n){const r=Pe(t),o=be(t),i="fixed"===n,l=Be(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const s=J(0);function u(){s.x=Ve(o)}if(r||!r&&!i)if(("body"!==ye(t)||Se(o))&&(a=Le(t)),r){const e=Be(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&u();i&&!r&&o&&u();const c=!o||r||i?J(0):Ue(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}function Xe(e){return"static"===Fe(e).position}function Ke(e,t){if(!Pe(e)||"fixed"===Fe(e).position)return null;if(t)return t(e);let n=e.offsetParent;return be(e)===n&&(n=n.ownerDocument.body),n}function Ge(e,t){const n=we(e);if(Re(e))return n;if(!Pe(e)){let t=De(e);for(;t&&!ke(t);){if(Ee(t)&&!Xe(t))return t;t=De(t)}return n}let r=Ke(e,t);for(;r&&Te(r)&&Xe(r);)r=Ke(r,t);return r&&ke(r)&&Xe(r)&&!Oe(r)?n:r||function(e){let t=De(e);for(;Pe(t)&&!ke(t);){if(Oe(t))return t;if(Re(t))return null;t=De(t)}return null}(e)||n}const Ze={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,l=be(r),a=!!t&&Re(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=J(1);const c=J(0),d=Pe(r);if((d||!d&&!i)&&(("body"!==ye(r)||Se(l))&&(s=Le(r)),Pe(r))){const e=Be(r);u=We(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}const f=!l||d||i?J(0):Ue(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:be,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?Re(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=je(e,[],!1).filter((e=>Ee(e)&&"body"!==ye(e))),o=null;const i="fixed"===Fe(e).position;let l=i?De(e):e;for(;Ee(l)&&!ke(l);){const t=Fe(l),n=Oe(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Se(l)&&!n&&Ye(e,l))?r=r.filter((e=>e!==l)):o=t,l=De(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce(((e,n)=>{const r=ze(t,n,o);return e.top=K(r.top,e.top),e.right=X(r.right,e.right),e.bottom=X(r.bottom,e.bottom),e.left=K(r.left,e.left),e}),ze(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:Ge,getElementRects:async function(e){const t=this.getOffsetParent||Ge,n=this.getDimensions,r=await n(e.floating);return{reference:qe(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Ie(e);return{width:t,height:n}},getScale:We,isElement:Ee,isRTL:function(e){return"rtl"===Fe(e).direction}};function Je(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Qe(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,u=_e(e),c=o||i?[...u?je(u):[],...je(t)]:[];c.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=u&&a?function(e,t){let n,r=null;const o=be(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();const u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(a||t(),!f||!p)return;const m={rootMargin:-Z(d)+"px "+-Z(o.clientWidth-(c+f))+"px "+-Z(o.clientHeight-(d+p))+"px "+-Z(c)+"px",threshold:K(0,X(1,s))||1};let h=!0;function v(t){const r=t[0].intersectionRatio;if(r!==s){if(!h)return l();r?l(!1,r):n=setTimeout((()=>{l(!1,1e-7)}),1e3)}1!==r||Je(u,e.getBoundingClientRect())||l(),h=!1}try{r=new IntersectionObserver(v,{...m,root:o.ownerDocument})}catch(g){r=new IntersectionObserver(v,m)}r.observe(e)}(!0),i}(u,n):null;let f,p=-1,m=null;l&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),u&&!s&&m.observe(u),m.observe(t));let h=s?Be(e):null;return s&&function t(){const r=Be(e);h&&!Je(h,r)&&n();h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,s&&cancelAnimationFrame(f)}}const et=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:a}=t,s=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),l=re(n),a=oe(n),s="y"===ae(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,d=ne(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}},tt=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=ne(e,t),u={x:n,y:r},c=await me(t,s),d=ae(re(o)),f=ie(d);let p=u[f],m=u[d];if(i){const e="y"===f?"bottom":"right";p=te(p+c["y"===f?"top":"left"],p,p-c[e])}if(l){const e="y"===d?"bottom":"right";m=te(m+c["y"===d?"top":"left"],m,m-c[e])}const h=a.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}},nt=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:l,initialPlacement:a,platform:s,elements:u}=t,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...v}=ne(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=re(o),y=ae(a),w=re(a)===a,b=await(null==s.isRTL?void 0:s.isRTL(u.floating)),x=f||(w||!h?[ce(a)]:function(e){const t=ce(e);return[ue(e),t,ue(t)]}(a)),E="none"!==m;!f&&E&&x.push(...function(e,t,n,r){const o=oe(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:l;default:return[]}}(re(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(ue)))),i}(a,h,m,b));const P=[a,...x],C=await me(t,v),S=[];let T=(null==(r=i.flip)?void 0:r.overflows)||[];if(c&&S.push(C[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=oe(e),o=se(e),i=le(o);let l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ce(l)),[l,ce(l)]}(o,l,b);S.push(C[e[0]],C[e[1]])}if(T=[...T,{placement:o,overflows:S}],!S.every((e=>e<=0))){var R,O;const e=((null==(R=i.flip)?void 0:R.index)||0)+1,t=P[e];if(t){var A;const n="alignment"===d&&y!==ae(t),r=(null==(A=T[0])?void 0:A.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:T},reset:{placement:t}}}let n=null==(O=T.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:O.placement;if(!n)switch(p){case"bestFit":{var k;const e=null==(k=T.filter((e=>{if(E){const t=ae(e.placement);return t===y||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:k[0];e&&(n=e);break}case"initialPlacement":n=a}if(o!==n)return{reset:{placement:n}}}return{}}}},rt=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:l,elements:a}=t,{apply:s=()=>{},...u}=ne(e,t),c=await me(t,u),d=re(o),f=oe(o),p="y"===ae(o),{width:m,height:h}=i.floating;let v,g;"top"===d||"bottom"===d?(v=d,g=f===(await(null==l.isRTL?void 0:l.isRTL(a.floating))?"start":"end")?"left":"right"):(g=d,v="end"===f?"top":"bottom");const y=h-c.top-c.bottom,w=m-c.left-c.right,b=X(h-c[v],y),x=X(m-c[g],w),E=!t.middlewareData.shift;let P=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=y),E&&!f){const e=K(c.left,0),t=K(c.right,0),n=K(c.top,0),r=K(c.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:K(c.left,c.right)):P=h-2*(0!==n||0!==r?n+r:K(c.top,c.bottom))}await s({...t,availableWidth:C,availableHeight:P});const S=await l.getDimensions(a.floating);return m!==S.width||h!==S.height?{reset:{rects:!0}}:{}}}},ot=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ne(e,t);switch(r){case"referenceHidden":{const e=he(await me(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ve(e)}}}case"escaped":{const e=he(await me(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ve(e)}}}default:return{}}}}},it=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=ne(e,t)||{};if(null==u)return{};const d=de(c),f={x:n,y:r},p=se(o),m=le(p),h=await l.getDimensions(u),v="y"===p,g=v?"top":"left",y=v?"bottom":"right",w=v?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[p]-f[p]-i.floating[m],x=f[p]-i.reference[p],E=await(null==l.getOffsetParent?void 0:l.getOffsetParent(u));let P=E?E[w]:0;P&&await(null==l.isElement?void 0:l.isElement(E))||(P=a.floating[w]||i.floating[m]);const C=b/2-x/2,S=P/2-h[m]/2-1,T=X(d[g],S),R=X(d[y],S),O=T,A=P-h[m]-R,k=P/2-h[m]/2+C,F=te(O,k,A),L=!s.arrow&&null!=oe(o)&&k!==F&&i.reference[m]/2-(k<O?T:R)-h[m]/2<0,D=L?k<O?k-O:k-A:0;return{[p]:f[p]+D,data:{[p]:F,centerOffset:k-F-D,...L&&{alignmentOffset:D}},reset:L}}}),lt=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=ne(e,t),c={x:n,y:r},d=ae(o),f=ie(d);let p=c[f],m=c[d];const h=ne(a,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;const e="y"===f?"width":"height",t=["top","left"].includes(re(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},at=(e,t,n)=>{const r=new Map,o={platform:Ze,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=pe(u,r,s),f=r,p={},m=0;for(let h=0;h<a.length;h++){const{name:n,fn:i}=a[h],{x:v,y:g,data:y,reset:w}=await i({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[n]:{...p[n],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:c,y:d}=pe(u,f,s))),h=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var st="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function ut(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!ut(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ut(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ct(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function dt(e,t){const n=ct(e);return Math.round(t*n)/n}function ft(t){const n=e.useRef(t);return st((()=>{n.current=t})),n}const pt=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?it({element:n.current,padding:r}).fn(t):{}:n?it({element:n,padding:r}).fn(t):{};var o}}),mt=(e,t)=>({...tt(e),options:[e,t]}),ht=(e,t)=>({...lt(e),options:[e,t]}),vt=(e,t)=>({...nt(e),options:[e,t]}),gt=(e,t)=>({...rt(e),options:[e,t]}),yt=(e,t)=>({...ot(e),options:[e,t]}),wt=(e,t)=>({...pt(e),options:[e,t]});var bt=e.forwardRef(((e,t)=>{const{children:n,width:r=10,height:o=5,...l}=e;return i.jsx(w.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:i.jsx("polygon",{points:"0,0 30,0 15,10"})})}));bt.displayName="Arrow";var xt=bt;var Et="Popper",[Pt,Ct]=d(Et),[St,Tt]=Pt(Et),Rt="PopperAnchor",Ot=e.forwardRef(((t,n)=>{const{__scopePopper:r,virtualRef:o,...l}=t,a=Tt(Rt,r),s=e.useRef(null),u=c(n,s);return e.useEffect((()=>{a.onAnchorChange((null==o?void 0:o.current)||s.current)})),o?null:i.jsx(w.div,{...l,ref:u})}));Ot.displayName=Rt;var At="PopperContent",[kt,Ft]=Pt(At),Lt=e.forwardRef(((n,r)=>{var o,l,a,s,u,d;const{__scopePopper:f,side:p="bottom",sideOffset:m=0,align:h="center",alignOffset:v=0,arrowPadding:g=0,avoidCollisions:y=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:P="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:T,...R}=n,O=Tt(At,f),[A,k]=e.useState(null),F=c(r,(e=>k(e))),[L,N]=e.useState(null),j=function(t){const[n,r]=e.useState(void 0);return D((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}(L),M=(null==j?void 0:j.width)??0,I=(null==j?void 0:j.height)??0,_=p+("center"!==h?"-"+h:""),W="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},H=Array.isArray(b)?b:[b],$=H.length>0,B={padding:W,boundary:H.filter(Mt),altBoundary:$},{refs:V,floatingStyles:U,placement:z,isPositioned:Y,middlewareData:q}=function(n){void 0===n&&(n={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:d}=n,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[m,h]=e.useState(i);ut(m,i)||h(i);const[v,g]=e.useState(null),[y,w]=e.useState(null),b=e.useCallback((e=>{e!==C.current&&(C.current=e,g(e))}),[]),x=e.useCallback((e=>{e!==S.current&&(S.current=e,w(e))}),[]),E=a||v,P=s||y,C=e.useRef(null),S=e.useRef(null),T=e.useRef(f),R=null!=c,O=ft(c),A=ft(l),k=ft(d),F=e.useCallback((()=>{if(!C.current||!S.current)return;const e={placement:r,strategy:o,middleware:m};A.current&&(e.platform=A.current),at(C.current,S.current,e).then((e=>{const n={...e,isPositioned:!1!==k.current};L.current&&!ut(T.current,n)&&(T.current=n,t.flushSync((()=>{p(n)})))}))}),[m,r,o,A,k]);st((()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const L=e.useRef(!1);st((()=>(L.current=!0,()=>{L.current=!1})),[]),st((()=>{if(E&&(C.current=E),P&&(S.current=P),E&&P){if(O.current)return O.current(E,P,F);F()}}),[E,P,F,O,R]);const D=e.useMemo((()=>({reference:C,floating:S,setReference:b,setFloating:x})),[b,x]),N=e.useMemo((()=>({reference:E,floating:P})),[E,P]),j=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!N.floating)return e;const t=dt(N.floating,f.x),n=dt(N.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...ct(N.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,u,N.floating,f.x,f.y]);return e.useMemo((()=>({...f,update:F,refs:D,elements:N,floatingStyles:j})),[f,F,D,N,j])}({strategy:"fixed",placement:_,whileElementsMounted:(...e)=>Qe(...e,{animationFrame:"always"===S}),elements:{reference:O.anchor},middleware:[(X={mainAxis:m+I,alignmentAxis:v},{...et(X),options:[X,K]}),y&&mt({mainAxis:!0,crossAxis:!1,limiter:"partial"===P?ht():void 0,...B}),y&&vt({...B}),gt({...B,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),L&&wt({element:L,padding:g}),It({arrowWidth:M,arrowHeight:I}),C&&yt({strategy:"referenceHidden",...B})]});var X,K;const[G,Z]=_t(z),J=x(T);D((()=>{Y&&(null==J||J())}),[Y,J]);const Q=null==(o=q.arrow)?void 0:o.x,ee=null==(l=q.arrow)?void 0:l.y,te=0!==(null==(a=q.arrow)?void 0:a.centerOffset),[ne,re]=e.useState();return D((()=>{A&&re(window.getComputedStyle(A).zIndex)}),[A]),i.jsx("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:Y?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ne,"--radix-popper-transform-origin":[null==(s=q.transformOrigin)?void 0:s.x,null==(u=q.transformOrigin)?void 0:u.y].join(" "),...(null==(d=q.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:i.jsx(kt,{scope:f,placedSide:G,onArrowChange:N,arrowX:Q,arrowY:ee,shouldHideArrow:te,children:i.jsx(w.div,{"data-side":G,"data-align":Z,...R,ref:F,style:{...R.style,animation:Y?void 0:"none"}})})})}));Lt.displayName=At;var Dt="PopperArrow",Nt={top:"bottom",right:"left",bottom:"top",left:"right"},jt=e.forwardRef((function(e,t){const{__scopePopper:n,...r}=e,o=Ft(Dt,n),l=Nt[o.placedSide];return i.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:i.jsx(xt,{...r,ref:t,style:{...r.style,display:"block"}})})}));function Mt(e){return null!==e}jt.displayName=Dt;var It=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o;const{placement:i,rects:l,middlewareData:a}=t,s=0!==(null==(n=a.arrow)?void 0:n.centerOffset),u=s?0:e.arrowWidth,c=s?0:e.arrowHeight,[d,f]=_t(i),p={start:"0%",center:"50%",end:"100%"}[f],m=((null==(r=a.arrow)?void 0:r.x)??0)+u/2,h=((null==(o=a.arrow)?void 0:o.y)??0)+c/2;let v="",g="";return"bottom"===d?(v=s?p:`${m}px`,g=-c+"px"):"top"===d?(v=s?p:`${m}px`,g=`${l.floating.height+c}px`):"right"===d?(v=-c+"px",g=s?p:`${h}px`):"left"===d&&(v=`${l.floating.width+c}px`,g=s?p:`${h}px`),{data:{x:v,y:g}}}});function _t(e){const[t,n="center"]=e.split("-");return[t,n]}var Wt=Ot,Ht=Lt,$t=jt,[Bt,Vt]=d("Tooltip",[Ct]),Ut=Ct(),zt="TooltipProvider",Yt=700,qt="tooltip.open",[Xt,Kt]=Bt(zt),Gt=t=>{const{__scopeTooltip:n,delayDuration:r=Yt,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:a}=t,s=e.useRef(!0),u=e.useRef(!1),c=e.useRef(0);return e.useEffect((()=>{const e=c.current;return()=>window.clearTimeout(e)}),[]),i.jsx(Xt,{scope:n,isOpenDelayedRef:s,delayDuration:r,onOpen:e.useCallback((()=>{window.clearTimeout(c.current),s.current=!1}),[]),onClose:e.useCallback((()=>{window.clearTimeout(c.current),c.current=window.setTimeout((()=>s.current=!0),o)}),[o]),isPointerInTransitRef:u,onPointerInTransitChange:e.useCallback((e=>{u.current=e}),[]),disableHoverableContent:l,children:a})};Gt.displayName=zt;var Zt="Tooltip",[Jt,Qt]=Bt(Zt),en="TooltipTrigger";e.forwardRef(((t,n)=>{const{__scopeTooltip:r,...o}=t,l=Qt(en,r),s=Kt(en,r),u=Ut(r),d=c(n,e.useRef(null),l.onTriggerChange),f=e.useRef(!1),p=e.useRef(!1),m=e.useCallback((()=>f.current=!1),[]);return e.useEffect((()=>()=>document.removeEventListener("pointerup",m)),[m]),i.jsx(Wt,{asChild:!0,...u,children:i.jsx(w.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...o,ref:d,onPointerMove:a(t.onPointerMove,(e=>{"touch"!==e.pointerType&&(p.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),p.current=!0))})),onPointerLeave:a(t.onPointerLeave,(()=>{l.onTriggerLeave(),p.current=!1})),onPointerDown:a(t.onPointerDown,(()=>{l.open&&l.onClose(),f.current=!0,document.addEventListener("pointerup",m,{once:!0})})),onFocus:a(t.onFocus,(()=>{f.current||l.onOpen()})),onBlur:a(t.onBlur,l.onClose),onClick:a(t.onClick,l.onClose)})})})).displayName=en;var[tn,nn]=Bt("TooltipPortal",{forceMount:void 0}),rn="TooltipContent",on=e.forwardRef(((e,t)=>{const n=nn(rn,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...l}=e,a=Qt(rn,e.__scopeTooltip);return i.jsx(j,{present:r||a.open,children:a.disableHoverableContent?i.jsx(cn,{side:o,...l,ref:t}):i.jsx(ln,{side:o,...l,ref:t})})})),ln=e.forwardRef(((t,n)=>{const r=Qt(rn,t.__scopeTooltip),o=Kt(rn,t.__scopeTooltip),l=e.useRef(null),a=c(n,l),[s,u]=e.useState(null),{trigger:d,onClose:f}=r,p=l.current,{onPointerInTransitChange:m}=o,h=e.useCallback((()=>{u(null),m(!1)}),[m]),v=e.useCallback(((e,t)=>{const n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,function(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}(r,n.getBoundingClientRect())),i=function(e){const t=e.slice();return t.sort(((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0)),function(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const n=e[r];for(;t.length>=2;){const e=t[t.length-1],r=t[t.length-2];if(!((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x)))break;t.pop()}t.push(n)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const t=e[r];for(;n.length>=2;){const e=n[n.length-1],r=n[n.length-2];if(!((e.x-r.x)*(t.y-r.y)>=(e.y-r.y)*(t.x-r.x)))break;n.pop()}n.push(t)}return n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}(t)}([...o,...function(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())]);u(i),m(!0)}),[m]);return e.useEffect((()=>()=>h()),[h]),e.useEffect((()=>{if(d&&p){const e=e=>v(e,p),t=e=>v(e,d);return d.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{d.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}}),[d,p,v,h]),e.useEffect((()=>{if(s){const e=e=>{const t=e.target,n={x:e.clientX,y:e.clientY},r=(null==d?void 0:d.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,l=t.length-1;i<t.length;l=i++){const e=t[i],a=t[l],s=e.x,u=e.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}(n,s);r?h():o&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}}),[d,p,s,f,h]),i.jsx(cn,{...t,ref:a})})),[an,sn]=Bt(Zt,{isInside:!1}),un=g("TooltipContent"),cn=e.forwardRef(((t,n)=>{const{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:a,onPointerDownOutside:s,...u}=t,c=Qt(rn,r),d=Ut(r),{onClose:f}=c;return e.useEffect((()=>(document.addEventListener(qt,f),()=>document.removeEventListener(qt,f))),[f]),e.useEffect((()=>{if(c.trigger){const e=e=>{const t=e.target;(null==t?void 0:t.contains(c.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}}),[c.trigger,f]),i.jsx(R,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:i.jsxs(Ht,{"data-state":c.stateAttribute,...d,...u,ref:n,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[i.jsx(un,{children:o}),i.jsx(an,{scope:r,isInside:!0,children:i.jsx($,{id:c.contentId,role:"tooltip",children:l||o})})]})})}));on.displayName=rn;var dn="TooltipArrow";e.forwardRef(((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Ut(n);return sn(dn,n).isInside?null:i.jsx($t,{...o,...r,ref:t})})).displayName=dn;var fn=Gt,pn=on,mn=Object.defineProperty,hn=(e,t,n)=>(((e,t,n)=>{t in e?mn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let vn=new class{constructor(){hn(this,"current",this.detect()),hn(this,"handoffState","pending"),hn(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}};function gn(e){var t,n;return vn.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}function yn(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function wn(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))),requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add((()=>cancelAnimationFrame(n)))},nextFrame:(...e)=>t.requestAnimationFrame((()=>t.requestAnimationFrame(...e))),setTimeout(...e){let n=setTimeout(...e);return t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return yn((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=wn();return e(t),this.add((()=>t.dispose()))},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function bn(){let[t]=e.useState(wn);return e.useEffect((()=>()=>t.dispose()),[t]),t}let xn=(t,n)=>{vn.isServer?e.useEffect(t,n):e.useLayoutEffect(t,n)};function En(t){let n=e.useRef(t);return xn((()=>{n.current=t}),[t]),n}let Pn=function(e){let t=En(e);return o.useCallback(((...e)=>t.current(...e)),[t])},Cn=e.createContext(void 0);function Sn(...e){return Array.from(new Set(e.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}function Tn(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,Tn),r}var Rn,On,An=((On=An||{})[On.None=0]="None",On[On.RenderStrategy=1]="RenderStrategy",On[On.Static=2]="Static",On),kn=((Rn=kn||{})[Rn.Unmount=0]="Unmount",Rn[Rn.Hidden=1]="Hidden",Rn);function Fn(){let t=function(){let t=e.useRef([]),n=e.useCallback((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}),[]);return(...e)=>{if(!e.every((e=>null==e)))return t.current=e,n}}();return e.useCallback((e=>function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:i=!0,name:l,mergeRefs:a}){a=null!=a?a:Dn;let s=Nn(t,e);if(i)return Ln(s,n,r,l,a);let u=null!=o?o:0;if(2&u){let{static:e=!1,...t}=s;if(e)return Ln(t,n,r,l,a)}if(1&u){let{unmount:e=!0,...t}=s;return Tn(e?0:1,{0:()=>null,1:()=>Ln({...t,hidden:!0,style:{display:"none"}},n,r,l,a)})}return Ln(s,n,r,l,a)}({mergeRefs:t,...e})),[t])}function Ln(t,n={},r,o,i){let{as:l=r,children:a,refName:s="ref",...u}=In(t,["unmount","static"]),c=void 0!==t.ref?{[s]:t.ref}:{},d="function"==typeof a?a(n):a;"className"in u&&u.className&&"function"==typeof u.className&&(u.className=u.className(n)),u["aria-labelledby"]&&u["aria-labelledby"]===u.id&&(u["aria-labelledby"]=void 0);let f={};if(n){let e=!1,t=[];for(let[r,o]of Object.entries(n))"boolean"==typeof o&&(e=!0),!0===o&&t.push(r.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)));if(e){f["data-headlessui-state"]=t.join(" ");for(let e of t)f[`data-${e}`]=""}}if(l===e.Fragment&&(Object.keys(Mn(u)).length>0||Object.keys(Mn(f)).length>0)){if(e.isValidElement(d)&&!(Array.isArray(d)&&d.length>1)){let t=d.props,n=null==t?void 0:t.className,r="function"==typeof n?(...e)=>Sn(n(...e),u.className):Sn(n,u.className),o=r?{className:r}:{},l=Nn(d.props,Mn(In(u,["ref"])));for(let e in f)e in l&&delete f[e];return e.cloneElement(d,Object.assign({},l,f,c,{ref:i(_n(d),c.ref)},o))}if(Object.keys(Mn(u)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Mn(u)).concat(Object.keys(Mn(f))).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"))}return e.createElement(l,Object.assign({},In(u,["ref"]),l!==e.Fragment&&c,l!==e.Fragment&&f),d)}function Dn(...e){return e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function Nn(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let r in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(n[r]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let r in n)Object.assign(t,{[r](e,...t){let o=n[r];for(let n of o){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;n(e,...t)}}});return t}function jn(t){var n;return Object.assign(e.forwardRef(t),{displayName:null!=(n=t.displayName)?n:t.name})}function Mn(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function In(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function _n(e){return o.version.split(".")[0]>="19"?e.props.ref:e.ref}var Wn=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Wn||{});let Hn=jn((function(e,t){var n;let{features:r=1,...o}=e,i={ref:t,"aria-hidden":!(2&~r)||(null!=(n=o["aria-hidden"])?n:void 0),hidden:!(4&~r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~r)&&!!(2&~r)&&{display:"none"}}};return Fn()({ourProps:i,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}));function $n(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function Bn(e){return $n(e)&&"tagName"in e}function Vn(e){return Bn(e)&&"accessKey"in e}function Un(e){return Bn(e)&&"tabIndex"in e}let zn=Symbol();function Yn(...t){let n=e.useRef(t);e.useEffect((()=>{n.current=t}),[t]);let r=Pn((e=>{for(let t of n.current)null!=t&&("function"==typeof t?t(e):t.current=e)}));return t.every((e=>null==e||(null==e?void 0:e[zn])))?void 0:r}let qn=e.createContext(null);function Xn(){let t=e.useContext(qn);if(null===t){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Xn),e}return t}qn.displayName="DescriptionContext";let Kn=jn((function(t,n){let r=e.useId(),o=e.useContext(Cn),{id:i=`headlessui-description-${r}`,...l}=t,a=Xn(),s=Yn(n);xn((()=>a.register(i)),[i,a.register]);let u=o||!1,c=e.useMemo((()=>({...a.slot,disabled:u})),[a.slot,u]),d={ref:s,...a.props,id:i};return Fn()({ourProps:d,theirProps:l,slot:c,defaultTag:"p",name:a.name||"Description"})})),Gn=Object.assign(Kn,{});var Zn,Jn=((Zn=Jn||{}).Space=" ",Zn.Enter="Enter",Zn.Escape="Escape",Zn.Backspace="Backspace",Zn.Delete="Delete",Zn.ArrowLeft="ArrowLeft",Zn.ArrowUp="ArrowUp",Zn.ArrowRight="ArrowRight",Zn.ArrowDown="ArrowDown",Zn.Home="Home",Zn.End="End",Zn.PageUp="PageUp",Zn.PageDown="PageDown",Zn.Tab="Tab",Zn);let Qn=e.createContext((()=>{}));function er({value:e,children:t}){return o.createElement(Qn.Provider,{value:e},t)}let tr=class extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}};var nr,rr,or,ir=Object.defineProperty,lr=(e,t,n)=>(((e,t,n)=>{t in e?ir(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,t+"",n),n),ar=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},sr=(e,t,n)=>(ar(e,t,"read from private field"),n?n.call(e):t.get(e)),ur=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},cr=(e,t,n,r)=>(ar(e,t,"write to private field"),t.set(e,n),n);let dr=class{constructor(e){ur(this,nr,{}),ur(this,rr,new tr((()=>new Set))),ur(this,or,new Set),lr(this,"disposables",wn()),cr(this,nr,e)}dispose(){this.disposables.dispose()}get state(){return sr(this,nr)}subscribe(e,t){let n={selector:e,callback:t,current:e(sr(this,nr))};return sr(this,or).add(n),this.disposables.add((()=>{sr(this,or).delete(n)}))}on(e,t){return sr(this,rr).get(e).add(t),this.disposables.add((()=>{sr(this,rr).get(e).delete(t)}))}send(e){let t=this.reduce(sr(this,nr),e);if(t!==sr(this,nr)){cr(this,nr,t);for(let e of sr(this,or)){let t=e.selector(sr(this,nr));fr(e.current,t)||(e.current=t,e.callback(t))}for(let t of sr(this,rr).get(e.type))t(sr(this,nr),e)}}};function fr(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&pr(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&pr(e.entries(),t.entries()):!(!mr(e)||!mr(t))&&pr(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function pr(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function mr(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}nr=new WeakMap,rr=new WeakMap,or=new WeakMap;var hr,vr=Object.defineProperty,gr=(e,t,n)=>(((e,t,n)=>{t in e?vr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),yr=((hr=yr||{})[hr.Push=0]="Push",hr[hr.Pop=1]="Pop",hr);let wr={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}},br=class e extends dr{constructor(){super(...arguments),gr(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),gr(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new e({stack:[]})}reduce(e,t){return Tn(t.type,wr,e,t)}};const xr=new tr((()=>br.new()));var Er,Pr,Cr={exports:{}},Sr={};var Tr=(Pr||(Pr=1,Cr.exports=function(){if(Er)return Sr;Er=1;var e=l(),t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=e.useSyncExternalStore,r=e.useRef,o=e.useEffect,i=e.useMemo,a=e.useDebugValue;return Sr.useSyncExternalStoreWithSelector=function(e,l,s,u,c){var d=r(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=i((function(){function e(e){if(!o){if(o=!0,n=e,e=u(e),void 0!==c&&f.hasValue){var i=f.value;if(c(i,e))return r=i}return r=e}if(i=r,t(n,e))return i;var l=u(e);return void 0!==c&&c(i,l)?(n=e,i):(n=e,r=l)}var n,r,o=!1,i=void 0===s?null:s;return[function(){return e(l())},null===i?void 0:function(){return e(i())}]}),[l,s,u,c]);var p=n(e,d[0],d[1]);return o((function(){f.hasValue=!0,f.value=p}),[p]),a(p),p},Sr}()),Cr.exports);function Rr(e,t,n=fr){return Tr.useSyncExternalStoreWithSelector(Pn((t=>e.subscribe(Or,t))),Pn((()=>e.state)),Pn((()=>e.state)),Pn(t),n)}function Or(e){return e}function Ar(t,n){let r=e.useId(),o=xr.get(n),[i,l]=Rr(o,e.useCallback((e=>[o.selectors.isTop(e,r),o.selectors.inStack(e,r)]),[o,r]));return xn((()=>{if(t)return o.actions.push(r),()=>o.actions.pop(r)}),[o,t,r]),!!t&&(!l||i)}let kr=new Map,Fr=new Map;function Lr(e){var t;let n=null!=(t=Fr.get(e))?t:0;return Fr.set(e,n+1),0!==n||(kr.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>Dr(e)}function Dr(e){var t;let n=null!=(t=Fr.get(e))?t:1;if(1===n?Fr.delete(e):Fr.set(e,n-1),1!==n)return;let r=kr.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,kr.delete(e))}let Nr=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(","),jr=["[data-autofocus]"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var Mr,Ir,_r=((Ir=_r||{})[Ir.First=1]="First",Ir[Ir.Previous=2]="Previous",Ir[Ir.Next=4]="Next",Ir[Ir.Last=8]="Last",Ir[Ir.WrapAround=16]="WrapAround",Ir[Ir.NoScroll=32]="NoScroll",Ir[Ir.AutoFocus=64]="AutoFocus",Ir),Wr=((Mr=Wr||{})[Mr.Error=0]="Error",Mr[Mr.Overflow=1]="Overflow",Mr[Mr.Success=2]="Success",Mr[Mr.Underflow=3]="Underflow",Mr),Hr=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(Hr||{});var $r=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))($r||{});var Br=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Br||{});function Vr(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let Ur=["textarea","input"].join(",");function zr(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,l=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(jr)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Nr)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e);o.length>0&&l.length>1&&(l=l.filter((e=>!o.some((t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))))),r=null!=r?r:i.activeElement;let a,s=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,l.indexOf(r))-1;if(4&t)return Math.max(0,l.indexOf(r))+1;if(8&t)return l.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=l.length;do{if(d>=f||d+f<=0)return 0;let e=u+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}a=l[e],null==a||a.focus(c),d+=s}while(a!==i.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,Ur))&&n}(a)&&a.select(),2}function Yr(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function qr(){return Yr()||/Android/gi.test(window.navigator.userAgent)}function Xr(t,n,r,o){let i=En(r);e.useEffect((()=>{if(t)return document.addEventListener(n,e,o),()=>document.removeEventListener(n,e,o);function e(e){i.current(e)}}),[t,n,o])}function Kr(t,n,r,o){let i=En(r);e.useEffect((()=>{if(t)return window.addEventListener(n,e,o),()=>window.removeEventListener(n,e,o);function e(e){i.current(e)}}),[t,n,o])}function Gr(t,n,r){let o=En(r),i=e.useCallback((function(e,t){if(e.defaultPrevented)return;let r=t(e);if(null===r||!r.getRootNode().contains(r)||!r.isConnected)return;let i=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(n);for(let n of i)if(null!==n&&(n.contains(r)||e.composed&&e.composedPath().includes(n)))return;return!function(e,t=0){var n;return e!==(null==(n=gn(e))?void 0:n.body)&&Tn(t,{0:()=>e.matches(Nr),1(){let t=e;for(;null!==t;){if(t.matches(Nr))return!0;t=t.parentElement}return!1}})}(r,$r.Loose)&&-1!==r.tabIndex&&e.preventDefault(),o.current(e,r)}),[o,n]),l=e.useRef(null);Xr(t,"pointerdown",(e=>{var t,n;qr()||(l.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),Xr(t,"pointerup",(e=>{if(qr()||!l.current)return;let t=l.current;return l.current=null,i(e,(()=>t))}),!0);let a=e.useRef({x:0,y:0});Xr(t,"touchstart",(e=>{a.current.x=e.touches[0].clientX,a.current.y=e.touches[0].clientY}),!0),Xr(t,"touchend",(e=>{let t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(!(Math.abs(t-a.current.x)>=30||Math.abs(n-a.current.y)>=30))return i(e,(()=>Un(e.target)?e.target:null))}),!0),Kr(t,"blur",(e=>i(e,(()=>function(e){return Vn(e)&&"IFRAME"===e.nodeName}(window.document.activeElement)?window.document.activeElement:null))),!0)}function Zr(...t){return e.useMemo((()=>gn(...t)),[...t])}function Jr(t,n,r,o){let i=En(r);e.useEffect((()=>{function e(e){i.current(e)}return(t=null!=t?t:window).addEventListener(n,e,o),()=>t.removeEventListener(n,e,o)}),[t,n,o])}function Qr(){let e;return{before({doc:t}){var n;let r=t.documentElement,o=null!=(n=t.defaultView)?n:window;e=Math.max(0,o.innerWidth-r.clientWidth)},after({doc:t,d:n}){let r=t.documentElement,o=Math.max(0,r.clientWidth-r.offsetWidth),i=Math.max(0,e-o);n.style(r,"paddingRight",`${i}px`)}}}function eo(){return Yr()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap((e=>e())).some((t=>t.contains(e)))}t.microTask((()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=wn();n.style(e.documentElement,"scrollBehavior","auto"),t.add((()=>t.microTask((()=>n.dispose()))))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",(t=>{if(Un(t.target))try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),l=e.querySelector(o);Un(l)&&!r(l)&&(i=l)}catch{}}),!0),t.addEventListener(e,"touchstart",(e=>{if(Un(e.target)&&function(e){return Bn(e)&&"style"in e}(e.target))if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")})),t.addEventListener(e,"touchmove",(e=>{if(Un(e.target)){if(function(e){return Vn(e)&&"INPUT"===e.nodeName}(e.target))return;if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}),{passive:!1}),t.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)}))}))}}:{}}function to(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let no=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...o){let i=t[e].call(n,...o);i&&(n=i,r.forEach((e=>e())))}}}((()=>new Map),{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:wn(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:to(n)},o=[eo(),Qr(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach((({before:e})=>null==e?void 0:e(r))),o.forEach((({after:e})=>null==e?void 0:e(r)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function ro(t,n,r=()=>({containers:[]})){let o=function(t){return e.useSyncExternalStore(t.subscribe,t.getSnapshot,t.getSnapshot)}(no),i=n?o.get(n):void 0,l=!!i&&i.count>0;return xn((()=>{if(n&&t)return no.dispatch("PUSH",n,r),()=>no.dispatch("POP",n,r)}),[t,n]),l}no.subscribe((()=>{let e=no.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&no.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&no.dispatch("TEARDOWN",n)}}));var oo,io;"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&"test"===(null==(oo=null==process?void 0:{})?void 0:oo.NODE_ENV)&&void 0===(null==(io=null==Element?void 0:Element.prototype)?void 0:io.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join("\n")),[]});var lo=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(lo||{});function ao(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}function so(t,n,r,o){let[i,l]=e.useState(r),{hasFlag:a,addFlag:s,removeFlag:u}=function(t=0){let[n,r]=e.useState(t),o=e.useCallback((e=>r(e)),[n]),i=e.useCallback((e=>r((t=>t|e))),[n]),l=e.useCallback((e=>(n&e)===e),[n]),a=e.useCallback((e=>r((t=>t&~e))),[r]),s=e.useCallback((e=>r((t=>t^e))),[r]);return{flags:n,setFlag:o,addFlag:i,hasFlag:l,removeFlag:a,toggleFlag:s}}(t&&i?3:0),c=e.useRef(!1),d=e.useRef(!1),f=bn();return xn((()=>{var e;if(t)return r&&l(!0),n?(null==(e=null==o?void 0:o.start)||e.call(o,r),function(e,{prepare:t,run:n,done:r,inFlight:o}){let i=wn();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return void n();let r=e.style.transition;e.style.transition="none",n(),e.style.transition=r}(e,{prepare:t,inFlight:o}),i.nextFrame((()=>{n(),i.requestAnimationFrame((()=>{i.add(function(e,t){var n,r;let o=wn();if(!e)return o.dispose;let i=!1;o.add((()=>{i=!0}));let l=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter((e=>e instanceof CSSTransition)))?r:[];return 0===l.length?(t(),o.dispose):(Promise.allSettled(l.map((e=>e.finished))).then((()=>{i||t()})),o.dispose)}(e,r))}))})),i.dispose}(n,{inFlight:c,prepare(){d.current?d.current=!1:d.current=c.current,c.current=!0,!d.current&&(r?(s(3),u(4)):(s(4),u(2)))},run(){d.current?r?(u(3),s(4)):(u(4),s(3)):r?u(1):s(1)},done(){var e;d.current&&"function"==typeof n.getAnimations&&n.getAnimations().length>0||(c.current=!1,u(7),r||l(!1),null==(e=null==o?void 0:o.end)||e.call(o,r))}})):void(r&&s(3))}),[t,r,n,f]),t?[i,{closed:a(1),enter:a(2),leave:a(4),transition:a(2)||a(4)}]:[r,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function uo(t,n){let r=e.useRef([]),o=Pn(t);e.useEffect((()=>{let e=[...r.current];for(let[t,i]of n.entries())if(r.current[t]!==i){let t=o(n,e);return r.current=n,t}}),[o,...n])}let co=e.createContext(null);co.displayName="OpenClosedContext";var fo=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(fo||{});function po(){return e.useContext(co)}function mo({value:e,children:t}){return o.createElement(co.Provider,{value:e},t)}function ho({children:e}){return o.createElement(co.Provider,{value:null},e)}let vo=[];function go(t){let n=Pn(t),r=e.useRef(!1);e.useEffect((()=>(r.current=!1,()=>{r.current=!0,yn((()=>{r.current&&n()}))})),[n])}function yo(){let t=function(){let e="undefined"==typeof document;return"useSyncExternalStore"in r&&r.useSyncExternalStore((()=>()=>{}),(()=>!1),(()=>!e))}(),[n,o]=e.useState(vn.isHandoffComplete);return n&&!1===vn.isHandoffComplete&&o(!1),e.useEffect((()=>{!0!==n&&o(!0)}),[n]),e.useEffect((()=>vn.handoff()),[]),!t&&n}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}((()=>{function e(e){if(!Un(e.target)||e.target===document.body||vo[0]===e.target)return;let t=e.target;t=t.closest(Nr),vo.unshift(null!=t?t:e.target),vo=vo.filter((e=>null!=e&&e.isConnected)),vo.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})}));let wo=e.createContext(!1);function bo(e){return o.createElement(wo.Provider,{value:e.force},e.children)}function xo(t){let n=e.useContext(wo),r=e.useContext(So),[o,i]=e.useState((()=>{var e;if(!n&&null!==r)return null!=(e=r.current)?e:null;if(vn.isServer)return null;let o=null==t?void 0:t.getElementById("headlessui-portal-root");if(o)return o;if(null===t)return null;let i=t.createElement("div");return i.setAttribute("id","headlessui-portal-root"),t.body.appendChild(i)}));return e.useEffect((()=>{null!==o&&(null!=t&&t.body.contains(o)||null==t||t.body.appendChild(o))}),[o,t]),e.useEffect((()=>{n||null!==r&&i(r.current)}),[r,i,n]),o}let Eo=e.Fragment,Po=jn((function(n,r){let{ownerDocument:o=null,...i}=n,l=e.useRef(null),a=Yn(function(e,t=!0){return Object.assign(e,{[zn]:t})}((e=>{l.current=e})),r),s=Zr(l),u=null!=o?o:s,c=xo(u),[d]=e.useState((()=>{var e;return vn.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null})),f=e.useContext(To),p=yo();xn((()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))}),[c,d]),xn((()=>{if(d&&f)return f.register(d)}),[f,d]),go((()=>{var e;!c||!d||($n(d)&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))}));let m=Fn();return p&&c&&d?t.createPortal(m({ourProps:{ref:a},theirProps:i,slot:{},defaultTag:Eo,name:"Portal"}),d):null}));let Co=e.Fragment,So=e.createContext(null);let To=e.createContext(null);let Ro=jn((function(e,t){let n=Yn(t),{enabled:r=!0,ownerDocument:i,...l}=e,a=Fn();return r?o.createElement(Po,{...l,ownerDocument:i,ref:n}):a({ourProps:{ref:n},theirProps:l,slot:{},defaultTag:Eo,name:"Portal"})})),Oo=jn((function(e,t){let{target:n,...r}=e,i={ref:Yn(t)},l=Fn();return o.createElement(So.Provider,{value:n},l({ourProps:i,theirProps:r,defaultTag:Co,name:"Popover.Group"}))})),Ao=Object.assign(Ro,{Group:Oo});let ko=e.createContext(null);function Fo({children:t,node:n}){let[r,i]=e.useState(null),l=Lo(null!=n?n:r);return o.createElement(ko.Provider,{value:l},t,null===l&&o.createElement(Hn,{features:Wn.Hidden,ref:e=>{var t,n;if(e)for(let r of null!=(n=null==(t=gn(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&Bn(r)&&null!=r&&r.contains(e)){i(r);break}}}))}function Lo(t=null){var n;return null!=(n=e.useContext(ko))?n:t}function Do(){let t=e.useRef(!1);return xn((()=>(t.current=!0,()=>{t.current=!1})),[]),t}var No=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(No||{});function jo(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)Bn(n.current)&&t.add(n.current);return t}var Mo=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(Mo||{});let Io=jn((function(t,n){let r=e.useRef(null),i=Yn(r,n),{initialFocus:l,initialFocusFallback:a,containers:s,features:u=15,...c}=t;yo()||(u=0);let d=Zr(r);!function(t,{ownerDocument:n}){let r=!!(8&t),o=function(t=!0){let n=e.useRef(vo.slice());return uo((([e],[t])=>{!0===t&&!1===e&&yn((()=>{n.current.splice(0)})),!1===t&&!0===e&&(n.current=vo.slice())}),[t,vo,n]),Pn((()=>{var e;return null!=(e=n.current.find((e=>null!=e&&e.isConnected)))?e:null}))}(r);uo((()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&Vr(o())}),[r]),go((()=>{r&&Vr(o())}))}(u,{ownerDocument:d});let f=function(t,{ownerDocument:n,container:r,initialFocus:o,initialFocusFallback:i}){let l=e.useRef(null),a=Ar(!!(1&t),"focus-trap#initial-focus"),s=Do();return uo((()=>{if(0===t)return;if(!a)return void(null!=i&&i.current&&Vr(i.current));let e=r.current;e&&yn((()=>{if(!s.current)return;let r=null==n?void 0:n.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===r)return void(l.current=r)}else if(e.contains(r))return void(l.current=r);if(null!=o&&o.current)Vr(o.current);else{if(16&t){if(zr(e,_r.First|_r.AutoFocus)!==Wr.Error)return}else if(zr(e,_r.First)!==Wr.Error)return;if(null!=i&&i.current&&(Vr(i.current),(null==n?void 0:n.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}l.current=null==n?void 0:n.activeElement}))}),[i,a,t]),l}(u,{ownerDocument:d,container:r,initialFocus:l,initialFocusFallback:a});!function(e,{ownerDocument:t,container:n,containers:r,previousActiveElement:o}){let i=Do(),l=!!(4&e);Jr(null==t?void 0:t.defaultView,"focus",(e=>{if(!l||!i.current)return;let t=jo(r);Vn(n.current)&&t.add(n.current);let a=o.current;if(!a)return;let s=e.target;Vn(s)?Wo(t,s)?(o.current=s,Vr(s)):(e.preventDefault(),e.stopPropagation(),Vr(a)):Vr(o.current)}),!0)}(u,{ownerDocument:d,container:r,containers:s,previousActiveElement:f});let p=function(){let t=e.useRef(0);return Kr(!0,"keydown",(e=>{"Tab"===e.key&&(t.current=e.shiftKey?1:0)}),!0),t}(),m=Pn((e=>{if(!Vn(r.current))return;let t=r.current;Tn(p.current,{[No.Forwards]:()=>{zr(t,_r.First,{skipElements:[e.relatedTarget,a]})},[No.Backwards]:()=>{zr(t,_r.Last,{skipElements:[e.relatedTarget,a]})}})})),h=Ar(!!(2&u),"focus-trap#tab-lock"),v=bn(),g=e.useRef(!1),y={ref:i,onKeyDown(e){"Tab"==e.key&&(g.current=!0,v.requestAnimationFrame((()=>{g.current=!1})))},onBlur(e){if(!(4&u))return;let t=jo(s);Vn(r.current)&&t.add(r.current);let n=e.relatedTarget;Un(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(Wo(t,n)||(g.current?zr(r.current,Tn(p.current,{[No.Forwards]:()=>_r.Next,[No.Backwards]:()=>_r.Previous})|_r.WrapAround,{relativeTo:e.target}):Un(e.target)&&Vr(e.target)))}},w=Fn();return o.createElement(o.Fragment,null,h&&o.createElement(Hn,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:Wn.Focusable}),w({ourProps:y,theirProps:c,defaultTag:"div",name:"FocusTrap"}),h&&o.createElement(Hn,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:Wn.Focusable}))})),_o=Object.assign(Io,{features:Mo});function Wo(e,t){for(let n of e)if(n.contains(t))return!0;return!1}function Ho(t){var n;return!!(t.enter||t.enterFrom||t.enterTo||t.leave||t.leaveFrom||t.leaveTo)||(null!=(n=t.as)?n:Yo)!==e.Fragment||1===o.Children.count(t.children)}let $o=e.createContext(null);$o.displayName="TransitionContext";var Bo=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Bo||{});let Vo=e.createContext(null);function Uo(e){return"children"in e?Uo(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function zo(t,n){let r=En(t),o=e.useRef([]),i=Do(),l=bn(),a=Pn(((e,t=kn.Hidden)=>{let n=o.current.findIndex((({el:t})=>t===e));-1!==n&&(Tn(t,{[kn.Unmount](){o.current.splice(n,1)},[kn.Hidden](){o.current[n].state="hidden"}}),l.microTask((()=>{var e;!Uo(o)&&i.current&&(null==(e=r.current)||e.call(r))})))})),s=Pn((e=>{let t=o.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):o.current.push({el:e,state:"visible"}),()=>a(e,kn.Unmount)})),u=e.useRef([]),c=e.useRef(Promise.resolve()),d=e.useRef({enter:[],leave:[]}),f=Pn(((e,t,r)=>{u.current.splice(0),n&&(n.chains.current[t]=n.chains.current[t].filter((([t])=>t!==e))),null==n||n.chains.current[t].push([e,new Promise((e=>{u.current.push(e)}))]),null==n||n.chains.current[t].push([e,new Promise((e=>{Promise.all(d.current[t].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===t?c.current=c.current.then((()=>null==n?void 0:n.wait.current)).then((()=>r(t))):r(t)})),p=Pn(((e,t,n)=>{Promise.all(d.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=u.current.shift())||e()})).then((()=>n(t)))}));return e.useMemo((()=>({children:o,register:s,unregister:a,onStart:f,onStop:p,wait:c,chains:d})),[s,a,o,f,p,d,c])}Vo.displayName="NestingContext";let Yo=e.Fragment,qo=An.RenderStrategy;let Xo=jn((function(t,n){let{show:r,appear:i=!1,unmount:l=!0,...a}=t,s=e.useRef(null),u=Yn(...Ho(t)?[s,n]:null===n?[]:[n]);yo();let c=po();if(void 0===r&&null!==c&&(r=(c&fo.Open)===fo.Open),void 0===r)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,f]=e.useState(r?"visible":"hidden"),p=zo((()=>{r||f("hidden")})),[m,h]=e.useState(!0),v=e.useRef([r]);xn((()=>{!1!==m&&v.current[v.current.length-1]!==r&&(v.current.push(r),h(!1))}),[v,r]);let g=e.useMemo((()=>({show:r,appear:i,initial:m})),[r,i,m]);xn((()=>{r?f("visible"):!Uo(p)&&null!==s.current&&f("hidden")}),[r,p]);let y={unmount:l},w=Pn((()=>{var e;m&&h(!1),null==(e=t.beforeEnter)||e.call(t)})),b=Pn((()=>{var e;m&&h(!1),null==(e=t.beforeLeave)||e.call(t)})),x=Fn();return o.createElement(Vo.Provider,{value:p},o.createElement($o.Provider,{value:g},x({ourProps:{...y,as:e.Fragment,children:o.createElement(Ko,{ref:u,...y,...a,beforeEnter:w,beforeLeave:b})},theirProps:{},defaultTag:e.Fragment,features:qo,visible:"visible"===d,name:"Transition"})))})),Ko=jn((function(t,n){var r,i;let{transition:l=!0,beforeEnter:a,afterEnter:s,beforeLeave:u,afterLeave:c,enter:d,enterFrom:f,enterTo:p,entered:m,leave:h,leaveFrom:v,leaveTo:g,...y}=t,[w,b]=e.useState(null),x=e.useRef(null),E=Ho(t),P=Yn(...E?[x,n,b]:null===n?[]:[n]),C=null==(r=y.unmount)||r?kn.Unmount:kn.Hidden,{show:S,appear:T,initial:R}=function(){let t=e.useContext($o);if(null===t)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}(),[O,A]=e.useState(S?"visible":"hidden"),k=function(){let t=e.useContext(Vo);if(null===t)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}(),{register:F,unregister:L}=k;xn((()=>F(x)),[F,x]),xn((()=>{if(C===kn.Hidden&&x.current)return S&&"visible"!==O?void A("visible"):Tn(O,{hidden:()=>L(x),visible:()=>F(x)})}),[O,x,F,L,S,C]);let D=yo();xn((()=>{if(E&&D&&"visible"===O&&null===x.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[x,O,D,E]);let N=R&&!T,j=T&&S&&R,M=e.useRef(!1),I=zo((()=>{M.current||(A("hidden"),L(x))}),k),_=Pn((e=>{M.current=!0;let t=e?"enter":"leave";I.onStart(x,t,(e=>{"enter"===e?null==a||a():"leave"===e&&(null==u||u())}))})),W=Pn((e=>{let t=e?"enter":"leave";M.current=!1,I.onStop(x,t,(e=>{"enter"===e?null==s||s():"leave"===e&&(null==c||c())})),"leave"===t&&!Uo(I)&&(A("hidden"),L(x))}));e.useEffect((()=>{E&&l||(_(S),W(S))}),[S,E,l]);let H=(()=>!(!l||!E||!D||N))(),[,$]=so(H,w,S,{start:_,end:W}),B=Mn({ref:P,className:(null==(i=Sn(y.className,j&&d,j&&f,$.enter&&d,$.enter&&$.closed&&f,$.enter&&!$.closed&&p,$.leave&&h,$.leave&&!$.closed&&v,$.leave&&$.closed&&g,!$.transition&&S&&m))?void 0:i.trim())||void 0,...ao($)}),V=0;"visible"===O&&(V|=fo.Open),"hidden"===O&&(V|=fo.Closed),S&&"hidden"===O&&(V|=fo.Opening),!S&&"visible"===O&&(V|=fo.Closing);let U=Fn();return o.createElement(Vo.Provider,{value:I},o.createElement(mo,{value:V},U({ourProps:B,theirProps:y,defaultTag:Yo,features:qo,visible:"visible"===O,name:"Transition.Child"})))})),Go=jn((function(t,n){let r=null!==e.useContext($o),i=null!==po();return o.createElement(o.Fragment,null,!r&&i?o.createElement(Xo,{ref:n,...t}):o.createElement(Ko,{ref:n,...t}))})),Zo=Object.assign(Xo,{Child:Go,Root:Xo});var Jo=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Jo||{}),Qo=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Qo||{});let ei={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},ti=e.createContext(null);function ni(t){let n=e.useContext(ti);if(null===n){let e=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,ni),e}return n}function ri(e,t){return Tn(t.type,ei,e,t)}ti.displayName="DialogContext";let oi=jn((function(t,n){let r=e.useId(),{id:i=`headlessui-dialog-${r}`,open:l,onClose:a,initialFocus:s,role:u="dialog",autoFocus:c=!0,__demoMode:d=!1,unmount:f=!1,...p}=t,m=e.useRef(!1);u="dialog"===u||"alertdialog"===u?u:(m.current||(m.current=!0,console.warn(`Invalid role [${u}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let h=po();void 0===l&&null!==h&&(l=(h&fo.Open)===fo.Open);let v=e.useRef(null),g=Yn(v,n),y=Zr(v),w=l?0:1,[b,x]=e.useReducer(ri,{titleId:null,descriptionId:null,panelRef:e.createRef()}),E=Pn((()=>a(!1))),P=Pn((e=>x({type:0,id:e}))),C=!!yo()&&0===w,[S,T]=function(){let t=e.useContext(To),n=e.useRef([]),r=Pn((e=>(n.current.push(e),t&&t.register(e),()=>i(e)))),i=Pn((e=>{let r=n.current.indexOf(e);-1!==r&&n.current.splice(r,1),t&&t.unregister(e)})),l=e.useMemo((()=>({register:r,unregister:i,portals:n})),[r,i,n]);return[n,e.useMemo((()=>function({children:e}){return o.createElement(To.Provider,{value:l},e)}),[l])]}(),R={get current(){var e;return null!=(e=b.panelRef.current)?e:v.current}},O=Lo(),{resolveContainers:A}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=Zr(n),o=Pn((()=>{var o,i;let l=[];for(let t of e)null!==t&&(Bn(t)?l.push(t):"current"in t&&Bn(t.current)&&l.push(t.current));if(null!=t&&t.current)for(let e of t.current)l.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&Bn(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(i=null==n?void 0:n.getRootNode())?void 0:i.host))||l.some((t=>e.contains(t)))||l.push(e));return l}));return{resolveContainers:o,contains:Pn((e=>o().some((t=>t.contains(e)))))}}({mainTreeNode:O,portals:S,defaultContainers:[R]}),k=null!==h&&(h&fo.Closing)===fo.Closing;!function(e,{allowed:t,disallowed:n}={}){let r=Ar(e,"inert-others");xn((()=>{var e,o;if(!r)return;let i=wn();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&i.add(Lr(t));let l=null!=(o=null==t?void 0:t())?o:[];for(let t of l){if(!t)continue;let e=gn(t);if(!e)continue;let n=t.parentElement;for(;n&&n!==e.body;){for(let e of n.children)l.some((t=>e.contains(t)))||i.add(Lr(e));n=n.parentElement}}return i.dispose}),[r,t,n])}(!d&&!k&&C,{allowed:Pn((()=>{var e,t;return[null!=(t=null==(e=v.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]})),disallowed:Pn((()=>{var e;return[null!=(e=null==O?void 0:O.closest("body > *:not(#headlessui-portal-root)"))?e:null]}))});let F=xr.get(null);xn((()=>{if(C)return F.actions.push(i),()=>F.actions.pop(i)}),[F,i,C]);let L=Rr(F,e.useCallback((e=>F.selectors.isTop(e,i)),[F,i]));Gr(L,A,(e=>{e.preventDefault(),E()})),function(e,t=("undefined"!=typeof document?document.defaultView:null),n){let r=Ar(e,"escape");Jr(t,"keydown",(e=>{r&&(e.defaultPrevented||e.key===Jn.Escape&&n(e))}))}(L,null==y?void 0:y.defaultView,(e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),E()})),function(e,t,n=()=>[document.body]){ro(Ar(e,"scroll-lock"),t,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}}))}(!d&&!k&&C,y,A),function(t,n,r){let o=En((e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&r()}));e.useEffect((()=>{if(!t)return;let e=null===n?null:Vn(n)?n:n.current;if(!e)return;let r=wn();if("undefined"!=typeof ResizeObserver){let t=new ResizeObserver((()=>o.current(e)));t.observe(e),r.add((()=>t.disconnect()))}if("undefined"!=typeof IntersectionObserver){let t=new IntersectionObserver((()=>o.current(e)));t.observe(e),r.add((()=>t.disconnect()))}return()=>r.dispose()}),[n,o,t])}(C,v,E);let[D,N]=function(){let[t,n]=e.useState([]);return[t.length>0?t.join(" "):void 0,e.useMemo((()=>function(t){let r=Pn((e=>(n((t=>[...t,e])),()=>n((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),i=e.useMemo((()=>({register:r,slot:t.slot,name:t.name,props:t.props,value:t.value})),[r,t.slot,t.name,t.props,t.value]);return o.createElement(qn.Provider,{value:i},t.children)}),[n])]}(),j=e.useMemo((()=>[{dialogState:w,close:E,setTitleId:P,unmount:f},b]),[w,b,E,P,f]),M=e.useMemo((()=>({open:0===w})),[w]),I={ref:g,id:i,role:u,tabIndex:-1,"aria-modal":d?void 0:0===w||void 0,"aria-labelledby":b.titleId,"aria-describedby":D,unmount:f},_=!function(){var t;let[n]=e.useState((()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null)),[r,o]=e.useState(null!=(t=null==n?void 0:n.matches)&&t);return xn((()=>{if(n)return n.addEventListener("change",e),()=>n.removeEventListener("change",e);function e(e){o(e.matches)}}),[n]),r}(),W=Mo.None;C&&!d&&(W|=Mo.RestoreFocus,W|=Mo.TabLock,c&&(W|=Mo.AutoFocus),_&&(W|=Mo.InitialFocus));let H=Fn();return o.createElement(ho,null,o.createElement(bo,{force:!0},o.createElement(Ao,null,o.createElement(ti.Provider,{value:j},o.createElement(Oo,{target:v},o.createElement(bo,{force:!1},o.createElement(N,{slot:M},o.createElement(T,null,o.createElement(_o,{initialFocus:s,initialFocusFallback:v,containers:A,features:W},o.createElement(er,{value:E},H({ourProps:I,theirProps:p,slot:M,defaultTag:ii,features:li,visible:0===w,name:"Dialog"})))))))))))})),ii="div",li=An.RenderStrategy|An.Static;let ai=jn((function(e,t){let{transition:n=!1,open:r,...i}=e,l=po(),a=e.hasOwnProperty("open")||null!==l,s=e.hasOwnProperty("onClose");if(!a&&!s)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!s)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!l&&"boolean"!=typeof e.open)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return void 0===r&&!n||i.static?o.createElement(Fo,null,o.createElement(oi,{ref:t,open:r,...i})):o.createElement(Fo,null,o.createElement(Zo,{show:r,transition:n,unmount:i.unmount},o.createElement(oi,{ref:t,...i})))})),si=jn((function(t,n){let r=e.useId(),{id:i=`headlessui-dialog-panel-${r}`,transition:l=!1,...a}=t,[{dialogState:s,unmount:u},c]=ni("Dialog.Panel"),d=Yn(n,c.panelRef),f=e.useMemo((()=>({open:0===s})),[s]),p={ref:d,id:i,onClick:Pn((e=>{e.stopPropagation()}))},m=l?Go:e.Fragment,h=l?{unmount:u}:{},v=Fn();return o.createElement(m,{...h},v({ourProps:p,theirProps:a,slot:f,defaultTag:"div",name:"Dialog.Panel"}))}));jn((function(t,n){let{transition:r=!1,...i}=t,[{dialogState:l,unmount:a}]=ni("Dialog.Backdrop"),s=e.useMemo((()=>({open:0===l})),[l]),u={ref:n,"aria-hidden":!0},c=r?Go:e.Fragment,d=r?{unmount:a}:{},f=Fn();return o.createElement(c,{...d},f({ourProps:u,theirProps:i,slot:s,defaultTag:"div",name:"Dialog.Backdrop"}))}));let ui=jn((function(t,n){let r=e.useId(),{id:o=`headlessui-dialog-title-${r}`,...i}=t,[{dialogState:l,setTitleId:a}]=ni("Dialog.Title"),s=Yn(n);e.useEffect((()=>(a(o),()=>a(null))),[o,a]);let u=e.useMemo((()=>({open:0===l})),[l]),c={ref:s,id:o};return Fn()({ourProps:c,theirProps:i,slot:u,defaultTag:"h2",name:"Dialog.Title"})})),ci=Object.assign(ai,{Panel:si,Title:ui,Description:Gn});export{L as B,pn as C,ci as L,w as P,F as R,m as S,H as V,p as a,_ as b,d as c,j as d,a as e,x as f,N as g,D as h,b as i,V as j,fn as k,Y as l,c as u};
//# sourceMappingURL=chunk-DU4M193A.js.map

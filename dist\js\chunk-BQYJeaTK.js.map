{"version": 3, "file": "chunk-BQYJeaTK.js", "sources": ["../../src/utils/translationHelpers.ts"], "sourcesContent": ["// Utilitários para lidar com traduções de forma segura\r\nimport { logger } from './logger';\r\n\r\n/**\r\n * Garante que o resultado de uma tradução seja um array\r\n * Útil para quando usamos returnObjects: true e esperamos um array\r\n */\r\nexport const ensureArray = <T = any>(value: any): T[] => {\r\n  if (Array.isArray(value)) {\r\n    return value;\r\n  }\r\n\r\n  if (value === null || value === undefined || value === '') {\r\n    return [];\r\n  }\r\n\r\n  // Se for uma string ou objeto, coloca em um array\r\n  return [value];\r\n};\r\n\r\n/**\r\n * Garante que o resultado de uma tradução seja um array de strings\r\n * Remove valores falsy (null, undefined, '')\r\n */\r\nexport const ensureStringArray = (value: any): string[] => {\r\n  const array = ensureArray<string>(value);\r\n  return array.filter(item => item && typeof item === 'string');\r\n};\r\n\r\n/**\r\n * Hook personalizado para traduções que retornam arrays\r\n * Garante que sempre temos um array válido para usar com .map()\r\n */\r\nexport const useTranslationArray = (key: string, t: (key: string, options?: any) => any): any[] => {\r\n  try {\r\n    const result = t(key, { returnObjects: true });\r\n\r\n    // Debug temporário para verificar o que está sendo retornado\r\n    if (import.meta.env.DEV && key === 'backlog.items') {\r\n      console.log('🔍 Debug backlog.items:', {\r\n        key,\r\n        result,\r\n        type: typeof result,\r\n        isArray: Array.isArray(result),\r\n        length: Array.isArray(result) ? result.length : 'N/A'\r\n      });\r\n    }\r\n\r\n    return ensureArray(result);\r\n  } catch (error) {\r\n    logger.warn(`Translation error for key \"${key}\":`, error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Função para obter traduções de objetos de forma segura\r\n * Retorna um objeto vazio se a tradução falhar\r\n */\r\nexport const getTranslationObject = (key: string, t: (key: string, options?: any) => any): Record<string, any> => {\r\n  try {\r\n    const result = t(key, { returnObjects: true });\r\n    return typeof result === 'object' && result !== null ? result : {};\r\n  } catch (error) {\r\n    logger.warn(`Translation error for key \"${key}\":`, error);\r\n    return {};\r\n  }\r\n};\r\n\r\n/**\r\n * Função para obter uma tradução específica de um array\r\n * Com fallback para evitar erros\r\n */\r\nexport const getArrayTranslation = (\r\n  key: string,\r\n  index: number,\r\n  t: (key: string, options?: any) => any,\r\n  fallback: string = ''\r\n): string => {\r\n  try {\r\n    const array = ensureStringArray(t(key, { returnObjects: true }));\r\n    return array[index] || fallback;\r\n  } catch (error) {\r\n    logger.warn(`Translation error for key \"${key}[${index}]\":`, error);\r\n    return fallback;\r\n  }\r\n};\r\n\r\n/**\r\n * Função para verificar se uma tradução existe e é válida\r\n */\r\nexport const hasValidTranslation = (key: string, t: (key: string, options?: any) => any): boolean => {\r\n  try {\r\n    const result = t(key);\r\n    return result && result !== key; // i18next retorna a key se não encontrar tradução\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Função para obter traduções com fallback\r\n */\r\nexport const getTranslationWithFallback = (\r\n  key: string,\r\n  fallback: string,\r\n  t: (key: string, options?: any) => any\r\n): string => {\r\n  try {\r\n    const result = t(key);\r\n    return (result && result !== key) ? result : fallback;\r\n  } catch (error) {\r\n    console.warn(`Translation error for key \"${key}\":`, error);\r\n    return fallback;\r\n  }\r\n};\r\n"], "names": ["ensureArray", "value", "Array", "isArray", "ensureStringArray", "filter", "item", "useTranslationArray", "key", "t", "result", "returnObjects", "error"], "mappings": "AAOaA,MAAAA,EAAwBC,GAC/BC,MAAMC,QAAQF,GACTA,EAGLA,SAAmD,KAAVA,EACpC,GAIF,CAACA,GAOGG,EAAqBH,GAClBD,EAAoBC,GACrBI,QAAOC,GAAQA,GAAwB,iBAATA,IAOhCC,EAAsB,CAACC,EAAaC,KAC3C,IACIC,MAAAA,EAASD,EAAED,EAAK,CAAEG,eAAe,IAavC,OAAOX,EAAYU,SACZE,GAEP,MAAO,EAAE"}
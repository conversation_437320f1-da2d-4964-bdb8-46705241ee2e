{"version": 3, "file": "FluidGradientBackground-fz9tBY22.js", "sources": ["../../src/hooks/useFluidGradient.ts", "../../src/components/FluidGradientBackground.tsx"], "sourcesContent": ["import { useEffect, useState, useCallback } from 'react';\n\ninterface SectionConfig {\n  id: string;\n  className: string;\n  threshold: number; // Porcentagem da tela para ativar\n}\n\nconst SECTIONS: SectionConfig[] = [\n  { id: 'perfil', className: 'section-profile', threshold: 0.3 },\n  { id: 'projetos', className: 'section-projects', threshold: 0.3 },\n  { id: 'backlog', className: 'section-backlog', threshold: 0.3 },\n  { id: 'contato', className: 'section-contact', threshold: 0.3 },\n];\n\nexport const useFluidGradient = () => {\n  const [currentSection, setCurrentSection] = useState<string>('section-profile');\n  const [isScrolling, setIsScrolling] = useState(false);\n\n  // Função para detectar qual seção está ativa\n  const detectActiveSection = useCallback(() => {\n    const scrollY = window.scrollY;\n    const windowHeight = window.innerHeight;\n    \n    // Se estiver no topo da página, sempre mostrar perfil\n    if (scrollY < windowHeight * 0.2) {\n      return 'section-profile';\n    }\n\n    // Verificar cada seção\n    for (const section of SECTIONS) {\n      const element = document.getElementById(section.id);\n      if (!element) continue;\n\n      const rect = element.getBoundingClientRect();\n      const elementTop = rect.top + scrollY;\n      const elementHeight = rect.height;\n      const elementCenter = elementTop + (elementHeight / 2);\n\n      // Se o centro da seção está visível na tela\n      const viewportCenter = scrollY + (windowHeight / 2);\n      const distanceFromCenter = Math.abs(elementCenter - viewportCenter);\n      \n      // Se está próximo do centro da viewport\n      if (distanceFromCenter < elementHeight * 0.6) {\n        return section.className;\n      }\n    }\n\n    return currentSection; // Manter seção atual se nenhuma for detectada\n  }, [currentSection]);\n\n  // Função otimizada de scroll com throttling\n  const handleScroll = useCallback(() => {\n    if (!isScrolling) {\n      setIsScrolling(true);\n      \n      requestAnimationFrame(() => {\n        const newSection = detectActiveSection();\n        if (newSection !== currentSection) {\n          setCurrentSection(newSection);\n        }\n        setIsScrolling(false);\n      });\n    }\n  }, [currentSection, detectActiveSection, isScrolling]);\n\n  // Aplicar classe ao container de gradiente\n  const applyGradientClass = useCallback((sectionClass: string) => {\n    const container = document.querySelector('.fluid-gradient-container');\n    if (!container) return;\n\n    // Remover todas as classes de seção\n    SECTIONS.forEach(section => {\n      container.classList.remove(section.className);\n    });\n\n    // Adicionar nova classe com pequeno delay para transição suave\n    setTimeout(() => {\n      container.classList.add(sectionClass);\n    }, 50);\n  }, []);\n\n  // Effect para scroll listener\n  useEffect(() => {\n    // Configurar scroll listener otimizado\n    const scrollOptions = {\n      passive: true,\n      capture: false\n    };\n\n    window.addEventListener('scroll', handleScroll, scrollOptions);\n    \n    // Detectar seção inicial\n    const initialSection = detectActiveSection();\n    setCurrentSection(initialSection);\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll, scrollOptions);\n    };\n  }, [handleScroll, detectActiveSection]);\n\n  // Effect para aplicar classes CSS\n  useEffect(() => {\n    applyGradientClass(currentSection);\n  }, [currentSection, applyGradientClass]);\n\n  // Função para forçar mudança de seção (útil para navegação)\n  const setSection = useCallback((sectionId: string) => {\n    const sectionConfig = SECTIONS.find(s => s.id === sectionId);\n    if (sectionConfig) {\n      setCurrentSection(sectionConfig.className);\n    }\n  }, []);\n\n  // Função para obter informações da seção atual\n  const getCurrentSectionInfo = useCallback(() => {\n    const config = SECTIONS.find(s => s.className === currentSection);\n    return {\n      id: config?.id || 'perfil',\n      className: currentSection,\n      displayName: getSectionDisplayName(config?.id || 'perfil')\n    };\n  }, [currentSection]);\n\n  return {\n    currentSection,\n    setSection,\n    getCurrentSectionInfo,\n    isScrolling\n  };\n};\n\n// Função auxiliar para nomes de exibição\nconst getSectionDisplayName = (sectionId: string): string => {\n  const names: Record<string, string> = {\n    'perfil': 'Perfil',\n    'projetos': 'Projetos',\n    'backlog': 'Backlog Estratégico',\n    'contato': 'Contato'\n  };\n  return names[sectionId] || sectionId;\n};\n\n// Hook para criar o container de gradiente\nexport const useGradientContainer = () => {\n  useEffect(() => {\n    // Verificar se já existe\n    if (document.querySelector('.fluid-gradient-container')) {\n      return;\n    }\n\n    // Criar container de gradiente\n    const gradientContainer = document.createElement('div');\n    gradientContainer.className = 'fluid-gradient-container section-profile';\n    \n    // Inserir no início do body\n    document.body.insertBefore(gradientContainer, document.body.firstChild);\n\n    // Cleanup\n    return () => {\n      const container = document.querySelector('.fluid-gradient-container');\n      if (container) {\n        container.remove();\n      }\n    };\n  }, []);\n};\n\n// Hook combinado para facilitar uso\nexport const useFluidGradientSystem = () => {\n  useGradientContainer();\n  const gradientControls = useFluidGradient();\n  \n  return gradientControls;\n};\n\n// Função utilitária para transições manuais (navegação)\nexport const triggerSectionTransition = (sectionId: string) => {\n  const container = document.querySelector('.fluid-gradient-container');\n  if (!container) return;\n\n  const sectionConfig = SECTIONS.find(s => s.id === sectionId);\n  if (!sectionConfig) return;\n\n  // Remover todas as classes\n  SECTIONS.forEach(section => {\n    container.classList.remove(section.className);\n  });\n\n  // Adicionar nova classe\n  container.classList.add(sectionConfig.className);\n};\n\nexport default useFluidGradient;\n", "import React, { useEffect } from 'react';\nimport { useFluidGradientSystem } from '@/hooks/useFluidGradient';\n\ninterface FluidGradientBackgroundProps {\n  children?: React.ReactNode;\n  className?: string;\n}\n\n/**\n * Componente que gerencia o sistema de gradientes fluidos\n * Cria automaticamente o fundo gradiente e controla as transições baseadas no scroll\n */\nconst FluidGradientBackground: React.FC<FluidGradientBackgroundProps> = ({ \n  children, \n  className = '' \n}) => {\n  // Inicializar sistema de gradientes\n  const { currentSection, getCurrentSectionInfo } = useFluidGradientSystem();\n\n  // Debug em desenvolvimento\n  useEffect(() => {\n    if (import.meta.env.DEV) {\n      const sectionInfo = getCurrentSectionInfo();\n      console.log('🎨 Fluid Gradient:', {\n        section: sectionInfo.displayName,\n        className: currentSection\n      });\n    }\n  }, [currentSection, getCurrentSectionInfo]);\n\n  return (\n    <>\n      {/* O hook já cria o container de gradiente automaticamente */}\n      {children && (\n        <div className={`relative z-10 ${className}`}>\n          {children}\n        </div>\n      )}\n    </>\n  );\n};\n\n/**\n * Componente indicador visual da seção atual (opcional)\n * Útil para debug ou como indicador de navegação\n */\nexport const GradientSectionIndicator: React.FC = () => {\n  const { getCurrentSectionInfo } = useFluidGradientSystem();\n  const sectionInfo = getCurrentSectionInfo();\n\n  if (import.meta.env.PROD) {\n    return null; // Não mostrar em produção\n  }\n\n  return (\n    <div className=\"fixed top-20 right-4 z-50 bg-black/80 text-white px-3 py-2 rounded-lg text-sm font-mono\">\n      <div className=\"text-xs opacity-70\">Seção Atual:</div>\n      <div className=\"font-semibold\">{sectionInfo.displayName}</div>\n      <div className=\"text-xs opacity-50\">{sectionInfo.className}</div>\n    </div>\n  );\n};\n\n/**\n * Hook para componentes que precisam reagir às mudanças de seção\n */\nexport const useCurrentSection = () => {\n  const { getCurrentSectionInfo } = useFluidGradientSystem();\n  return getCurrentSectionInfo();\n};\n\n/**\n * Componente wrapper para seções que precisam de gradientes específicos\n */\ninterface GradientSectionProps {\n  sectionId: 'perfil' | 'projetos' | 'backlog' | 'contato';\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const GradientSection: React.FC<GradientSectionProps> = ({\n  sectionId,\n  children,\n  className = ''\n}) => {\n  return (\n    <section \n      id={sectionId}\n      className={`relative ${className}`}\n      data-gradient-section={sectionId}\n    >\n      {children}\n    </section>\n  );\n};\n\n/**\n * Componente para transições manuais de gradiente\n * Útil para navegação ou botões que mudam seção\n */\ninterface GradientTriggerProps {\n  targetSection: 'perfil' | 'projetos' | 'backlog' | 'contato';\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n}\n\nexport const GradientTrigger: React.FC<GradientTriggerProps> = ({\n  targetSection,\n  children,\n  className = '',\n  onClick\n}) => {\n  const { setSection } = useFluidGradientSystem();\n\n  const handleClick = () => {\n    setSection(targetSection);\n    onClick?.();\n  };\n\n  return (\n    <div \n      className={`cursor-pointer ${className}`}\n      onClick={handleClick}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default FluidGradientBackground;\n"], "names": ["SECTIONS", "id", "className", "threshold", "getSectionDisplayName", "sectionId", "perfil", "projetos", "backlog", "contato", "useFluidGradientSystem", "useEffect", "document", "querySelector", "gradientContainer", "createElement", "body", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "container", "remove", "gradientControls", "currentSection", "setCurrentSection", "useState", "isScrolling", "setIsScrolling", "detectActiveSection", "useCallback", "scrollY", "window", "windowHeight", "innerHeight", "section", "element", "getElementById", "rect", "getBoundingClientRect", "elementTop", "top", "elementHeight", "height", "elementCenter", "viewportCenter", "distanceFromCenter", "Math", "abs", "handleScroll", "requestAnimationFrame", "newSection", "applyGradientClass", "sectionClass", "for<PERSON>ach", "classList", "setTimeout", "add", "scrollOptions", "passive", "capture", "addEventListener", "initialSection", "removeEventListener", "setSection", "sectionConfig", "find", "s", "getCurrentSectionInfo", "config", "displayName", "useFluidGradient", "FluidGradientBackground", "children", "_Fragment", "jsxRuntimeExports", "jsx", "div", "GradientSectionIndicator", "useCurrentSection", "GradientSection", "data-gradient-section", "GradientTrigger", "targetSection", "onClick"], "mappings": "4GAQA,MAAMA,EAA4B,CAChC,CAAEC,GAAI,SAAUC,UAAW,kBAAmBC,UAAW,IACzD,CAAEF,GAAI,WAAYC,UAAW,mBAAoBC,UAAW,IAC5D,CAAEF,GAAI,UAAWC,UAAW,kBAAmBC,UAAW,IAC1D,CAAEF,GAAI,UAAWC,UAAW,kBAAmBC,UAAW,KA0HtDC,EAAyBC,IACS,CACpCC,OAAU,SACVC,SAAY,WACZC,QAAW,sBACXC,QAAW,WAEAJ,IAAcA,GA6BhBK,EAAyB,KAxBpCC,EAAAA,WAAU,KAEJC,GAAAA,SAASC,cAAc,6BACzB,OAIIC,MAAAA,EAAoBF,SAASG,cAAc,OAOjD,OANAD,EAAkBZ,UAAY,2CAG9BU,SAASI,KAAKC,aAAaH,EAAmBF,SAASI,KAAKE,YAGrD,KACCC,MAAAA,EAAYP,SAASC,cAAc,6BACrCM,GACFA,EAAUC,QAAM,CAEpB,GACC,IAQIC,MA/JuB,MAC9B,MAAOC,EAAgBC,GAAqBC,EAAAA,SAAiB,oBACtDC,EAAaC,GAAkBF,EAAAA,UAAS,GAGzCG,EAAsBC,EAAAA,aAAY,KACtC,MAAMC,EAAUC,OAAOD,QACjBE,EAAeD,OAAOE,YAGxBH,GAAAA,EAAyB,GAAfE,EACL,MAAA,kBAIT,IAAA,MAAWE,KAAWjC,EAAU,CAC9B,MAAMkC,EAAUtB,SAASuB,eAAeF,EAAQhC,IAChD,IAAKiC,EAAS,SAERE,MAAAA,EAAOF,EAAQG,wBACfC,EAAaF,EAAKG,IAAMV,EACxBW,EAAgBJ,EAAKK,OACrBC,EAAgBJ,EAAcE,EAAgB,EAG9CG,EAAiBd,EAAWE,EAAe,EAI7Ca,GAHuBC,KAAKC,IAAIJ,EAAgBC,GAGX,GAAhBH,EACvB,OAAOP,EAAQ/B,SACjB,CAGKoB,OAAAA,CAAAA,GACN,CAACA,IAGEyB,EAAenB,EAAAA,aAAY,KAC1BH,IACHC,GAAe,GAEfsB,uBAAsB,KACpB,MAAMC,EAAatB,IACfsB,IAAe3B,GACjBC,EAAkB0B,GAEpBvB,GAAe,EAAA,IACjB,GAED,CAACJ,EAAgBK,EAAqBF,IAGnCyB,EAAqBtB,eAAauB,IAChChC,MAAAA,EAAYP,SAASC,cAAc,6BACpCM,IAGIiC,EAAAA,SAAQnB,IACLoB,EAAAA,UAAUjC,OAAOa,EAAQ/B,UAAS,IAI9CoD,YAAW,KACCD,EAAAA,UAAUE,IAAIJ,EAAAA,GACvB,IAAA,GACF,IAGHxC,EAAAA,WAAU,KAER,MAAM6C,EAAgB,CACpBC,SAAS,EACTC,SAAS,GAGJC,OAAAA,iBAAiB,SAAUZ,EAAcS,GAGhD,MAAMI,EAAiBjC,IAGvB,OAFAJ,EAAkBqC,GAEX,KACEC,OAAAA,oBAAoB,SAAUd,EAAcS,EAAAA,CACrD,GACC,CAACT,EAAcpB,IAGlBhB,EAAAA,WAAU,KACRuC,EAAmB5B,EAAAA,GAClB,CAACA,EAAgB4B,IAGdY,MAAAA,EAAalC,eAAavB,IAC9B,MAAM0D,EAAgB/D,EAASgE,MAAKC,GAAKA,EAAEhE,KAAOI,IAC9C0D,GACFxC,EAAkBwC,EAAc7D,UAAS,GAE1C,IAGGgE,EAAwBtC,EAAAA,aAAY,KACxC,MAAMuC,EAASnE,EAASgE,MAAKC,GAAKA,EAAE/D,YAAcoB,IAC3C,MAAA,CACLrB,UAAIkE,WAAQlE,KAAM,SAClBC,UAAWoB,EACX8C,YAAahE,GAA8BH,MAARkE,OAAQlE,EAAAA,EAAAA,KAAM,UACnD,GACC,CAACqB,IAEG,MAAA,CACLA,iBACAwC,aACAI,wBACAzC,cACF,EA0CyB4C,EAElBhD,EClKHiD,EAAkE,EACtEC,WACArE,YAAY,OAGZ,MAAMoB,eAAEA,EAAAA,sBAAgB4C,GAA0BxD,WAGlDC,EAAAA,WAAU,QAQP,CAACW,EAAgB4C,UAGlBM,EAAAA,SAAA,CAEGD,SAAAA,GACCE,EAAAC,IAACC,MAAAA,CAAIzE,UAAW,iBAAiBA,IAC9BqE,gBAWEK,EAAqC,KAC1C,MAAAV,sBAAEA,GAA0BxD,IAIzB,OAHWwD,IAGX,IAAA,EAeEW,EAAoB,KACzB,MAAAX,sBAAEA,GAA0BxD,IAClC,OAAOwD,GAAAA,EAYIY,EAAkD,EAC7DzE,YACAkE,WACArE,YAAY,YAGT+B,UAAAA,CACChC,GAAII,EACJH,UAAW,YAAYA,IACvB6E,wBAAuB1E,EAEtBkE,aAgBMS,EAAkD,EAC7DC,gBACAV,WACArE,YAAY,GACZgF,cAEM,MAAApB,WAAEA,GAAepD,iBAQpBiE,MAAAA,CACCzE,UAAW,kBAAkBA,IAC7BgF,QARgB,KAClBpB,EAAWmB,GACXC,MAAAA,GAAAA,GAAAA,EAQGX"}
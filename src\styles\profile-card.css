/**
 * 🎯 PROFILE CARD STYLES
 * 
 * Estilos específicos para o cartão de perfil baseado no repositório de referência
 */

/* ===== PROFILE CARD BASE ===== */
.profile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.profile-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 25px 50px -12px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* ===== PROFILE RING ANIMATION ===== */
.profile-ring {
  background: conic-gradient(
    from 0deg,
    #3b82f6,
    #8b5cf6,
    #06b6d4,
    #10b981,
    #f59e0b,
    #ef4444,
    #3b82f6
  );
  animation: profileRingRotate 4s linear infinite;
  border-radius: 50%;
}

@keyframes profileRingRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== PROFILE IMAGE HOVER ===== */
.profile-image-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.profile-image-hover:hover {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.05);
}

/* ===== STATUS ONLINE ANIMATION ===== */
.status-online {
  animation: statusPulse 2s ease-in-out infinite;
  box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
}

@keyframes statusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .profile-card {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .profile-card:hover {
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.4),
    0 25px 50px -12px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .profile-card {
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .profile-card {
    max-width: 320px;
    padding: 1.5rem 1rem;
    margin: 0 auto;
  }
  
  .profile-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

@media (max-width: 480px) {
  .profile-card {
    max-width: 280px;
    padding: 1rem;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.profile-card,
.profile-ring,
.profile-image-hover,
.status-online {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .profile-ring,
  .status-online,
  .profile-image-hover,
  .profile-card {
    animation: none !important;
    transition: none !important;
  }
  
  .profile-card:hover {
    transform: none !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .profile-card {
    border: 2px solid currentColor;
    background: var(--color-surface);
  }
  
  .profile-ring {
    background: currentColor;
  }
}

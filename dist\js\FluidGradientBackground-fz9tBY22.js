import{j as e}from"./chunk-L3uak9dD.js";import{r as t}from"./chunk-D3Ns84uO.js";import"./chunk-DJqUFuPP.js";const n=[{id:"perfil",className:"section-profile",threshold:.3},{id:"projetos",className:"section-projects",threshold:.3},{id:"backlog",className:"section-backlog",threshold:.3},{id:"contato",className:"section-contact",threshold:.3}],o=e=>({perfil:"Perfil",projetos:"Projetos",backlog:"Backlog Estratégico",contato:"Contato"}[e]||e),s=()=>{t.useEffect((()=>{if(document.querySelector(".fluid-gradient-container"))return;const e=document.createElement("div");return e.className="fluid-gradient-container section-profile",document.body.insertBefore(e,document.body.firstChild),()=>{const e=document.querySelector(".fluid-gradient-container");e&&e.remove()}}),[]);return(()=>{const[e,s]=t.useState("section-profile"),[c,r]=t.useState(!1),i=t.useCallback((()=>{const t=window.scrollY,o=window.innerHeight;if(t<.2*o)return"section-profile";for(const e of n){const n=document.getElementById(e.id);if(!n)continue;const s=n.getBoundingClientRect(),c=s.top+t,r=s.height,i=c+r/2,a=t+o/2;if(Math.abs(i-a)<.6*r)return e.className}return e}),[e]),a=t.useCallback((()=>{c||(r(!0),requestAnimationFrame((()=>{const t=i();t!==e&&s(t),r(!1)})))}),[e,i,c]),l=t.useCallback((e=>{const t=document.querySelector(".fluid-gradient-container");t&&(n.forEach((e=>{t.classList.remove(e.className)})),setTimeout((()=>{t.classList.add(e)}),50))}),[]);t.useEffect((()=>{const e={passive:!0,capture:!1};window.addEventListener("scroll",a,e);const t=i();return s(t),()=>{window.removeEventListener("scroll",a,e)}}),[a,i]),t.useEffect((()=>{l(e)}),[e,l]);const d=t.useCallback((e=>{const t=n.find((t=>t.id===e));t&&s(t.className)}),[]),u=t.useCallback((()=>{const t=n.find((t=>t.className===e));return{id:(null==t?void 0:t.id)||"perfil",className:e,displayName:o((null==t?void 0:t.id)||"perfil")}}),[e]);return{currentSection:e,setSection:d,getCurrentSectionInfo:u,isScrolling:c}})()},c=({children:n,className:o=""})=>{const{currentSection:c,getCurrentSectionInfo:r}=s();return t.useEffect((()=>{}),[c,r]),e.jsx(e.Fragment,{children:n&&e.jsx("div",{className:`relative z-10 ${o}`,children:n})})},r=()=>{const{getCurrentSectionInfo:e}=s();return e(),null},i=()=>{const{getCurrentSectionInfo:e}=s();return e()},a=({sectionId:t,children:n,className:o=""})=>e.jsx("section",{id:t,className:`relative ${o}`,"data-gradient-section":t,children:n}),l=({targetSection:t,children:n,className:o="",onClick:c})=>{const{setSection:r}=s();return e.jsx("div",{className:`cursor-pointer ${o}`,onClick:()=>{r(t),null==c||c()},children:n})};export{a as GradientSection,r as GradientSectionIndicator,l as GradientTrigger,c as default,i as useCurrentSection};
//# sourceMappingURL=FluidGradientBackground-fz9tBY22.js.map

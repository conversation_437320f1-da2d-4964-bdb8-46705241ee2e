import{j as o,A as e,m as t}from"./chunk-L3uak9dD.js";import{r as a}from"./chunk-D3Ns84uO.js";import{q as s}from"./chunk-BxTnlCnP.js";import"./chunk-DJqUFuPP.js";const n=({showAfter:n=300,className:r=""})=>{const[i,l]=a.useState(!1);a.useEffect((()=>{const o=()=>{l(window.pageYOffset>n)},e=()=>{requestAnimationFrame(o)};return window.addEventListener("scroll",e,{passive:!0}),()=>window.removeEventListener("scroll",e)}),[n]);const c=()=>{window.scrollTo({top:0,behavior:"smooth"})};return o.jsx(e,{children:i&&o.jsx(t.button,{initial:{opacity:0,scale:.8,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:20},whileHover:{scale:1.1},whileTap:{scale:.9},onClick:c,onKeyDown:o=>{"Enter"!==o.key&&" "!==o.key||(o.preventDefault(),c())},className:`\n            fixed bottom-20 right-20 z-40\n            p-4 md:p-5 rounded-full shadow-lg hover:shadow-xl\n            bg-[var(--color-primary)] text-white\n            transition-all duration-300\n            focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-offset-2\n            ${r}\n          `,"aria-label":"Voltar ao topo da página",title:"Voltar ao topo",children:o.jsx(s,{className:"w-6 h-6 md:w-7 md:h-7"})})})};export{n as BackToTop,n as default};
//# sourceMappingURL=BackToTop-Dir6kW45.js.map

/**
 * 🎯 WCAG COMPLIANCE
 * 
 * Estilos para conformidade com WCAG 2.2 AA
 */

/* ===== FOCUS MANAGEMENT ===== */
.focus-visible:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --color-text: #000000;
    --color-bg: #ffffff;
    --color-border: #000000;
    --color-primary: #0000ff;
  }
  
  [data-theme="dark"] {
    --color-text: #ffffff;
    --color-bg: #000000;
    --color-border: #ffffff;
    --color-primary: #00ffff;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== LARGE TEXT MODE ===== */
@media (prefers-reduced-data: reduce) {
  .large-text {
    font-size: 1.25em;
  }
  
  .large-text h1 { font-size: 2.5em; }
  .large-text h2 { font-size: 2em; }
  .large-text h3 { font-size: 1.75em; }
  .large-text p { font-size: 1.25em; }
}

/* ===== TOUCH TARGETS ===== */
@media (max-width: 768px) {
  button,
  a,
  input,
  select,
  textarea,
  [role="button"],
  [role="link"] {
    min-height: 44px;
    min-width: 44px;
    padding: var(--space-sm);
  }
}

/* ===== KEYBOARD NAVIGATION ===== */
.keyboard-navigation *:focus {
  outline: 3px solid var(--color-primary) !important;
  outline-offset: 2px !important;
}

/* ===== SCREEN READER ONLY ===== */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* ===== SKIP LINKS ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-sm);
  z-index: var(--z-modal);
  transition: top var(--transition);
}

.skip-link:focus {
  top: 6px;
}

/* ===== COLOR CONTRAST ===== */
.text-contrast-aa {
  color: var(--color-text);
  background: var(--color-bg);
}

.text-contrast-aaa {
  color: #000000;
  background: #ffffff;
}

[data-theme="dark"] .text-contrast-aaa {
  color: #ffffff;
  background: #000000;
}

/* ===== ERROR STATES ===== */
.error-message {
  color: #dc2626;
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-xs);
}

.error-border {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 1px #dc2626;
}

/* ===== SUCCESS STATES ===== */
.success-message {
  color: var(--color-secondary);
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-xs);
}

.success-border {
  border-color: var(--color-secondary) !important;
  box-shadow: 0 0 0 1px var(--color-secondary);
}

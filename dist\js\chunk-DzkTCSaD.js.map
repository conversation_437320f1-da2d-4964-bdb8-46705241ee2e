{"version": 3, "file": "chunk-DzkTCSaD.js", "sources": ["../../node_modules/@microsoft/clarity/index.js", "../../node_modules/@microsoft/clarity/src/utils.js", "../../node_modules/logrocket/dist/build.umd.js"], "sourcesContent": ["import { injectScript } from \"./src/utils\"\n\nconst Clarity = {\n    init(projectId) {\n        injectScript(projectId, 'clarity-script');\n    },\n\n    setTag(key, value) {\n        window.clarity('set', key, value);\n    },\n\n    identify(customerId, customSessionId, customPageId, friendlyName) {\n        window.clarity('identify', customerId, customSessionId, customPageId, friendlyName);\n    },\n\n    consent(consent = true) {\n        window.clarity('consent', consent);\n    },\n\n    upgrade(reason) {\n        window.clarity('upgrade', reason);\n    },\n\n    event(eventName) {\n        window.clarity('event', eventName);\n    },\n};\n\nexport default Clarity;", "export function injectScript(projectId){\r\n  try{\r\n      (function (c, l, a, r, i, t, y) {\r\n          if(l.getElementById(\"clarity-script\")){\r\n            return;\r\n          }\r\n          c[a] = c[a] ||\r\n            function () {\r\n              (c[a].q = c[a].q || []).push(arguments);\r\n            };\r\n          t = l.createElement(r);\r\n          t.async = 1;\r\n          t.src = \"https://www.clarity.ms/tag/\" + i + \"?ref=npm\";\r\n          t.id = \"clarity-script\"\r\n          y = l.getElementsByTagName(r)[0];\r\n          y.parentNode.insertBefore(t, y);\r\n        })(window, document, \"clarity\", \"script\", projectId);\r\n      return;\r\n  }catch(error){\r\n      return;\r\n  }\r\n};", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(this, function() {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./packages/@logrocket/console/src/index.js\":\n/*!**************************************************!*\\\n  !*** ./packages/@logrocket/console/src/index.js ***!\n  \\**************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _registerConsole = _interopRequireDefault(__webpack_require__(/*! ./registerConsole */ \"./packages/@logrocket/console/src/registerConsole.js\"));\nvar _default = _registerConsole.default;\nexports[\"default\"] = _default;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/console/src/registerConsole.js\":\n/*!************************************************************!*\\\n  !*** ./packages/@logrocket/console/src/registerConsole.js ***!\n  \\************************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = registerConsole;\nvar _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\"));\nvar _enhanceFunc = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/enhanceFunc */ \"./packages/@logrocket/utils/src/enhanceFunc.ts\"));\nvar _exceptions = __webpack_require__(/*! @logrocket/exceptions */ \"./packages/@logrocket/exceptions/src/index.js\");\n// eslint-disable-line no-restricted-imports\n\nfunction registerConsole(logger) {\n  var unsubFunctions = [];\n  var methods = ['log', 'warn', 'info', 'error', 'debug'];\n  methods.forEach(function (method) {\n    unsubFunctions.push((0, _enhanceFunc.default)(console, method, function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      logger.addEvent('lr.core.LogEvent', function () {\n        var consoleOptions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var isEnabled = consoleOptions.isEnabled;\n        if ((0, _typeof2.default)(isEnabled) === 'object' && isEnabled[method] === false || isEnabled === false) {\n          return null;\n        }\n        if (method === 'error' && consoleOptions.shouldAggregateConsoleErrors) {\n          // some customers have loggers that add the log level as the first arg\n          if (!!args && args.length >= 2 && args[0] === 'ERROR') {\n            var secondArg = '';\n            try {\n              secondArg = \" \".concat(args[1]);\n            } catch (_unused) {} // eslint-disable-line no-empty\n            _exceptions.Capture.captureMessage(logger, \"\".concat(args[0]).concat(secondArg), args, {}, true);\n          } else {\n            _exceptions.Capture.captureMessage(logger, args[0], args, {}, true);\n          }\n        }\n        return {\n          logLevel: method.toUpperCase(),\n          args: args\n        };\n      });\n    }));\n  });\n  return function () {\n    unsubFunctions.forEach(function (unsubFunction) {\n      return unsubFunction();\n    });\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/exceptions/src/Capture.js\":\n/*!*******************************************************!*\\\n  !*** ./packages/@logrocket/exceptions/src/Capture.js ***!\n  \\*******************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.captureMessage = captureMessage;\nexports.captureException = captureException;\nvar _scrubException = __webpack_require__(/*! @logrocket/utils/src/scrubException */ \"./packages/@logrocket/utils/src/scrubException.ts\");\nvar _TraceKit = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/TraceKit */ \"./packages/@logrocket/utils/src/TraceKit.js\"));\nvar _stackTraceFromError = _interopRequireDefault(__webpack_require__(/*! ./stackTraceFromError */ \"./packages/@logrocket/exceptions/src/stackTraceFromError.js\"));\n// eslint-disable-line no-restricted-imports\n// eslint-disable-line no-restricted-imports\n\nfunction captureMessage(logger, message, messageArgs) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var isConsole = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  var data = {\n    exceptionType: isConsole ? 'CONSOLE' : 'MESSAGE',\n    message: message,\n    messageArgs: messageArgs,\n    browserHref: window.location ? window.location.href : ''\n  };\n  (0, _scrubException.scrubException)(data, options);\n  logger.addEvent('lr.core.Exception', function () {\n    return data;\n  });\n}\nfunction captureException(logger, exception) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var preppedTrace = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  var exceptionType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'WINDOW';\n  var trace = preppedTrace || _TraceKit.default.computeStackTrace(exception);\n  var data = {\n    exceptionType: exceptionType,\n    errorType: trace.name,\n    message: trace.message,\n    browserHref: window.location ? window.location.href : ''\n  };\n  (0, _scrubException.scrubException)(data, options);\n  var addEventOptions = {\n    _stackTrace: (0, _stackTraceFromError.default)(trace)\n  };\n  logger.addEvent('lr.core.Exception', function () {\n    return data;\n  }, addEventOptions);\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/exceptions/src/index.js\":\n/*!*****************************************************!*\\\n  !*** ./packages/@logrocket/exceptions/src/index.js ***!\n  \\*****************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"registerExceptions\", ({\n  enumerable: true,\n  get: function get() {\n    return _registerExceptions.default;\n  }\n}));\nexports.Capture = void 0;\nvar _registerExceptions = _interopRequireDefault(__webpack_require__(/*! ./registerExceptions */ \"./packages/@logrocket/exceptions/src/registerExceptions.js\"));\nvar Capture = _interopRequireWildcard(__webpack_require__(/*! ./Capture */ \"./packages/@logrocket/exceptions/src/Capture.js\"));\nexports.Capture = Capture;\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/exceptions/src/raven/raven.js\":\n/*!***********************************************************!*\\\n  !*** ./packages/@logrocket/exceptions/src/raven/raven.js ***!\n  \\***********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _TraceKit = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/TraceKit */ \"./packages/@logrocket/utils/src/TraceKit.js\"));\n/* eslint-disable */\n\n/*\nSome contents of this file were originaly from raven-js, BSD-2 Clause\n\nCopyright (c) 2018 Sentry (https://sentry.io) and individual contributors.\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n* Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\nvar objectPrototype = Object.prototype;\nfunction isUndefined(what) {\n  return what === void 0;\n}\nfunction isFunction(what) {\n  return typeof what === 'function';\n}\nfunction each(obj, callback) {\n  var i, j;\n  if (isUndefined(obj.length)) {\n    for (i in obj) {\n      if (hasKey(obj, i)) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  } else {\n    j = obj.length;\n    if (j) {\n      for (i = 0; i < j; i++) {\n        callback.call(null, i, obj[i]);\n      }\n    }\n  }\n}\n\n/**\n * hasKey, a better form of hasOwnProperty\n * Example: hasKey(MainHostObject, property) === true/false\n *\n * @param {Object} host object to check property\n * @param {string} key to check\n */\nfunction hasKey(object, key) {\n  return objectPrototype.hasOwnProperty.call(object, key);\n}\n\n/**\n * Polyfill a method\n * @param obj object e.g. `document`\n * @param name method name present on object e.g. `addEventListener`\n * @param replacement replacement function\n * @param track {optional} record instrumentation to an array\n */\nfunction fill(obj, name, replacement, track) {\n  var orig = obj[name];\n  obj[name] = replacement(orig);\n  if (track) {\n    track.push([obj, name, orig]);\n  }\n}\nvar _window = typeof window !== 'undefined' ? window : typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof self !== 'undefined' ? self : {};\nvar _document = _window.document;\nvar Handler = /*#__PURE__*/function () {\n  function Handler(_ref) {\n    var captureException = _ref.captureException;\n    (0, _classCallCheck2.default)(this, Handler);\n    this._errorHandler = this._errorHandler.bind(this);\n    this._ignoreOnError = 0;\n    this._wrappedBuiltIns = [];\n    this.captureException = captureException;\n    _TraceKit.default.report.subscribe(this._errorHandler);\n    this._instrumentTryCatch();\n  }\n  (0, _createClass2.default)(Handler, [{\n    key: \"uninstall\",\n    value: function uninstall() {\n      _TraceKit.default.report.unsubscribe(this._errorHandler);\n\n      // restore any wrapped builtins\n      var builtin;\n      while (this._wrappedBuiltIns.length) {\n        builtin = this._wrappedBuiltIns.shift();\n        var obj = builtin[0],\n          name = builtin[1],\n          orig = builtin[2];\n        obj[name] = orig;\n      }\n    }\n  }, {\n    key: \"_errorHandler\",\n    value: function _errorHandler(report) {\n      if (!this._ignoreOnError) {\n        this.captureException(report);\n      }\n    }\n  }, {\n    key: \"_ignoreNextOnError\",\n    value: function _ignoreNextOnError() {\n      var _this = this;\n      this._ignoreOnError += 1;\n      setTimeout(function () {\n        // onerror should trigger before setTimeout\n        _this._ignoreOnError -= 1;\n      });\n    }\n\n    /*\n     * Wrap code within a context so Handler can capture errors\n     * reliably across domains that is executed immediately.\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The callback to be immediately executed within the context\n     * @param {array} args An array of arguments to be called with the callback [optional]\n     */\n  }, {\n    key: \"context\",\n    value: function context(options, func, args) {\n      if (isFunction(options)) {\n        args = func || [];\n        func = options;\n        options = undefined;\n      }\n      return this.wrap(options, func).apply(this, args);\n    }\n  }, {\n    key: \"wrap\",\n    value:\n    /*\n     * Wrap code within a context and returns back a new function to be executed\n     *\n     * @param {object} options A specific set of options for this context [optional]\n     * @param {function} func The function to be wrapped in a new context\n     * @param {function} func A function to call before the try/catch wrapper [optional, private]\n     * @return {function} The newly wrapped functions with a context\n     */\n    function wrap(options, func, _before) {\n      var self = this;\n      // 1 argument has been passed, and it's not a function\n      // so just return it\n      if (isUndefined(func) && !isFunction(options)) {\n        return options;\n      }\n\n      // options is optional\n      if (isFunction(options)) {\n        func = options;\n        options = undefined;\n      }\n\n      // At this point, we've passed along 2 arguments, and the second one\n      // is not a function either, so we'll just return the second argument.\n      if (!isFunction(func)) {\n        return func;\n      }\n\n      // We don't wanna wrap it twice!\n      try {\n        if (func.__lr__) {\n          return func;\n        }\n\n        // If this has already been wrapped in the past, return that\n        if (func.__lr_wrapper__) {\n          return func.__lr_wrapper__;\n        }\n\n        // If func is not extensible, return the function as-is to prevent TypeErrors\n        // when trying to add new props & to assure immutable funcs aren't changed\n        if (!Object.isExtensible(func)) {\n          return func;\n        }\n      } catch (e) {\n        // Just accessing custom props in some Selenium environments\n        // can cause a \"Permission denied\" exception (see lr-js#495).\n        // Bail on wrapping and return the function as-is (defers to window.onerror).\n        return func;\n      }\n      function wrapped() {\n        var args = [],\n          i = arguments.length,\n          deep = !options || options && options.deep !== false;\n        if (_before && isFunction(_before)) {\n          _before.apply(this, arguments);\n        }\n\n        // Recursively wrap all of a function's arguments that are\n        // functions themselves.\n        while (i--) {\n          args[i] = deep ? self.wrap(options, arguments[i]) : arguments[i];\n        }\n        try {\n          // Attempt to invoke user-land function. This is part of the LogRocket SDK.\n          // If you're seeing this frame in a stack trace, it means that LogRocket caught\n          // an unhandled error thrown by your application code, reported it, then bubbled\n          // it up. This is expected behavior and is not a bug with LogRocket.\n          return func.apply(this, args);\n        } catch (e) {\n          self._ignoreNextOnError();\n          self.captureException(_TraceKit.default.computeStackTrace(e), options);\n          throw e;\n        }\n      }\n\n      // copy over properties of the old function\n      for (var property in func) {\n        if (hasKey(func, property)) {\n          wrapped[property] = func[property];\n        }\n      }\n      wrapped.prototype = func.prototype;\n      func.__lr_wrapper__ = wrapped;\n      // Signal that this function has been wrapped already\n      // for both debugging and to prevent it to being wrapped twice\n      wrapped.__lr__ = true;\n      wrapped.__inner__ = func;\n      return wrapped;\n    }\n  }, {\n    key: \"_instrumentTryCatch\",\n    value:\n    /**\n     * Install any queued plugins\n     */\n    function _instrumentTryCatch() {\n      var self = this;\n      var wrappedBuiltIns = self._wrappedBuiltIns;\n      function wrapTimeFn(orig) {\n        return function (fn, t) {\n          // preserve arity\n          // Make a copy of the arguments to prevent deoptimization\n          // https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n          var args = new Array(arguments.length);\n          for (var i = 0; i < args.length; ++i) {\n            args[i] = arguments[i];\n          }\n          var originalCallback = args[0];\n          if (isFunction(originalCallback)) {\n            args[0] = self.wrap(originalCallback);\n          }\n\n          // IE < 9 doesn't support .call/.apply on setInterval/setTimeout, but it\n          // also supports only two arguments and doesn't care what this is, so we\n          // can just call the original function directly.\n          if (orig.apply) {\n            return orig.apply(this, args);\n          } else {\n            return orig(args[0], args[1]);\n          }\n        };\n      }\n      function wrapEventTarget(global) {\n        var proto = _window[global] && _window[global].prototype;\n        if (proto && proto.hasOwnProperty && proto.hasOwnProperty('addEventListener')) {\n          fill(proto, 'addEventListener', function (orig) {\n            return function (evtName, fn, capture, secure) {\n              // preserve arity\n              try {\n                if (fn && fn.handleEvent) {\n                  fn.handleEvent = self.wrap(fn.handleEvent);\n                }\n              } catch (err) {\n                // can sometimes get 'Permission denied to access property \"handle Event'\n              }\n\n              // More breadcrumb DOM capture ... done here and not in `_instrumentBreadcrumbs`\n              // so that we don't have more than one wrapper function\n              var before;\n              return orig.call(this, evtName, self.wrap(fn, undefined, before), capture, secure);\n            };\n          }, wrappedBuiltIns);\n          fill(proto, 'removeEventListener', function (orig) {\n            return function (evt, fn, capture, secure) {\n              /**\n               * There are 3 scenararios to consider when removing an event listener\n               *\n               * 1. 'addEventListener' was called when the LR SDK was uninitialized.\n               * In this case, the event handler has not been wrapped, so this behaves \n               * as a passthrough and just removes the original handler.\n               *\n               * 2. 'addEventListener' was called while the LR SDK was initialized, which means\n               * our wrapped 'addEventListener' was called. In this case, the tracked event handler\n               * is also wrapped, so we need to remove that wrapped version of the handler.\n               *\n               * 3. 'addEventListener' was called both before and after the LR SDK was initialized\n               * for the same event handler. In this case, we have to remove both of them.\n               * If we'd remove only the wrapped one, the initial handler would stick around forever.\n               */\n              try {\n                var wrappedFn = fn === null || fn === void 0 ? void 0 : fn.__lr_wrapper__;\n                if (wrappedFn) {\n                  orig.call(this, evt, wrappedFn, capture, secure);\n                }\n              } catch (e) {\n                // ignore, accessing __lr_wrapper__ will throw in some Selenium environments\n              }\n              return orig.call(this, evt, fn, capture, secure);\n            };\n          },\n          // undefined is provided here to skip tracking for uninstall.\n          // Once our removeEventListener is installed, it can't be uninstalled.\n          // We always need to support removing logrocket wrapped event handlers (event\n          // handlers added when logrocket was installed) even after SDK shutdown.\n          undefined);\n        }\n      }\n      fill(_window, 'setTimeout', wrapTimeFn, wrappedBuiltIns);\n      fill(_window, 'setInterval', wrapTimeFn, wrappedBuiltIns);\n      if (_window.requestAnimationFrame) {\n        fill(_window, 'requestAnimationFrame', function (orig) {\n          return function (cb) {\n            return orig(self.wrap(cb));\n          };\n        }, wrappedBuiltIns);\n      }\n\n      // event targets borrowed from bugsnag-js:\n      // https://github.com/bugsnag/bugsnag-js/blob/master/src/bugsnag.js#L666\n      var eventTargets = ['EventTarget', 'Window', 'Node', 'ApplicationCache', 'AudioTrackList', 'ChannelMergerNode', 'CryptoOperation', 'EventSource', 'FileReader', 'HTMLUnknownElement', 'IDBDatabase', 'IDBRequest', 'IDBTransaction', 'KeyOperation', 'MediaController', 'MessagePort', 'ModalWindow', 'Notification', 'SVGElementInstance', 'Screen', 'TextTrack', 'TextTrackCue', 'TextTrackList', 'WebSocket', 'WebSocketWorker', 'Worker', 'XMLHttpRequest', 'XMLHttpRequestEventTarget', 'XMLHttpRequestUpload'];\n      for (var i = 0; i < eventTargets.length; i++) {\n        wrapEventTarget(eventTargets[i]);\n      }\n      var $ = _window.jQuery || _window.$;\n      if ($ && $.fn && $.fn.ready) {\n        fill($.fn, 'ready', function (orig) {\n          return function (fn) {\n            return orig.call(this, self.wrap(fn));\n          };\n        }, wrappedBuiltIns);\n      }\n    }\n  }]);\n  return Handler;\n}();\nexports[\"default\"] = Handler;\n;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/exceptions/src/registerExceptions.js\":\n/*!******************************************************************!*\\\n  !*** ./packages/@logrocket/exceptions/src/registerExceptions.js ***!\n  \\******************************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = registerCore;\nvar _raven = _interopRequireDefault(__webpack_require__(/*! ./raven/raven */ \"./packages/@logrocket/exceptions/src/raven/raven.js\"));\nvar Capture = _interopRequireWildcard(__webpack_require__(/*! ./Capture */ \"./packages/@logrocket/exceptions/src/Capture.js\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction registerCore(logger) {\n  var raven = new _raven.default({\n    captureException: function captureException(errorReport) {\n      Capture.captureException(logger, null, null, errorReport);\n    }\n  });\n  var rejectionHandler = function rejectionHandler(evt) {\n    // http://2ality.com/2016/04/unhandled-rejections.html\n    if (evt.reason instanceof Error) {\n      Capture.captureException(logger, evt.reason, null, null, 'UNHANDLED_REJECTION');\n    } else {\n      logger.addEvent('lr.core.Exception', function () {\n        return {\n          exceptionType: 'UNHANDLED_REJECTION',\n          message: evt.reason || 'Unhandled Promise rejection'\n        };\n      });\n    }\n  };\n  window.addEventListener('unhandledrejection', rejectionHandler);\n  return function () {\n    window.removeEventListener('unhandledrejection', rejectionHandler);\n    raven.uninstall();\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/exceptions/src/stackTraceFromError.js\":\n/*!*******************************************************************!*\\\n  !*** ./packages/@logrocket/exceptions/src/stackTraceFromError.js ***!\n  \\*******************************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = stackTraceFromError;\nfunction stackTraceFromError(errorReport) {\n  function makeNotNull(val) {\n    return val === null ? undefined : val;\n  }\n  return errorReport.stack ? errorReport.stack.map(function (frame) {\n    return {\n      lineNumber: makeNotNull(frame.line),\n      columnNumber: makeNotNull(frame.column),\n      fileName: makeNotNull(frame.url),\n      functionName: makeNotNull(frame.func)\n    };\n  }) : undefined;\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/network/src/fetchIntercept.js\":\n/*!***********************************************************!*\\\n  !*** ./packages/@logrocket/network/src/fetchIntercept.js ***!\n  \\***********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nvar _registerXHR = __webpack_require__(/*! ./registerXHR */ \"./packages/@logrocket/network/src/registerXHR.js\");\nvar interceptors = [];\nfunction makeInterceptor(fetch, fetchId) {\n  var reversedInterceptors = interceptors.reduce(function (array, interceptor) {\n    return [interceptor].concat(array);\n  }, []);\n  // if a browser supports fetch, it supports promise\n  // eslint-disable-next-line compat/compat\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  var promise = Promise.resolve(args);\n\n  // Register request interceptors\n  reversedInterceptors.forEach(function (_ref) {\n    var request = _ref.request,\n      requestError = _ref.requestError;\n    if (request || requestError) {\n      promise = promise.then(function (args) {\n        return request.apply(void 0, [fetchId].concat((0, _toConsumableArray2.default)(args)));\n      }, function (args) {\n        return requestError.apply(void 0, [fetchId].concat((0, _toConsumableArray2.default)(args)));\n      });\n    }\n  });\n  promise = promise.then(function (args) {\n    (0, _registerXHR.setActive)(false);\n    var res;\n    var err;\n    try {\n      res = fetch.apply(void 0, (0, _toConsumableArray2.default)(args));\n    } catch (_err) {\n      err = _err;\n    }\n    (0, _registerXHR.setActive)(true);\n    if (err) {\n      throw err;\n    }\n    return res;\n  });\n  reversedInterceptors.forEach(function (_ref2) {\n    var response = _ref2.response,\n      responseError = _ref2.responseError;\n    if (response || responseError) {\n      promise = promise.then(function (res) {\n        return response(fetchId, res);\n      }, function (err) {\n        return responseError && responseError(fetchId, err);\n      });\n    }\n  });\n  return promise;\n}\nfunction attach(env) {\n  if (!env.fetch || !env.Promise) {\n    // Make sure fetch is available in the given environment. If it's not, then\n    // default to using XHR intercept.\n    return;\n  }\n  var isPolyfill = env.fetch.polyfill;\n\n  // eslint-disable-next-line no-param-reassign\n  env.fetch = function (fetch) {\n    var fetchId = 0;\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return makeInterceptor.apply(void 0, [fetch, fetchId++].concat(args));\n    };\n  }(env.fetch);\n\n  // Forward the polyfill properly from fetch (set by github/whatwg-fetch).\n  if (isPolyfill) {\n    // eslint-disable-next-line no-param-reassign\n    env.fetch.polyfill = isPolyfill;\n  }\n}\n\n// TODO: React Native\n//   attach(global);\n\nvar didAttach = false;\nvar _default = {\n  register: function register(interceptor) {\n    if (!didAttach) {\n      didAttach = true;\n      attach(window);\n    }\n    interceptors.push(interceptor);\n    return function () {\n      var index = interceptors.indexOf(interceptor);\n      if (index >= 0) {\n        interceptors.splice(index, 1);\n      }\n    };\n  },\n  clear: function clear() {\n    interceptors = [];\n  }\n};\nexports[\"default\"] = _default;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/network/src/index.js\":\n/*!**************************************************!*\\\n  !*** ./packages/@logrocket/network/src/index.js ***!\n  \\**************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = registerNetwork;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\"));\nvar _registerFetch = _interopRequireDefault(__webpack_require__(/*! ./registerFetch */ \"./packages/@logrocket/network/src/registerFetch.js\"));\nvar _registerIonic = __webpack_require__(/*! ./registerIonic */ \"./packages/@logrocket/network/src/registerIonic.ts\");\nvar _registerNetworkInformation = _interopRequireDefault(__webpack_require__(/*! ./registerNetworkInformation */ \"./packages/@logrocket/network/src/registerNetworkInformation.js\"));\nvar _registerXHR = _interopRequireDefault(__webpack_require__(/*! ./registerXHR */ \"./packages/@logrocket/network/src/registerXHR.js\"));\nvar _mapValues = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/mapValues */ \"./packages/@logrocket/utils/src/mapValues.ts\"));\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n// eslint-disable-line no-restricted-imports\n\nfunction registerNetwork(logger) {\n  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    isReactNative: false,\n    isDisabled: false\n  };\n  if ((config === null || config === void 0 ? void 0 : config.isDisabled) === true) {\n    return function () {};\n  }\n  var isReactNative = config.isReactNative,\n    shouldAugmentNPS = config.shouldAugmentNPS,\n    shouldParseXHRBlob = config.shouldParseXHRBlob;\n  var ignoredNetwork = {};\n\n  // truncate if > 4MB in size\n  var truncate = function truncate(data) {\n    var limit = 1024 * 1000 * 4;\n    var str = data;\n    if ((0, _typeof2.default)(data) === 'object' && data != null) {\n      var proto = Object.getPrototypeOf(data);\n      if (proto === Object.prototype || proto === null) {\n        // plain object - jsonify for the size check\n        str = JSON.stringify(data);\n      }\n    }\n    if (str && str.length && str.length > limit && typeof str === 'string') {\n      var beginning = str.substring(0, 1000);\n      return \"\".concat(beginning, \" ... LogRocket truncating to first 1000 characters.\\n      Keep data under 4MB to prevent truncation. https://docs.logrocket.com/reference/network\");\n    }\n    return data;\n  };\n  var addRequest = function addRequest(reqId, request) {\n    var method = request.method;\n    logger.addEvent('lr.network.RequestEvent', function () {\n      var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref$isEnabled = _ref.isEnabled,\n        isEnabled = _ref$isEnabled === void 0 ? true : _ref$isEnabled,\n        _ref$requestSanitizer = _ref.requestSanitizer,\n        requestSanitizer = _ref$requestSanitizer === void 0 ? function (f) {\n          return f;\n        } : _ref$requestSanitizer;\n      if (!isEnabled) {\n        return null;\n      }\n      var sanitized = null;\n      try {\n        // only try catch user defined functions\n        sanitized = requestSanitizer(_objectSpread(_objectSpread({}, request), {}, {\n          reqId: reqId\n        }));\n      } catch (err) {\n        console.error(err);\n      }\n      if (sanitized) {\n        var url = sanitized.url;\n        if (typeof document !== 'undefined' && typeof document.createElement === 'function') {\n          // Writing and then reading from an a tag turns a relative\n          // url into an absolute one.\n          var a = document.createElement('a');\n          a.href = sanitized.url;\n          url = a.href;\n        }\n        return {\n          reqId: reqId,\n          // default\n          url: url,\n          // sanitized\n          headers: (0, _mapValues.default)(sanitized.headers, function (headerValue) {\n            // sanitized\n            return \"\".concat(headerValue);\n          }),\n          body: truncate(sanitized.body),\n          // sanitized\n          method: method,\n          // default\n          referrer: sanitized.referrer || undefined,\n          // sanitized\n          mode: sanitized.mode || undefined,\n          // sanitized\n          credentials: sanitized.credentials || undefined // sanitized\n        };\n      }\n\n      ignoredNetwork[reqId] = true;\n      return null;\n    });\n  };\n  var addResponse = function addResponse(reqId, response) {\n    var method = response.method,\n      status = response.status,\n      responseType = response.responseType;\n    logger.addEvent('lr.network.ResponseEvent', function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref2$isEnabled = _ref2.isEnabled,\n        isEnabled = _ref2$isEnabled === void 0 ? true : _ref2$isEnabled,\n        _ref2$responseSanitiz = _ref2.responseSanitizer,\n        responseSanitizer = _ref2$responseSanitiz === void 0 ? function (f) {\n          return f;\n        } : _ref2$responseSanitiz;\n      if (!isEnabled) {\n        return null;\n      } else if (ignoredNetwork[reqId]) {\n        delete ignoredNetwork[reqId];\n        return null;\n      }\n      var sanitized = null;\n      try {\n        // only try catch user defined functions\n        sanitized = responseSanitizer(_objectSpread(_objectSpread({}, response), {}, {\n          reqId: reqId\n        }));\n      } catch (err) {\n        console.error(err);\n        // fall through to redacted log\n      }\n\n      if (sanitized) {\n        return {\n          reqId: reqId,\n          // default\n          responseType: responseType,\n          status: sanitized.status,\n          // sanitized\n          headers: (0, _mapValues.default)(sanitized.headers, function (headerValue) {\n            // sanitized\n            return \"\".concat(headerValue);\n          }),\n          body: truncate(sanitized.body),\n          // sanitized\n          method: method // default\n        };\n      }\n\n      return {\n        reqId: reqId,\n        // default\n        responseType: responseType,\n        status: status,\n        // default\n        headers: {},\n        // redacted\n        body: null,\n        // redacted\n        method: method // default\n      };\n    });\n  };\n\n  var isIgnored = function isIgnored(reqId) {\n    return logger.isDisabled || ignoredNetwork[reqId] === true;\n  };\n  var unsubFetch = (0, _registerFetch.default)({\n    addRequest: addRequest,\n    addResponse: addResponse,\n    isIgnored: isIgnored\n  });\n  var unsubXHR = (0, _registerXHR.default)({\n    addRequest: addRequest,\n    addResponse: addResponse,\n    isIgnored: isIgnored,\n    logger: logger,\n    shouldAugmentNPS: shouldAugmentNPS,\n    shouldParseXHRBlob: shouldParseXHRBlob\n  });\n  var unsubIonic = (0, _registerIonic.registerIonic)({\n    addRequest: addRequest,\n    addResponse: addResponse,\n    isIgnored: isIgnored\n  });\n  var unsubNetworkInformation = isReactNative ? function () {} : (0, _registerNetworkInformation.default)(logger);\n  return function () {\n    unsubNetworkInformation();\n    unsubFetch();\n    unsubXHR();\n    unsubIonic();\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/network/src/registerFetch.js\":\n/*!**********************************************************!*\\\n  !*** ./packages/@logrocket/network/src/registerFetch.js ***!\n  \\**********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = registerFetch;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _mapValues = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/mapValues */ \"./packages/@logrocket/utils/src/mapValues.ts\"));\nvar _fetchIntercept = _interopRequireDefault(__webpack_require__(/*! ./fetchIntercept */ \"./packages/@logrocket/network/src/fetchIntercept.js\"));\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction makeObjectFromHeaders(headers) {\n  // If using real fetch, we must stringify the Headers object.\n  if (headers == null || typeof headers.forEach !== 'function') {\n    return headers;\n  }\n  var result = {};\n  headers.forEach(function (value, key) {\n    if (result[key]) {\n      result[key] = \"\".concat(result[key], \",\").concat(value);\n    } else {\n      result[key] = \"\".concat(value);\n    }\n  });\n  return result;\n}\n\n// XHR specification is unclear of what types to allow in value so using toString method for now\nvar stringifyHeaders = function stringifyHeaders(headers) {\n  return (0, _mapValues.default)(makeObjectFromHeaders(headers), function (value) {\n    return \"\".concat(value);\n  });\n};\nfunction pluckFetchFields() {\n  var arg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return {\n    url: arg.url,\n    headers: stringifyHeaders(arg.headers),\n    method: arg.method && arg.method.toUpperCase(),\n    referrer: arg.referrer || undefined,\n    mode: arg.mode || undefined,\n    credentials: arg.credentials || undefined\n  };\n}\nfunction registerFetch(_ref) {\n  var addRequest = _ref.addRequest,\n    addResponse = _ref.addResponse,\n    isIgnored = _ref.isIgnored;\n  var LOGROCKET_FETCH_LABEL = 'fetch-';\n  var fetchMethodMap = {};\n  var unregister = _fetchIntercept.default.register({\n    request: function request(fetchId) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      var p;\n      if (typeof Request !== 'undefined' && args[0] instanceof Request) {\n        var clonedText;\n\n        // Request.clone() and Request.text() may throw in Safari (e.g., when\n        // request body contains FormData)\n        try {\n          clonedText = args[0].clone().text();\n        } catch (err) {\n          // if a browser supports fetch, it supports promise\n          // eslint-disable-next-line compat/compat\n          clonedText = Promise.resolve(\"LogRocket fetch error: \".concat(err.message));\n        }\n        p = clonedText.then(function (body) {\n          return _objectSpread(_objectSpread({}, pluckFetchFields(args[0])), {}, {\n            body: body\n          });\n        }, function (err) {\n          return _objectSpread(_objectSpread({}, pluckFetchFields(args[0])), {}, {\n            body: \"LogRocket fetch error: \".concat(err.message)\n          });\n        });\n      } else {\n        // if a browser supports fetch, it supports promise\n        // eslint-disable-next-line compat/compat\n        p = Promise.resolve(_objectSpread(_objectSpread({}, pluckFetchFields(args[1])), {}, {\n          url: \"\".concat(args[0]),\n          body: (args[1] || {}).body\n        }));\n      }\n      return p.then(function (req) {\n        fetchMethodMap[fetchId] = req.method;\n        addRequest(\"\".concat(LOGROCKET_FETCH_LABEL).concat(fetchId), req);\n        return args;\n      });\n    },\n    requestError: function requestError(fetchId, error) {\n      // if a browser supports fetch, it supports promise\n      // eslint-disable-next-line compat/compat\n      return Promise.reject(error);\n    },\n    response: function response(fetchId, _response) {\n      var responseClone;\n      var responseTextPromise;\n      if (isIgnored(\"\".concat(LOGROCKET_FETCH_LABEL).concat(fetchId))) {\n        // Don't even try to read ignored requests\n        return _response;\n      }\n\n      // event-streams are meant to remain open and be consumed over time by the sender. Attempting to capture the body\n      // of these requests will cause us to \"stall\" here, and prevents the users of our SDK from using event-streams\n      // unless they have explicitly ignored the request.\n      if (_response.headers.get('content-type') === 'text/event-stream') {\n        // eslint-disable-next-line compat/compat\n        responseTextPromise = Promise.resolve('LogRocket skipped consuming an event-stream body.');\n      } else {\n        try {\n          // TODO: enhance function on original response and future clones for:\n          // text(), json(), blob(), formdata(), arraybuffer()\n          responseClone = _response.clone();\n        } catch (err) {\n          // safari has a bug where cloning can fail\n          var responseHash = {\n            url: _response.url,\n            responseType: _response.type.toUpperCase(),\n            status: _response.status,\n            headers: stringifyHeaders(_response.headers),\n            body: \"LogRocket fetch error: \".concat(err.message),\n            method: fetchMethodMap[fetchId]\n          };\n          delete fetchMethodMap[fetchId];\n          addResponse(\"\".concat(LOGROCKET_FETCH_LABEL).concat(fetchId), responseHash);\n          return _response;\n        }\n        try {\n          if (window.TextDecoder && responseClone.body) {\n            // use a reader to manually read the response body rather than calling response.text()\n            // response.text() was timing out for some responses, in some cases because Apollo sends\n            //   an abort signal or because the stream wasn't getting terminated cleanly\n            // using a reader allows us to capture what we can from response bodies before the\n            //   response receives an abort signal\n            var reader = responseClone.body.getReader();\n            // response bodies always decode with UTF-8\n            // https://developer.mozilla.org/en-US/docs/Web/API/Response/text\n            var utf8Decoder = new window.TextDecoder('utf-8');\n            var bodyContents = '';\n            responseTextPromise = reader.read().then(function readResponseBody(_ref2) {\n              var done = _ref2.done,\n                value = _ref2.value;\n              if (done) {\n                return bodyContents;\n              }\n              var chunk = value ? utf8Decoder.decode(value, {\n                stream: true\n              }) : '';\n              bodyContents += chunk;\n              return reader.read().then(readResponseBody);\n            });\n          } else {\n            // TextDecoder doesn't have support across all browsers that LR supports, so if there's\n            //  no TextDecoder, fall back to the old approach\n            responseTextPromise = responseClone.text();\n          }\n        } catch (error) {\n          // eslint-disable-next-line compat/compat\n          responseTextPromise = Promise.resolve(\"LogRocket error reading body: \".concat(error.message));\n        }\n      }\n      responseTextPromise.catch(function (error) {\n        // don't drop request & log to console when the request is aborted,\n        // as it may have already completed\n        // https://github.com/LogRocket/logrocket/issues/34\n        if (error.name === 'AbortError' && error instanceof DOMException) {\n          return;\n        }\n        return \"LogRocket error reading body: \".concat(error.message);\n      }).then(function (data) {\n        var responseHash = {\n          url: _response.url,\n          responseType: _response.type.toUpperCase(),\n          status: _response.status,\n          headers: stringifyHeaders(_response.headers),\n          body: data,\n          method: fetchMethodMap[fetchId]\n        };\n        delete fetchMethodMap[fetchId];\n        addResponse(\"\".concat(LOGROCKET_FETCH_LABEL).concat(fetchId), responseHash);\n      });\n      return _response;\n    },\n    responseError: function responseError(fetchId, error) {\n      var response = {\n        url: undefined,\n        status: 0,\n        headers: {},\n        body: \"\".concat(error)\n      };\n      addResponse(\"\".concat(LOGROCKET_FETCH_LABEL).concat(fetchId), response);\n      // if a browser supports fetch, it supports promise\n      // eslint-disable-next-line compat/compat\n      return Promise.reject(error);\n    }\n  });\n  return unregister;\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/network/src/registerIonic.ts\":\n/*!**********************************************************!*\\\n  !*** ./packages/@logrocket/network/src/registerIonic.ts ***!\n  \\**********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.mergeHeaders = mergeHeaders;\nexports.serializeQueryParams = serializeQueryParams;\nexports.appendQueryParamsString = appendQueryParamsString;\nexports.processData = processData;\nexports.registerIonic = registerIonic;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\"));\nvar _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ \"./node_modules/@babel/runtime/helpers/toConsumableArray.js\"));\nvar _protectFunc = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/protectFunc */ \"./packages/@logrocket/utils/src/protectFunc.ts\"));\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nvar VALID_HTTP_METHODS = new Set(['get', 'put', 'post', 'patch', 'head', 'delete', 'options', 'upload', 'download']);\nvar VALID_SERIALIZERS = new Set(['urlencoded', 'json', 'utf8']);\nvar UNSUPPORTED_SERIALIZERS = new Set(['raw', 'multipart']);\nvar LOGROCKET_IONIC_LABEL = 'ionic-';\nvar UNSUPPORTED_PLATFORMS = new Set(['desktop', 'mobileweb', 'pwa']);\nvar FORM_DATA = new Set(['FormData']);\nvar EMPTY_SET = new Set();\nvar STRING_SET = new Set(['string']);\nvar STRING_ARRAY_SET = new Set(['string', 'array']);\nvar ALLOWED_DATA_TYPES = {\n  utf8: STRING_SET,\n  urlencoded: new Set(['object']),\n  json: new Set(['array', 'object']),\n  raw: new Set(['Uint8Array', 'ArrayBuffer']),\n  default: EMPTY_SET\n};\n\n// Used in intercepting Ionic pugin requests found here https://www.npmjs.com/package/@ionic-native/http\n// based on https://github.com/silkimen/cordova-plugin-advanced-http/blob/master/www/public-interface.js\n\nfunction checkForValidStringValue(list, value, fieldName) {\n  if (typeof value !== 'string') {\n    throw new Error(\"\".concat(fieldName, \" must be one of: \").concat((0, _toConsumableArray2.default)(list).join(', ')));\n  }\n\n  // eslint-disable-next-line no-param-reassign\n  value = value.trim().toLowerCase();\n  if (!list.has(value)) {\n    throw new Error(\"\".concat(fieldName, \" must be one of: \").concat((0, _toConsumableArray2.default)(list).join(', ')));\n  }\n  return value;\n}\nfunction checkKeyValuePairObject(obj, allowedChildren, onInvalidValueMessage) {\n  if ((0, _typeof2.default)(obj) !== 'object') {\n    throw new Error(onInvalidValueMessage);\n  }\n  for (var _i = 0, _Object$keys = Object.keys(obj); _i < _Object$keys.length; _i++) {\n    var key = _Object$keys[_i];\n    if (!allowedChildren.has((0, _typeof2.default)(obj[key]))) {\n      throw new Error(onInvalidValueMessage);\n    }\n  }\n  return obj;\n}\nfunction getMatchingHostHeaders(url, ionicHttp) {\n  var _URL = new URL(url),\n    host = _URL.host;\n  return ionicHttp.getHeaders(host) || null;\n}\nfunction mergeHeaders(defaultHeaders, headers) {\n  return _objectSpread(_objectSpread({}, defaultHeaders), headers);\n}\nfunction getMergedHeaders(url, requestHeaders, ionicHttp) {\n  // get global headers via public method\n  var globalHeaders = ionicHttp.getHeaders('*') || {};\n  var hostHeaders = getMatchingHostHeaders(url, ionicHttp) || {};\n  return mergeHeaders(mergeHeaders(globalHeaders, hostHeaders), requestHeaders);\n}\nfunction serializeValue(value, encode) {\n  if (encode) {\n    return encodeURIComponent(value);\n  } else {\n    return value;\n  }\n}\nfunction serializeIdentifier(parentKey, key, encode) {\n  if (!parentKey.length) {\n    return encode ? encodeURIComponent(key) : key;\n  }\n  if (encode) {\n    return \"\".concat(encodeURIComponent(parentKey), \"[\").concat(encodeURIComponent(key), \"]\");\n  } else {\n    return \"\".concat(parentKey, \"[\").concat(key, \"]\");\n  }\n}\nfunction serializeArray(parentKey, array, encode) {\n  var parts = [];\n  var _iterator = _createForOfIteratorHelper(array),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var e = _step.value;\n      if (Array.isArray(e)) {\n        parts.push(serializeArray(\"\".concat(parentKey, \"[]\"), e, encode));\n        continue;\n      } else if ((0, _typeof2.default)(e) === 'object') {\n        /* This replicates what appears to be a bug in the Ionic code in order to capture everything identically\n           parts.push(serializeObject(`${parentKey}[]${array[i]}`, encode));\n           https://github.com/silkimen/cordova-plugin-advanced-http/blob/master/www/url-util.js#L73\n        */\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        parts.push(serializeObject(\"\".concat(parentKey, \"[]\").concat(e), encode, undefined));\n        continue;\n      }\n      parts.push(\"\".concat(serializeIdentifier(parentKey, '', encode), \"=\").concat(serializeValue(e, encode)));\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  return parts.join('&');\n}\nfunction serializeObject(parentKey, object, encode) {\n  var parts = [];\n  for (var key in object) {\n    if (!object.hasOwnProperty(key)) {\n      continue;\n    }\n    var identifier = parentKey.length ? \"\".concat(parentKey, \"[\").concat(key, \"]\") : key;\n    if (Array.isArray(object[key])) {\n      parts.push(serializeArray(identifier, object[key], encode));\n      continue;\n    } else if ((0, _typeof2.default)(object[key]) === 'object' && object[key] !== null) {\n      parts.push(serializeObject(identifier, object[key], encode));\n      continue;\n    }\n    parts.push(\"\".concat(serializeIdentifier(parentKey, key, encode), \"=\").concat(serializeValue(object[key], encode)));\n  }\n  return parts.join('&');\n}\nfunction serializeQueryParams(params, encode) {\n  return serializeObject('', params, encode);\n}\nfunction appendQueryParamsString(url, params) {\n  if (!url.length || !params.length) {\n    return url;\n  }\n  var _URL2 = new URL(url),\n    host = _URL2.host,\n    pathname = _URL2.pathname,\n    search = _URL2.search,\n    hash = _URL2.hash,\n    protocol = _URL2.protocol;\n  return \"\".concat(protocol, \"//\").concat(host).concat(pathname).concat(search.length ? \"\".concat(search, \"&\").concat(params) : \"?\".concat(params)).concat(hash);\n}\nfunction getAllowedDataTypes(dataSerializer) {\n  return ALLOWED_DATA_TYPES[dataSerializer] || ALLOWED_DATA_TYPES.default;\n}\nfunction getAllowedInstanceTypes(dataSerializer) {\n  return dataSerializer === 'multipart' ? FORM_DATA : EMPTY_SET;\n}\nfunction processData(data, dataSerializer) {\n  var currentDataType = (0, _typeof2.default)(data);\n  var allowedDataTypes = getAllowedDataTypes(dataSerializer);\n  var allowedInstanceTypes = getAllowedInstanceTypes(dataSerializer);\n  if (allowedInstanceTypes.size > 0) {\n    var isCorrectInstanceType = false;\n    allowedInstanceTypes.forEach(function (type) {\n      if (__webpack_require__.g[type] && data instanceof __webpack_require__.g[type]) {\n        isCorrectInstanceType = true;\n      }\n    });\n    if (!isCorrectInstanceType) {\n      throw new Error(\"INSTANCE_TYPE_MISMATCH_DATA \".concat((0, _toConsumableArray2.default)(allowedInstanceTypes).join(', ')));\n    }\n  }\n  if (allowedInstanceTypes.size === 0 && !allowedDataTypes.has(currentDataType)) {\n    throw new Error(\"TYPE_MISMATCH_DATA \".concat((0, _toConsumableArray2.default)(allowedDataTypes).join(', ')));\n  }\n  switch (dataSerializer) {\n    case 'utf8':\n      // already a string\n      return data;\n    default:\n      // object of some sort (urlencoded or json)\n      return JSON.stringify(data, undefined, 2);\n  }\n}\nfunction handleMissingOptions(options, ionicHttp) {\n  // eslint-disable-next-line no-param-reassign\n  options = options || {};\n  var serializer;\n  var data = options.data;\n  try {\n    // need to check special case if data type is not handled by LR but otherwise valid\n    serializer = checkForValidStringValue(VALID_SERIALIZERS, options.serializer || ionicHttp.getDataSerializer(), 'serializer / data payload type');\n  } catch (_unused) {\n    // if this fails it's of entirely invalid type, let it fail\n    serializer = checkForValidStringValue(UNSUPPORTED_SERIALIZERS, options.serializer || ionicHttp.getDataSerializer(), 'serializer / data payload type');\n    // is of a valid but unsupported to LR type, set data to empty\n    data = {};\n  }\n  return {\n    data: data,\n    filePath: options.filePath,\n    followRedirect: options.followRedirect,\n    headers: checkKeyValuePairObject(options.headers || {}, STRING_SET, 'Invalid header type, must be string'),\n    method: checkForValidStringValue(VALID_HTTP_METHODS, options.method || VALID_HTTP_METHODS[0], 'method'),\n    name: options.name,\n    params: checkKeyValuePairObject(options.params || {}, STRING_ARRAY_SET, 'Invalid param, must be of type string or array'),\n    responseType: options.responseType,\n    serializer: serializer,\n    connectTimeout: options.connectTimeout,\n    readTimeout: options.readTimeout,\n    timeout: options.timeout\n  };\n}\nvar ionicIdCounter = 0;\nfunction registerIonic(_ref) {\n  var _cordova, _cordova$plugin, _window$ionic;\n  var addRequest = _ref.addRequest,\n    addResponse = _ref.addResponse,\n    isIgnored = _ref.isIgnored;\n  var ionicHttp = (_cordova = window.cordova) === null || _cordova === void 0 ? void 0 : (_cordova$plugin = _cordova.plugin) === null || _cordova$plugin === void 0 ? void 0 : _cordova$plugin.http;\n  var ionicMap = {};\n  var unsubscribedFromIonic = false;\n  if (typeof ionicHttp === 'undefined') {\n    // Plugin does not exist! Empty uninstall hook.\n    return function () {};\n  }\n  var platforms = (_window$ionic = window.ionic) === null || _window$ionic === void 0 ? void 0 : _window$ionic.platforms;\n  if (typeof platforms !== 'undefined' && typeof platforms.some === 'function' && platforms.some(function (e) {\n    return UNSUPPORTED_PLATFORMS.has(e);\n  })) {\n    // We appear to be running in a web browser, do not hook and let XHR wrap instead.\n    return function () {};\n  }\n  var originalSendRequest = ionicHttp.sendRequest;\n  var handleResponse = (0, _protectFunc.default)(function (response, isSuccess, ionicReqId) {\n    if (!isIgnored(\"\".concat(LOGROCKET_IONIC_LABEL).concat(ionicReqId))) {\n      try {\n        var responseHash = {\n          url: response.url || '',\n          status: response.status < 600 && response.status >= 100 ? response.status : 0,\n          headers: response.headers || {},\n          body: isSuccess ? response.data : response.error,\n          method: ionicMap[ionicReqId].toUpperCase()\n        };\n        addResponse(\"\".concat(LOGROCKET_IONIC_LABEL).concat(ionicReqId), responseHash);\n      } catch (err) {\n        var _responseHash = {\n          url: response.url || '',\n          status: response.status < 600 && response.status >= 100 ? response.status : 0,\n          headers: response.headers || {},\n          body: \"LogRocket fetch error: \".concat(err.message),\n          method: ionicMap[ionicReqId].toUpperCase()\n        };\n        addResponse(\"\".concat(LOGROCKET_IONIC_LABEL).concat(ionicReqId), _responseHash);\n      }\n    } // Otherwise, don't even try to read ignored / unsubscribed requests\n  });\n\n  ionicHttp.sendRequest = function (url, options, success, failure) {\n    var currentId = ++ionicIdCounter;\n    var ourSuccessHandler = function ourSuccessHandler(response) {\n      if (!unsubscribedFromIonic) {\n        handleResponse(response, true, currentId);\n        delete ionicMap[currentId];\n      }\n      success(response);\n    };\n    var ourFailureHandler = function ourFailureHandler(response) {\n      if (!unsubscribedFromIonic) {\n        handleResponse(response, false, currentId);\n        delete ionicMap[currentId];\n      }\n      failure(response);\n    };\n    if (!unsubscribedFromIonic) {\n      try {\n        // will throw just like identical ionic code if input is invalid\n        var modifiedOptions = handleMissingOptions(options, ionicHttp);\n        var modifiedUrl = appendQueryParamsString(url, serializeObject('', modifiedOptions.params, true));\n        // need to minic behavior of Ionic API by adding global headers\n        var mergedHeaders = getMergedHeaders(url, modifiedOptions.headers, ionicHttp);\n\n        // ionic APIs default to get method\n        var method = modifiedOptions.method || 'get';\n        ionicMap[currentId] = method;\n        var requestHash = {\n          url: modifiedUrl,\n          method: method.toUpperCase(),\n          headers: mergedHeaders || {},\n          // only applicable on post, put or patch methods\n          body: processData(modifiedOptions.data || {}, modifiedOptions.serializer)\n        };\n        addRequest(\"\".concat(LOGROCKET_IONIC_LABEL).concat(currentId), requestHash);\n      } catch (err) {\n        var _requestHash = {\n          url: url,\n          method: (options.method || 'get').toUpperCase(),\n          headers: {},\n          body: \"LogRocket fetch error: \".concat(err.message)\n        };\n        addRequest(\"\".concat(LOGROCKET_IONIC_LABEL).concat(currentId), _requestHash);\n      }\n    }\n    return originalSendRequest(url, options, ourSuccessHandler, ourFailureHandler);\n  };\n  return function () {\n    unsubscribedFromIonic = true;\n    ionicHttp.sendRequest = originalSendRequest;\n    ionicMap = {};\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/network/src/registerNetworkInformation.js\":\n/*!***********************************************************************!*\\\n  !*** ./packages/@logrocket/network/src/registerNetworkInformation.js ***!\n  \\***********************************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = registerNetworkInformation;\nvar EFFECTIVE_TYPE_VALS = {\n  'slow-2g': 'SLOW2G',\n  '2g': 'TWOG',\n  '3g': 'THREEG',\n  '4g': 'FOURG'\n};\nfunction registerNetworkInformation(logger) {\n  var lastStatus = undefined;\n  function sendNetworkInformation() {\n    var newStatus = {\n      online: window.navigator.onLine,\n      effectiveType: 'UNKOWN'\n    };\n    if (!window.navigator.onLine) {\n      newStatus.effectiveType = 'NONE';\n    } else if (window.navigator.connection && window.navigator.connection.effectiveType) {\n      newStatus.effectiveType = EFFECTIVE_TYPE_VALS[window.navigator.connection.effectiveType] || 'UNKNOWN';\n    }\n    if (lastStatus && newStatus.online === lastStatus.online && newStatus.effectiveType === lastStatus.effectiveType) {\n      return;\n    }\n    lastStatus = newStatus;\n    logger.addEvent('lr.network.NetworkStatusEvent', function () {\n      var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref$isEnabled = _ref.isEnabled,\n        isEnabled = _ref$isEnabled === void 0 ? true : _ref$isEnabled;\n      if (!isEnabled) {\n        return null;\n      }\n      return newStatus;\n    });\n  }\n  setTimeout(sendNetworkInformation);\n  if (window.navigator.connection && typeof window.navigator.connection.addEventListener === 'function') {\n    window.navigator.connection.addEventListener('change', sendNetworkInformation);\n  }\n  window.addEventListener('online', sendNetworkInformation);\n  window.addEventListener('offline', sendNetworkInformation);\n  return function () {\n    window.removeEventListener('offline', sendNetworkInformation);\n    window.removeEventListener('online', sendNetworkInformation);\n    if (window.navigator.connection && typeof window.navigator.connection.removeEventListener === 'function') {\n      window.navigator.connection.removeEventListener('change', sendNetworkInformation);\n    }\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/network/src/registerXHR.js\":\n/*!********************************************************!*\\\n  !*** ./packages/@logrocket/network/src/registerXHR.js ***!\n  \\********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.setActive = setActive;\nexports[\"default\"] = registerXHR;\nvar _mapValues = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/mapValues */ \"./packages/@logrocket/utils/src/mapValues.ts\"));\nvar _enhanceFunc = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/enhanceFunc */ \"./packages/@logrocket/utils/src/enhanceFunc.ts\"));\nvar _startsWith = _interopRequireDefault(__webpack_require__(/*! @logrocket/utils/src/startsWith */ \"./packages/@logrocket/utils/src/startsWith.js\"));\nvar _nps = __webpack_require__(/*! @logrocket/utils/src/constants/nps */ \"./packages/@logrocket/utils/src/constants/nps.js\");\n// eslint-disable-line no-restricted-imports\n// eslint-disable-line no-restricted-imports\n// eslint-disable-line no-restricted-imports\n\nvar isActive = true;\nfunction setActive(shouldBeActive) {\n  isActive = shouldBeActive;\n}\nvar currentXHRId = 0;\nfunction registerXHR(_ref) {\n  var addRequest = _ref.addRequest,\n    addResponse = _ref.addResponse,\n    isIgnored = _ref.isIgnored,\n    logger = _ref.logger,\n    _ref$shouldAugmentNPS = _ref.shouldAugmentNPS,\n    shouldAugmentNPS = _ref$shouldAugmentNPS === void 0 ? true : _ref$shouldAugmentNPS,\n    _ref$shouldParseXHRBl = _ref.shouldParseXHRBlob,\n    shouldParseXHRBlob = _ref$shouldParseXHRBl === void 0 ? false : _ref$shouldParseXHRBl;\n  var _XHR = XMLHttpRequest;\n  var xhrMap = new WeakMap();\n  var unsubscribedFromXhr = false;\n  var LOGROCKET_XHR_LABEL = 'xhr-';\n  window._lrXMLHttpRequest = XMLHttpRequest;\n\n  // eslint-disable-next-line no-native-reassign\n  XMLHttpRequest = function XMLHttpRequest(mozAnon, mozSystem) {\n    var xhrObject = new _XHR(mozAnon, mozSystem);\n    if (!isActive) {\n      return xhrObject;\n    }\n    xhrMap.set(xhrObject, {\n      xhrId: ++currentXHRId,\n      headers: {}\n    });\n    var openOriginal = xhrObject.open;\n    function openShim() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      try {\n        var url = args[1];\n        if (window.URL && typeof window.URL === 'function' && url.search(_nps.WOOTRIC_RESPONSES_REGEX) === 0) {\n          var logrocketSessionURL = new window.URL(logger.recordingURL);\n          logrocketSessionURL.searchParams.set('nps', 'wootric');\n          var urlObj = new window.URL(url);\n          var responseText = urlObj.searchParams.get('response[text]');\n          var feedback = responseText ? \"\".concat(responseText, \"\\n\\n\") : '';\n          urlObj.searchParams.set('response[text]', \"\".concat(feedback, \"<\").concat(logrocketSessionURL.href, \"|View LogRocket session>\"));\n          args[1] = urlObj.href; // eslint-disable-line no-param-reassign\n        }\n      } catch (e) {/* do nothing */}\n      return openOriginal.apply(this, args);\n    }\n    var sendOriginal = xhrObject.send;\n    function sendShim() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      try {\n        var currentXHR = xhrMap.get(xhrObject);\n        if (window.URL && typeof window.URL === 'function' && currentXHR && currentXHR.url && currentXHR.url.search(_nps.DELIGHTED_RESPONSES_REGEX) === 0 && args.length && args[0].indexOf(_nps.DELIGHTED_FEEDBACK_PREFIX) !== -1) {\n          var recordingURL = new window.URL(logger.recordingURL);\n          recordingURL.searchParams.set('nps', 'delighted');\n          var logrocketSessionURL = encodeURIComponent(recordingURL.href);\n          var data = args[0].split('&').map(function (dataString) {\n            if ((0, _startsWith.default)(dataString, _nps.DELIGHTED_FEEDBACK_PREFIX)) {\n              var isEmpty = dataString === _nps.DELIGHTED_FEEDBACK_PREFIX;\n              return \"\".concat(dataString).concat(isEmpty ? '' : '\\n\\n', \"<\").concat(logrocketSessionURL, \"|View LogRocket session>\");\n            }\n            return dataString;\n          }).join('&');\n          args[0] = data; // eslint-disable-line no-param-reassign\n        }\n      } catch (e) {/* do nothing */}\n      return sendOriginal.apply(this, args);\n    }\n    if (shouldAugmentNPS) {\n      xhrObject.open = openShim;\n      xhrObject.send = sendShim;\n    }\n\n    // ..., 'open', (method, url, async, username, password) => {\n    (0, _enhanceFunc.default)(xhrObject, 'open', function (method, url) {\n      if (unsubscribedFromXhr) {\n        return;\n      }\n      var currentXHR = xhrMap.get(xhrObject);\n      currentXHR.method = method;\n      currentXHR.url = url;\n    });\n    (0, _enhanceFunc.default)(xhrObject, 'send', function (data) {\n      if (unsubscribedFromXhr) {\n        return;\n      }\n      var currentXHR = xhrMap.get(xhrObject);\n      if (!currentXHR) {\n        return;\n      }\n      var request = {\n        url: currentXHR.url,\n        method: currentXHR.method && currentXHR.method.toUpperCase(),\n        headers: (0, _mapValues.default)(currentXHR.headers || {}, function (headerValues) {\n          return headerValues.join(', ');\n        }),\n        body: data\n      };\n      addRequest(\"\".concat(LOGROCKET_XHR_LABEL).concat(currentXHR.xhrId), request);\n    });\n    (0, _enhanceFunc.default)(xhrObject, 'setRequestHeader', function (header, value) {\n      if (unsubscribedFromXhr) {\n        return;\n      }\n      var currentXHR = xhrMap.get(xhrObject);\n      if (!currentXHR) {\n        return;\n      }\n      currentXHR.headers = currentXHR.headers || {};\n      currentXHR.headers[header] = currentXHR.headers[header] || [];\n      currentXHR.headers[header].push(value);\n    });\n    var xhrListeners = {\n      readystatechange: function readystatechange() {\n        if (unsubscribedFromXhr) {\n          return;\n        }\n        if (xhrObject.readyState === 4) {\n          var currentXHR = xhrMap.get(xhrObject);\n          if (!currentXHR) {\n            return;\n          }\n\n          // Do not read ignored requests at all.\n          if (isIgnored(\"\".concat(LOGROCKET_XHR_LABEL).concat(currentXHR.xhrId))) {\n            return;\n          }\n          var headerString = xhrObject.getAllResponseHeaders() || '';\n          var headers = headerString.split(/[\\r\\n]+/).reduce(function (previous, current) {\n            var next = previous;\n            var headerParts = current.split(': ');\n            if (headerParts.length > 0) {\n              var key = headerParts.shift(); // first index of the array\n              var value = headerParts.join(': '); // rest of the array repaired\n              if (previous[key]) {\n                next[key] += \", \".concat(value);\n              } else {\n                next[key] = value;\n              }\n            }\n            return next;\n          }, {});\n          var body;\n\n          // IE 11 sometimes throws when trying to access large responses\n          try {\n            switch (xhrObject.responseType) {\n              case 'json':\n                body = logger._shouldCloneResponse ? JSON.parse(JSON.stringify(xhrObject.response)) : xhrObject.response;\n                break;\n              case 'arraybuffer':\n              case 'blob':\n                {\n                  body = xhrObject.response;\n                  break;\n                }\n              case 'document':\n                {\n                  body = xhrObject.responseXML;\n                  break;\n                }\n              case 'text':\n              case '':\n                {\n                  body = xhrObject.responseText;\n                  break;\n                }\n              default:\n                {\n                  body = '';\n                }\n            }\n          } catch (err) {\n            body = 'LogRocket: Error accessing response.';\n          }\n          var response = {\n            url: currentXHR.url,\n            status: xhrObject.status,\n            headers: headers,\n            body: body,\n            method: (currentXHR.method || '').toUpperCase()\n          };\n          if (shouldParseXHRBlob && response.body instanceof Blob) {\n            var blobReader = new FileReader();\n            blobReader.readAsText(response.body);\n            blobReader.onload = function () {\n              try {\n                response.body = JSON.parse(blobReader.result);\n              } catch (_unused) {} // eslint-disable-line no-empty\n              addResponse(\"\".concat(LOGROCKET_XHR_LABEL).concat(currentXHR.xhrId), response);\n            };\n          } else {\n            addResponse(\"\".concat(LOGROCKET_XHR_LABEL).concat(currentXHR.xhrId), response);\n          }\n        }\n      }\n      // // Unused Event Listeners\n      // loadstart: () => {},\n      // progress: () => {},\n      // abort: () => {},\n      // error: () => {},\n      // load: () => {},\n      // timeout: () => {},\n      // loadend: () => {},\n    };\n\n    Object.keys(xhrListeners).forEach(function (key) {\n      xhrObject.addEventListener(key, xhrListeners[key]);\n    });\n    return xhrObject;\n  };\n\n  // this allows \"instanceof XMLHttpRequest\" to work\n  XMLHttpRequest.prototype = _XHR.prototype;\n\n  // Persist the static variables.\n  ['UNSENT', 'OPENED', 'HEADERS_RECEIVED', 'LOADING', 'DONE'].forEach(function (variable) {\n    XMLHttpRequest[variable] = _XHR[variable];\n  });\n  return function () {\n    unsubscribedFromXhr = true;\n    // eslint-disable-next-line no-native-reassign\n    XMLHttpRequest = _XHR;\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/now/src/index.js\":\n/*!**********************************************!*\\\n  !*** ./packages/@logrocket/now/src/index.js ***!\n  \\**********************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\n/* eslint-disable compat/compat */\nvar dateNow = Date.now.bind(Date);\nvar loadTime = dateNow();\nvar _default = typeof performance !== 'undefined' && performance.now ? performance.now.bind(performance) : function () {\n  return dateNow() - loadTime;\n};\nexports[\"default\"] = _default;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/redux/src/createEnhancer.js\":\n/*!*********************************************************!*\\\n  !*** ./packages/@logrocket/redux/src/createEnhancer.js ***!\n  \\*********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = createEnhancer;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _now = _interopRequireDefault(__webpack_require__(/*! @logrocket/now */ \"./packages/@logrocket/now/src/index.js\"));\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nvar storeIdCounter = 0;\nfunction createEnhancer(logger) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    _ref$stateSanitizer = _ref.stateSanitizer,\n    stateSanitizer = _ref$stateSanitizer === void 0 ? function (f) {\n      return f;\n    } : _ref$stateSanitizer,\n    _ref$actionSanitizer = _ref.actionSanitizer,\n    actionSanitizer = _ref$actionSanitizer === void 0 ? function (f) {\n      return f;\n    } : _ref$actionSanitizer;\n  // an enhancer is a function that returns a Store\n  return function (createStore) {\n    return function (reducer, initialState, enhancer) {\n      var store = createStore(reducer, initialState, enhancer);\n      var originalDispatch = store.dispatch;\n      var storeId = storeIdCounter++;\n      logger.addEvent('lr.redux.InitialState', function () {\n        var sanitizedState;\n        try {\n          // only try catch user defined functions\n          sanitizedState = stateSanitizer(store.getState());\n        } catch (err) {\n          console.error(err.toString());\n        }\n        return {\n          state: sanitizedState,\n          storeId: storeId\n        };\n      });\n      var dispatch = function dispatch(action) {\n        var start = (0, _now.default)();\n        var err;\n        var res;\n        try {\n          res = originalDispatch(action);\n        } catch (_err) {\n          err = _err;\n        } finally {\n          var duration = (0, _now.default)() - start;\n          logger.addEvent('lr.redux.ReduxAction', function () {\n            var sanitizedState = null;\n            var sanitizedAction = null;\n            try {\n              // only try catch user defined functions\n              sanitizedState = stateSanitizer(store.getState());\n              sanitizedAction = actionSanitizer(action);\n            } catch (err) {\n              console.error(err.toString());\n            }\n            if (sanitizedState && sanitizedAction) {\n              return {\n                storeId: storeId,\n                action: sanitizedAction,\n                duration: duration,\n                stateDelta: sanitizedState\n              };\n            }\n            return null;\n          });\n        }\n        if (err) {\n          throw err;\n        }\n        return res;\n      };\n      return _objectSpread(_objectSpread({}, store), {}, {\n        dispatch: dispatch\n      });\n    };\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/redux/src/createMiddleware.js\":\n/*!***********************************************************!*\\\n  !*** ./packages/@logrocket/redux/src/createMiddleware.js ***!\n  \\***********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = createMiddleware;\nvar _now = _interopRequireDefault(__webpack_require__(/*! @logrocket/now */ \"./packages/@logrocket/now/src/index.js\"));\nvar storeIdCounter = 0;\nfunction createMiddleware(logger) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    _ref$stateSanitizer = _ref.stateSanitizer,\n    stateSanitizer = _ref$stateSanitizer === void 0 ? function (f) {\n      return f;\n    } : _ref$stateSanitizer,\n    _ref$actionSanitizer = _ref.actionSanitizer,\n    actionSanitizer = _ref$actionSanitizer === void 0 ? function (f) {\n      return f;\n    } : _ref$actionSanitizer;\n  return function (store) {\n    var storeId = storeIdCounter++;\n    logger.addEvent('lr.redux.InitialState', function () {\n      var sanitizedState;\n      try {\n        // only try catch user defined functions\n        sanitizedState = stateSanitizer(store.getState());\n      } catch (err) {\n        console.error(err.toString());\n      }\n      return {\n        state: sanitizedState,\n        storeId: storeId\n      };\n    });\n    return function (next) {\n      return function (action) {\n        var start = (0, _now.default)();\n        var err;\n        var res;\n        try {\n          res = next(action);\n        } catch (_err) {\n          err = _err;\n        } finally {\n          var duration = (0, _now.default)() - start;\n          logger.addEvent('lr.redux.ReduxAction', function () {\n            var sanitizedState = null;\n            var sanitizedAction = null;\n            try {\n              // only try catch user defined functions\n              sanitizedState = stateSanitizer(store.getState());\n              sanitizedAction = actionSanitizer(action);\n            } catch (err) {\n              console.error(err.toString());\n            }\n            if (sanitizedState && sanitizedAction) {\n              return {\n                storeId: storeId,\n                action: sanitizedAction,\n                duration: duration,\n                stateDelta: sanitizedState\n              };\n            }\n            return null;\n          });\n        }\n        if (err) {\n          throw err;\n        }\n        return res;\n      };\n    };\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/redux/src/index.js\":\n/*!************************************************!*\\\n  !*** ./packages/@logrocket/redux/src/index.js ***!\n  \\************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"createEnhancer\", ({\n  enumerable: true,\n  get: function get() {\n    return _createEnhancer.default;\n  }\n}));\nObject.defineProperty(exports, \"createMiddleware\", ({\n  enumerable: true,\n  get: function get() {\n    return _createMiddleware.default;\n  }\n}));\nvar _createEnhancer = _interopRequireDefault(__webpack_require__(/*! ./createEnhancer */ \"./packages/@logrocket/redux/src/createEnhancer.js\"));\nvar _createMiddleware = _interopRequireDefault(__webpack_require__(/*! ./createMiddleware */ \"./packages/@logrocket/redux/src/createMiddleware.js\"));\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/TraceKit.js\":\n/*!***************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/TraceKit.js ***!\n  \\***************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n/* eslint-disable */\n\n\n\n/*\n TraceKit - Cross browser stack traces - github.com/occ/TraceKit\n\n This was originally forked from github.com/occ/TraceKit, but has since been\n largely re-written and is now maintained as part of raven-js.  Tests for\n this are in test/vendor.\n\n MIT license\n*/\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar TraceKit = {\n  collectWindowErrors: true,\n  debug: false\n};\n\n// This is to be defensive in environments where window does not exist (see https://github.com/getsentry/raven-js/pull/785)\nvar _window = typeof window !== 'undefined' ? window : typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof self !== 'undefined' ? self : {};\n\n// global reference to slice\nvar _slice = [].slice;\nvar UNKNOWN_FUNCTION = '?';\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Error_types\nvar ERROR_TYPES_RE = /^(?:Uncaught (?:exception: )?)?((?:Eval|Internal|Range|Reference|Syntax|Type|URI)Error): ?(.*)$/;\nfunction getLocationHref() {\n  if (typeof document === 'undefined' || typeof document.location === 'undefined') return '';\n  return document.location.href;\n}\n\n/**\n * TraceKit.report: cross-browser processing of unhandled exceptions\n *\n * Syntax:\n *   TraceKit.report.subscribe(function(stackInfo) { ... })\n *   TraceKit.report.unsubscribe(function(stackInfo) { ... })\n *   TraceKit.report(exception)\n *   try { ...code... } catch(ex) { TraceKit.report(ex); }\n *\n * Supports:\n *   - Firefox: full stack trace with line numbers, plus column number\n *              on top frame; column number is not guaranteed\n *   - Opera:   full stack trace with line and column numbers\n *   - Chrome:  full stack trace with line and column numbers\n *   - Safari:  line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *   - IE:      line and column number for the top frame only; some frames\n *              may be missing, and column number is not guaranteed\n *\n * In theory, TraceKit should work on all of the following versions:\n *   - IE5.5+ (only 8.0 tested)\n *   - Firefox 0.9+ (only 3.5+ tested)\n *   - Opera 7+ (only 10.50 tested; versions 9 and earlier may require\n *     Exceptions Have Stacktrace to be enabled in opera:config)\n *   - Safari 3+ (only 4+ tested)\n *   - Chrome 1+ (only 5+ tested)\n *   - Konqueror 3.5+ (untested)\n *\n * Requires TraceKit.computeStackTrace.\n *\n * Tries to catch all unhandled exceptions and report them to the\n * subscribed handlers. Please note that TraceKit.report will rethrow the\n * exception. This is REQUIRED in order to get a useful stack trace in IE.\n * If the exception does not reach the top of the browser, you will only\n * get a stack trace from the point where TraceKit.report was called.\n *\n * Handlers receive a stackInfo object as described in the\n * TraceKit.computeStackTrace docs.\n */\nTraceKit.report = function reportModuleWrapper() {\n  var handlers = [],\n    lastArgs = null,\n    lastException = null,\n    lastExceptionStack = null;\n\n  /**\n   * Add a crash handler.\n   * @param {Function} handler\n   */\n  function subscribe(handler) {\n    installGlobalHandler();\n    handlers.push(handler);\n  }\n\n  /**\n   * Remove a crash handler.\n   * @param {Function} handler\n   */\n  function unsubscribe(handler) {\n    for (var i = handlers.length - 1; i >= 0; --i) {\n      if (handlers[i] === handler) {\n        handlers.splice(i, 1);\n      }\n    }\n  }\n\n  /**\n   * Remove all crash handlers.\n   */\n  function unsubscribeAll() {\n    uninstallGlobalHandler();\n    handlers = [];\n  }\n\n  /**\n   * Dispatch stack information to all handlers.\n   * @param {Object.<string, *>} stack\n   */\n  function notifyHandlers(stack, isWindowError) {\n    var exception = null;\n    if (isWindowError && !TraceKit.collectWindowErrors) {\n      return;\n    }\n    for (var i in handlers) {\n      if (handlers.hasOwnProperty(i)) {\n        try {\n          handlers[i].apply(null, [stack].concat(_slice.call(arguments, 2)));\n        } catch (inner) {\n          exception = inner;\n        }\n      }\n    }\n    if (exception) {\n      throw exception;\n    }\n  }\n  var _oldOnerrorHandler, _onErrorHandlerInstalled;\n\n  /**\n   * Ensures all global unhandled exceptions are recorded.\n   * Supported by Gecko and IE.\n   * @param {string} message Error message.\n   * @param {string} url URL of script that generated the exception.\n   * @param {(number|string)} lineNo The line number at which the error\n   * occurred.\n   * @param {?(number|string)} colNo The column number at which the error\n   * occurred.\n   * @param {?Error} ex The actual Error object.\n   */\n  function traceKitWindowOnError(message, url, lineNo, colNo, ex) {\n    var stack = null;\n    if (lastExceptionStack) {\n      TraceKit.computeStackTrace.augmentStackTraceWithInitialElement(lastExceptionStack, url, lineNo, message);\n      processLastException();\n    } else if (ex) {\n      // New chrome and blink send along a real error object\n      // Let's just report that like a normal error.\n      // See: https://mikewest.org/2013/08/debugging-runtime-errors-with-window-onerror\n      stack = TraceKit.computeStackTrace(ex);\n      notifyHandlers(stack, true);\n    } else {\n      var location = {\n        'url': url,\n        'line': lineNo,\n        'column': colNo\n      };\n      var name = undefined;\n      var msg = message; // must be new var or will modify original `arguments`\n      var groups;\n      if ({}.toString.call(message) === '[object String]') {\n        var groups = message.match(ERROR_TYPES_RE);\n        if (groups) {\n          name = groups[1];\n          msg = groups[2];\n        }\n      }\n      location.func = UNKNOWN_FUNCTION;\n      stack = {\n        'name': name,\n        'message': msg,\n        'url': getLocationHref(),\n        'stack': [location]\n      };\n      notifyHandlers(stack, true);\n    }\n    if (_oldOnerrorHandler) {\n      return _oldOnerrorHandler.apply(this, arguments);\n    }\n    return false;\n  }\n  function installGlobalHandler() {\n    if (_onErrorHandlerInstalled) {\n      return;\n    }\n    _oldOnerrorHandler = _window.onerror;\n    _window.onerror = traceKitWindowOnError;\n    _onErrorHandlerInstalled = true;\n  }\n  function uninstallGlobalHandler() {\n    if (!_onErrorHandlerInstalled) {\n      return;\n    }\n    _window.onerror = _oldOnerrorHandler;\n    _onErrorHandlerInstalled = false;\n    _oldOnerrorHandler = undefined;\n  }\n  function processLastException() {\n    var _lastExceptionStack = lastExceptionStack,\n      _lastArgs = lastArgs;\n    lastArgs = null;\n    lastExceptionStack = null;\n    lastException = null;\n    notifyHandlers.apply(null, [_lastExceptionStack, false].concat(_lastArgs));\n  }\n\n  /**\n   * Reports an unhandled Error to TraceKit.\n   * @param {Error} ex\n   * @param {?boolean} rethrow If false, do not re-throw the exception.\n   * Only used for window.onerror to not cause an infinite loop of\n   * rethrowing.\n   */\n  function report(ex, rethrow) {\n    var args = _slice.call(arguments, 1);\n    if (lastExceptionStack) {\n      if (lastException === ex) {\n        return; // already caught by an inner catch block, ignore\n      } else {\n        processLastException();\n      }\n    }\n    var stack = TraceKit.computeStackTrace(ex);\n    lastExceptionStack = stack;\n    lastException = ex;\n    lastArgs = args;\n\n    // If the stack trace is incomplete, wait for 2 seconds for\n    // slow slow IE to see if onerror occurs or not before reporting\n    // this exception; otherwise, we will end up with an incomplete\n    // stack trace\n    setTimeout(function () {\n      if (lastException === ex) {\n        processLastException();\n      }\n    }, stack.incomplete ? 2000 : 0);\n    if (rethrow !== false) {\n      throw ex; // re-throw to propagate to the top level (and cause window.onerror)\n    }\n  }\n\n  report.subscribe = subscribe;\n  report.unsubscribe = unsubscribe;\n  report.uninstall = unsubscribeAll;\n  return report;\n}();\n\n/**\n * TraceKit.computeStackTrace: cross-browser stack traces in JavaScript\n *\n * Syntax:\n *   s = TraceKit.computeStackTrace(exception) // consider using TraceKit.report instead (see below)\n * Returns:\n *   s.name              - exception name\n *   s.message           - exception message\n *   s.stack[i].url      - JavaScript or HTML file URL\n *   s.stack[i].func     - function name, or empty for anonymous functions (if guessing did not work)\n *   s.stack[i].args     - arguments passed to the function, if known\n *   s.stack[i].line     - line number, if known\n *   s.stack[i].column   - column number, if known\n *\n * Supports:\n *   - Firefox:  full stack trace with line numbers and unreliable column\n *               number on top frame\n *   - Opera 10: full stack trace with line and column numbers\n *   - Opera 9-: full stack trace with line numbers\n *   - Chrome:   full stack trace with line and column numbers\n *   - Safari:   line and column number for the topmost stacktrace element\n *               only\n *   - IE:       no line numbers whatsoever\n *\n * Tries to guess names of anonymous functions by looking for assignments\n * in the source code. In IE and Safari, we have to guess source file names\n * by searching for function bodies inside all page scripts. This will not\n * work for scripts that are loaded cross-domain.\n * Here be dragons: some function names may be guessed incorrectly, and\n * duplicate functions may be mismatched.\n *\n * TraceKit.computeStackTrace should only be used for tracing purposes.\n * Logging of unhandled exceptions should be done with TraceKit.report,\n * which builds on top of TraceKit.computeStackTrace and provides better\n * IE support by utilizing the window.onerror event to retrieve information\n * about the top of the stack.\n *\n * Note: In IE and Safari, no stack trace is recorded on the Error object,\n * so computeStackTrace instead walks its *own* chain of callers.\n * This means that:\n *  * in Safari, some methods may be missing from the stack trace;\n *  * in IE, the topmost function in the stack trace will always be the\n *    caller of computeStackTrace.\n *\n * This is okay for tracing (because you are likely to be calling\n * computeStackTrace from the function you want to be the topmost element\n * of the stack trace anyway), but not okay for logging unhandled\n * exceptions (because your catch block will likely be far away from the\n * inner function that actually caused the exception).\n *\n */\nTraceKit.computeStackTrace = function computeStackTraceWrapper() {\n  /**\n   * Escapes special characters, except for whitespace, in a string to be\n   * used inside a regular expression as a string literal.\n   * @param {string} text The string.\n   * @return {string} The escaped string literal.\n   */\n  function escapeRegExp(text) {\n    return text.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#]/g, '\\\\$&');\n  }\n\n  /**\n   * Escapes special characters in a string to be used inside a regular\n   * expression as a string literal. Also ensures that HTML entities will\n   * be matched the same as their literal friends.\n   * @param {string} body The string.\n   * @return {string} The escaped string.\n   */\n  function escapeCodeAsRegExpForMatchingInsideHTML(body) {\n    return escapeRegExp(body).replace('<', '(?:<|&lt;)').replace('>', '(?:>|&gt;)').replace('&', '(?:&|&amp;)').replace('\"', '(?:\"|&quot;)').replace(/\\s+/g, '\\\\s+');\n  }\n\n  // Contents of Exception in various browsers.\n  //\n  // SAFARI:\n  // ex.message = Can't find variable: qq\n  // ex.line = 59\n  // ex.sourceId = 580238192\n  // ex.sourceURL = http://...\n  // ex.expressionBeginOffset = 96\n  // ex.expressionCaretOffset = 98\n  // ex.expressionEndOffset = 98\n  // ex.name = ReferenceError\n  //\n  // FIREFOX:\n  // ex.message = qq is not defined\n  // ex.fileName = http://...\n  // ex.lineNumber = 59\n  // ex.columnNumber = 69\n  // ex.stack = ...stack trace... (see the example below)\n  // ex.name = ReferenceError\n  //\n  // CHROME:\n  // ex.message = qq is not defined\n  // ex.name = ReferenceError\n  // ex.type = not_defined\n  // ex.arguments = ['aa']\n  // ex.stack = ...stack trace...\n  //\n  // INTERNET EXPLORER:\n  // ex.message = ...\n  // ex.name = ReferenceError\n  //\n  // OPERA:\n  // ex.message = ...message... (see the example below)\n  // ex.name = ReferenceError\n  // ex.opera#sourceloc = 11  (pretty much useless, duplicates the info in ex.message)\n  // ex.stacktrace = n/a; see 'opera:config#UserPrefs|Exceptions Have Stacktrace'\n\n  /**\n   * Computes stack trace information from the stack property.\n   * Chrome and Gecko use this property.\n   * @param {Error} ex\n   * @return {?Object.<string, *>} Stack trace information.\n   */\n  function computeStackTraceFromStackProp(ex) {\n    if (typeof ex.stack === 'undefined' || !ex.stack) return;\n    var chrome = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|<anonymous>).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,\n      gecko = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|resource|\\[native).*?)(?::(\\d+))?(?::(\\d+))?\\s*$/i,\n      winjs = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i,\n      lines = ex.stack.split('\\n'),\n      stack = [],\n      parts,\n      element,\n      reference = /^(.*) is undefined$/.exec(ex.message);\n    for (var i = 0, j = lines.length; i < j; ++i) {\n      if (parts = chrome.exec(lines[i])) {\n        var isNative = parts[2] && parts[2].indexOf('native') !== -1;\n        element = {\n          'url': !isNative ? parts[2] : null,\n          'func': parts[1] || UNKNOWN_FUNCTION,\n          'args': isNative ? [parts[2]] : [],\n          'line': parts[3] ? +parts[3] : null,\n          'column': parts[4] ? +parts[4] : null\n        };\n      } else if (parts = winjs.exec(lines[i])) {\n        element = {\n          'url': parts[2],\n          'func': parts[1] || UNKNOWN_FUNCTION,\n          'args': [],\n          'line': +parts[3],\n          'column': parts[4] ? +parts[4] : null\n        };\n      } else if (parts = gecko.exec(lines[i])) {\n        element = {\n          'url': parts[3],\n          'func': parts[1] || UNKNOWN_FUNCTION,\n          'args': parts[2] ? parts[2].split(',') : [],\n          'line': parts[4] ? +parts[4] : null,\n          'column': parts[5] ? +parts[5] : null\n        };\n      } else {\n        continue;\n      }\n      if (!element.func && element.line) {\n        element.func = UNKNOWN_FUNCTION;\n      }\n      stack.push(element);\n    }\n    if (!stack.length) {\n      return null;\n    }\n    if (!stack[0].column && typeof ex.columnNumber !== 'undefined') {\n      // FireFox uses this awesome columnNumber property for its top frame\n      // Also note, Firefox's column number is 0-based and everything else expects 1-based,\n      // so adding 1\n      stack[0].column = ex.columnNumber + 1;\n    }\n    return {\n      'name': ex.name,\n      'message': ex.message,\n      'url': getLocationHref(),\n      'stack': stack\n    };\n  }\n\n  /**\n   * Adds information about the first frame to incomplete stack traces.\n   * Safari and IE require this to get complete data on the first frame.\n   * @param {Object.<string, *>} stackInfo Stack trace information from\n   * one of the compute* methods.\n   * @param {string} url The URL of the script that caused an error.\n   * @param {(number|string)} lineNo The line number of the script that\n   * caused an error.\n   * @param {string=} message The error generated by the browser, which\n   * hopefully contains the name of the object that caused the error.\n   * @return {boolean} Whether or not the stack information was\n   * augmented.\n   */\n  function augmentStackTraceWithInitialElement(stackInfo, url, lineNo, message) {\n    var initial = {\n      'url': url,\n      'line': lineNo\n    };\n    if (initial.url && initial.line) {\n      stackInfo.incomplete = false;\n      if (!initial.func) {\n        initial.func = UNKNOWN_FUNCTION;\n      }\n      if (stackInfo.stack.length > 0) {\n        if (stackInfo.stack[0].url === initial.url) {\n          if (stackInfo.stack[0].line === initial.line) {\n            return false; // already in stack trace\n          } else if (!stackInfo.stack[0].line && stackInfo.stack[0].func === initial.func) {\n            stackInfo.stack[0].line = initial.line;\n            return false;\n          }\n        }\n      }\n      stackInfo.stack.unshift(initial);\n      stackInfo.partial = true;\n      return true;\n    } else {\n      stackInfo.incomplete = true;\n    }\n    return false;\n  }\n\n  /**\n   * Computes stack trace information by walking the arguments.caller\n   * chain at the time the exception occurred. This will cause earlier\n   * frames to be missed but is the only way to get any stack trace in\n   * Safari and IE. The top frame is restored by\n   * {@link augmentStackTraceWithInitialElement}.\n   * @param {Error} ex\n   * @return {?Object.<string, *>} Stack trace information.\n   */\n  function computeStackTraceByWalkingCallerChain(ex, depth) {\n    var functionName = /function\\s+([_$a-zA-Z\\xA0-\\uFFFF][_$a-zA-Z0-9\\xA0-\\uFFFF]*)?\\s*\\(/i,\n      stack = [],\n      funcs = {},\n      recursion = false,\n      parts,\n      item,\n      source;\n    for (var curr = computeStackTraceByWalkingCallerChain.caller; curr && !recursion; curr = curr.caller) {\n      if (curr === computeStackTrace || curr === TraceKit.report) {\n        // console.log('skipping internal function');\n        continue;\n      }\n      item = {\n        'url': null,\n        'func': UNKNOWN_FUNCTION,\n        'line': null,\n        'column': null\n      };\n      if (curr.name) {\n        item.func = curr.name;\n      } else if (parts = functionName.exec(curr.toString())) {\n        item.func = parts[1];\n      }\n      if (typeof item.func === 'undefined') {\n        try {\n          item.func = parts.input.substring(0, parts.input.indexOf('{'));\n        } catch (e) {}\n      }\n      if (funcs['' + curr]) {\n        recursion = true;\n      } else {\n        funcs['' + curr] = true;\n      }\n      stack.push(item);\n    }\n    if (depth) {\n      // console.log('depth is ' + depth);\n      // console.log('stack is ' + stack.length);\n      stack.splice(0, depth);\n    }\n    var result = {\n      'name': ex.name,\n      'message': ex.message,\n      'url': getLocationHref(),\n      'stack': stack\n    };\n    augmentStackTraceWithInitialElement(result, ex.sourceURL || ex.fileName, ex.line || ex.lineNumber, ex.message || ex.description);\n    return result;\n  }\n\n  /**\n   * Computes a stack trace for an exception.\n   * @param {Error} ex\n   * @param {(string|number)=} depth\n   */\n  function computeStackTrace(ex, depth) {\n    var stack = null;\n    depth = depth == null ? 0 : +depth;\n    try {\n      stack = computeStackTraceFromStackProp(ex);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n    try {\n      stack = computeStackTraceByWalkingCallerChain(ex, depth + 1);\n      if (stack) {\n        return stack;\n      }\n    } catch (e) {\n      if (TraceKit.debug) {\n        throw e;\n      }\n    }\n    return {\n      'name': ex.name,\n      'message': ex.message,\n      'url': getLocationHref()\n    };\n  }\n  computeStackTrace.augmentStackTraceWithInitialElement = augmentStackTraceWithInitialElement;\n  computeStackTrace.computeStackTraceFromStackProp = computeStackTraceFromStackProp;\n  return computeStackTrace;\n}();\nvar _default = TraceKit;\nexports[\"default\"] = _default;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/constants/nps.js\":\n/*!********************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/constants/nps.js ***!\n  \\********************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.DELIGHTED_FEEDBACK_PREFIX = exports.DELIGHTED_RESPONSES_REGEX = exports.WOOTRIC_RESPONSES_REGEX = void 0;\nvar WOOTRIC_RESPONSES_REGEX = /^https:\\/\\/production.wootric.com\\/responses/;\nexports.WOOTRIC_RESPONSES_REGEX = WOOTRIC_RESPONSES_REGEX;\nvar DELIGHTED_RESPONSES_REGEX = /^https:\\/\\/web.delighted.com\\/e\\/[a-zA-Z-]*\\/c/;\nexports.DELIGHTED_RESPONSES_REGEX = DELIGHTED_RESPONSES_REGEX;\nvar DELIGHTED_FEEDBACK_PREFIX = 'comment=';\nexports.DELIGHTED_FEEDBACK_PREFIX = DELIGHTED_FEEDBACK_PREFIX;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/createUnsubListener.ts\":\n/*!**************************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/createUnsubListener.ts ***!\n  \\**************************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createUnsubListener = createUnsubListener;\nexports.Handler = void 0;\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar Handler = /*#__PURE__*/function () {\n  function Handler(value) {\n    (0, _classCallCheck2.default)(this, Handler);\n    this._value = void 0;\n    this._value = value;\n  }\n  (0, _createClass2.default)(Handler, [{\n    key: \"get\",\n    value: function get() {\n      return this._value;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this._value = undefined;\n    }\n  }]);\n  return Handler;\n}();\n/*\n * This approach ensures the unsub function never keeps a reference to the event listener's target element.\n * This is not a concern in Chrome or Firefox, but Safari will hold the entire context of a function's parent function\n * in scope of that child function. This means that creating the unsub function inside 'addListener' could cause the\n * target element (the 'obj' argument) to be held in scope of the unsub function even though it is never referenced\n * in the unsub function itself. This would lead to a memory leak after the target element is removed from the DOM.\n *\n * This function is in a separate file to prevent the Terser minifier from inline-ing the function into 'addListener'.\n */\nexports.Handler = Handler;\nfunction createUnsubListener(handler) {\n  return function () {\n    handler.clear();\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/enhanceFunc.ts\":\n/*!******************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/enhanceFunc.ts ***!\n  \\******************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = enhanceFunc;\nvar _createUnsubListener = __webpack_require__(/*! ./createUnsubListener */ \"./packages/@logrocket/utils/src/createUnsubListener.ts\");\nvar NO_OP_UNREGISTER = function NO_OP_UNREGISTER() {};\nfunction enhanceFunc(obj, method, handler) {\n  if (typeof obj[method] !== 'function') {\n    return NO_OP_UNREGISTER;\n  }\n  try {\n    // eslint-disable-next-line no-inner-declarations\n    var shim = function shim() {\n      var _h$get;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var res = original.apply(this, args);\n      (_h$get = h.get()) === null || _h$get === void 0 ? void 0 : _h$get.apply(this, args);\n      return res;\n    }; // eslint-disable-next-line no-param-reassign\n    var original = obj[method];\n    var h = new _createUnsubListener.Handler(handler);\n    obj[method] = shim;\n    return function () {\n      // Disable our shim from running, and if the method hasn't been re-shimmed\n      // since our shim was applied, safely reset the method to its prior state\n      h.clear();\n      if (obj[method] === shim) {\n        // eslint-disable-next-line no-param-reassign\n        obj[method] = original;\n      }\n    };\n  } catch (_error) {\n    return NO_OP_UNREGISTER;\n  }\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/logError.js\":\n/*!***************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/logError.js ***!\n  \\***************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar canBind = typeof console !== 'undefined' && console.error && console.error.bind;\nvar logError = canBind ? console.error.bind(console) : function () {};\nvar _default = logError;\nexports[\"default\"] = _default;\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/mapValues.ts\":\n/*!****************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/mapValues.ts ***!\n  \\****************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = mapValues;\nfunction mapValues(obj, f) {\n  if (obj == null) {\n    return {};\n  }\n  var res = {};\n  Object.keys(obj).forEach(function (key) {\n    res[key] = f(obj[key]);\n  });\n  return res;\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/protectFunc.ts\":\n/*!******************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/protectFunc.ts ***!\n  \\******************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = protectFunc;\nvar _sendTelemetryData = _interopRequireDefault(__webpack_require__(/*! ./sendTelemetryData */ \"./packages/@logrocket/utils/src/sendTelemetryData.js\"));\nvar _logError = _interopRequireDefault(__webpack_require__(/*! ./logError */ \"./packages/@logrocket/utils/src/logError.js\"));\nfunction protectFunc(f) {\n  var onFail = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function () {};\n  return function () {\n    var result;\n    try {\n      result = f.apply(void 0, arguments);\n    } catch (err) {\n      if (typeof window !== 'undefined' && window._lrdebug) {\n        throw err;\n      }\n      var payload = onFail(err);\n      (0, _logError.default)('LogRocket', err);\n      (0, _sendTelemetryData.default)(err, payload);\n    }\n    return result;\n  };\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/scrubException.ts\":\n/*!*********************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/scrubException.ts ***!\n  \\*********************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.scrubException = scrubException;\nvar _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ \"./node_modules/@babel/runtime/helpers/typeof.js\"));\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\nfunction isScalar(value) {\n  return /boolean|number|string/.test((0, _typeof2.default)(value));\n}\nvar optionalScalars = [\n// Valid values for 'level' are 'fatal', 'error', 'warning', 'info',\n// and 'debug'. Defaults to 'error'.\n'level', 'logger'];\nvar optionalMaps = ['tags', 'extra'];\nfunction scrubException(data, options) {\n  if (options) {\n    var _iterator = _createForOfIteratorHelper(optionalScalars),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var field = _step.value;\n        var value = options[field];\n        if (isScalar(value)) {\n          // eslint-disable-next-line no-param-reassign\n          data[field] = value.toString();\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    var _iterator2 = _createForOfIteratorHelper(optionalMaps),\n      _step2;\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var _field = _step2.value;\n        var dirty = options[_field] || {};\n        var scrubbed = {};\n        for (var _i = 0, _Object$keys = Object.keys(dirty); _i < _Object$keys.length; _i++) {\n          var key = _Object$keys[_i];\n          var _value = dirty[key];\n          if (isScalar(_value)) {\n            scrubbed[key.toString()] = _value.toString();\n          }\n        }\n\n        // eslint-disable-next-line no-param-reassign\n        data[_field] = scrubbed;\n      }\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n  }\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/sendTelemetryData.js\":\n/*!************************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/sendTelemetryData.js ***!\n  \\************************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.sendTelemetry = sendTelemetry;\nexports[\"default\"] = sendErrorTelemetry;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _logError = _interopRequireDefault(__webpack_require__(/*! ./logError */ \"./packages/@logrocket/utils/src/logError.js\"));\nvar _TraceKit = _interopRequireDefault(__webpack_require__(/*! ./TraceKit */ \"./packages/@logrocket/utils/src/TraceKit.js\"));\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n// eslint-disable-next-line no-undef\nvar SCRIPT_VERSION =  true ? \"ebaecd9a57844dd1dc6ccde131dc931d7f22ffb8\" : 0;\nfunction _sendToSentry(data) {\n  try {\n    var _window, _window$__SDKCONFIG__;\n    var message = data.message;\n    var url = 'https://e.logrocket.com/api/3/store/?sentry_version=7&sentry_client=http%2F3.8.0&sentry_key=b64162b4187a4c5caae8a68a7e291793';\n    var body = JSON.stringify(_objectSpread({\n      message: message,\n      logger: 'javascript',\n      platform: 'javascript',\n      request: {\n        headers: {\n          'User-Agent': typeof navigator !== 'undefined' && navigator.userAgent\n        },\n        url: typeof location !== 'undefined' && location.href\n      },\n      release: SCRIPT_VERSION,\n      environment: ((_window = window) === null || _window === void 0 ? void 0 : (_window$__SDKCONFIG__ = _window.__SDKCONFIG__) === null || _window$__SDKCONFIG__ === void 0 ? void 0 : _window$__SDKCONFIG__.scriptEnv) || 'prod'\n    }, data));\n    if (typeof window !== 'undefined') {\n      var XHR = window._lrXMLHttpRequest || XMLHttpRequest;\n      var req = new XHR();\n      req.open('POST', url);\n      req.send(body);\n    } else if (typeof fetch !== 'undefined') {\n      // NOTE: fetch is intended only to be used in a web worker context where the main web SDK hasn't be initialized\n      //       (ex. Shopify SDK). Otherwise this fetch call would get captured by LogRocket and we don't want to record\n      //       network requests to LogRocket. Eventually this potential issue will be resolved by SSDK-2149.\n      // eslint-disable-next-line compat/compat\n      fetch(url, {\n        method: 'POST',\n        body: body\n      }).catch(function (rejectReason) {\n        (0, _logError.default)('Failed to send via fetch', rejectReason);\n      });\n    }\n  } catch (err) {\n    (0, _logError.default)('Failed to send', err);\n  }\n}\nfunction sendTelemetry(message, more) {\n  if (typeof window !== 'undefined' && window._lrdebug) {\n    return void (0, _logError.default)(message);\n  }\n  if (more && more.extra && more.extra.appID && typeof more.extra.appID.indexOf === 'function' && more.extra.appID.indexOf('au2drp/') === 0 && Math.random() >= 0.25) {\n    return;\n  }\n  _sendToSentry(_objectSpread({\n    message: message\n  }, more));\n}\nfunction sendErrorTelemetry(err, payload) {\n  try {\n    var message = err.message;\n    var stringPayload;\n    try {\n      stringPayload = JSON.stringify(payload).slice(0, 1000);\n    } catch (err) {\n      try {\n        stringPayload = \"Could not stringify payload: \".concat(Object.prototype.toString.call(payload));\n      } catch (err) {/* nada */}\n    }\n    var stack;\n    try {\n      stack = _TraceKit.default.computeStackTrace(err).stack.map(function (frame) {\n        return {\n          filename: frame.url,\n          lineno: frame.line,\n          colno: frame.column,\n          function: frame.func || '?'\n        };\n      });\n    } catch (err) {\n      /* nada */\n    }\n    _sendToSentry({\n      message: message,\n      extra: {\n        stringPayload: stringPayload\n      },\n      exception: {\n        values: [{\n          type: err.type,\n          value: message,\n          stacktrace: {\n            frames: stack\n          }\n        }]\n      }\n    });\n  } catch (err) {\n    (0, _logError.default)('Failed to send', err);\n  }\n}\n\n/***/ }),\n\n/***/ \"./packages/@logrocket/utils/src/startsWith.js\":\n/*!*****************************************************!*\\\n  !*** ./packages/@logrocket/utils/src/startsWith.js ***!\n  \\*****************************************************/\n/***/ (function(__unused_webpack_module, exports) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = startsWith;\nfunction startsWith(value, search) {\n  var pos = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  return value && search && value.substring(pos, pos + search.length) === search;\n}\n\n/***/ }),\n\n/***/ \"./packages/logrocket/src/LogRocket.js\":\n/*!*********************************************!*\\\n  !*** ./packages/logrocket/src/LogRocket.js ***!\n  \\*********************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = exports.MAX_QUEUE_SIZE = void 0;\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _objectWithoutProperties2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\"));\nvar _network = _interopRequireDefault(__webpack_require__(/*! @logrocket/network */ \"./packages/@logrocket/network/src/index.js\"));\nvar _exceptions = __webpack_require__(/*! @logrocket/exceptions */ \"./packages/@logrocket/exceptions/src/index.js\");\nvar _console = _interopRequireDefault(__webpack_require__(/*! @logrocket/console */ \"./packages/@logrocket/console/src/index.js\"));\nvar _redux = __webpack_require__(/*! @logrocket/redux */ \"./packages/@logrocket/redux/src/index.js\");\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nvar MAX_QUEUE_SIZE = 1000;\nexports.MAX_QUEUE_SIZE = MAX_QUEUE_SIZE;\nvar considerIngestServerOption = function considerIngestServerOption() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    ingestServer = _ref.ingestServer,\n    options = (0, _objectWithoutProperties2.default)(_ref, [\"ingestServer\"]);\n  if (ingestServer) {\n    return _objectSpread({\n      serverURL: \"\".concat(ingestServer, \"/i\"),\n      statsURL: \"\".concat(ingestServer, \"/s\")\n    }, options);\n  }\n  return options;\n};\nvar LogRocket = /*#__PURE__*/function () {\n  function LogRocket() {\n    var _this = this;\n    (0, _classCallCheck2.default)(this, LogRocket);\n    this._buffer = [];\n    // TODO: tests for these exposed methods.\n    ['log', 'info', 'warn', 'error', 'debug'].forEach(function (method) {\n      _this[method] = function () {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        _this.addEvent('lr.core.LogEvent', function () {\n          var consoleOptions = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n          if (method === 'error' && consoleOptions.shouldAggregateConsoleErrors) {\n            _exceptions.Capture.captureMessage(_this, args[0], args, {}, true);\n          }\n          return {\n            logLevel: method.toUpperCase(),\n            args: args\n          };\n        }, {\n          shouldCaptureStackTrace: true\n        });\n      };\n    });\n    this._isInitialized = false;\n    this._installed = [];\n\n    // expose a callback to get the session URL from the global context\n    window._lr_surl_cb = this.getSessionURL.bind(this);\n  }\n  (0, _createClass2.default)(LogRocket, [{\n    key: \"addEvent\",\n    value: function addEvent(type, getMessage) {\n      var opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      var time = Date.now();\n      this._run(function (logger) {\n        logger.addEvent(type, getMessage, _objectSpread(_objectSpread({}, opts), {}, {\n          timeOverride: time\n        }));\n      });\n    }\n  }, {\n    key: \"onLogger\",\n    value: function onLogger(logger) {\n      this._logger = logger;\n      while (this._buffer.length > 0) {\n        var f = this._buffer.shift();\n        f(this._logger);\n      }\n    }\n  }, {\n    key: \"_run\",\n    value: function _run(f) {\n      if (this._isDisabled) {\n        return;\n      }\n      if (this._logger) {\n        f(this._logger);\n      } else {\n        if (this._buffer.length >= MAX_QUEUE_SIZE) {\n          this._isDisabled = true;\n          console.warn('LogRocket: script did not load. Check that you have a valid network connection.');\n          this.uninstall();\n          return;\n        }\n        this._buffer.push(f.bind(this));\n      }\n    }\n  }, {\n    key: \"init\",\n    value: function init(appID) {\n      var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      if (!this._isInitialized) {\n        var _opts$network;\n        var _opts$shouldAugmentNP = opts.shouldAugmentNPS,\n          shouldAugmentNPS = _opts$shouldAugmentNP === void 0 ? true : _opts$shouldAugmentNP,\n          _opts$shouldParseXHRB = opts.shouldParseXHRBlob,\n          shouldParseXHRBlob = _opts$shouldParseXHRB === void 0 ? false : _opts$shouldParseXHRB,\n          _opts$shouldDetectExc = opts.shouldDetectExceptions,\n          shouldDetectExceptions = _opts$shouldDetectExc === void 0 ? true : _opts$shouldDetectExc;\n        if (shouldDetectExceptions) {\n          this._installed.push((0, _exceptions.registerExceptions)(this));\n        }\n        this._installed.push((0, _network.default)(this, {\n          shouldAugmentNPS: !!shouldAugmentNPS,\n          shouldParseXHRBlob: !!shouldParseXHRBlob,\n          isDisabled: (opts === null || opts === void 0 ? void 0 : (_opts$network = opts.network) === null || _opts$network === void 0 ? void 0 : _opts$network.isEnabled) === false\n        }));\n        this._installed.push((0, _console.default)(this));\n        this._isInitialized = true;\n        this._run(function (logger) {\n          logger.init(appID, considerIngestServerOption(opts));\n        });\n      }\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      this._run(function (logger) {\n        logger.start();\n      });\n    }\n  }, {\n    key: \"uninstall\",\n    value: function uninstall() {\n      this._installed.forEach(function (f) {\n        return f();\n      });\n      this._buffer = [];\n      this._run(function (logger) {\n        logger.uninstall();\n      });\n    }\n  }, {\n    key: \"identify\",\n    value: function identify(id, opts) {\n      this._run(function (logger) {\n        logger.identify(id, opts);\n      });\n    }\n  }, {\n    key: \"startNewSession\",\n    value: function startNewSession() {\n      this._run(function (logger) {\n        logger.startNewSession();\n      });\n    }\n  }, {\n    key: \"track\",\n    value: function track(customEventName, eventProperties) {\n      this._run(function (logger) {\n        logger.track(customEventName, eventProperties);\n      });\n    }\n  }, {\n    key: \"getSessionURL\",\n    value: function getSessionURL(cb) {\n      if (typeof cb !== 'function') {\n        throw new Error('LogRocket: must pass callback to getSessionURL()');\n      }\n      this._run(function (logger) {\n        if (logger.getSessionURL) {\n          logger.getSessionURL(cb);\n        } else {\n          cb(logger.recordingURL);\n        }\n      });\n    }\n  }, {\n    key: \"trackScrollEvent\",\n    value: function trackScrollEvent(target) {\n      // Only reigster the event if the core logger is available,\n      //  otherwise we won't yet have captured the target element:\n      if (this._logger) {\n        this._logger.trackScrollEvent(target);\n      }\n    }\n  }, {\n    key: \"getVersion\",\n    value: function getVersion(cb) {\n      this._run(function (logger) {\n        cb(logger.version);\n      });\n    }\n  }, {\n    key: \"captureMessage\",\n    value: function captureMessage(message) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      _exceptions.Capture.captureMessage(this, message, [message], options);\n    }\n  }, {\n    key: \"captureException\",\n    value: function captureException(exception) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      _exceptions.Capture.captureException(this, exception, options);\n    }\n  }, {\n    key: \"version\",\n    get: function get() {\n      return this._logger && this._logger.version;\n    }\n  }, {\n    key: \"sessionURL\",\n    get: function get() {\n      return this._logger && this._logger.recordingURL;\n    }\n  }, {\n    key: \"recordingURL\",\n    get: function get() {\n      return this._logger && this._logger.recordingURL;\n    }\n  }, {\n    key: \"recordingID\",\n    get: function get() {\n      return this._logger && this._logger.recordingID;\n    }\n  }, {\n    key: \"threadID\",\n    get: function get() {\n      return this._logger && this._logger.threadID;\n    }\n  }, {\n    key: \"tabID\",\n    get: function get() {\n      return this._logger && this._logger.tabID;\n    }\n  }, {\n    key: \"reduxEnhancer\",\n    value: function reduxEnhancer() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return (0, _redux.createEnhancer)(this, options);\n    }\n  }, {\n    key: \"reduxMiddleware\",\n    value: function reduxMiddleware() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return (0, _redux.createMiddleware)(this, options);\n    }\n  }, {\n    key: \"isDisabled\",\n    get: function get() {\n      return !!(this._isDisabled || this._logger && this._logger._isDisabled);\n    }\n  }]);\n  return LogRocket;\n}();\nexports[\"default\"] = LogRocket;\n\n/***/ }),\n\n/***/ \"./packages/logrocket/src/makeLogRocket.js\":\n/*!*************************************************!*\\\n  !*** ./packages/logrocket/src/makeLogRocket.js ***!\n  \\*************************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = makeLogRocket;\nvar _LogRocket = _interopRequireDefault(__webpack_require__(/*! ./LogRocket */ \"./packages/logrocket/src/LogRocket.js\"));\nvar REACT_NATIVE_NOTICE = 'LogRocket on React Native requires the LogRocket React Native specific SDK. See setup guide here https://docs.logrocket.com/reference/react-native.';\nvar makeNoopPolyfill = function makeNoopPolyfill() {\n  return {\n    init: function init() {},\n    uninstall: function uninstall() {},\n    log: function log() {},\n    info: function info() {},\n    warn: function warn() {},\n    error: function error() {},\n    debug: function debug() {},\n    addEvent: function addEvent() {},\n    identify: function identify() {},\n    start: function start() {},\n    get threadID() {\n      return null;\n    },\n    get recordingID() {\n      return null;\n    },\n    get recordingURL() {\n      return null;\n    },\n    reduxEnhancer: function reduxEnhancer() {\n      return function (store) {\n        return function () {\n          return store.apply(void 0, arguments);\n        };\n      };\n    },\n    reduxMiddleware: function reduxMiddleware() {\n      return function () {\n        return function (next) {\n          return function (action) {\n            return next(action);\n          };\n        };\n      };\n    },\n    track: function track() {},\n    getSessionURL: function getSessionURL() {},\n    getVersion: function getVersion() {},\n    startNewSession: function startNewSession() {},\n    onLogger: function onLogger() {},\n    setClock: function setClock() {},\n    captureMessage: function captureMessage() {},\n    captureException: function captureException() {}\n  };\n};\nfunction makeLogRocket() {\n  var getLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function () {};\n  var getNoopPolyfill = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : makeNoopPolyfill;\n  var getInstance = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return new _LogRocket.default();\n  };\n  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    throw new Error(REACT_NATIVE_NOTICE);\n  }\n  if (typeof window !== 'undefined') {\n    if (window._disableLogRocket) {\n      return getNoopPolyfill();\n    }\n    if (window.MutationObserver && window.WeakMap) {\n      // Save window globals that we rely on.\n      window._lrMutationObserver = window.MutationObserver;\n      var instance = getInstance();\n      getLogger(instance);\n      return instance;\n    }\n  }\n  return makeNoopPolyfill();\n}\n\n/***/ }),\n\n/***/ \"./packages/logrocket/src/setup.js\":\n/*!*****************************************!*\\\n  !*** ./packages/logrocket/src/setup.js ***!\n  \\*****************************************/\n/***/ (function(__unused_webpack_module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.getDomainsAndEnv = getDomainsAndEnv;\nexports.setupBaseSDKCONFIG = setupBaseSDKCONFIG;\nexports[\"default\"] = setup;\nvar _objectWithoutProperties2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\"));\nvar _makeLogRocket = _interopRequireDefault(__webpack_require__(/*! ./makeLogRocket */ \"./packages/logrocket/src/makeLogRocket.js\"));\nvar CDN_SERVER_MAP = {\n  'cdn.logrocket.com': 'https://r.logrocket.io',\n  'cdn.logrocket.io': 'https://r.logrocket.io',\n  'cdn.lr-ingest.io': 'https://r.lr-ingest.io',\n  'cdn.lr-in.com': 'https://r.lr-in.com',\n  'cdn.lr-in-prod.com': 'https://r.lr-in-prod.com',\n  'cdn.lr-ingest.com': 'https://r.lr-ingest.com',\n  'cdn.ingest-lr.com': 'https://r.ingest-lr.com',\n  'cdn.lr-intake.com': 'https://r.lr-intake.com',\n  'cdn.intake-lr.com': 'https://r.intake-lr.com',\n  'cdn.logr-ingest.com': 'https://r.logr-ingest.com',\n  'cdn.lrkt-in.com': 'https://r.lrkt-in.com',\n  'cdn.lgrckt-in.com': 'https://r.lgrckt-in.com',\n  'cdn-staging.logrocket.io': 'https://staging-i.logrocket.io',\n  'cdn-staging.lr-ingest.io': 'https://staging-i.lr-ingest.io',\n  'cdn-staging.lr-in.com': 'https://staging-i.lr-in.com',\n  'cdn-staging.lr-in-prod.com': 'https://staging-i.lr-in-prod.com',\n  'cdn-staging.lr-ingest.com': 'https://staging-i.lr-ingest.com',\n  'cdn-staging.ingest-lr.com': 'https://staging-i.ingest-lr.com',\n  'cdn-staging.lr-intake.com': 'https://staging-i.lr-intake.com',\n  'cdn-staging.intake-lr.com': 'https://staging-i.intake-lr.com',\n  'cdn-staging.logr-ingest.com': 'https://staging-i.logr-ingest.com',\n  'cdn-staging.lrkt-in.com': 'https://staging-i.lrkt-in.com',\n  'cdn-staging.lgrckt-in.com': 'https://staging-i.lgrckt-in.com'\n};\nfunction envFromHostname(hostname) {\n  if (hostname.startsWith('cdn-staging')) {\n    return 'staging';\n  } else if (hostname.startsWith('localhost')) {\n    return 'development';\n  } else {\n    return 'prod';\n  }\n}\nfunction getDomainsAndEnv(sdkVersion) {\n  if (sdkVersion === 'script' || sdkVersion === 'shopify-pixel') {\n    // We cannot rely on the DEPLOY_ENV config for script tag usage: we deploy the script built for staging to prod to\n    // improve deployment times, so the DEPLOY_ENV is always 'staging' for script usage! Instead we default to 'prod'\n    // and inspect the script's URL to determine if the environment should instead be staging or development.\n    try {\n      // eslint-disable-next-line compat/compat\n      var scriptTag = document.currentScript;\n      var matches = scriptTag.src.match(/^(https?:\\/\\/([^\\\\]+))\\/.+$/);\n      var scriptHostname = matches && matches[2];\n      if (scriptHostname && CDN_SERVER_MAP[scriptHostname]) {\n        return {\n          scriptEnv: envFromHostname(scriptHostname),\n          scriptOrigin: matches && matches[1],\n          scriptIngest: CDN_SERVER_MAP[scriptHostname]\n        };\n      }\n    } catch (_) {\n      /* no-op */\n    }\n\n    // If we cannot determine the env based on the script's URL we default to prod.\n    return {\n      scriptEnv: 'prod',\n      scriptOrigin: 'https://cdn.logrocket.io'\n    };\n  } else {\n    // NPM package\n    return {\n      scriptEnv: undefined,\n      scriptOrigin:  false ? 0 : 'https://cdn.lgrckt-in.com',\n      scriptIngest:  false ? 0 : 'https://r.lgrckt-in.com'\n    };\n  }\n}\nfunction setupBaseSDKCONFIG(ingestServer) {\n  if (typeof window.__SDKCONFIG__ === 'undefined') {\n    window.__SDKCONFIG__ = {};\n  }\n  window.__SDKCONFIG__.serverURL = \"\".concat(ingestServer, \"/i\");\n  window.__SDKCONFIG__.statsURL = \"\".concat(ingestServer, \"/s\");\n}\nfunction setup() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    enterpriseServer = _ref.enterpriseServer,\n    _ref$sdkVersion = _ref.sdkVersion,\n    sdkVersion = _ref$sdkVersion === void 0 ? \"10.0.0\" : _ref$sdkVersion,\n    opts = (0, _objectWithoutProperties2.default)(_ref, [\"enterpriseServer\", \"sdkVersion\"]);\n  var _getDomainsAndEnv = getDomainsAndEnv(sdkVersion),\n    scriptEnv = _getDomainsAndEnv.scriptEnv,\n    scriptOrigin = _getDomainsAndEnv.scriptOrigin,\n    scriptIngest = _getDomainsAndEnv.scriptIngest;\n  var sdkServer = opts.sdkServer || enterpriseServer;\n  var ingestServer = opts.ingestServer || enterpriseServer || scriptIngest;\n  var instance = (0, _makeLogRocket.default)(function () {\n    var script = document.createElement('script');\n    if (ingestServer) {\n      setupBaseSDKCONFIG(ingestServer);\n      window.__SDKCONFIG__.scriptEnv = scriptEnv;\n    }\n    if (sdkServer) {\n      script.src = \"\".concat(sdkServer, \"/logger.min.js\");\n    } else if (window.__SDKCONFIG__ && window.__SDKCONFIG__.loggerURL) {\n      script.src = window.__SDKCONFIG__.loggerURL;\n    } else if (window._lrAsyncScript) {\n      script.src = window._lrAsyncScript;\n    } else {\n      script.src = \"\".concat(scriptOrigin, \"/logger-1.min.js\");\n    }\n    script.async = true;\n    document.head.appendChild(script);\n    script.onload = function () {\n      // Brave browser: Advertises its user-agent as Chrome ##.##... then\n      // loads logger.min.js, but blocks the execution of the script\n      // causing _LRlogger to be undefined.  Let's make sure its there first.\n      if (typeof window._LRLogger === 'function') {\n        setTimeout(function () {\n          instance.onLogger(new window._LRLogger({\n            sdkVersion: sdkVersion\n          }));\n        });\n      } else {\n        console.warn('LogRocket: script execution has been blocked by a product or service.');\n        instance.uninstall();\n      }\n    };\n    script.onerror = function () {\n      console.warn('LogRocket: script could not load. Check that you have a valid network connection.');\n      instance.uninstall();\n    };\n  });\n  return instance;\n}\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js\":\n/*!*****************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/arrayLikeToArray.js ***!\n  \\*****************************************************************/\n/***/ (function(module) {\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nmodule.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js\":\n/*!******************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js ***!\n  \\******************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar arrayLikeToArray = __webpack_require__(/*! ./arrayLikeToArray.js */ \"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js\");\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\nmodule.exports = _arrayWithoutHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/classCallCheck.js\":\n/*!***************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/classCallCheck.js ***!\n  \\***************************************************************/\n/***/ (function(module) {\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/createClass.js\":\n/*!************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/createClass.js ***!\n  \\************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar toPropertyKey = __webpack_require__(/*! ./toPropertyKey.js */ \"./node_modules/@babel/runtime/helpers/toPropertyKey.js\");\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/defineProperty.js\":\n/*!***************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/defineProperty.js ***!\n  \\***************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar toPropertyKey = __webpack_require__(/*! ./toPropertyKey.js */ \"./node_modules/@babel/runtime/helpers/toPropertyKey.js\");\nfunction _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\":\n/*!**********************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/interopRequireDefault.js ***!\n  \\**********************************************************************/\n/***/ (function(module) {\n\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/iterableToArray.js\":\n/*!****************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/iterableToArray.js ***!\n  \\****************************************************************/\n/***/ (function(module) {\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nmodule.exports = _iterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/nonIterableSpread.js\":\n/*!******************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/nonIterableSpread.js ***!\n  \\******************************************************************/\n/***/ (function(module) {\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableSpread, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\":\n/*!************************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/objectWithoutProperties.js ***!\n  \\************************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar objectWithoutPropertiesLoose = __webpack_require__(/*! ./objectWithoutPropertiesLoose.js */ \"./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\":\n/*!*****************************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js ***!\n  \\*****************************************************************************/\n/***/ (function(module) {\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/toConsumableArray.js\":\n/*!******************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/toConsumableArray.js ***!\n  \\******************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar arrayWithoutHoles = __webpack_require__(/*! ./arrayWithoutHoles.js */ \"./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js\");\nvar iterableToArray = __webpack_require__(/*! ./iterableToArray.js */ \"./node_modules/@babel/runtime/helpers/iterableToArray.js\");\nvar unsupportedIterableToArray = __webpack_require__(/*! ./unsupportedIterableToArray.js */ \"./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js\");\nvar nonIterableSpread = __webpack_require__(/*! ./nonIterableSpread.js */ \"./node_modules/@babel/runtime/helpers/nonIterableSpread.js\");\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\nmodule.exports = _toConsumableArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/toPrimitive.js\":\n/*!************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/toPrimitive.js ***!\n  \\************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar _typeof = (__webpack_require__(/*! ./typeof.js */ \"./node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"]);\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nmodule.exports = _toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/toPropertyKey.js\":\n/*!**************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/toPropertyKey.js ***!\n  \\**************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar _typeof = (__webpack_require__(/*! ./typeof.js */ \"./node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"]);\nvar toPrimitive = __webpack_require__(/*! ./toPrimitive.js */ \"./node_modules/@babel/runtime/helpers/toPrimitive.js\");\nfunction _toPropertyKey(arg) {\n  var key = toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nmodule.exports = _toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/typeof.js\":\n/*!*******************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!\n  \\*******************************************************/\n/***/ (function(module) {\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(obj);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ }),\n\n/***/ \"./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js\":\n/*!***************************************************************************!*\\\n  !*** ./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js ***!\n  \\***************************************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar arrayLikeToArray = __webpack_require__(/*! ./arrayLikeToArray.js */ \"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js\");\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\nmodule.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/global */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.g = (function() {\n/******/ \t\t\tif (typeof globalThis === 'object') return globalThis;\n/******/ \t\t\ttry {\n/******/ \t\t\t\treturn this || new Function('return this')();\n/******/ \t\t\t} catch (e) {\n/******/ \t\t\t\tif (typeof window === 'object') return window;\n/******/ \t\t\t}\n/******/ \t\t})();\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be in strict mode.\n!function() {\n\"use strict\";\nvar exports = __webpack_exports__;\n/*!**********************************************!*\\\n  !*** ./packages/logrocket/src/module-npm.js ***!\n  \\**********************************************/\n\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _setup = _interopRequireDefault(__webpack_require__(/*! ./setup */ \"./packages/logrocket/src/setup.js\"));\nvar instance = (0, _setup.default)();\nvar _default = instance;\nexports[\"default\"] = _default;\n}();\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["Clarity", "init", "projectId", "c", "window", "l", "document", "a", "r", "i", "getElementById", "q", "push", "arguments", "t", "createElement", "async", "src", "id", "y", "getElementsByTagName", "parentNode", "insertBefore", "error", "injectScript", "setTag", "key", "value", "clarity", "identify", "customerId", "customSessionId", "customPageId", "friendlyName", "consent", "upgrade", "reason", "event", "eventName", "factory", "__webpack_modules__", "__unused_webpack_module", "exports", "__webpack_require__", "_interopRequireDefault", "Object", "defineProperty", "_default", "default", "logger", "unsubFunctions", "for<PERSON>ach", "method", "_enhanceFunc", "console", "_len", "length", "args", "Array", "_key", "addEvent", "consoleOptions", "isEnabled", "_typeof2", "shouldAggregateConsoleErrors", "secondArg", "concat", "_unused", "_exceptions", "Capture", "captureMessage", "logLevel", "toUpperCase", "unsubFunction", "message", "messageArgs", "options", "isConsole", "data", "exceptionType", "browserHref", "location", "href", "_scrubException", "scrubException", "captureException", "exception", "preppedTrace", "trace", "_TraceKit", "computeStackTrace", "errorType", "name", "addEventOptions", "_stackTrace", "_stackTraceFromError", "_typeof", "enumerable", "get", "_registerExceptions", "obj", "nodeInterop", "__esModule", "cache", "_getRequireWildcardCache", "has", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "prototype", "hasOwnProperty", "call", "desc", "set", "_interopRequireWildcard", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "_classCallCheck2", "_createClass2", "objectPrototype", "isFunction", "what", "fill", "replacement", "track", "orig", "_window", "g", "self", "Handler", "_ref", "this", "_error<PERSON><PERSON><PERSON>", "bind", "_ignoreOnError", "_wrappedBuiltIns", "report", "subscribe", "_instrumentTryCatch", "builtin", "unsubscribe", "shift", "_this", "setTimeout", "func", "wrap", "apply", "_before", "object", "__lr__", "__lr_wrapper__", "isExtensible", "e", "wrapped", "deep", "_ignoreNextOnError", "property", "__inner__", "wrappedBuiltIns", "wrapTimeFn", "fn", "originalCallback", "requestAnimationFrame", "cb", "global", "proto", "eventTargets", "evtName", "capture", "secure", "handleEvent", "err", "before", "evt", "wrappedFn", "$", "j<PERSON><PERSON><PERSON>", "ready", "raven", "_raven", "errorReport", "<PERSON><PERSON><PERSON><PERSON>", "Error", "addEventListener", "removeEventListener", "uninstall", "makeNotNull", "val", "stack", "map", "frame", "lineNumber", "line", "columnNumber", "column", "fileName", "url", "functionName", "_toConsumableArray2", "_registerXHR", "interceptors", "makeInterceptor", "fetch", "fetchId", "reversedInterceptors", "reduce", "array", "interceptor", "promise", "Promise", "resolve", "request", "requestError", "then", "res", "setActive", "_err", "_ref2", "response", "responseError", "attach", "env", "isPolyfill", "polyfill", "_len2", "_key2", "<PERSON><PERSON><PERSON><PERSON>", "register", "index", "indexOf", "splice", "clear", "config", "isReactNative", "isDisabled", "shouldAugmentNPS", "shouldParseXHRBlob", "ignoredNetwork", "truncate", "str", "getPrototypeOf", "JSON", "stringify", "beginning", "substring", "addRequest", "reqId", "_ref$isEnabled", "_ref$requestSanitizer", "requestSanitizer", "f", "sanitized", "_objectSpread", "headers", "_mapValues", "headerValue", "body", "referrer", "mode", "credentials", "addResponse", "status", "responseType", "_ref2$isEnabled", "_ref2$responseSanitiz", "responseSanitizer", "isIgnored", "unsubFetch", "_registerFetch", "unsubXHR", "unsubIonic", "_registerIonic", "registerIonic", "unsubNetworkInformation", "_registerNetworkInformation", "_defineProperty2", "ownKeys", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "target", "source", "getOwnPropertyDescriptors", "defineProperties", "LOGROCKET_FETCH_LABEL", "fetchMethodMap", "unregister", "_fetchIntercept", "p", "Request", "clonedText", "clone", "text", "pluck<PERSON><PERSON><PERSON><PERSON>ields", "req", "reject", "_response", "responseClone", "responseTextPromise", "responseHash", "type", "stringifyHeaders", "TextDecoder", "reader", "<PERSON><PERSON><PERSON><PERSON>", "utf8Decoder", "bodyContents", "read", "readResponseBody", "done", "chunk", "decode", "stream", "catch", "DOMException", "result", "makeObjectFromHeaders", "arg", "mergeHeaders", "serializeQueryParams", "params", "encode", "serializeObject", "appendQueryParamsString", "processData", "_cordova", "_cordova$plugin", "_window$ionic", "ionicHttp", "<PERSON><PERSON>", "plugin", "http", "ionicMap", "unsubscribedFromIonic", "platforms", "ionic", "some", "UNSUPPORTED_PLATFORMS", "originalSendRequest", "sendRequest", "handleResponse", "_protectFunc", "isSuccess", "ionicReqId", "LOGROCKET_IONIC_LABEL", "_responseHash", "success", "failure", "currentId", "ionicIdCounter", "modifiedOptions", "serializer", "checkForValidStringValue", "VALID_SERIALIZERS", "getDataSerializer", "UNSUPPORTED_SERIALIZERS", "filePath", "followRedirect", "checkKeyValuePairObject", "STRING_SET", "VALID_HTTP_METHODS", "STRING_ARRAY_SET", "connectTimeout", "readTimeout", "timeout", "handleMissingOptions", "modifiedUrl", "mergedHeaders", "requestHeaders", "globalHeaders", "getHeaders", "hostHeaders", "_URL", "URL", "host", "getMatchingHostHeaders", "getMergedHeaders", "requestHash", "_requestHash", "_createForOfIteratorHelper", "o", "allowArrayLike", "it", "Symbol", "iterator", "isArray", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "constructor", "from", "test", "_unsupportedIterableToArray", "F", "s", "_e", "TypeError", "normalCompletion", "didErr", "step", "next", "_e2", "return", "arr", "len", "arr2", "Set", "FORM_DATA", "EMPTY_SET", "ALLOWED_DATA_TYPES", "utf8", "u<PERSON><PERSON><PERSON>", "json", "raw", "list", "fieldName", "join", "trim", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onInvalidValueMessage", "_i", "_Object$keys", "defaultHeaders", "serializeValue", "encodeURIComponent", "serializeIdentifier", "parent<PERSON><PERSON>", "serializeArray", "_step", "parts", "_iterator", "identifier", "_URL2", "pathname", "search", "hash", "protocol", "dataSerializer", "currentDataType", "allowedDataTypes", "getAllowedDataTypes", "allowedInstanceTypes", "getAllowedInstanceTypes", "size", "isCorrectInstanceType", "lastStatus", "sendNetworkInformation", "newStatus", "online", "navigator", "onLine", "effectiveType", "connection", "EFFECTIVE_TYPE_VALS", "shouldBeActive", "isActive", "_ref$shouldAugmentNPS", "_ref$shouldParseXHRBl", "_XHR", "XMLHttpRequest", "xhrMap", "unsubscribedFromXhr", "LOGROCKET_XHR_LABEL", "_lrXMLHttpRequest", "mozAnon", "mozSystem", "xhrObject", "xhrId", "currentXHRId", "openOriginal", "open", "sendOriginal", "send", "_nps", "WOOTRIC_RESPONSES_REGEX", "logrocketSessionURL", "recordingURL", "searchParams", "url<PERSON>bj", "responseText", "feedback", "currentXHR", "DELIGHTED_RESPONSES_REGEX", "DELIGHTED_FEEDBACK_PREFIX", "split", "dataString", "_startsWith", "isEmpty", "headerValues", "header", "xhrListeners", "readystatechange", "readyState", "getAllResponseHeaders", "previous", "current", "headerParts", "_shouldCloneResponse", "parse", "responseXML", "Blob", "blob<PERSON><PERSON><PERSON>", "FileReader", "readAsText", "onload", "variable", "dateNow", "Date", "now", "loadTime", "performance", "_ref$stateSanitizer", "stateSanitizer", "_ref$actionSanitizer", "actionSanitizer", "createStore", "reducer", "initialState", "enhancer", "store", "originalDispatch", "dispatch", "storeId", "storeIdCounter", "sanitizedState", "getState", "state", "action", "start", "_now", "duration", "sanitizedAction", "stateDelta", "_createEnhancer", "_createMiddleware", "TraceKit", "collectWindowErrors", "debug", "_slice", "UNKNOWN_FUNCTION", "ERROR_TYPES_RE", "getLocationHref", "_old<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onErrorHandlerInstalled", "handlers", "lastArgs", "lastException", "lastExceptionStack", "notifyHandlers", "isWindowError", "inner", "traceKitWindowOnError", "lineNo", "colNo", "ex", "augmentStackTraceWithInitialElement", "processLastException", "groups", "msg", "match", "_lastExceptionStack", "_lastArgs", "rethrow", "incomplete", "handler", "onerror", "computeStackTraceFromStackProp", "element", "chrome", "gecko", "winjs", "lines", "j", "exec", "isNative", "stackInfo", "initial", "unshift", "partial", "computeStackTraceByWalkingCallerChain", "depth", "item", "funcs", "recursion", "curr", "caller", "input", "sourceURL", "createUnsubListener", "_value", "NO_OP_UNREGISTER", "shim", "_h$get", "original", "h", "_createUnsubListener", "_error", "onFail", "_lrdebug", "payload", "_logError", "_sendTelemetryData", "optionalScalars", "field", "isScalar", "_step2", "_iterator2", "optionalMaps", "_field", "dirty", "scrubbed", "sendTelemetry", "more", "extra", "appID", "Math", "random", "_sendToSentry", "stringPayload", "filename", "lineno", "colno", "function", "values", "stacktrace", "frames", "SCRIPT_VERSION", "_window$__SDKCONFIG__", "platform", "userAgent", "release", "environment", "__SDKCONFIG__", "scriptEnv", "rejectReason", "pos", "exports2", "MAX_QUEUE_SIZE", "_objectWithoutProperties2", "_network", "_console", "_redux", "LogRocket", "_buffer", "shouldCaptureStackTrace", "_isInitialized", "_installed", "_lr_surl_cb", "getSessionURL", "getMessage", "opts", "time", "_run", "timeOverride", "_logger", "_isDisabled", "warn", "_opts$network", "_opts$shouldAugmentNP", "_opts$shouldParseXHRB", "_opts$shouldDetectExc", "shouldDetectExceptions", "registerExceptions", "network", "ingestServer", "serverURL", "statsURL", "considerIngestServerOption", "startNewSession", "customEventName", "eventProperties", "trackScrollEvent", "version", "recordingID", "threadID", "tabID", "createEnhancer", "createMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "getNoopPolyfill", "makeNoopPolyfill", "getInstance", "_LogRocket", "product", "REACT_NATIVE_NOTICE", "_disableLogRocket", "MutationObserver", "_lrMutationObserver", "instance", "log", "info", "reduxEnhancer", "reduxMiddleware", "getVersion", "<PERSON><PERSON><PERSON>ger", "setClock", "getDomainsAndEnv", "setupBaseSDKCONFIG", "enterpriseServer", "_ref$sdkVersion", "sdkVersion", "_getDomainsAndEnv", "<PERSON><PERSON><PERSON><PERSON>", "scriptIngest", "sdkServer", "_makeLogRocket", "script", "loggerURL", "_lrAsyncScript", "head", "append<PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON>", "CDN_SERVER_MAP", "matches", "currentScript", "scriptHostname", "hostname", "startsWith", "_", "module", "module2", "__unused_webpack_exports", "arrayLikeToArray", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "configurable", "writable", "protoProps", "staticProps", "iter", "objectWithoutPropertiesLoose", "excluded", "sourceSymbolKeys", "propertyIsEnumerable", "sourceKeys", "arrayWithoutHoles", "iterableToArray", "unsupportedIterableToArray", "nonIterableSpread", "hint", "prim", "toPrimitive", "String", "Number", "__webpack_module_cache__", "moduleId", "cachedModule", "globalThis", "Function", "__webpack_exports__"], "mappings": "AAEK,MAACA,EAAU,CACZ,IAAAC,CAAKC,ICHF,SAAsBA,GACxB,IAgBC,OAfWC,EAcNC,OAdSC,EAcDC,SAdIC,EAcM,UAdHC,EAcc,SAdXC,EAcqBP,OAbrCG,EAAEK,eAAe,oBAGpBP,EAAEI,GAAKJ,EAAEI,IACP,YACGJ,EAAEI,GAAGI,EAAIR,EAAEI,GAAGI,GAAK,IAAIC,KAAKC,UAC3C,GACcC,EAAAT,EAAEU,cAAcP,IAClBQ,MAAQ,EACRF,EAAAG,IAAM,8BAAgCR,EAAI,WAC5CK,EAAEI,GAAK,kBACPC,EAAId,EAAEe,qBAAqBZ,GAAG,IAC5Ba,WAAWC,aAAaR,EAAGK,IAGpC,OAAMI,GACH,MACH,CAlBG,IAAWpB,EAAGE,EAAGE,EAAGC,EAAGC,EAAGK,EAAGK,CAmBnC,CDjBQK,CAAatB,EAChB,EAED,MAAAuB,CAAOC,EAAKC,GACDvB,OAAAwB,QAAQ,MAAOF,EAAKC,EAC9B,EAED,QAAAE,CAASC,EAAYC,EAAiBC,EAAcC,GAChD7B,OAAOwB,QAAQ,WAAYE,EAAYC,EAAiBC,EAAcC,EACzE,EAED,OAAAC,CAAQA,GAAU,GACP9B,OAAAwB,QAAQ,UAAWM,EAC7B,EAED,OAAAC,CAAQC,GACGhC,OAAAwB,QAAQ,UAAWQ,EAC7B,EAED,KAAAC,CAAMC,GACKlC,OAAAwB,QAAQ,QAASU,EAC3B,6DEzB4CC,EASxC,WACT,OAAiB,WACP,IAAIC,EAAuB,CAE/B,6CAAA,SAIUC,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EACrB,IACIK,EADmBH,EAAuBD,EAA6C,yDAC3DK,QAChCN,EAAiB,QAAIK,CAAA,EAIf,uDAAA,SAIUN,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAMjB,SAAyBO,GACvB,IAAIC,EAAiB,GAgCrB,MA/Bc,CAAC,MAAO,OAAQ,OAAQ,QAAS,SACvCC,SAAQ,SAAUC,GACxBF,EAAetC,QAASyC,EAAaL,SAASM,QAASF,GAAQ,WAC7D,IAAA,IAASG,EAAO1C,UAAU2C,OAAQC,EAAO,IAAIC,MAAMH,GAAOI,EAAO,EAAGA,EAAOJ,EAAMI,IAC1EF,EAAAE,GAAQ9C,UAAU8C,GAElBV,EAAAW,SAAS,oBAAoB,WAC9B,IAAAC,EAAiBhD,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACtFiD,EAAYD,EAAeC,aACU,cAAjCC,EAASf,SAASc,KAAiD,IAAtBA,EAAUV,KAAmC,IAAdU,EAC3E,OAAA,KAEL,GAAW,UAAXV,GAAsBS,EAAeG,6BAEnC,GAAEP,GAAQA,EAAKD,QAAU,GAAiB,UAAZC,EAAK,GAAgB,CACrD,IAAIQ,EAAY,GACZ,IACFA,EAAY,IAAIC,OAAOT,EAAK,UACrBU,GAAS,CAClBC,EAAYC,QAAQC,eAAerB,EAAQ,GAAGiB,OAAOT,EAAK,IAAIS,OAAOD,GAAYR,EAAM,CAAA,GAAI,EAAI,MAEnFW,EAAAC,QAAQC,eAAerB,EAAQQ,EAAK,GAAIA,EAAM,CAAC,GAAG,GAG3D,MAAA,CACLc,SAAUnB,EAAOoB,cACjBf,OACF,GACD,IACD,IAEG,WACUP,EAAAC,SAAQ,SAAUsB,GAC/B,OAAOA,GAAc,GAEzB,CAAA,EA1CF,IAAIV,EAAWnB,EAAuBD,EAAyD,oDAC3FU,EAAeT,EAAuBD,EAA4D,mDAClGyB,EAAczB,EAAiD,gDAyCnE,EAIM,kDAAA,SAIUF,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQ4B,eAQC,SAAerB,EAAQyB,EAASC,GACnC,IAAAC,EAAU/D,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC/EgE,EAAYhE,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,IAAmBA,UAAU,GAC3EiE,EAAO,CACTC,cAAeF,EAAY,UAAY,UACvCH,UACAC,cACAK,YAAa5E,OAAO6E,SAAW7E,OAAO6E,SAASC,KAAO,KAExD,EAAIC,EAAgBC,gBAAgBN,EAAMF,GACnC3B,EAAAW,SAAS,qBAAqB,WAC5B,OAAAkB,CAAA,GACR,EAnBHpC,EAAQ2C,iBAqBC,SAAiBpC,EAAQqC,GAC5B,IAAAV,EAAU/D,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC/E0E,EAAe1E,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,KACnFkE,EAAgBlE,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,SACpF2E,EAAQD,GAAgBE,EAAUzC,QAAQ0C,kBAAkBJ,GAC5DR,EAAO,CACTC,gBACAY,UAAWH,EAAMI,KACjBlB,QAASc,EAAMd,QACfM,YAAa5E,OAAO6E,SAAW7E,OAAO6E,SAASC,KAAO,KAExD,EAAIC,EAAgBC,gBAAgBN,EAAMF,GAC1C,IAAIiB,EAAkB,CACpBC,aAAc,EAAGC,EAAqB/C,SAASwC,IAE1CvC,EAAAW,SAAS,qBAAqB,WAC5B,OAAAkB,IACNe,EAAe,EArCpB,IAAIV,EAAkBxC,EAA+D,qDACjF8C,EAAY7C,EAAuBD,EAAyD,gDAC5FoD,EAAuBnD,EAAuBD,EAAiD,+DAoCnG,EAIM,gDAAA,SAIUF,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEACjGqD,EAAUrD,EAAyD,mDAChEE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAEFkB,OAAAC,eAAeJ,EAAS,qBAAuB,CACpDuD,YAAY,EACZC,IAAK,WACH,OAAOC,EAAoBnD,OAAA,IAG/BN,EAAQ2B,aAAU,EAClB,IAAI8B,EAAsBvD,EAAuBD,EAAgD,+DAC7F0B,EAGK,SAAwB+B,EAAKC,GAAmC,GAAAD,GAAOA,EAAIE,WAAqB,OAAAF,EAAW,GAAQ,OAARA,GAAiC,WAAjBJ,EAAQI,IAAoC,mBAARA,EAA6B,MAAA,CAAEpD,QAASoD,GAAa,IAAAG,EAAQC,EAAyBH,GAAc,GAAIE,GAASA,EAAME,IAAIL,GAAe,OAAAG,EAAML,IAAIE,GAAQ,IAAIM,EAAS,CAAC,EAAOC,EAAwB9D,OAAOC,gBAAkBD,OAAO+D,yBAA0B,IAAA,IAASlF,KAAO0E,EAAW,GAAQ,YAAR1E,GAAqBmB,OAAOgE,UAAUC,eAAeC,KAAKX,EAAK1E,GAAM,CAAE,IAAIsF,EAAOL,EAAwB9D,OAAO+D,yBAAyBR,EAAK1E,GAAO,KAAUsF,IAASA,EAAKd,KAAOc,EAAKC,KAAepE,OAAAC,eAAe4D,EAAQhF,EAAKsF,GAAuBN,EAAAhF,GAAO0E,EAAI1E,EAAM,CAA0E,OAApEgF,EAAO1D,QAAUoD,EAASG,GAAeA,EAAAU,IAAIb,EAAKM,GAAkBA,CAAA,CAH/wBQ,CAAwBvE,EAAqC,oDAE3E,SAAS6D,EAAyBH,GAAmB,GAAmB,mBAAZc,QAA+B,OAAA,KAAU,IAAAC,MAAwBD,QAAeE,MAAuBF,QAAmB,OAAAX,EAA2B,SAAkCH,GAAe,OAAOA,EAAcgB,EAAmBD,IAAsBf,EAAW,CAD3U3D,EAAQ2B,QAAUA,CAEmxB,EAI/xB,sDAAA,SAIU5B,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EACrB,IAAI4E,EAAmB1E,EAAuBD,EAAiE,4DAC3G4E,EAAgB3E,EAAuBD,EAA8D,yDACrG8C,EAAY7C,EAAuBD,EAAyD,gDAiB5F6E,EAAkB3E,OAAOgE,UAI7B,SAASY,EAAWC,GAClB,MAAuB,mBAATA,CAAS,CAsCzB,SAASC,EAAKvB,EAAKR,EAAMgC,EAAaC,GAChC,IAAAC,EAAO1B,EAAIR,GACXQ,EAAAR,GAAQgC,EAAYE,GACpBD,GACFA,EAAMjH,KAAK,CAACwF,EAAKR,EAAMkC,GACzB,CAEF,IAAIC,EAA4B,oBAAX3H,OAAyBA,YAA0C,IAA1BuC,EAAoBqF,EAAoBrF,EAAoBqF,EAAoB,oBAATC,KAAuBA,KAAO,CAAC,EAEhKC,EAAmC,WACrC,SAASA,EAAQC,GACf,IAAI9C,EAAmB8C,EAAK9C,kBAC5B,EAAIiC,EAAiBtE,SAASoF,KAAMF,GACpCE,KAAKC,cAAgBD,KAAKC,cAAcC,KAAKF,MAC7CA,KAAKG,eAAiB,EACtBH,KAAKI,iBAAmB,GACxBJ,KAAK/C,iBAAmBA,EACxBI,EAAUzC,QAAQyF,OAAOC,UAAUN,KAAKC,eACxCD,KAAKO,qBAAoB,CAoQpBT,OAlQP,EAAIX,EAAcvE,SAASkF,EAAS,CAAC,CACnCxG,IAAK,YACLC,MAAO,WAID,IAAAiH,EACG,IAJPnD,EAAUzC,QAAQyF,OAAOI,YAAYT,KAAKC,eAInCD,KAAKI,iBAAiBhF,QAAQ,CAE/B,IAAA4C,GADMwC,EAAAR,KAAKI,iBAAiBM,SACd,GAChBlD,EAAOgD,EAAQ,GACfd,EAAOc,EAAQ,GACjBxC,EAAIR,GAAQkC,CAAA,CACd,GAED,CACDpG,IAAK,gBACLC,MAAO,SAAuB8G,GACvBL,KAAKG,gBACRH,KAAK/C,iBAAiBoD,EACxB,GAED,CACD/G,IAAK,qBACLC,MAAO,WACL,IAAIoH,EAAQX,KACZA,KAAKG,gBAAkB,EACvBS,YAAW,WAETD,EAAMR,gBAAkB,CAAA,GACzB,GAWF,CACD7G,IAAK,UACLC,MAAO,SAAiBiD,EAASqE,EAAMxF,GAMrC,OALIgE,EAAW7C,KACbnB,EAAOwF,GAAQ,GACRA,EAAArE,EACGA,OAAA,GAELwD,KAAKc,KAAKtE,EAASqE,GAAME,MAAMf,KAAM3E,EAAI,GAEjD,CACD/B,IAAK,OACLC,MAAA,SASciD,EAASqE,EAAMG,GAC3B,IA9FUC,EAAQ3H,EA8FduG,EAAOG,KAGX,QA/HY,IA+HIa,IAAUxB,EAAW7C,GAC5B,OAAAA,EAWL,GAPA6C,EAAW7C,KACNqE,EAAArE,EACGA,OAAA,IAKP6C,EAAWwB,GACP,OAAAA,EAIL,IACF,GAAIA,EAAKK,OACA,OAAAL,EAIT,GAAIA,EAAKM,eACP,OAAON,EAAKM,eAKd,IAAK1G,OAAO2G,aAAaP,GAChB,OAAAA,QAEFQ,GAIA,OAAAR,CAAA,CAET,SAASS,IACH,IAAAjG,EAAO,GACThD,EAAII,UAAU2C,OACdmG,GAAQ/E,GAAWA,IAA4B,IAAjBA,EAAQ+E,KAOxC,IANIP,GAAW3B,EAAW2B,IAChBA,EAAAD,MAAMf,KAAMvH,WAKfJ,KACAgD,EAAAhD,GAAKkJ,EAAO1B,EAAKiB,KAAKtE,EAAS/D,UAAUJ,IAAMI,UAAUJ,GAE5D,IAKK,OAAAwI,EAAKE,MAAMf,KAAM3E,SACjBgG,GAGD,MAFNxB,EAAK2B,qBACL3B,EAAK5C,iBAAiBI,EAAUzC,QAAQ0C,kBAAkB+D,GAAI7E,GACxD6E,CAAA,CACR,CAIF,IAAA,IAASI,KAAYZ,EAlKXI,EAmKGJ,EAnKKvH,EAmKCmI,EAlKhBrC,EAAgBV,eAAeC,KAAKsC,EAAQ3H,KAmKnCgI,EAAAG,GAAYZ,EAAKY,IAStB,OANPH,EAAQ7C,UAAYoC,EAAKpC,UACzBoC,EAAKM,eAAiBG,EAGtBA,EAAQJ,QAAS,EACjBI,EAAQI,UAAYb,EACbS,CAAA,GAER,CACDhI,IAAK,sBACLC,MAAA,WAKE,IAAIsG,EAAOG,KACP2B,EAAkB9B,EAAKO,iBAC3B,SAASwB,EAAWlC,GACX,OAAA,SAAUmC,EAAInJ,GAKnB,IADA,IAAI2C,EAAO,IAAIC,MAAM7C,UAAU2C,QACtB/C,EAAI,EAAGA,EAAIgD,EAAKD,SAAU/C,EAC5BA,EAAAA,GAAKI,UAAUJ,GAElB,IAAAyJ,EAAmBzG,EAAK,GAQ5B,OAPIgE,EAAWyC,KACbzG,EAAK,GAAKwE,EAAKiB,KAAKgB,IAMlBpC,EAAKqB,MACArB,EAAKqB,MAAMf,KAAM3E,GAEjBqE,EAAKrE,EAAK,GAAIA,EAAK,GAE9B,CAAA,CAyDGkE,EAAAI,EAAS,aAAciC,EAAYD,GACnCpC,EAAAI,EAAS,cAAeiC,EAAYD,GACrChC,EAAQoC,uBACLxC,EAAAI,EAAS,yBAAyB,SAAUD,GAC/C,OAAO,SAAUsC,GACf,OAAOtC,EAAKG,EAAKiB,KAAKkB,GACxB,IACCL,GAML,IADA,IAnEyBM,EACnBC,EAkEFC,EAAe,CAAC,cAAe,SAAU,OAAQ,mBAAoB,iBAAkB,oBAAqB,kBAAmB,cAAe,aAAc,qBAAsB,cAAe,aAAc,iBAAkB,eAAgB,kBAAmB,cAAe,cAAe,eAAgB,qBAAsB,SAAU,YAAa,eAAgB,gBAAiB,YAAa,kBAAmB,SAAU,iBAAkB,4BAA6B,wBACpd9J,EAAI,EAAGA,EAAI8J,EAAa/G,OAAQ/C,IAnEnC6J,YAAQvC,EADWsC,EAqEPE,EAAa9J,KApEEsH,EAAQsC,GAAQxD,YAClCyD,EAAMxD,gBAAkBwD,EAAMxD,eAAe,sBACnDa,EAAA2C,EAAO,oBAAoB,SAAUxC,GACxC,OAAO,SAAU0C,EAASP,EAAIQ,EAASC,GAEjC,IACET,GAAMA,EAAGU,cACXV,EAAGU,YAAc1C,EAAKiB,KAAKe,EAAGU,oBAEzBC,GAAK,CAOP,OAAA9C,EAAKf,KAAKqB,KAAMoC,EAASvC,EAAKiB,KAAKe,OAAI,OAD1CY,GAC8DJ,EAASC,EAC7E,IACCX,GACHpC,EAAK2C,EAAO,uBAAuB,SAAUxC,GAC3C,OAAO,SAAUgD,EAAKb,EAAIQ,EAASC,GAgB7B,IACF,IAAIK,EAAYd,aAA+B,EAASA,EAAGV,eACvDwB,GACFjD,EAAKf,KAAKqB,KAAM0C,EAAKC,EAAWN,EAASC,SAEpCjB,GAAG,CAGZ,OAAO3B,EAAKf,KAAKqB,KAAM0C,EAAKb,EAAIQ,EAASC,EAC3C,CACF,QAKA,IAmBA,IAAAM,EAAIjD,EAAQkD,QAAUlD,EAAQiD,EAC9BA,GAAKA,EAAEf,IAAMe,EAAEf,GAAGiB,OACpBvD,EAAKqD,EAAEf,GAAI,SAAS,SAAUnC,GAC5B,OAAO,SAAUmC,GACf,OAAOnC,EAAKf,KAAKqB,KAAMH,EAAKiB,KAAKe,GACnC,IACCF,EACL,KAGG7B,CAAA,CA7Q8B,GA+QvCxF,EAAiB,QAAIwF,CACrB,EAIM,6DAAA,SAIUzF,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEACjGqD,EAAUrD,EAAyD,mDAChEE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAKjB,SAAsBO,GAChB,IAAAkI,EAAQ,IAAIC,EAAOpI,QAAQ,CAC7BqC,iBAAkB,SAA0BgG,GAC1ChH,EAAQgB,iBAAiBpC,EAAQ,KAAM,KAAMoI,EAAW,IAGxDC,EAAmB,SAA0BR,GAE3CA,EAAI1I,kBAAkBmJ,MACxBlH,EAAQgB,iBAAiBpC,EAAQ6H,EAAI1I,OAAQ,KAAM,KAAM,uBAElDa,EAAAW,SAAS,qBAAqB,WAC5B,MAAA,CACLmB,cAAe,sBACfL,QAASoG,EAAI1I,QAAU,8BACzB,GAGN,EAEA,OADOhC,OAAAoL,iBAAiB,qBAAsBF,GACvC,WACElL,OAAAqL,oBAAoB,qBAAsBH,GACjDH,EAAMO,WACR,CAAA,EA3BF,IAAIN,EAASxI,EAAuBD,EAAyC,wDACzE0B,EAEK,SAAwB+B,EAAKC,GAAmC,GAAAD,GAAOA,EAAIE,WAAqB,OAAAF,EAAW,GAAQ,OAARA,GAAiC,WAAjBJ,EAAQI,IAAoC,mBAARA,EAA6B,MAAA,CAAEpD,QAASoD,GAAa,IAAAG,EAAQC,EAAyBH,GAAc,GAAIE,GAASA,EAAME,IAAIL,GAAe,OAAAG,EAAML,IAAIE,GAAQ,IAAIM,EAAS,CAAC,EAAOC,EAAwB9D,OAAOC,gBAAkBD,OAAO+D,yBAA0B,IAAA,IAASlF,KAAO0E,EAAW,GAAQ,YAAR1E,GAAqBmB,OAAOgE,UAAUC,eAAeC,KAAKX,EAAK1E,GAAM,CAAE,IAAIsF,EAAOL,EAAwB9D,OAAO+D,yBAAyBR,EAAK1E,GAAO,KAAUsF,IAASA,EAAKd,KAAOc,EAAKC,KAAepE,OAAAC,eAAe4D,EAAQhF,EAAKsF,GAAuBN,EAAAhF,GAAO0E,EAAI1E,EAAM,CAA0E,OAApEgF,EAAO1D,QAAUoD,EAASG,GAAeA,EAAAU,IAAIb,EAAKM,GAAkBA,CAAA,CAF/wBQ,CAAwBvE,EAAqC,oDAC3E,SAAS6D,EAAyBH,GAAmB,GAAmB,mBAAZc,QAA+B,OAAA,KAAU,IAAAC,MAAwBD,QAAeE,MAAuBF,QAAmB,OAAAX,EAA2B,SAAkCH,GAAe,OAAOA,EAAcgB,EAAmBD,IAAsBf,EAAW,CA0B3U,EAIM,8DAAA,SAIU5D,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QACjB,SAA6B2I,GAC3B,SAASM,EAAYC,GACZ,OAAQ,OAARA,OAAe,EAAYA,CAAA,CAEpC,OAAOP,EAAYQ,MAAQR,EAAYQ,MAAMC,KAAI,SAAUC,GAClD,MAAA,CACLC,WAAYL,EAAYI,EAAME,MAC9BC,aAAcP,EAAYI,EAAMI,QAChCC,SAAUT,EAAYI,EAAMM,KAC5BC,aAAcX,EAAYI,EAAM9C,MAEnC,SAAI,CAAA,CACP,EAIM,sDAAA,SAIUxG,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EACrB,IAAI6J,EAAsB3J,EAAuBD,EAAoE,+DACjH6J,EAAe7J,EAAyC,oDACxD8J,EAAe,GACV,SAAAC,EAAgBC,EAAOC,GAM9B,IALA,IAAIC,EAAuBJ,EAAaK,QAAO,SAAUC,EAAOC,GAC9D,MAAO,CAACA,GAAa9I,OAAO6I,EAC9B,GAAG,IAGMxJ,EAAO1C,UAAU2C,OAAQC,EAAO,IAAIC,MAAMH,EAAO,EAAIA,EAAO,EAAI,GAAII,EAAO,EAAGA,EAAOJ,EAAMI,IAClGF,EAAKE,EAAO,GAAK9C,UAAU8C,GAEzB,IAAAsJ,EAAUC,QAAQC,QAAQ1J,GAwCvB,OArCcoJ,EAAA1J,SAAQ,SAAUgF,GACrC,IAAIiF,EAAUjF,EAAKiF,QACjBC,EAAelF,EAAKkF,cAClBD,GAAWC,KACHJ,EAAAA,EAAQK,MAAK,SAAU7J,GAC/B,OAAO2J,EAAQjE,WAAM,EAAQ,CAACyD,GAAS1I,UAAWqI,EAAoBvJ,SAASS,IACjF,IAAG,SAAUA,GACX,OAAO4J,EAAalE,WAAM,EAAQ,CAACyD,GAAS1I,UAAWqI,EAAoBvJ,SAASS,IAAM,IAE9F,IAEQwJ,EAAAA,EAAQK,MAAK,SAAU7J,GAE3B,IAAA8J,EACA3C,KAFA4B,EAAagB,YAAW,GAGxB,IACFD,EAAMZ,EAAMxD,WAAM,GAAA,EAAYoD,EAAoBvJ,SAASS,UACpDgK,GACD7C,EAAA6C,CAAA,CAGR,MADIjB,EAAagB,YAAW,GACxB5C,EACI,MAAAA,EAED,OAAA2C,CAAA,IAEYV,EAAA1J,SAAQ,SAAUuK,GACrC,IAAIC,EAAWD,EAAMC,SACnBC,EAAgBF,EAAME,eACpBD,GAAYC,KACJX,EAAAA,EAAQK,MAAK,SAAUC,GACxB,OAAAI,EAASf,EAASW,EAC3B,IAAG,SAAU3C,GACJ,OAAAgD,GAAiBA,EAAchB,EAAShC,EAAG,IAEtD,IAEKqC,CAAA,CAET,SAASY,EAAOC,GACd,GAAKA,EAAInB,OAAUmB,EAAIZ,QAAvB,CAKI,IAAAa,EAAaD,EAAInB,MAAMqB,SAGvBF,EAAAnB,eAAkBA,GACpB,IAAIC,EAAU,EACd,OAAO,WACL,IAAA,IAASqB,EAAQpN,UAAU2C,OAAQC,EAAO,IAAIC,MAAMuK,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/EzK,EAAAyK,GAASrN,UAAUqN,GAEnB,OAAAxB,EAAgBvD,WAAM,EAAQ,CAACwD,EAAOC,KAAW1I,OAAOT,GACjE,CAAA,EACAqK,EAAInB,OAGFoB,IAEFD,EAAInB,MAAMqB,SAAWD,EAlBrB,CAmBF,CAMF,IAAII,GAAY,EACZpL,EAAW,CACbqL,SAAU,SAAkBpB,GAM1B,OALKmB,IACSA,GAAA,EACZN,EAAOzN,SAETqM,EAAa7L,KAAKoM,GACX,WACD,IAAAqB,EAAQ5B,EAAa6B,QAAQtB,GAC7BqB,GAAS,GACE5B,EAAA8B,OAAOF,EAAO,EAE/B,CACF,EACAG,MAAO,WACL/B,EAAe,EAAC,GAGpB/J,EAAiB,QAAIK,CAAA,EAIf,6CAAA,SAIUN,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAYjB,SAAyBO,GACnB,IAAAwL,EAAS5N,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAC/E6N,eAAe,EACfC,YAAY,GAEd,IAA4E,KAAvEF,aAAuC,EAASA,EAAOE,YAC1D,OAAO,WAAa,EAEtB,IAAID,EAAgBD,EAAOC,cACzBE,EAAmBH,EAAOG,iBAC1BC,EAAqBJ,EAAOI,mBAC1BC,EAAiB,CAAC,EAGlBC,EAAW,SAAkBjK,GAC3B,IACAkK,EAAMlK,EACV,GAAoC,YAApC,EAAQf,EAASf,SAAS8B,IAA8B,MAARA,EAAc,CACxD,IAAAwF,EAAQzH,OAAOoM,eAAenK,GAC9BwF,IAAUzH,OAAOgE,WAAuB,OAAVyD,IAE1B0E,EAAAE,KAAKC,UAAUrK,GACvB,CAEE,GAAAkK,GAAOA,EAAIxL,QAAUwL,EAAIxL,OATjB,QASkD,iBAARwL,EAAkB,CACtE,IAAII,EAAYJ,EAAIK,UAAU,EAAG,KAC1B,MAAA,GAAGnL,OAAOkL,EAAW,qJAAoJ,CAE3K,OAAAtK,CACT,EACIwK,EAAa,SAAoBC,EAAOnC,GAC1C,IAAIhK,EAASgK,EAAQhK,OACdH,EAAAW,SAAS,2BAA2B,WACzC,IAAIuE,EAAOtH,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAC7E2O,EAAiBrH,EAAKrE,UACtBA,OAA+B,IAAnB0L,GAAmCA,EAC/CC,EAAwBtH,EAAKuH,iBAC7BA,OAA6C,IAA1BD,EAAmC,SAAUE,GACvD,OAAAA,CAAA,EACLF,EACN,IAAK3L,EACI,OAAA,KAET,IAAI8L,EAAY,KACZ,IAEUA,EAAAF,EAAiBG,EAAcA,EAAc,CAAA,EAAIzC,GAAU,GAAI,CACzEmC,iBAEK3E,GACPtH,QAAQ/B,MAAMqJ,EAAG,CAEnB,GAAIgF,EAAW,CACb,IAAIvD,EAAMuD,EAAUvD,IACpB,GAAwB,oBAAb/L,UAA8D,mBAA3BA,SAASS,cAA8B,CAG/E,IAAAR,EAAID,SAASS,cAAc,KAC/BR,EAAE2E,KAAO0K,EAAUvD,IACnBA,EAAM9L,EAAE2E,IAAA,CAEH,MAAA,CACLqK,QAEAlD,MAEAyD,WAAaC,EAAW/M,SAAS4M,EAAUE,SAAS,SAAUE,GAErD,MAAA,GAAG9L,OAAO8L,EAAW,IAE9BC,KAAMlB,EAASa,EAAUK,MAEzB7M,SAEA8M,SAAUN,EAAUM,eAAY,EAEhCC,KAAMP,EAAUO,WAAQ,EAExBC,YAAaR,EAAUQ,kBAAe,EACxC,CAIK,OADPtB,EAAeS,IAAS,EACjB,IAAA,GAEX,EACIc,EAAc,SAAqBd,EAAO5B,GAC5C,IAAIvK,EAASuK,EAASvK,OACpBkN,EAAS3C,EAAS2C,OAClBC,EAAe5C,EAAS4C,aACnBtN,EAAAW,SAAS,4BAA4B,WAC1C,IAAI8J,EAAQ7M,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAC9E2P,EAAkB9C,EAAM5J,UACxBA,OAAgC,IAApB0M,GAAoCA,EAChDC,EAAwB/C,EAAMgD,kBAC9BA,OAA8C,IAA1BD,EAAmC,SAAUd,GACxD,OAAAA,CAAA,EACLc,EACN,IAAK3M,EACI,OAAA,KAAA,GACEgL,EAAeS,GAEjB,cADAT,EAAeS,GACf,KAET,IAAIK,EAAY,KACZ,IAEUA,EAAAc,EAAkBb,EAAcA,EAAc,CAAA,EAAIlC,GAAW,GAAI,CAC3E4B,iBAEK3E,GACPtH,QAAQ/B,MAAMqJ,EAAG,CAInB,OAAIgF,EACK,CACLL,QAEAgB,eACAD,OAAQV,EAAUU,OAElBR,WAAaC,EAAW/M,SAAS4M,EAAUE,SAAS,SAAUE,GAErD,MAAA,GAAG9L,OAAO8L,EAAW,IAE9BC,KAAMlB,EAASa,EAAUK,MAEzB7M,UAIG,CACLmM,QAEAgB,eACAD,SAEAR,QAAS,CAAC,EAEVG,KAAM,KAEN7M,SACF,GAEJ,EAEIuN,EAAY,SAAmBpB,GACjC,OAAOtM,EAAO0L,aAAwC,IAA1BG,EAAeS,EAC7C,EACIqB,GAAA,EAAiBC,EAAe7N,SAAS,CAC3CsM,aACAe,cACAM,cAEEG,GAAA,EAAetE,EAAaxJ,SAAS,CACvCsM,aACAe,cACAM,YACA1N,SACA2L,mBACAC,uBAEEkC,GAAA,EAAiBC,EAAeC,eAAe,CACjD3B,aACAe,cACAM,cAEEO,EAA0BxC,EAAgB,cAAqB,EAAAyC,EAA4BnO,SAASC,GACxG,OAAO,WACmBiO,IACbN,IACFE,IACEC,GACb,CAAA,EAzLF,IAAIK,EAAmBxO,EAAuBD,EAAiE,4DAC3GoB,EAAWnB,EAAuBD,EAAyD,oDAC3FkO,EAAiBjO,EAAuBD,EAA2C,uDACnFqO,EAAiBrO,EAA2C,sDAC5DwO,EAA8BvO,EAAuBD,EAAwD,oEAC7G6J,EAAe5J,EAAuBD,EAAyC,qDAC/EoN,EAAanN,EAAuBD,EAA0D,iDACzF,SAAA0O,EAAQhI,EAAQiI,GAAsB,IAAAC,EAAO1O,OAAO0O,KAAKlI,GAAS,GAAIxG,OAAO2O,sBAAuB,CAAM,IAAAC,EAAU5O,OAAO2O,sBAAsBnI,GAASiI,IAAmBG,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO9O,OAAO+D,yBAAyByC,EAAQsI,GAAK1L,UAAA,KAAiBsL,EAAK3Q,KAAKuI,MAAMoI,EAAME,EAAO,CAAY,OAAAF,CAAA,CAC9U,SAAS1B,EAAc+B,GAAU,IAAA,IAASnR,EAAI,EAAGA,EAAII,UAAU2C,OAAQ/C,IAAK,CAAM,IAAAoR,EAAS,MAAQhR,UAAUJ,GAAKI,UAAUJ,GAAK,CAAC,EAAOA,EAAA,EAAI4Q,EAAQxO,OAAOgP,IAAS,GAAI1O,SAAQ,SAAUzB,IAAO,EAAI0P,EAAiBpO,SAAS4O,EAAQlQ,EAAKmQ,EAAOnQ,GAAI,IAAQmB,OAAOiP,0BAA4BjP,OAAOkP,iBAAiBH,EAAQ/O,OAAOiP,0BAA0BD,IAAWR,EAAQxO,OAAOgP,IAAS1O,SAAQ,SAAUzB,GAAOmB,OAAOC,eAAe8O,EAAQlQ,EAAKmB,OAAO+D,yBAAyBiL,EAAQnQ,GAAI,GAAI,CAAY,OAAAkQ,CAAA,CAkL/f,EAIM,qDAAA,SAIUnP,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAuCjB,SAAuByF,GACrB,IAAImH,EAAanH,EAAKmH,WACpBe,EAAclI,EAAKkI,YACnBM,EAAYxI,EAAKwI,UACfqB,EAAwB,SACxBC,EAAiB,CAAC,EAClBC,EAAaC,EAAgBnP,QAAQoL,SAAS,CAChDhB,QAAS,SAAiBR,GACxB,IAAA,IAASrJ,EAAO1C,UAAU2C,OAAQC,EAAO,IAAIC,MAAMH,EAAO,EAAIA,EAAO,EAAI,GAAII,EAAO,EAAGA,EAAOJ,EAAMI,IAClGF,EAAKE,EAAO,GAAK9C,UAAU8C,GAEzB,IAAAyO,EACJ,GAAuB,oBAAZC,SAA2B5O,EAAK,aAAc4O,QAAS,CAC5D,IAAAC,EAIA,IACFA,EAAa7O,EAAK,GAAG8O,QAAQC,aACtB5H,GAGP0H,EAAapF,QAAQC,QAAQ,0BAA0BjJ,OAAO0G,EAAIlG,SAAQ,CAExE0N,EAAAE,EAAWhF,MAAK,SAAU2C,GACrB,OAAAJ,EAAcA,EAAc,GAAI4C,EAAiBhP,EAAK,KAAM,GAAI,CACrEwM,QAEJ,IAAG,SAAUrF,GACJ,OAAAiF,EAAcA,EAAc,GAAI4C,EAAiBhP,EAAK,KAAM,GAAI,CACrEwM,KAAM,0BAA0B/L,OAAO0G,EAAIlG,UAC5C,GACF,MAID0N,EAAIlF,QAAQC,QAAQ0C,EAAcA,EAAc,CAAA,EAAI4C,EAAiBhP,EAAK,KAAM,CAAA,EAAI,CAClF4I,IAAK,GAAGnI,OAAOT,EAAK,IACpBwM,MAAOxM,EAAK,IAAM,CAAA,GAAIwM,QAGnB,OAAAmC,EAAE9E,MAAK,SAAUoF,GAGf,OAFQT,EAAArF,GAAW8F,EAAItP,OAC9BkM,EAAW,GAAGpL,OAAO8N,GAAuB9N,OAAO0I,GAAU8F,GACtDjP,CAAA,GAEX,EACA4J,aAAc,SAAsBT,EAASrL,GAGpC,OAAA2L,QAAQyF,OAAOpR,EACxB,EACAoM,SAAU,SAAkBf,EAASgG,GAC/B,IAAAC,EACAC,EACA,GAAAnC,EAAU,GAAGzM,OAAO8N,GAAuB9N,OAAO0I,IAE7C,OAAAgG,EAMT,GAA8C,sBAA1CA,EAAU9C,QAAQ5J,IAAI,gBAEF4M,EAAA5F,QAAQC,QAAQ,yDACjC,CACD,IAGF0F,EAAgBD,EAAUL,cACnB3H,GAEP,IAAImI,EAAe,CACjB1G,IAAKuG,EAAUvG,IACfkE,aAAcqC,EAAUI,KAAKxO,cAC7B8L,OAAQsC,EAAUtC,OAClBR,QAASmD,EAAiBL,EAAU9C,SACpCG,KAAM,0BAA0B/L,OAAO0G,EAAIlG,SAC3CtB,OAAQ6O,EAAerF,IAIlB,cAFAqF,EAAerF,GACtByD,EAAY,GAAGnM,OAAO8N,GAAuB9N,OAAO0I,GAAUmG,GACvDH,CAAA,CAEL,IACE,GAAAxS,OAAO8S,aAAeL,EAAc5C,KAAM,CAMxC,IAAAkD,EAASN,EAAc5C,KAAKmD,YAG5BC,EAAc,IAAIjT,OAAO8S,YAAY,SACrCI,EAAe,GACnBR,EAAsBK,EAAOI,OAAOjG,MAAK,SAASkG,EAAiB9F,GACjE,IAAI+F,EAAO/F,EAAM+F,KACf9R,EAAQ+L,EAAM/L,MAChB,GAAI8R,EACK,OAAAH,EAET,IAAII,EAAQ/R,EAAQ0R,EAAYM,OAAOhS,EAAO,CAC5CiS,QAAQ,IACL,GAEL,OADgBN,GAAAI,EACTP,EAAOI,OAAOjG,KAAKkG,EAAgB,GAC3C,MAIDV,EAAsBD,EAAcL,aAE/BjR,GAEPuR,EAAsB5F,QAAQC,QAAQ,iCAAiCjJ,OAAO3C,EAAMmD,SAAQ,CAC9F,CAsBK,OApBaoO,EAAAe,OAAM,SAAUtS,GAIlC,KAAmB,eAAfA,EAAMqE,MAAyBrE,aAAiBuS,cAG7C,MAAA,iCAAiC5P,OAAO3C,EAAMmD,QAAO,IAC3D4I,MAAK,SAAUxI,GAChB,IAAIiO,EAAe,CACjB1G,IAAKuG,EAAUvG,IACfkE,aAAcqC,EAAUI,KAAKxO,cAC7B8L,OAAQsC,EAAUtC,OAClBR,QAASmD,EAAiBL,EAAU9C,SACpCG,KAAMnL,EACN1B,OAAQ6O,EAAerF,WAElBqF,EAAerF,GACtByD,EAAY,GAAGnM,OAAO8N,GAAuB9N,OAAO0I,GAAUmG,EAAY,IAErEH,CACT,EACAhF,cAAe,SAAuBhB,EAASrL,GAC7C,IAAIoM,EAAW,CACbtB,SAAK,EACLiE,OAAQ,EACRR,QAAS,CAAC,EACVG,KAAM,GAAG/L,OAAO3C,IAKX,OAHP8O,EAAY,GAAGnM,OAAO8N,GAAuB9N,OAAO0I,GAAUe,GAGvDT,QAAQyF,OAAOpR,EAAK,IAGxB,OAAA2Q,CAAA,EAhMT,IAAId,EAAmBxO,EAAuBD,EAAiE,4DAC3GoN,EAAanN,EAAuBD,EAA0D,iDAC9FwP,EAAkBvP,EAAuBD,EAA4C,wDAChF,SAAA0O,EAAQhI,EAAQiI,GAAsB,IAAAC,EAAO1O,OAAO0O,KAAKlI,GAAS,GAAIxG,OAAO2O,sBAAuB,CAAM,IAAAC,EAAU5O,OAAO2O,sBAAsBnI,GAASiI,IAAmBG,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO9O,OAAO+D,yBAAyByC,EAAQsI,GAAK1L,UAAA,KAAiBsL,EAAK3Q,KAAKuI,MAAMoI,EAAME,EAAO,CAAY,OAAAF,CAAA,CAC9U,SAAS1B,EAAc+B,GAAU,IAAA,IAASnR,EAAI,EAAGA,EAAII,UAAU2C,OAAQ/C,IAAK,CAAM,IAAAoR,EAAS,MAAQhR,UAAUJ,GAAKI,UAAUJ,GAAK,CAAC,EAAOA,EAAA,EAAI4Q,EAAQxO,OAAOgP,IAAS,GAAI1O,SAAQ,SAAUzB,IAAO,EAAI0P,EAAiBpO,SAAS4O,EAAQlQ,EAAKmQ,EAAOnQ,GAAI,IAAQmB,OAAOiP,0BAA4BjP,OAAOkP,iBAAiBH,EAAQ/O,OAAOiP,0BAA0BD,IAAWR,EAAQxO,OAAOgP,IAAS1O,SAAQ,SAAUzB,GAAOmB,OAAOC,eAAe8O,EAAQlQ,EAAKmB,OAAO+D,yBAAyBiL,EAAQnQ,GAAI,GAAI,CAAY,OAAAkQ,CAAA,CAkB3f,IAAAqB,EAAmB,SAA0BnD,GAC/C,OAAA,EAAWC,EAAW/M,SAlBxB,SAA+B8M,GAE7B,GAAe,MAAXA,GAA8C,mBAApBA,EAAQ3M,QAC7B,OAAA2M,EAET,IAAIiE,EAAS,CAAC,EAQP,OAPCjE,EAAA3M,SAAQ,SAAUxB,EAAOD,GAC3BqS,EAAOrS,GACFqS,EAAArS,GAAO,GAAGwC,OAAO6P,EAAOrS,GAAM,KAAKwC,OAAOvC,GAEjDoS,EAAOrS,GAAO,GAAGwC,OAAOvC,EAC1B,IAEKoS,CAAA,CAKwBC,CAAsBlE,IAAU,SAAUnO,GAChE,MAAA,GAAGuC,OAAOvC,EAAK,GAE1B,EACA,SAAS8Q,IACH,IAAAwB,EAAMpT,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACxE,MAAA,CACLwL,IAAK4H,EAAI5H,IACTyD,QAASmD,EAAiBgB,EAAInE,SAC9B1M,OAAQ6Q,EAAI7Q,QAAU6Q,EAAI7Q,OAAOoB,cACjC0L,SAAU+D,EAAI/D,eAAY,EAC1BC,KAAM8D,EAAI9D,WAAQ,EAClBC,YAAa6D,EAAI7D,kBAAe,EAClC,CA6JF,EAIM,qDAAA,SAIU3N,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQwR,aAAeA,EACvBxR,EAAQyR,qBAsIC,SAAqBC,EAAQC,GAC7B,OAAAC,EAAgB,GAAIF,EAAQC,EAAM,EAtI3C3R,EAAQ6R,wBAA0BA,EAClC7R,EAAQ8R,YAAcA,EACtB9R,EAAQuO,cAiNR,SAAuB9I,GACrB,IAAIsM,EAAUC,EAAiBC,EAC3BrF,EAAanH,EAAKmH,WACpBe,EAAclI,EAAKkI,YACnBM,EAAYxI,EAAKwI,UACfiE,EAA4C,QAA/BH,EAAWrU,OAAOyU,eAAkC,IAAbJ,GAAuE,QAAvCC,EAAkBD,EAASK,cAAwC,IAApBJ,OAAzD,EAA+FA,EAAgBK,KACzLC,EAAW,CAAC,EACZC,GAAwB,EACxB,QAAqB,IAAdL,EAET,OAAO,WAAa,EAElB,IAAAM,EAA+C,QAAlCP,EAAgBvU,OAAO+U,aAAqC,IAAlBR,OAA2B,EAASA,EAAcO,UACzG,QAAqB,IAAdA,GAAuD,mBAAnBA,EAAUE,MAAuBF,EAAUE,MAAK,SAAU3L,GAChG,OAAA4L,EAAsB5O,IAAIgD,EAAC,IAGlC,OAAO,WAAa,EAEtB,IAAI6L,EAAsBV,EAAUW,YAChCC,KAAqBC,EAAazS,UAAS,SAAU2K,EAAU+H,EAAWC,GACxE,IAAChF,EAAU,GAAGzM,OAAO0R,GAAuB1R,OAAOyR,IACjD,IACF,IAAI5C,EAAe,CACjB1G,IAAKsB,EAAStB,KAAO,GACrBiE,OAAQ3C,EAAS2C,OAAS,KAAO3C,EAAS2C,QAAU,IAAM3C,EAAS2C,OAAS,EAC5ER,QAASnC,EAASmC,SAAW,CAAC,EAC9BG,KAAMyF,EAAY/H,EAAS7I,KAAO6I,EAASpM,MAC3C6B,OAAQ4R,EAASW,GAAYnR,eAE/B6L,EAAY,GAAGnM,OAAO0R,GAAuB1R,OAAOyR,GAAa5C,SAC1DnI,GACP,IAAIiL,EAAgB,CAClBxJ,IAAKsB,EAAStB,KAAO,GACrBiE,OAAQ3C,EAAS2C,OAAS,KAAO3C,EAAS2C,QAAU,IAAM3C,EAAS2C,OAAS,EAC5ER,QAASnC,EAASmC,SAAW,CAAC,EAC9BG,KAAM,0BAA0B/L,OAAO0G,EAAIlG,SAC3CtB,OAAQ4R,EAASW,GAAYnR,eAE/B6L,EAAY,GAAGnM,OAAO0R,GAAuB1R,OAAOyR,GAAaE,EAAa,CAElF,IAkDF,OA/CAjB,EAAUW,YAAc,SAAUlJ,EAAKzH,EAASkR,EAASC,GACvD,IAAIC,IAAcC,EAelB,IAAKhB,EACC,IAEE,IAAAiB,EA7FH,SAAqBtR,EAASgQ,GAGjC,IAAAuB,EACArR,GAFJF,EAAUA,GAAW,CAAC,GAEHE,KACf,IAEFqR,EAAaC,EAAyBC,EAAmBzR,EAAQuR,YAAcvB,EAAU0B,oBAAqB,wCACvGnS,GAEPgS,EAAaC,EAAyBG,EAAyB3R,EAAQuR,YAAcvB,EAAU0B,oBAAqB,kCAEpHxR,EAAO,CAAC,CAAA,CAEH,MAAA,CACLA,OACA0R,SAAU5R,EAAQ4R,SAClBC,eAAgB7R,EAAQ6R,eACxB3G,QAAS4G,EAAwB9R,EAAQkL,SAAW,CAAC,EAAG6G,EAAY,uCACpEvT,OAAQgT,EAAyBQ,EAAoBhS,EAAQxB,QAAUwT,EAAmB,GAAI,UAC9FhR,KAAMhB,EAAQgB,KACdwO,OAAQsC,EAAwB9R,EAAQwP,QAAU,CAAC,EAAGyC,EAAkB,kDACxEtG,aAAc3L,EAAQ2L,aACtB4F,aACAW,eAAgBlS,EAAQkS,eACxBC,YAAanS,EAAQmS,YACrBC,QAASpS,EAAQoS,QACnB,CAkE4BC,CAAqBrS,EAASgQ,GAChDsC,EAAc3C,EAAwBlI,EAAKiI,EAAgB,GAAI4B,EAAgB9B,QAAQ,IAEvF+C,EArNH,SAAiB9K,EAAK+K,EAAgBxC,GAE7C,IAAIyC,EAAgBzC,EAAU0C,WAAW,MAAQ,CAAC,EAC9CC,EAXG,SAAuBlL,EAAKuI,GACnC,IAAI4C,EAAO,IAAIC,IAAIpL,GACjBqL,EAAOF,EAAKE,KACP,OAAA9C,EAAU0C,WAAWI,IAAS,IAAA,CAQnBC,CAAuBtL,EAAKuI,IAAc,CAAC,EAC7D,OAAOV,EAAaA,EAAamD,EAAeE,GAAcH,EAAc,CAiNlDQ,CAAiBvL,EAAK6J,EAAgBpG,QAAS8E,GAG/DxR,EAAS8S,EAAgB9S,QAAU,MACvC4R,EAASgB,GAAa5S,EACtB,IAAIyU,EAAc,CAChBxL,IAAK6K,EACL9T,OAAQA,EAAOoB,cACfsL,QAASqH,GAAiB,CAAC,EAE3BlH,KAAMuE,EAAY0B,EAAgBpR,MAAQ,CAAC,EAAGoR,EAAgBC,aAEhE7G,EAAW,GAAGpL,OAAO0R,GAAuB1R,OAAO8R,GAAY6B,SACxDjN,GACP,IAAIkN,EAAe,CACjBzL,MACAjJ,QAASwB,EAAQxB,QAAU,OAAOoB,cAClCsL,QAAS,CAAC,EACVG,KAAM,0BAA0B/L,OAAO0G,EAAIlG,UAE7C4K,EAAW,GAAGpL,OAAO0R,GAAuB1R,OAAO8R,GAAY8B,EAAY,CAG/E,OAAOxC,EAAoBjJ,EAAKzH,GA3CR,SAA2B+I,GAC5CsH,IACYO,EAAA7H,GAAU,EAAMqI,UACxBhB,EAASgB,IAElBF,EAAQnI,EACV,IACwB,SAA2BA,GAC5CsH,IACYO,EAAA7H,GAAU,EAAOqI,UACzBhB,EAASgB,IAElBD,EAAQpI,EACV,GA+BF,EACO,WACmBsH,GAAA,EACxBL,EAAUW,YAAcD,EACxBN,EAAW,CAAC,CACd,CAAA,EA/SF,IAAI5D,EAAmBxO,EAAuBD,EAAiE,4DAC3GoB,EAAWnB,EAAuBD,EAAyD,oDAC3F4J,EAAsB3J,EAAuBD,EAAoE,+DACjH8S,EAAe7S,EAAuBD,EAA4D,mDAC7F,SAAAoV,EAA2BC,EAAGC,GAAsB,IAAAC,EAAuB,oBAAXC,QAA0BH,EAAEG,OAAOC,WAAaJ,EAAE,cAAe,IAAKE,EAAI,CAAM,GAAAxU,MAAM2U,QAAQL,KAAOE,EACrK,SAA4BF,EAAGM,GAAU,GAAKN,EAAL,CAAgB,GAAiB,iBAANA,EAAuB,OAAAO,EAAkBP,EAAGM,GAAa,IAAAE,EAAI3V,OAAOgE,UAAU4R,SAAS1R,KAAKiR,GAAGU,MAAM,GAAK,GAAgE,MAAnD,WAANF,GAAkBR,EAAEW,cAAaH,EAAIR,EAAEW,YAAY/S,MAAgB,QAAN4S,GAAqB,QAANA,EAAoB9U,MAAMkV,KAAKZ,GAAc,cAANQ,GAAqB,2CAA2CK,KAAKL,GAAWD,EAAkBP,EAAGM,QAArG,CAAvP,CAAkW,CADzOQ,CAA4Bd,KAAOC,EAAqD,CAAMC,IAAQF,EAAAE,GAAI,IAAIzX,EAAI,EAAOsY,EAAI,WAAc,EAAG,MAAO,CAAEC,EAAGD,EAAGP,EAAG,WAAe,OAAI/X,GAAKuX,EAAExU,OAAe,CAAEiQ,MAAM,GAAe,CAAEA,MAAM,EAAO9R,MAAOqW,EAAEvX,KAAK,EAAMgJ,EAAG,SAAWwP,GAAY,MAAAA,CAAA,EAAOtJ,EAAGoJ,EAAE,CAAW,MAAA,IAAIG,UAAU,wIAAuI,CAAS,IAAyCtO,EAAzCuO,GAAmB,EAAMC,GAAS,EAAmB,MAAA,CAAEJ,EAAG,WAAoBd,EAAAA,EAAGnR,KAAKiR,EAAC,EAAMQ,EAAG,WAAmB,IAAAa,EAAOnB,EAAGoB,OAA6C,OAArCH,EAAmBE,EAAK5F,KAAa4F,CAAA,EAAS5P,EAAG,SAAW8P,GAAgBH,GAAA,EAAYxO,EAAA2O,CAAA,EAAQ5J,EAAG,WAAmB,IAAOwJ,GAAiC,MAAbjB,EAAGsB,UAAmBA,QAAO,CAAK,QAAU,GAAIJ,EAAc,MAAAxO,CAAA,CAAK,EAAI,CAEz9B,SAAA2N,EAAkBkB,EAAKC,IAAkB,MAAPA,GAAeA,EAAMD,EAAIjW,YAAciW,EAAIjW,QAAiB,IAAA,IAAA/C,EAAI,EAAGkZ,EAAO,IAAIjW,MAAMgW,GAAMjZ,EAAIiZ,EAAKjZ,IAAYkZ,EAAAlZ,GAAKgZ,EAAIhZ,GAAa,OAAAkZ,CAAA,CACvK,SAAAtI,EAAQhI,EAAQiI,GAAsB,IAAAC,EAAO1O,OAAO0O,KAAKlI,GAAS,GAAIxG,OAAO2O,sBAAuB,CAAM,IAAAC,EAAU5O,OAAO2O,sBAAsBnI,GAASiI,IAAmBG,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO9O,OAAO+D,yBAAyByC,EAAQsI,GAAK1L,UAAA,KAAiBsL,EAAK3Q,KAAKuI,MAAMoI,EAAME,EAAO,CAAY,OAAAF,CAAA,CAC9U,SAAS1B,EAAc+B,GAAU,IAAA,IAASnR,EAAI,EAAGA,EAAII,UAAU2C,OAAQ/C,IAAK,CAAM,IAAAoR,EAAS,MAAQhR,UAAUJ,GAAKI,UAAUJ,GAAK,CAAC,EAAOA,EAAA,EAAI4Q,EAAQxO,OAAOgP,IAAS,GAAI1O,SAAQ,SAAUzB,IAAO,EAAI0P,EAAiBpO,SAAS4O,EAAQlQ,EAAKmQ,EAAOnQ,GAAI,IAAQmB,OAAOiP,0BAA4BjP,OAAOkP,iBAAiBH,EAAQ/O,OAAOiP,0BAA0BD,IAAWR,EAAQxO,OAAOgP,IAAS1O,SAAQ,SAAUzB,GAAOmB,OAAOC,eAAe8O,EAAQlQ,EAAKmB,OAAO+D,yBAAyBiL,EAAQnQ,GAAI,GAAI,CAAY,OAAAkQ,CAAA,CAC/f,IAAIgF,EAAqB,IAAIgD,IAAI,CAAC,MAAO,MAAO,OAAQ,QAAS,OAAQ,SAAU,UAAW,SAAU,aACpGvD,EAAwB,IAAAuD,IAAI,CAAC,aAAc,OAAQ,SACnDrD,EAA8B,IAAAqD,IAAI,CAAC,MAAO,cAC1ChE,EAAwB,SACxBP,EAA4B,IAAAuE,IAAI,CAAC,UAAW,YAAa,QACzDC,EAAY,IAAID,IAAI,CAAC,aACrBE,MAAgBF,IAChBjD,EAAa,IAAIiD,IAAI,CAAC,WACtB/C,EAAuB,IAAA+C,IAAI,CAAC,SAAU,UACtCG,EAAqB,CACvBC,KAAMrD,EACNsD,WAAY,IAAIL,IAAI,CAAC,WACrBM,KAAU,IAAAN,IAAI,CAAC,QAAS,WACxBO,IAAS,IAAAP,IAAI,CAAC,aAAc,gBAC5B5W,QAAS8W,GAMF,SAAA1D,EAAyBgE,EAAMzY,EAAO0Y,GACzC,GAAiB,iBAAV1Y,EACT,MAAM,IAAI4J,MAAM,GAAGrH,OAAOmW,EAAW,qBAAqBnW,QAAA,EAAWqI,EAAoBvJ,SAASoX,GAAME,KAAK,QAK/G,GADQ3Y,EAAAA,EAAM4Y,OAAOC,eAChBJ,EAAK3T,IAAI9E,GACZ,MAAM,IAAI4J,MAAM,GAAGrH,OAAOmW,EAAW,qBAAqBnW,QAAA,EAAWqI,EAAoBvJ,SAASoX,GAAME,KAAK,QAExG,OAAA3Y,CAAA,CAEA,SAAA+U,EAAwBtQ,EAAKqU,EAAiBC,GACrD,GAAmC,YAAnC,EAAQ3W,EAASf,SAASoD,GAClB,MAAA,IAAImF,MAAMmP,GAET,IAAA,IAAAC,EAAK,EAAGC,EAAe/X,OAAO0O,KAAKnL,GAAMuU,EAAKC,EAAapX,OAAQmX,IAAM,CAC5E,IAAAjZ,EAAMkZ,EAAaD,GACnB,IAACF,EAAgBhU,KAAA,EAAQ1C,EAASf,SAASoD,EAAI1E,KAC3C,MAAA,IAAI6J,MAAMmP,EAClB,CAEK,OAAAtU,CAAA,CAOA,SAAA8N,EAAa2G,EAAgB/K,GACpC,OAAOD,EAAcA,EAAc,CAAI,EAAAgL,GAAiB/K,EAAO,CAQxD,SAAAgL,EAAenZ,EAAO0S,GAC7B,OAAIA,EACK0G,mBAAmBpZ,GAEnBA,CACT,CAEO,SAAAqZ,EAAoBC,EAAWvZ,EAAK2S,GACvC,OAAC4G,EAAUzX,OAGX6Q,EACK,GAAGnQ,OAAO6W,mBAAmBE,GAAY,KAAK/W,OAAO6W,mBAAmBrZ,GAAM,KAE9E,GAAGwC,OAAO+W,EAAW,KAAK/W,OAAOxC,EAAK,KALtC2S,EAAS0G,mBAAmBrZ,GAAOA,CAM5C,CAEO,SAAAwZ,EAAeD,EAAWlO,EAAOsH,GACxC,IAEE8G,EAFEC,EAAQ,GACRC,EAAYtD,EAA2BhL,GAEvC,IACG,IAAAsO,EAAUrC,MAAOmC,EAAQE,EAAU7C,KAAK/E,MAAO,CAClD,IAAIhK,EAAI0R,EAAMxZ,MACV+B,MAAM2U,QAAQ5O,GACV2R,EAAAxa,KAAKsa,EAAe,GAAGhX,OAAO+W,EAAW,MAAOxR,EAAG4K,IAEnB,cAAzBtQ,EAASf,SAASyG,GASjC2R,EAAMxa,KAAK,GAAGsD,OAAO8W,EAAoBC,EAAW,GAAI5G,GAAS,KAAKnQ,OAAO4W,EAAerR,EAAG4K,KAH7F+G,EAAMxa,KAAK0T,EAAgB,GAAGpQ,OAAO+W,EAAW,MAAM/W,OAAOuF,GAAI4K,OAAQ,GAG4B,QAElGzJ,GACPyQ,EAAU5R,EAAEmB,EAAG,CACf,QACAyQ,EAAU1L,GAAE,CAEP,OAAAyL,EAAMd,KAAK,IAAG,CAEd,SAAAhG,EAAgB2G,EAAW5R,EAAQgL,GAC1C,IAAI+G,EAAQ,GACZ,IAAA,IAAS1Z,KAAO2H,EACd,GAAKA,EAAOvC,eAAepF,GAA3B,CAGI,IAAA4Z,EAAaL,EAAUzX,OAAS,GAAGU,OAAO+W,EAAW,KAAK/W,OAAOxC,EAAK,KAAOA,EAC7EgC,MAAM2U,QAAQhP,EAAO3H,IACvB0Z,EAAMxa,KAAKsa,EAAeI,EAAYjS,EAAO3H,GAAM2S,IAEH,YAAnC,EAAAtQ,EAASf,SAASqG,EAAO3H,KAAsC,OAAhB2H,EAAO3H,GAIrE0Z,EAAMxa,KAAK,GAAGsD,OAAO8W,EAAoBC,EAAWvZ,EAAK2S,GAAS,KAAKnQ,OAAO4W,EAAezR,EAAO3H,GAAM2S,KAHxG+G,EAAMxa,KAAK0T,EAAgBgH,EAAYjS,EAAO3H,GAAM2S,GAPpD,CAYG,OAAA+G,EAAMd,KAAK,IAAG,CAKd,SAAA/F,EAAwBlI,EAAK+H,GACpC,IAAK/H,EAAI7I,SAAW4Q,EAAO5Q,OAClB,OAAA6I,EAET,IAAIkP,EAAQ,IAAI9D,IAAIpL,GAClBqL,EAAO6D,EAAM7D,KACb8D,EAAWD,EAAMC,SACjBC,EAASF,EAAME,OACfC,EAAOH,EAAMG,KACbC,EAAWJ,EAAMI,SACnB,MAAO,GAAGzX,OAAOyX,EAAU,MAAMzX,OAAOwT,GAAMxT,OAAOsX,GAAUtX,OAAOuX,EAAOjY,OAAS,GAAGU,OAAOuX,EAAQ,KAAKvX,OAAOkQ,GAAU,IAAIlQ,OAAOkQ,IAASlQ,OAAOwX,EAAI,CAQtJ,SAAAlH,EAAY1P,EAAM8W,GACzB,IAAIC,GAAmB,EAAG9X,EAASf,SAAS8B,GACxCgX,EARN,SAA6BF,GACpB,OAAA7B,EAAmB6B,IAAmB7B,EAAmB/W,OAAA,CAOzC+Y,CAAoBH,GACvCI,EANN,SAAiCJ,GACxB,MAAmB,cAAnBA,EAAiC/B,EAAYC,CAAA,CAKzBmC,CAAwBL,GAC/C,GAAAI,EAAqBE,KAAO,EAAG,CACjC,IAAIC,GAAwB,EAM5B,GALqBH,EAAA7Y,SAAQ,SAAU6P,GACjCrQ,EAAoBqF,EAAEgL,IAASlO,aAAgBnC,EAAoBqF,EAAEgL,KAC/CmJ,GAAA,EAC1B,KAEGA,EACH,MAAM,IAAI5Q,MAAM,+BAA+BrH,QAAA,EAAWqI,EAAoBvJ,SAASgZ,GAAsB1B,KAAK,OACpH,CAEF,GAAkC,IAA9B0B,EAAqBE,OAAeJ,EAAiBrV,IAAIoV,GAC3D,MAAM,IAAItQ,MAAM,sBAAsBrH,QAAA,EAAWqI,EAAoBvJ,SAAS8Y,GAAkBxB,KAAK,QAEvG,MACO,SADCsB,EAGG9W,EAGAoK,KAAKC,UAAUrK,OAAM,EAAW,EAC3C,CA+BF,IAAImR,EAAiB,CAiGrB,EAIM,kEAAA,SAIUxT,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAOjB,SAAoCO,GAClC,IAAImZ,OAAa,EACjB,SAASC,IACP,IAAIC,EAAY,CACdC,OAAQnc,OAAOoc,UAAUC,OACzBC,cAAe,UAEZtc,OAAOoc,UAAUC,OAEXrc,OAAOoc,UAAUG,YAAcvc,OAAOoc,UAAUG,WAAWD,gBACpEJ,EAAUI,cAAgBE,EAAoBxc,OAAOoc,UAAUG,WAAWD,gBAAkB,WAF5FJ,EAAUI,cAAgB,OAIxBN,GAAcE,EAAUC,SAAWH,EAAWG,QAAUD,EAAUI,gBAAkBN,EAAWM,gBAGtFN,EAAAE,EACNrZ,EAAAW,SAAS,iCAAiC,WAC/C,IACE4L,GADS3O,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,IACvDiD,UAExB,YADiC,IAAnB0L,GAAmCA,EAI1C8M,EAFE,IAEF,IACR,CAQH,OANAtT,WAAWqT,GACPjc,OAAOoc,UAAUG,YAAsE,mBAAjDvc,OAAOoc,UAAUG,WAAWnR,kBACpEpL,OAAOoc,UAAUG,WAAWnR,iBAAiB,SAAU6Q,GAElDjc,OAAAoL,iBAAiB,SAAU6Q,GAC3Bjc,OAAAoL,iBAAiB,UAAW6Q,GAC5B,WACEjc,OAAAqL,oBAAoB,UAAW4Q,GAC/Bjc,OAAAqL,oBAAoB,SAAU4Q,GACjCjc,OAAOoc,UAAUG,YAAyE,mBAApDvc,OAAOoc,UAAUG,WAAWlR,qBACpErL,OAAOoc,UAAUG,WAAWlR,oBAAoB,SAAU4Q,EAE9D,CAAA,EA5CF,IAAIO,EAAsB,CACxB,UAAW,SACX,KAAM,OACN,KAAM,SACN,KAAM,QAyCR,EAIM,mDAAA,SAIUna,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQ8K,UAWR,SAAmBqP,GACNC,EAAAD,CAAA,EAXbna,EAAiB,QAcjB,SAAqByF,GACf,IAAAmH,EAAanH,EAAKmH,WACpBe,EAAclI,EAAKkI,YACnBM,EAAYxI,EAAKwI,UACjB1N,EAASkF,EAAKlF,OACd8Z,EAAwB5U,EAAKyG,iBAC7BA,OAA6C,IAA1BmO,GAA0CA,EAC7DC,EAAwB7U,EAAK0G,mBAC7BA,OAA+C,IAA1BmO,GAA2CA,EAC9DC,EAAOC,eACPC,MAAahW,QACbiW,GAAsB,EACtBC,EAAsB,OA8M1B,OA7MAjd,OAAOkd,kBAAoBJ,eAGVA,eAAA,SAAwBK,EAASC,GAChD,IAAIC,EAAY,IAAIR,EAAKM,EAASC,GAClC,IAAKV,EACI,OAAAW,EAETN,EAAOlW,IAAIwW,EAAW,CACpBC,QAASC,EACT7N,QAAS,CAAA,IAEX,IAAI8N,EAAeH,EAAUI,KAmBzBC,EAAeL,EAAUM,KAuBzBnP,IACF6O,EAAUI,KA1CZ,WACE,IAAA,IAASta,EAAO1C,UAAU2C,OAAQC,EAAO,IAAIC,MAAMH,GAAOI,EAAO,EAAGA,EAAOJ,EAAMI,IAC1EF,EAAAE,GAAQ9C,UAAU8C,GAErB,IACE,IAAA0I,EAAM5I,EAAK,GACX,GAAArD,OAAOqX,KAA6B,mBAAfrX,OAAOqX,KAAmE,IAA7CpL,EAAIoP,OAAOuC,EAAKC,yBAAgC,CACpG,IAAIC,EAAsB,IAAI9d,OAAOqX,IAAIxU,EAAOkb,cAC5BD,EAAAE,aAAanX,IAAI,MAAO,WAC5C,IAAIoX,EAAS,IAAIje,OAAOqX,IAAIpL,GACxBiS,EAAeD,EAAOD,aAAalY,IAAI,kBACvCqY,EAAWD,EAAe,GAAGpa,OAAOoa,EAAc,QAAU,GAChED,EAAOD,aAAanX,IAAI,iBAAkB,GAAG/C,OAAOqa,EAAU,KAAKra,OAAOga,EAAoBhZ,KAAM,6BAC/FzB,EAAA,GAAK4a,EAAOnZ,IAAA,QAEZuE,GAAG,CACL,OAAAmU,EAAazU,MAAMf,KAAM3E,EAAI,EA2BpCga,EAAUM,KAxBZ,WACE,IAAA,IAAS9P,EAAQpN,UAAU2C,OAAQC,EAAO,IAAIC,MAAMuK,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC/EzK,EAAAyK,GAASrN,UAAUqN,GAEtB,IACE,IAAAsQ,EAAarB,EAAOjX,IAAIuX,GACxB,GAAArd,OAAOqX,KAA6B,mBAAfrX,OAAOqX,KAAsB+G,GAAcA,EAAWnS,KAAiE,IAA1DmS,EAAWnS,IAAIoP,OAAOuC,EAAKS,4BAAoChb,EAAKD,SAAkE,IAAxDC,EAAK,GAAG6K,QAAQ0P,EAAKU,2BAAmC,CAC1N,IAAIP,EAAe,IAAI/d,OAAOqX,IAAIxU,EAAOkb,cAC5BA,EAAAC,aAAanX,IAAI,MAAO,aACjC,IAAAiX,EAAsBnD,mBAAmBoD,EAAajZ,MACtDJ,EAAOrB,EAAK,GAAGkb,MAAM,KAAK7S,KAAI,SAAU8S,GAC1C,IAAA,EAAQC,EAAY7b,SAAS4b,EAAYZ,EAAKU,2BAA4B,CACpE,IAAAI,EAAUF,IAAeZ,EAAKU,0BAClC,MAAO,GAAGxa,OAAO0a,GAAY1a,OAAO4a,EAAU,GAAK,OAAQ,KAAK5a,OAAOga,EAAqB,2BAA0B,CAEjH,OAAAU,CAAA,IACNtE,KAAK,KACR7W,EAAK,GAAKqB,CAAA,QAEL2E,GAAG,CACL,OAAAqU,EAAa3U,MAAMf,KAAM3E,EAAI,IAQtC,EAAIJ,EAAaL,SAASya,EAAW,QAAQ,SAAUra,EAAQiJ,GAC7D,IAAI+Q,EAAJ,CAGI,IAAAoB,EAAarB,EAAOjX,IAAIuX,GAC5Be,EAAWpb,OAASA,EACpBob,EAAWnS,IAAMA,CAJf,CAIe,KAEnB,EAAIhJ,EAAaL,SAASya,EAAW,QAAQ,SAAU3Y,GACrD,IAAIsY,EAAJ,CAGI,IAAAoB,EAAarB,EAAOjX,IAAIuX,GAC5B,GAAKe,EAAL,CAGA,IAAIpR,EAAU,CACZf,IAAKmS,EAAWnS,IAChBjJ,OAAQob,EAAWpb,QAAUob,EAAWpb,OAAOoB,cAC/CsL,SAAA,EAAaC,EAAW/M,SAASwb,EAAW1O,SAAW,CAAA,GAAI,SAAUiP,GAC5D,OAAAA,EAAazE,KAAK,KAAI,IAE/BrK,KAAMnL,GAEGwK,EAAA,GAAGpL,OAAOmZ,GAAqBnZ,OAAOsa,EAAWd,OAAQtQ,EAVlE,CAJA,CAcyE,KAE7E,EAAI/J,EAAaL,SAASya,EAAW,oBAAoB,SAAUuB,EAAQrd,GACzE,IAAIyb,EAAJ,CAGI,IAAAoB,EAAarB,EAAOjX,IAAIuX,GACvBe,IAGMA,EAAA1O,QAAU0O,EAAW1O,SAAW,CAAC,EAC5C0O,EAAW1O,QAAQkP,GAAUR,EAAW1O,QAAQkP,IAAW,GAC3DR,EAAW1O,QAAQkP,GAAQpe,KAAKe,GAR9B,CAQmC,IAEvC,IAAIsd,EAAe,CACjBC,iBAAkB,WAChB,IAAI9B,GAGyB,IAAzBK,EAAU0B,WAAkB,CAC1B,IAAAX,EAAarB,EAAOjX,IAAIuX,GAC5B,IAAKe,EACH,OAIE,GAAA7N,EAAU,GAAGzM,OAAOmZ,GAAqBnZ,OAAOsa,EAAWd,QAC7D,OAEE,IAeAzN,EAdAH,GADe2N,EAAU2B,yBAA2B,IAC7BT,MAAM,WAAW7R,QAAO,SAAUuS,EAAUC,GACrE,IAAIhG,EAAO+F,EACPE,EAAcD,EAAQX,MAAM,MAC5B,GAAAY,EAAY/b,OAAS,EAAG,CACtB,IAAA9B,EAAM6d,EAAYzW,QAClBnH,EAAQ4d,EAAYjF,KAAK,MACzB+E,EAAS3d,GACX4X,EAAK5X,IAAQ,KAAKwC,OAAOvC,GAEzB2X,EAAK5X,GAAOC,CACd,CAEK,OAAA2X,CACT,GAAG,IAIC,IACF,OAAQmE,EAAUlN,cAChB,IAAK,OACIN,EAAAhN,EAAOuc,qBAAuBtQ,KAAKuQ,MAAMvQ,KAAKC,UAAUsO,EAAU9P,WAAa8P,EAAU9P,SAChG,MACF,IAAK,cACL,IAAK,OAEDsC,EAAOwN,EAAU9P,SACjB,MAEJ,IAAK,WAEDsC,EAAOwN,EAAUiC,YACjB,MAEJ,IAAK,OACL,IAAK,GAEDzP,EAAOwN,EAAUa,aACjB,MAEJ,QAEWrO,EAAA,UAGNrF,GACAqF,EAAA,sCAAA,CAET,IAAItC,EAAW,CACbtB,IAAKmS,EAAWnS,IAChBiE,OAAQmN,EAAUnN,OAClBR,UACAG,OACA7M,QAASob,EAAWpb,QAAU,IAAIoB,eAEhC,GAAAqK,GAAsBlB,EAASsC,gBAAgB0P,KAAM,CACnD,IAAAC,EAAa,IAAIC,WACVD,EAAAE,WAAWnS,EAASsC,MAC/B2P,EAAWG,OAAS,WACd,IACFpS,EAASsC,KAAOf,KAAKuQ,MAAMG,EAAW7L,cAC/B5P,GAAS,CACNkM,EAAA,GAAGnM,OAAOmZ,GAAqBnZ,OAAOsa,EAAWd,OAAQ/P,EACvE,CAAA,MAEY0C,EAAA,GAAGnM,OAAOmZ,GAAqBnZ,OAAOsa,EAAWd,OAAQ/P,EACvE,CACF,GAeG,OAHP9K,OAAO0O,KAAK0N,GAAc9b,SAAQ,SAAUzB,GAC1C+b,EAAUjS,iBAAiB9J,EAAKud,EAAavd,GAAI,IAE5C+b,CACT,EAGAP,eAAerW,UAAYoW,EAAKpW,UAG/B,CAAA,SAAU,SAAU,mBAAoB,UAAW,QAAQ1D,SAAQ,SAAU6c,GAC7D9C,eAAA8C,GAAY/C,EAAK+C,EAAQ,IAEnC,WACiB5C,GAAA,EAELF,eAAAD,CACnB,CAAA,EA3OF,IAAIlN,EAAanN,EAAuBD,EAA0D,iDAC9FU,EAAeT,EAAuBD,EAA4D,mDAClGkc,EAAcjc,EAAuBD,EAA2D,kDAChGqb,EAAOrb,EAA8D,oDAKrEma,GAAW,EAIXa,EAAe,CAgOnB,EAIM,yCAAA,SAIUlb,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EAErB,IAAIud,EAAUC,KAAKC,IAAI7X,KAAK4X,MACxBE,EAAWH,IACXld,EAAkC,oBAAhBsd,aAA+BA,YAAYF,IAAME,YAAYF,IAAI7X,KAAK+X,aAAe,WACzG,OAAOJ,IAAYG,CACrB,EACA1d,EAAiB,QAAIK,CAAA,EAIf,oDAAA,SAIUN,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAMjB,SAAwBO,GAClB,IAAAkF,EAAOtH,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC9Eyf,EAAsBnY,EAAKoY,eAC3BA,OAAyC,IAAxBD,EAAiC,SAAU3Q,GACnD,OAAAA,CAAA,EACL2Q,EACJE,EAAuBrY,EAAKsY,gBAC5BA,OAA2C,IAAzBD,EAAkC,SAAU7Q,GACrD,OAAAA,CAAA,EACL6Q,EAEN,OAAO,SAAUE,GACR,OAAA,SAAUC,EAASC,EAAcC,GACtC,IAAIC,EAAQJ,EAAYC,EAASC,EAAcC,GAC3CE,EAAmBD,EAAME,SACzBC,EAAUC,IAkDd,OAjDOje,EAAAW,SAAS,yBAAyB,WACnC,IAAAud,EACA,IAEeA,EAAAZ,EAAeO,EAAMM,kBAC/BxW,GACCtH,QAAA/B,MAAMqJ,EAAI6N,WAAU,CAEvB,MAAA,CACL4I,MAAOF,EACPF,UACF,IAsCKpR,EAAcA,EAAc,GAAIiR,GAAQ,CAAA,EAAI,CACjDE,SArCa,SAAkBM,GAC3B,IACA1W,EACA2C,EAFAgU,GAAA,EAAYC,EAAKxe,WAGjB,IACFuK,EAAMwT,EAAiBO,SAChB7T,GACD7C,EAAA6C,CAAA,CACN,QACA,IAAIgU,GAAY,EAAGD,EAAKxe,WAAaue,EAC9Bte,EAAAW,SAAS,wBAAwB,WACtC,IAAIud,EAAiB,KACjBO,EAAkB,KAClB,IAEeP,EAAAZ,EAAeO,EAAMM,YACtCM,EAAkBjB,EAAgBa,SAC3B1W,GACCtH,QAAA/B,MAAMqJ,EAAI6N,WAAU,CAE9B,OAAI0I,GAAkBO,EACb,CACLT,UACAK,OAAQI,EACRD,WACAE,WAAYR,GAGT,IAAA,GACR,CAEH,GAAIvW,EACI,MAAAA,EAED,OAAA2C,CACT,GAIF,CACF,CAAA,EA1EF,IAAI6D,EAAmBxO,EAAuBD,EAAiE,4DAC3G6e,EAAO5e,EAAuBD,EAA0C,2CACnE,SAAA0O,EAAQhI,EAAQiI,GAAsB,IAAAC,EAAO1O,OAAO0O,KAAKlI,GAAS,GAAIxG,OAAO2O,sBAAuB,CAAM,IAAAC,EAAU5O,OAAO2O,sBAAsBnI,GAASiI,IAAmBG,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO9O,OAAO+D,yBAAyByC,EAAQsI,GAAK1L,UAAA,KAAiBsL,EAAK3Q,KAAKuI,MAAMoI,EAAME,EAAO,CAAY,OAAAF,CAAA,CAC9U,SAAS1B,EAAc+B,GAAU,IAAA,IAASnR,EAAI,EAAGA,EAAII,UAAU2C,OAAQ/C,IAAK,CAAM,IAAAoR,EAAS,MAAQhR,UAAUJ,GAAKI,UAAUJ,GAAK,CAAC,EAAOA,EAAA,EAAI4Q,EAAQxO,OAAOgP,IAAS,GAAI1O,SAAQ,SAAUzB,IAAO,EAAI0P,EAAiBpO,SAAS4O,EAAQlQ,EAAKmQ,EAAOnQ,GAAI,IAAQmB,OAAOiP,0BAA4BjP,OAAOkP,iBAAiBH,EAAQ/O,OAAOiP,0BAA0BD,IAAWR,EAAQxO,OAAOgP,IAAS1O,SAAQ,SAAUzB,GAAOmB,OAAOC,eAAe8O,EAAQlQ,EAAKmB,OAAO+D,yBAAyBiL,EAAQnQ,GAAI,GAAI,CAAY,OAAAkQ,CAAA,CAC/f,IAAIsP,EAAiB,CAuErB,EAIM,sDAAA,SAIUze,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAGjB,SAA0BO,GACpB,IAAAkF,EAAOtH,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC9Eyf,EAAsBnY,EAAKoY,eAC3BA,OAAyC,IAAxBD,EAAiC,SAAU3Q,GACnD,OAAAA,CAAA,EACL2Q,EACJE,EAAuBrY,EAAKsY,gBAC5BA,OAA2C,IAAzBD,EAAkC,SAAU7Q,GACrD,OAAAA,CAAA,EACL6Q,EACN,OAAO,SAAUM,GACf,IAAIG,EAAUC,IAcd,OAbOje,EAAAW,SAAS,yBAAyB,WACnC,IAAAud,EACA,IAEeA,EAAAZ,EAAeO,EAAMM,kBAC/BxW,GACCtH,QAAA/B,MAAMqJ,EAAI6N,WAAU,CAEvB,MAAA,CACL4I,MAAOF,EACPF,UACF,IAEK,SAAU3H,GACf,OAAO,SAAUgI,GACX,IACA1W,EACA2C,EAFAgU,GAAA,EAAYC,EAAKxe,WAGjB,IACFuK,EAAM+L,EAAKgI,SACJ7T,GACD7C,EAAA6C,CAAA,CACN,QACA,IAAIgU,GAAY,EAAGD,EAAKxe,WAAaue,EAC9Bte,EAAAW,SAAS,wBAAwB,WACtC,IAAIud,EAAiB,KACjBO,EAAkB,KAClB,IAEeP,EAAAZ,EAAeO,EAAMM,YACtCM,EAAkBjB,EAAgBa,SAC3B1W,GACCtH,QAAA/B,MAAMqJ,EAAI6N,WAAU,CAE9B,OAAI0I,GAAkBO,EACb,CACLT,UACAK,OAAQI,EACRD,WACAE,WAAYR,GAGT,IAAA,GACR,CAEH,GAAIvW,EACI,MAAAA,EAED,OAAA2C,CACT,CACF,CACF,CAAA,EAjEF,IAAIiU,EAAO5e,EAAuBD,EAA0C,2CACxEue,EAAiB,CAiErB,EAIM,2CAAA,SAIUze,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAEFkB,OAAAC,eAAeJ,EAAS,iBAAmB,CAChDuD,YAAY,EACZC,IAAK,WACH,OAAO0b,EAAgB5e,OAAA,IAGpBH,OAAAC,eAAeJ,EAAS,mBAAqB,CAClDuD,YAAY,EACZC,IAAK,WACH,OAAO2b,EAAkB7e,OAAA,IAG7B,IAAI4e,EAAkBhf,EAAuBD,EAA4C,sDACrFkf,EAAoBjf,EAAuBD,EAA8C,uDAAsD,EAI7I,8CAAA,SAIUF,EAAyBC,EAASC,GAgB3CE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EACrB,IAAIof,EAAW,CACbC,qBAAqB,EACrBC,OAAO,GAILja,EAA4B,oBAAX3H,OAAyBA,YAA0C,IAA1BuC,EAAoBqF,EAAoBrF,EAAoBqF,EAAoB,oBAATC,KAAuBA,KAAO,CAAC,EAGhKga,EAAS,GAAGvJ,MACZwJ,EAAmB,IAGnBC,EAAiB,kGACrB,SAASC,IACP,MAAwB,oBAAb9hB,eAAyD,IAAtBA,SAAS2E,SAAiC,GACjF3E,SAAS2E,SAASC,IAAA,CA0ClB4c,EAAArZ,OAAS,WAChB,IAwDI4Z,EAAoBC,EAxDpBC,EAAW,GACbC,EAAW,KACXC,EAAgB,KAChBC,EAAqB,KAmCd,SAAAC,EAAe9W,EAAO+W,GAC7B,IAAItd,EAAY,KACZ,IAAAsd,GAAkBd,EAASC,oBAA3B,CAGJ,IAAA,IAASthB,KAAK8hB,EACR,GAAAA,EAASzb,eAAerG,GACtB,IACF8hB,EAAS9hB,GAAG0I,MAAM,KAAM,CAAC0C,GAAO3H,OAAO+d,EAAOlb,KAAKlG,UAAW,WACvDgiB,GACKvd,EAAAud,CAAA,CAIlB,GAAIvd,EACI,MAAAA,CAZN,CAaF,CAeF,SAASwd,EAAsBpe,EAAS2H,EAAK0W,EAAQC,EAAOC,GAE1D,GAAIP,EACFZ,EAASpc,kBAAkBwd,oCAAoCR,EAAoBrW,EAAK0W,EAAQre,GAC3Eye,YACZF,EAKTN,EADQb,EAASpc,kBAAkBud,IACb,OACjB,CACL,IASMG,EATFne,EAAW,CACboH,IAAOA,EACPJ,KAAQ8W,EACR5W,OAAU6W,GAERpd,OAAO,EACPyd,EAAM3e,EAEwB,oBAA9B,CAAG,EAAA+T,SAAS1R,KAAKrC,KACf0e,EAAS1e,EAAQ4e,MAAMnB,MAEzBvc,EAAOwd,EAAO,GACdC,EAAMD,EAAO,IAGjBne,EAASgE,KAAOiZ,EAOhBS,EANQ,CACN/c,KAAQA,EACRlB,QAAW2e,EACXhX,IAAO+V,IACPvW,MAAS,CAAC5G,KAEU,EAAI,CAE5B,QAAIod,GACKA,EAAmBlZ,MAAMf,KAAMvH,UAEjC,CAkBT,SAASsiB,IACH,IAAAI,EAAsBb,EACxBc,EAAYhB,EACHA,EAAA,KACUE,EAAA,KACLD,EAAA,KACDE,EAAAxZ,MAAM,KAAM,CAACoa,GAAqB,GAAOrf,OAAOsf,GAAU,CAUlE,SAAA/a,EAAOwa,EAAIQ,GAClB,IAAIhgB,EAAOwe,EAAOlb,KAAKlG,UAAW,GAClC,GAAI6hB,EAAoB,CACtB,GAAID,IAAkBQ,EACpB,OAEqBE,GACvB,CAEE,IAAAtX,EAAQiW,EAASpc,kBAAkBud,GAcvC,GAbqBP,EAAA7W,EACL4W,EAAAQ,EACLT,EAAA/e,EAMXuF,YAAW,WACLyZ,IAAkBQ,GACCE,GAEtB,GAAAtX,EAAM6X,WAAa,IAAO,IACb,IAAZD,EACI,MAAAR,CACR,CAMK,OAHPxa,EAAOC,UAjKP,SAAmBib,GAsGbrB,IAGJD,EAAqBta,EAAQ6b,QAC7B7b,EAAQ6b,QAAUd,EACSR,GAAA,GAzG3BC,EAAS3hB,KAAK+iB,EAAO,EAgKvBlb,EAAOI,YAzJP,SAAqB8a,GACnB,IAAA,IAASljB,EAAI8hB,EAAS/e,OAAS,EAAG/C,GAAK,IAAKA,EACtC8hB,EAAS9hB,KAAOkjB,GACTpB,EAAAhU,OAAO9N,EAAG,EAEvB,EAqJFgI,EAAOiD,UA/IP,WA0FO4W,IAGLva,EAAQ6b,QAAUvB,EACSC,GAAA,EACND,OAAA,GA7FrBE,EAAW,EAAC,EA8IP9Z,CAAA,CA9KS,GAoOTqZ,EAAApc,kBAAoB,WAiE3B,SAASme,EAA+BZ,GACtC,QAAwB,IAAbA,EAAGpX,OAA0BoX,EAAGpX,MAA3C,CASS,QAHPuP,EACA0I,EANEC,EAAS,mHACXC,EAAQ,8GACRC,EAAQ,wGACRC,EAAQjB,EAAGpX,MAAM8S,MAAM,MACvB9S,EAAQ,GAIDpL,EAAI,EAAG0jB,EAAID,EAAM1gB,OAAQ/C,EAAI0jB,IAAK1jB,EAAG,CAC5C,GAAI2a,EAAQ2I,EAAOK,KAAKF,EAAMzjB,IAAK,CAC7B,IAAA4jB,EAAWjJ,EAAM,KAAqC,IAA/BA,EAAM,GAAG9M,QAAQ,UAClCwV,EAAA,CACRzX,IAAQgY,EAAsB,KAAXjJ,EAAM,GACzBnS,KAAQmS,EAAM,IAAM8G,EACpBze,KAAQ4gB,EAAW,CAACjJ,EAAM,IAAM,GAChCnP,KAAQmP,EAAM,IAAMA,EAAM,GAAK,KAC/BjP,OAAUiP,EAAM,IAAMA,EAAM,GAAK,KACnC,SACSA,EAAQ6I,EAAMG,KAAKF,EAAMzjB,IACxBqjB,EAAA,CACRzX,IAAO+O,EAAM,GACbnS,KAAQmS,EAAM,IAAM8G,EACpBze,KAAQ,GACRwI,MAASmP,EAAM,GACfjP,OAAUiP,EAAM,IAAMA,EAAM,GAAK,gBAE1BA,EAAQ4I,EAAMI,KAAKF,EAAMzjB,KASlC,SARUqjB,EAAA,CACRzX,IAAO+O,EAAM,GACbnS,KAAQmS,EAAM,IAAM8G,EACpBze,KAAQ2X,EAAM,GAAKA,EAAM,GAAGuD,MAAM,KAAO,GACzC1S,KAAQmP,EAAM,IAAMA,EAAM,GAAK,KAC/BjP,OAAUiP,EAAM,IAAMA,EAAM,GAAK,KAGnC,EAEG0I,EAAQ7a,MAAQ6a,EAAQ7X,OAC3B6X,EAAQ7a,KAAOiZ,GAEjBrW,EAAMjL,KAAKkjB,EAAO,CAEhB,OAACjY,EAAMrI,QAGNqI,EAAM,GAAGM,aAAqC,IAApB8W,EAAG/W,eAIhCL,EAAM,GAAGM,OAAS8W,EAAG/W,aAAe,GAE/B,CACLtG,KAAQqd,EAAGrd,KACXlB,QAAWue,EAAGve,QACd2H,IAAO+V,IACPvW,MAASA,IAZF,IA5CyC,CAyDlD,CAgBF,SAASqX,EAAoCoB,EAAWjY,EAAK0W,EAAQre,GACnE,IAAI6f,EAAU,CACZlY,IAAOA,EACPJ,KAAQ8W,GAEN,GAAAwB,EAAQlY,KAAOkY,EAAQtY,KAAM,CAK3B,GAJJqY,EAAUZ,YAAa,EAClBa,EAAQtb,OACXsb,EAAQtb,KAAOiZ,GAEboC,EAAUzY,MAAMrI,OAAS,GACvB8gB,EAAUzY,MAAM,GAAGQ,MAAQkY,EAAQlY,IAAK,CAC1C,GAAIiY,EAAUzY,MAAM,GAAGI,OAASsY,EAAQtY,KAC/B,OAAA,EACE,IAACqY,EAAUzY,MAAM,GAAGI,MAAQqY,EAAUzY,MAAM,GAAG5C,OAASsb,EAAQtb,KAElE,OADPqb,EAAUzY,MAAM,GAAGI,KAAOsY,EAAQtY,MAC3B,CACT,CAKG,OAFGqY,EAAAzY,MAAM2Y,QAAQD,GACxBD,EAAUG,SAAU,GACb,CAAA,CAIF,OAFLH,EAAUZ,YAAa,GAElB,CAAA,CAYA,SAAAgB,EAAsCzB,EAAI0B,GAQxC,IAPL,IAIFvJ,EACAwJ,EALEtY,EAAe,qEACjBT,EAAQ,GACRgZ,EAAQ,CACR,EAAAC,GAAY,EAILC,EAAOL,EAAsCM,OAAQD,IAASD,EAAWC,EAAOA,EAAKC,OAC5F,GAAID,IAASrf,GAAqBqf,IAASjD,EAASrZ,OAApD,CAeI,GAXGmc,EAAA,CACLvY,IAAO,KACPpD,KAAQiZ,EACRjW,KAAQ,KACRE,OAAU,MAER4Y,EAAKnf,KACPgf,EAAK3b,KAAO8b,EAAKnf,MACRwV,EAAQ9O,EAAa8X,KAAKW,EAAKtM,eACnCmM,EAAA3b,KAAOmS,EAAM,SAEK,IAAdwJ,EAAK3b,KACV,IACG2b,EAAA3b,KAAOmS,EAAM6J,MAAM5V,UAAU,EAAG+L,EAAM6J,MAAM3W,QAAQ,YAClD7E,GAAG,CAEVob,EAAM,GAAKE,GACDD,GAAA,EAEND,EAAA,GAAKE,IAAQ,EAErBlZ,EAAMjL,KAAKgkB,EAvBT,CAyBAD,GAGI9Y,EAAA0C,OAAO,EAAGoW,GAElB,IAAI5Q,EAAS,CACXnO,KAAQqd,EAAGrd,KACXlB,QAAWue,EAAGve,QACd2H,IAAO+V,IACPvW,MAASA,GAGJ,OAD6BqX,EAAAnP,EAAQkP,EAAGiC,WAAajC,EAAG7W,SAAU6W,EAAGhX,MAAQgX,EAAGjX,YAChF+H,CAAA,CAQA,SAAArO,EAAkBud,EAAI0B,GAC7B,IAAI9Y,EAAQ,KACJ8Y,EAAS,MAATA,EAAgB,GAAKA,EACzB,IAEF,GADA9Y,EAAQgY,EAA+BZ,GAE9B,OAAApX,QAEFpC,GACP,GAAIqY,EAASE,MACL,MAAAvY,CACR,CAEE,IAEF,GADQoC,EAAA6Y,EAAsCzB,EAAI0B,EAAQ,GAEjD,OAAA9Y,QAEFpC,GACP,GAAIqY,EAASE,MACL,MAAAvY,CACR,CAEK,MAAA,CACL7D,KAAQqd,EAAGrd,KACXlB,QAAWue,EAAGve,QACd2H,IAAO+V,IACT,CAIK,OAFP1c,EAAkBwd,oCAAsCA,EACxDxd,EAAkBme,+BAAiCA,EAC5Cne,CAAA,CAxQoB,GA0Q7B,IAAI3C,EAAW+e,EACfpf,EAAiB,QAAIK,CAAA,EAIf,mDAAA,SAIUN,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQgc,0BAA4Bhc,EAAQ+b,0BAA4B/b,EAAQub,6BAA0B,EAE1Gvb,EAAQub,wBADsB,+CAG9Bvb,EAAQ+b,0BADwB,iDAGhC/b,EAAQgc,0BADwB,UACI,EAI9B,yDAAA,SAIUjc,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQyiB,oBAiCR,SAA6BxB,GAC3B,OAAO,WACLA,EAAQnV,OACV,CAAA,EAnCF9L,EAAQwF,aAAU,EAClB,IAAIZ,EAAmB1E,EAAuBD,EAAiE,4DAC3G4E,EAAgB3E,EAAuBD,EAA8D,yDACrGuF,EAAmC,WACrC,SAASA,EAAQvG,IACf,EAAI2F,EAAiBtE,SAASoF,KAAMF,GACpCE,KAAKgd,YAAS,EACdhd,KAAKgd,OAASzjB,CAAA,CAaTuG,OAXP,EAAIX,EAAcvE,SAASkF,EAAS,CAAC,CACnCxG,IAAK,MACLC,MAAO,WACL,OAAOyG,KAAKgd,MAAA,GAEb,CACD1jB,IAAK,QACLC,MAAO,WACLyG,KAAKgd,YAAS,CAAA,KAGXld,CAAA,CAjB8B,GA4BvCxF,EAAQwF,QAAUA,CAKlB,EAIM,iDAAA,SAIUzF,EAAyBC,EAASC,GAK3CE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAGR,SAAY0D,EAAKhD,EAAQugB,GAChC,GAA2B,mBAAhBvd,EAAIhD,GACN,OAAAiiB,EAEL,IAEE,IAAAC,EAAO,WAET,IADI,IAAAC,EACKhiB,EAAO1C,UAAU2C,OAAQC,EAAO,IAAIC,MAAMH,GAAOI,EAAO,EAAGA,EAAOJ,EAAMI,IAC1EF,EAAAE,GAAQ9C,UAAU8C,GAEzB,IAAI4J,EAAMiY,EAASrc,MAAMf,KAAM3E,GAExB,OADgB,QAAtB8hB,EAASE,EAAEvf,aAA8B,IAAXqf,GAA6BA,EAAOpc,MAAMf,KAAM3E,GACxE8J,CACT,EACIiY,EAAWpf,EAAIhD,GACfqiB,EAAI,IAAIC,EAAqBxd,QAAQyb,GAEzC,OADAvd,EAAIhD,GAAUkiB,EACP,WAGLG,EAAEjX,QACEpI,EAAIhD,KAAYkiB,IAElBlf,EAAIhD,GAAUoiB,EAElB,QACOG,GACA,OAAAN,CAAA,CACT,EA/BF,IAAIK,EAAuB/iB,EAAiD,0DACxE0iB,EAAmB,WAA6B,CA+BpD,EAIM,8CAAA,SAIU5iB,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EACrB,IAEIK,EAF6B,oBAAZO,SAA2BA,QAAQ/B,OAAS+B,QAAQ/B,MAAM+G,KACtDhF,QAAQ/B,MAAM+G,KAAKhF,SAAW,WAAa,EAEpEZ,EAAiB,QAAIK,CAAA,EAIf,+CAAA,SAIUN,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QACR,SAAU0D,EAAKuJ,GACtB,GAAW,MAAPvJ,EACF,MAAO,CAAC,EAEV,IAAImH,EAAM,CAAC,EAIJ,OAHP1K,OAAO0O,KAAKnL,GAAKjD,SAAQ,SAAUzB,GACjC6L,EAAI7L,GAAOiO,EAAEvJ,EAAI1E,GAAI,IAEhB6L,CAAA,CACT,EAIM,iDAAA,SAIU9K,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAGjB,SAAqBiN,GACf,IAAAiW,EAAS/kB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,WAAa,EAC9F,OAAO,WACD,IAAAkT,EACA,IACOA,EAAApE,EAAExG,WAAM,EAAQtI,iBAClB+J,GACP,GAAsB,oBAAXxK,QAA0BA,OAAOylB,SACpC,MAAAjb,EAEJ,IAAAkb,EAAUF,EAAOhb,IACrB,EAAImb,EAAU/iB,SAAS,YAAa4H,IACpC,EAAIob,EAAmBhjB,SAAS4H,EAAKkb,EAAO,CAEvC,OAAA/R,CACT,CAAA,EAjBF,IAAIiS,EAAqBpjB,EAAuBD,EAA+C,yDAC3FojB,EAAYnjB,EAAuBD,EAAsC,+CAiB7E,EAIM,oDAAA,SAIUF,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQ0C,eAaC,SAAeN,EAAMF,GAC5B,GAAIA,EAAS,CACP,IACFuW,EADEE,EAAYtD,EAA2BkO,GAEvC,IACG,IAAA5K,EAAUrC,MAAOmC,EAAQE,EAAU7C,KAAK/E,MAAO,CAClD,IAAIyS,EAAQ/K,EAAMxZ,MACdA,EAAQiD,EAAQshB,GAChBC,EAASxkB,KAENmD,EAAAohB,GAASvkB,EAAM8W,WACtB,QAEK7N,GACPyQ,EAAU5R,EAAEmB,EAAG,CACf,QACAyQ,EAAU1L,GAAE,CAEV,IACFyW,EADEC,EAAatO,EAA2BuO,GAExC,IACG,IAAAD,EAAWrN,MAAOoN,EAASC,EAAW7N,KAAK/E,MAAO,CAI5C,IAHT,IAAI8S,EAASH,EAAOzkB,MAChB6kB,EAAQ5hB,EAAQ2hB,IAAW,CAAC,EAC5BE,EAAW,CAAC,EACP9L,EAAK,EAAGC,EAAe/X,OAAO0O,KAAKiV,GAAQ7L,EAAKC,EAAapX,OAAQmX,IAAM,CAC9E,IAAAjZ,EAAMkZ,EAAaD,GACnByK,EAASoB,EAAM9kB,GACfykB,EAASf,KACXqB,EAAS/kB,EAAI+W,YAAc2M,EAAO3M,WACpC,CAIF3T,EAAKyhB,GAAUE,CAAA,QAEV7b,GACPyb,EAAW5c,EAAEmB,EAAG,CAChB,QACAyb,EAAW1W,GAAE,CACf,CACF,EArDF,IAAI5L,EAAWnB,EAAuBD,EAAyD,oDACtF,SAAAoV,EAA2BC,EAAGC,GAAsB,IAAAC,EAAuB,oBAAXC,QAA0BH,EAAEG,OAAOC,WAAaJ,EAAE,cAAe,IAAKE,EAAI,CAAM,GAAAxU,MAAM2U,QAAQL,KAAOE,EACrK,SAA4BF,EAAGM,GAAU,GAAKN,EAAL,CAAgB,GAAiB,iBAANA,EAAuB,OAAAO,EAAkBP,EAAGM,GAAa,IAAAE,EAAI3V,OAAOgE,UAAU4R,SAAS1R,KAAKiR,GAAGU,MAAM,GAAK,GAAgE,MAAnD,WAANF,GAAkBR,EAAEW,cAAaH,EAAIR,EAAEW,YAAY/S,MAAgB,QAAN4S,GAAqB,QAANA,EAAoB9U,MAAMkV,KAAKZ,GAAc,cAANQ,GAAqB,2CAA2CK,KAAKL,GAAWD,EAAkBP,EAAGM,QAArG,CAAvP,CAAkW,CADzOQ,CAA4Bd,KAAOC,EAAqD,CAAMC,IAAQF,EAAAE,GAAI,IAAIzX,EAAI,EAAOsY,EAAI,WAAc,EAAG,MAAO,CAAEC,EAAGD,EAAGP,EAAG,WAAe,OAAI/X,GAAKuX,EAAExU,OAAe,CAAEiQ,MAAM,GAAe,CAAEA,MAAM,EAAO9R,MAAOqW,EAAEvX,KAAK,EAAMgJ,EAAG,SAAWwP,GAAY,MAAAA,CAAA,EAAOtJ,EAAGoJ,EAAE,CAAW,MAAA,IAAIG,UAAU,wIAAuI,CAAS,IAAyCtO,EAAzCuO,GAAmB,EAAMC,GAAS,EAAmB,MAAA,CAAEJ,EAAG,WAAoBd,EAAAA,EAAGnR,KAAKiR,EAAC,EAAMQ,EAAG,WAAmB,IAAAa,EAAOnB,EAAGoB,OAA6C,OAArCH,EAAmBE,EAAK5F,KAAa4F,CAAA,EAAS5P,EAAG,SAAW8P,GAAgBH,GAAA,EAAYxO,EAAA2O,CAAA,EAAQ5J,EAAG,WAAmB,IAAOwJ,GAAiC,MAAbjB,EAAGsB,UAAmBA,QAAO,CAAK,QAAU,GAAIJ,EAAc,MAAAxO,CAAA,CAAK,EAAI,CAEz9B,SAAA2N,EAAkBkB,EAAKC,IAAkB,MAAPA,GAAeA,EAAMD,EAAIjW,YAAciW,EAAIjW,QAAiB,IAAA,IAAA/C,EAAI,EAAGkZ,EAAO,IAAIjW,MAAMgW,GAAMjZ,EAAIiZ,EAAKjZ,IAAYkZ,EAAAlZ,GAAKgZ,EAAIhZ,GAAa,OAAAkZ,CAAA,CAChL,SAASwM,EAASxkB,GAChB,MAAO,wBAAwBkX,MAAM,EAAG9U,EAASf,SAASrB,GAAM,CAElE,IAAIskB,EAAkB,CAGtB,QAAS,UACLK,EAAe,CAAC,OAAQ,QA2C5B,EAIM,uDAAA,SAIU7jB,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQgkB,cAgDC,SAAchiB,EAASiiB,GACR,oBAAXvmB,QAA0BA,OAAOylB,UAC1B,EAAAE,EAAU/iB,SAAS0B,GAEjCiiB,GAAQA,EAAKC,OAASD,EAAKC,MAAMC,OAA6C,mBAA7BF,EAAKC,MAAMC,MAAMvY,SAAkE,IAAxCqY,EAAKC,MAAMC,MAAMvY,QAAQ,YAAoBwY,KAAKC,UAAY,KAG9JC,EAAcnX,EAAc,CAC1BnL,WACCiiB,GAAK,EAxDVjkB,EAAiB,QA0DR,SAAmBkI,EAAKkb,GAC3B,IACF,IACImB,EAQApb,EATAnH,EAAUkG,EAAIlG,QAEd,IACFuiB,EAAgB/X,KAAKC,UAAU2W,GAASpN,MAAM,EAAG,WAC1C9N,GACH,IACFqc,EAAgB,gCAAgC/iB,OAAOrB,OAAOgE,UAAU4R,SAAS1R,KAAK+e,UAC/Elb,GAAK,CAAW,CAGvB,IACMiB,EAAApG,EAAUzC,QAAQ0C,kBAAkBkF,GAAKiB,MAAMC,KAAI,SAAUC,GAC5D,MAAA,CACLmb,SAAUnb,EAAMM,IAChB8a,OAAQpb,EAAME,KACdmb,MAAOrb,EAAMI,OACbkb,SAAUtb,EAAM9C,MAAQ,IAC1B,UAEK2B,GAAK,CAGAoc,EAAA,CACZtiB,UACAkiB,MAAO,CACLK,iBAEF3hB,UAAW,CACTgiB,OAAQ,CAAC,CACPtU,KAAMpI,EAAIoI,KACVrR,MAAO+C,EACP6iB,WAAY,CACVC,OAAQ3b,cAKTjB,IACP,EAAImb,EAAU/iB,SAAS,iBAAkB4H,EAAG,CAC9C,EAlGF,IAAIwG,EAAmBxO,EAAuBD,EAAiE,4DAC3GojB,EAAYnjB,EAAuBD,EAAsC,gDACzE8C,EAAY7C,EAAuBD,EAAsC,gDACpE,SAAA0O,EAAQhI,EAAQiI,GAAsB,IAAAC,EAAO1O,OAAO0O,KAAKlI,GAAS,GAAIxG,OAAO2O,sBAAuB,CAAM,IAAAC,EAAU5O,OAAO2O,sBAAsBnI,GAASiI,IAAmBG,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO9O,OAAO+D,yBAAyByC,EAAQsI,GAAK1L,UAAA,KAAiBsL,EAAK3Q,KAAKuI,MAAMoI,EAAME,EAAO,CAAY,OAAAF,CAAA,CAC9U,SAAS1B,EAAc+B,GAAU,IAAA,IAASnR,EAAI,EAAGA,EAAII,UAAU2C,OAAQ/C,IAAK,CAAM,IAAAoR,EAAS,MAAQhR,UAAUJ,GAAKI,UAAUJ,GAAK,CAAC,EAAOA,EAAA,EAAI4Q,EAAQxO,OAAOgP,IAAS,GAAI1O,SAAQ,SAAUzB,IAAO,EAAI0P,EAAiBpO,SAAS4O,EAAQlQ,EAAKmQ,EAAOnQ,GAAI,IAAQmB,OAAOiP,0BAA4BjP,OAAOkP,iBAAiBH,EAAQ/O,OAAOiP,0BAA0BD,IAAWR,EAAQxO,OAAOgP,IAAS1O,SAAQ,SAAUzB,GAAOmB,OAAOC,eAAe8O,EAAQlQ,EAAKmB,OAAO+D,yBAAyBiL,EAAQnQ,GAAI,GAAI,CAAY,OAAAkQ,CAAA,CAE3f,IAAA6V,EAAyB,2CAC7B,SAAST,EAAcliB,GACjB,IACF,IAAIiD,EAAS2f,EACThjB,EAAUI,EAAKJ,QACf2H,EAAM,+HACN4D,EAAOf,KAAKC,UAAUU,EAAc,CACtCnL,UACAzB,OAAQ,aACR0kB,SAAU,aACVva,QAAS,CACP0C,QAAS,CACP,aAAmC,oBAAd0M,WAA6BA,UAAUoL,WAE9Dvb,IAAyB,oBAAbpH,UAA4BA,SAASC,MAEnD2iB,QAASJ,EACTK,aAAqC,QAAtB/f,EAAU3H,cAAgC,IAAZ2H,GAAkF,QAAnD2f,EAAwB3f,EAAQggB,qBAAqD,IAA1BL,OAArE,EAAiHA,EAAsBM,YAAc,QACtNljB,IACC,GAAkB,oBAAX1E,OAAwB,CAC7B,IACAsS,EAAM,IADAtS,OAAOkd,mBAAqBJ,gBAElCxK,EAAAmL,KAAK,OAAQxR,GACjBqG,EAAIqL,KAAK9N,EAAI,KACa,oBAAVtD,OAKhBA,MAAMN,EAAK,CACTjJ,OAAQ,OACR6M,SACC4D,OAAM,SAAUoU,IACjB,EAAIlC,EAAU/iB,SAAS,2BAA4BilB,EAAY,UAG5Drd,IACP,EAAImb,EAAU/iB,SAAS,iBAAkB4H,EAAG,CAC9C,CAuDF,EAIM,gDAAA,SAIUnI,EAAyBC,GAKlCG,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QACR,SAAWf,EAAO8Z,GACrB,IAAAyM,EAAMrnB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,EACvE,OAAAc,GAAS8Z,GAAU9Z,EAAM0N,UAAU6Y,EAAKA,EAAMzM,EAAOjY,UAAYiY,CAAA,CAC1E,EAIM,wCAAA,SAIUhZ,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAEDwmB,EAAS,QAAIzlB,EAAQ0lB,oBAAiB,EAC9C,IAAI9gB,EAAmB1E,EAAuBD,EAAiE,4DAC3G4E,EAAgB3E,EAAuBD,EAA8D,yDACrGyO,EAAmBxO,EAAuBD,EAAiE,4DAC3G0lB,EAA4BzlB,EAAuBD,EAA0E,qEAC7H2lB,EAAW1lB,EAAuBD,EAA8C,+CAChFyB,EAAczB,EAAiD,iDAC/D4lB,EAAW3lB,EAAuBD,EAA8C,+CAChF6lB,EAAS7lB,EAA4C,4CAChD,SAAA0O,EAAQhI,EAAQiI,GAAsB,IAAAC,EAAO1O,OAAO0O,KAAKlI,GAAS,GAAIxG,OAAO2O,sBAAuB,CAAM,IAAAC,EAAU5O,OAAO2O,sBAAsBnI,GAASiI,IAAmBG,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO9O,OAAO+D,yBAAyByC,EAAQsI,GAAK1L,UAAA,KAAiBsL,EAAK3Q,KAAKuI,MAAMoI,EAAME,EAAO,CAAY,OAAAF,CAAA,CAC9U,SAAS1B,EAAc+B,GAAU,IAAA,IAASnR,EAAI,EAAGA,EAAII,UAAU2C,OAAQ/C,IAAK,CAAM,IAAAoR,EAAS,MAAQhR,UAAUJ,GAAKI,UAAUJ,GAAK,CAAC,EAAOA,EAAA,EAAI4Q,EAAQxO,OAAOgP,IAAS,GAAI1O,SAAQ,SAAUzB,IAAO,EAAI0P,EAAiBpO,SAAS4O,EAAQlQ,EAAKmQ,EAAOnQ,GAAI,IAAQmB,OAAOiP,0BAA4BjP,OAAOkP,iBAAiBH,EAAQ/O,OAAOiP,0BAA0BD,IAAWR,EAAQxO,OAAOgP,IAAS1O,SAAQ,SAAUzB,GAAOmB,OAAOC,eAAe8O,EAAQlQ,EAAKmB,OAAO+D,yBAAyBiL,EAAQnQ,GAAI,GAAI,CAAY,OAAAkQ,CAAA,CAE/flP,EAAQ0lB,eADa,IAEjB,IAYAK,EAAqC,WACvC,SAASA,IACP,IAAI1f,EAAQX,MACZ,EAAId,EAAiBtE,SAASoF,KAAMqgB,GACpCrgB,KAAKsgB,QAAU,GAEd,CAAA,MAAO,OAAQ,OAAQ,QAAS,SAASvlB,SAAQ,SAAUC,GACpD2F,EAAA3F,GAAU,WACd,IAAA,IAASG,EAAO1C,UAAU2C,OAAQC,EAAO,IAAIC,MAAMH,GAAOI,EAAO,EAAGA,EAAOJ,EAAMI,IAC1EF,EAAAE,GAAQ9C,UAAU8C,GAEnBoF,EAAAnF,SAAS,oBAAoB,WAK1B,MAHQ,UAAXR,IADiBvC,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,GACjDmD,8BAC3BI,EAAAC,QAAQC,eAAeyE,EAAOtF,EAAK,GAAIA,EAAM,CAAC,GAAG,GAExD,CACLc,SAAUnB,EAAOoB,cACjBf,OACF,GACC,CACDklB,yBAAyB,GAE7B,CAAA,IAEFvgB,KAAKwgB,gBAAiB,EACtBxgB,KAAKygB,WAAa,GAGlBzoB,OAAO0oB,YAAc1gB,KAAK2gB,cAAczgB,KAAKF,KAAI,CAoM5CqgB,OAlMP,EAAIlhB,EAAcvE,SAASylB,EAAW,CAAC,CACrC/mB,IAAK,WACLC,MAAO,SAAkBqR,EAAMgW,GACzB,IAAAC,EAAOpoB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC5EqoB,EAAOhJ,KAAKC,MACX/X,KAAA+gB,MAAK,SAAUlmB,GACXA,EAAAW,SAASoP,EAAMgW,EAAYnZ,EAAcA,EAAc,CAAI,EAAAoZ,GAAO,GAAI,CAC3EG,aAAcF,IACd,GACH,GAEF,CACDxnB,IAAK,WACLC,MAAO,SAAkBsB,GAEhB,IADPmF,KAAKihB,QAAUpmB,EACRmF,KAAKsgB,QAAQllB,OAAS,GACnB4E,KAAKsgB,QAAQ5f,OACrB6G,CAAEvH,KAAKihB,QACT,GAED,CACD3nB,IAAK,OACLC,MAAO,SAAcgO,GACnB,IAAIvH,KAAKkhB,YAGT,GAAIlhB,KAAKihB,QACP1Z,EAAEvH,KAAKihB,aACF,CACD,GAAAjhB,KAAKsgB,QAAQllB,QA1EJ,IA8EX,OAHA4E,KAAKkhB,aAAc,EACnBhmB,QAAQimB,KAAK,wFACbnhB,KAAKsD,YAGPtD,KAAKsgB,QAAQ9nB,KAAK+O,EAAErH,KAAKF,MAAK,CAChC,GAED,CACD1G,IAAK,OACLC,MAAO,SAAcklB,GACf,IAAAoC,EAAOpoB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC5E,IAACuH,KAAKwgB,eAAgB,CACpB,IAAAY,EACAC,EAAwBR,EAAKra,iBAC/BA,OAA6C,IAA1B6a,GAA0CA,EAC7DC,EAAwBT,EAAKpa,mBAC7BA,OAA+C,IAA1B6a,GAA2CA,EAChEC,EAAwBV,EAAKW,6BACsB,IAA1BD,GAA0CA,IAEnEvhB,KAAKygB,WAAWjoB,MAAS,EAAAwD,EAAYylB,oBAAoBzhB,OAE3DA,KAAKygB,WAAWjoB,MAAA,EAAS0nB,EAAStlB,SAASoF,KAAM,CAC/CwG,mBAAoBA,EACpBC,qBAAsBA,EACtBF,YAAqK,KAAxJsa,SAA+E,QAAlCO,EAAgBP,EAAKa,eAAuC,IAAlBN,OAApD,EAAwFA,EAAc1lB,cAExJsE,KAAKygB,WAAWjoB,MAAS,EAAA2nB,EAASvlB,SAASoF,OAC3CA,KAAKwgB,gBAAiB,EACjBxgB,KAAA+gB,MAAK,SAAUlmB,GAClBA,EAAOhD,KAAK4mB,EAxGW,WAC3B,IAAA1e,EAAOtH,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAA,EAC7EkpB,EAAe5hB,EAAK4hB,aACpBnlB,KAAcyjB,EAA0BrlB,SAASmF,EAAM,CAAC,iBAC1D,OAAI4hB,EACKla,EAAc,CACnBma,UAAW,GAAG9lB,OAAO6lB,EAAc,MACnCE,SAAU,GAAG/lB,OAAO6lB,EAAc,OACjCnlB,GAEEA,CACT,CA6F6BslB,CAA2BjB,GAAK,GACpD,CACH,GAED,CACDvnB,IAAK,QACLC,MAAO,WACAyG,KAAA+gB,MAAK,SAAUlmB,GAClBA,EAAOse,OAAM,GACd,GAEF,CACD7f,IAAK,YACLC,MAAO,WACAyG,KAAAygB,WAAW1lB,SAAQ,SAAUwM,GAChC,OAAOA,GAAE,IAEXvH,KAAKsgB,QAAU,GACVtgB,KAAA+gB,MAAK,SAAUlmB,GAClBA,EAAOyI,WAAU,GAClB,GAEF,CACDhK,IAAK,WACLC,MAAO,SAAkBT,EAAI+nB,GACtB7gB,KAAA+gB,MAAK,SAAUlmB,GACXA,EAAApB,SAASX,EAAI+nB,EAAI,GACzB,GAEF,CACDvnB,IAAK,kBACLC,MAAO,WACAyG,KAAA+gB,MAAK,SAAUlmB,GAClBA,EAAOknB,iBAAgB,GACxB,GAEF,CACDzoB,IAAK,QACLC,MAAO,SAAeyoB,EAAiBC,GAChCjiB,KAAA+gB,MAAK,SAAUlmB,GACXA,EAAA4E,MAAMuiB,EAAiBC,EAAe,GAC9C,GAEF,CACD3oB,IAAK,gBACLC,MAAO,SAAuByI,GACxB,GAAc,mBAAPA,EACH,MAAA,IAAImB,MAAM,oDAEbnD,KAAA+gB,MAAK,SAAUlmB,GACdA,EAAO8lB,cACT9lB,EAAO8lB,cAAc3e,GAErBA,EAAGnH,EAAOkb,aACZ,GACD,GAEF,CACDzc,IAAK,mBACLC,MAAO,SAA0BiQ,GAG3BxJ,KAAKihB,SACFjhB,KAAAihB,QAAQiB,iBAAiB1Y,EAChC,GAED,CACDlQ,IAAK,aACLC,MAAO,SAAoByI,GACpBhC,KAAA+gB,MAAK,SAAUlmB,GAClBmH,EAAGnH,EAAOsnB,QAAO,GAClB,GAEF,CACD7oB,IAAK,iBACLC,MAAO,SAAwB+C,GACzB,IAAAE,EAAU/D,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnFuD,EAAYC,QAAQC,eAAe8D,KAAM1D,EAAS,CAACA,GAAUE,EAAO,GAErE,CACDlD,IAAK,mBACLC,MAAO,SAA0B2D,GAC3B,IAAAV,EAAU/D,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnFuD,EAAYC,QAAQgB,iBAAiB+C,KAAM9C,EAAWV,EAAO,GAE9D,CACDlD,IAAK,UACLwE,IAAK,WACI,OAAAkC,KAAKihB,SAAWjhB,KAAKihB,QAAQkB,OAAA,GAErC,CACD7oB,IAAK,aACLwE,IAAK,WACI,OAAAkC,KAAKihB,SAAWjhB,KAAKihB,QAAQlL,YAAA,GAErC,CACDzc,IAAK,eACLwE,IAAK,WACI,OAAAkC,KAAKihB,SAAWjhB,KAAKihB,QAAQlL,YAAA,GAErC,CACDzc,IAAK,cACLwE,IAAK,WACI,OAAAkC,KAAKihB,SAAWjhB,KAAKihB,QAAQmB,WAAA,GAErC,CACD9oB,IAAK,WACLwE,IAAK,WACI,OAAAkC,KAAKihB,SAAWjhB,KAAKihB,QAAQoB,QAAA,GAErC,CACD/oB,IAAK,QACLwE,IAAK,WACI,OAAAkC,KAAKihB,SAAWjhB,KAAKihB,QAAQqB,KAAA,GAErC,CACDhpB,IAAK,gBACLC,MAAO,WACD,IAAAiD,EAAU/D,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnF,OAAA,EAAW2nB,EAAOmC,gBAAgBviB,KAAMxD,EAAO,GAEhD,CACDlD,IAAK,kBACLC,MAAO,WACD,IAAAiD,EAAU/D,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EACnF,OAAA,EAAW2nB,EAAOoC,kBAAkBxiB,KAAMxD,EAAO,GAElD,CACDlD,IAAK,aACLwE,IAAK,WACH,SAAUkC,KAAKkhB,aAAelhB,KAAKihB,SAAWjhB,KAAKihB,QAAQC,YAAA,KAGxDb,CAAA,CAjOgC,GAmOzC/lB,EAAiB,QAAI+lB,CAAA,EAIf,4CAAA,SAIUhmB,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,QAkDjB,WACM,IAAAmoB,EAAYhqB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,WAAa,EAC7FiqB,EAAkBjqB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAKkqB,EACtFC,EAAcnqB,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,WAC7E,OAAA,IAAIoqB,EAAWjoB,OACxB,EACA,GAAyB,oBAAdwZ,WAAmD,gBAAtBA,UAAU0O,QAC1C,MAAA,IAAI3f,MAAM4f,GAEd,GAAkB,oBAAX/qB,OAAwB,CACjC,GAAIA,OAAOgrB,kBACT,OAAON,IAEL,GAAA1qB,OAAOirB,kBAAoBjrB,OAAO+G,QAAS,CAE7C/G,OAAOkrB,oBAAsBlrB,OAAOirB,iBACpC,IAAIE,EAAWP,IAER,OADPH,EAAUU,GACHA,CAAA,CACT,CAEF,OAAOR,GAAiB,EAtE1B,IAAIE,EAAaroB,EAAuBD,EAAuC,0CAC3EwoB,EAAsB,sJACtBJ,EAAmB,WACd,MAAA,CACL9qB,KAAM,WAAiB,EACvByL,UAAW,WAAsB,EACjC8f,IAAK,WAAgB,EACrBC,KAAM,WAAiB,EACvBlC,KAAM,WAAiB,EACvBhoB,MAAO,WAAkB,EACzBygB,MAAO,WAAkB,EACzBpe,SAAU,WAAqB,EAC/B/B,SAAU,WAAqB,EAC/B0f,MAAO,WAAkB,EACzB,YAAIkJ,GACK,OAAA,IACT,EACA,eAAID,GACK,OAAA,IACT,EACA,gBAAIrM,GACK,OAAA,IACT,EACAuN,cAAe,WACb,OAAO,SAAU5K,GACf,OAAO,WACE,OAAAA,EAAM3X,WAAM,EAAQtI,UAC7B,CACF,CACF,EACA8qB,gBAAiB,WACf,OAAO,WACL,OAAO,SAAUrS,GACf,OAAO,SAAUgI,GACf,OAAOhI,EAAKgI,EACd,CACF,CACF,CACF,EACAzZ,MAAO,WAAkB,EACzBkhB,cAAe,WAA0B,EACzC6C,WAAY,WAAuB,EACnCzB,gBAAiB,WAA4B,EAC7C0B,SAAU,WAAqB,EAC/BC,SAAU,WAAqB,EAC/BxnB,eAAgB,WAA2B,EAC3Ce,iBAAkB,WAA4B,EAElD,CAuBA,EAIM,oCAAA,SAIU5C,EAAyBC,EAASC,GAKlD,IAAIC,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAQqpB,iBAAmBA,EAC3BrpB,EAAQspB,mBAAqBA,EAC7BtpB,EAAiB,QA+EjB,WACE,IAAIyF,EAAOtH,UAAU2C,OAAS,QAAsB,IAAjB3C,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC9EorB,EAAmB9jB,EAAK8jB,iBACxBC,EAAkB/jB,EAAKgkB,WACvBA,OAAiC,IAApBD,EAA6B,SAAWA,EACrDjD,GAAA,EAAWZ,EAA0BrlB,SAASmF,EAAM,CAAC,mBAAoB,eACvEikB,EAAoBL,EAAiBI,GACvCnE,EAAYoE,EAAkBpE,UAC9BqE,EAAeD,EAAkBC,aACjCC,EAAeF,EAAkBE,aAC/BC,EAAYtD,EAAKsD,WAAaN,EAC9BlC,EAAed,EAAKc,cAAgBkC,GAAoBK,EACxDf,GAAY,EAAGiB,EAAexpB,UAAS,WACrC,IAAAypB,EAASnsB,SAASS,cAAc,UAChCgpB,IACFiC,EAAmBjC,GACnB3pB,OAAO2nB,cAAcC,UAAYA,GAE/BuE,EACFE,EAAOxrB,IAAM,GAAGiD,OAAOqoB,EAAW,kBACzBnsB,OAAO2nB,eAAiB3nB,OAAO2nB,cAAc2E,UAC/CD,EAAAxrB,IAAMb,OAAO2nB,cAAc2E,UACzBtsB,OAAOusB,eAChBF,EAAOxrB,IAAMb,OAAOusB,eAEpBF,EAAOxrB,IAAM,GAAGiD,OAAOmoB,EAAc,oBAEvCI,EAAOzrB,OAAQ,EACNV,SAAAssB,KAAKC,YAAYJ,GAC1BA,EAAO1M,OAAS,WAIkB,mBAArB3f,OAAO0sB,UAChB9jB,YAAW,WACAuiB,EAAAM,SAAS,IAAIzrB,OAAO0sB,UAAU,CACrCX,eACA,KAGJ7oB,QAAQimB,KAAK,yEACbgC,EAAS7f,YAEb,EACA+gB,EAAO7I,QAAU,WACftgB,QAAQimB,KAAK,qFACbgC,EAAS7f,WACX,CAAA,IAEK,OAAA6f,CAAA,EA/HT,IAAIlD,EAA4BzlB,EAAuBD,EAA0E,qEAC7H6pB,EAAiB5pB,EAAuBD,EAA2C,8CACnFoqB,EAAiB,CACnB,oBAAqB,yBACrB,mBAAoB,yBACpB,mBAAoB,yBACpB,gBAAiB,sBACjB,qBAAsB,2BACtB,oBAAqB,0BACrB,oBAAqB,0BACrB,oBAAqB,0BACrB,oBAAqB,0BACrB,sBAAuB,4BACvB,kBAAmB,wBACnB,oBAAqB,0BACrB,2BAA4B,iCAC5B,2BAA4B,iCAC5B,wBAAyB,8BACzB,6BAA8B,mCAC9B,4BAA6B,kCAC7B,4BAA6B,kCAC7B,4BAA6B,kCAC7B,4BAA6B,kCAC7B,8BAA+B,oCAC/B,0BAA2B,gCAC3B,4BAA6B,mCAW/B,SAAShB,EAAiBI,GACpB,GAAe,WAAfA,GAA0C,kBAAfA,EAAgC,CAIzD,IAEF,IACIa,EADY1sB,SAAS2sB,cACDhsB,IAAIqiB,MAAM,+BAC9B4J,EAAiBF,GAAWA,EAAQ,GACpC,GAAAE,GAAkBH,EAAeG,GAC5B,MAAA,CACLlF,WArBemF,EAqBYD,EApB/BC,EAASC,WAAW,eACf,UACED,EAASC,WAAW,aACtB,cAEA,QAgBDf,aAAcW,GAAWA,EAAQ,GACjCV,aAAcS,EAAeG,UAG1BG,GAAG,CAKL,MAAA,CACLrF,UAAW,OACXqE,aAAc,2BAChB,CAGO,MAAA,CACLrE,eAAW,EACXqE,aAA2B,4BAC3BC,aAA2B,2BAxCjC,IAAyBa,CA0CvB,CAEF,SAASnB,EAAmBjC,QACU,IAAzB3pB,OAAO2nB,gBAChB3nB,OAAO2nB,cAAgB,CAAC,GAE1B3nB,OAAO2nB,cAAciC,UAAY,GAAG9lB,OAAO6lB,EAAc,MACzD3pB,OAAO2nB,cAAckC,SAAW,GAAG/lB,OAAO6lB,EAAc,KAAI,CAoD9D,EAIM,4DAAA,SAIUuD,GAOTC,EAAA7qB,QALE,SAAkB+W,EAAKC,IACnB,MAAPA,GAAeA,EAAMD,EAAIjW,YAAciW,EAAIjW,QAC/C,IAAA,IAAS/C,EAAI,EAAGkZ,EAAO,IAAIjW,MAAMgW,GAAMjZ,EAAIiZ,EAAKjZ,IAAKkZ,EAAKlZ,GAAKgZ,EAAIhZ,GAC5D,OAAAkZ,CAAA,EAE2B2T,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAInG,6DAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAI8qB,EAAmB9qB,EAAiD,6DAIjE4qB,EAAA7qB,QAHP,SAA4B+W,GAC1B,GAAI/V,MAAM2U,QAAQoB,GAAM,OAAOgU,EAAiBhU,EAAG,EAEhB6T,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIpG,0DAAA,SAIU4qB,GAOTC,EAAA7qB,QALE,SAAgB6oB,EAAUmC,GAC7B,KAAEnC,aAAoBmC,GAClB,MAAA,IAAIxU,UAAU,oCACtB,EAEgCoU,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIjG,uDAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAIgrB,EAAgBhrB,EAA8C,0DACzD,SAAAirB,EAAkBhc,EAAQic,GACjC,IAAA,IAASptB,EAAI,EAAGA,EAAIotB,EAAMrqB,OAAQ/C,IAAK,CACjC,IAAAqtB,EAAaD,EAAMptB,GACZqtB,EAAA7nB,WAAa6nB,EAAW7nB,aAAc,EACjD6nB,EAAWC,cAAe,EACtB,UAAWD,IAAYA,EAAWE,UAAW,GACjDnrB,OAAOC,eAAe8O,EAAQ+b,EAAcG,EAAWpsB,KAAMosB,EAAU,CACzE,CAUKP,EAAA7qB,QARE,SAAagrB,EAAaO,EAAYC,GAMtC,OALHD,GAAYL,EAAkBF,EAAY7mB,UAAWonB,GACrDC,GAA+BN,EAAAF,EAAaQ,GACzCrrB,OAAAC,eAAe4qB,EAAa,YAAa,CAC9CM,UAAU,IAELN,CAAA,EAEsBJ,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAI9F,0DAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAIgrB,EAAgBhrB,EAA8C,0DAe3D4qB,EAAA7qB,QAdE,SAAgB0D,EAAK1E,EAAKC,GAY1B,OAXPD,EAAMisB,EAAcjsB,MACT0E,EACFvD,OAAAC,eAAesD,EAAK1E,EAAK,CAC9BC,QACAsE,YAAY,EACZ8nB,cAAc,EACdC,UAAU,IAGZ5nB,EAAI1E,GAAOC,EAENyE,CAAA,EAEyBknB,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIjG,iEAAA,SAIU4qB,GAOTC,EAAA7qB,QALP,SAAgC0D,GACvB,OAAAA,GAAOA,EAAIE,WAAaF,EAAM,CACnCpD,QAAWoD,EACb,EAEuCknB,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIxG,2DAAA,SAIU4qB,GAKTC,EAAA7qB,QAHP,SAA0ByrB,GACxB,GAAsB,oBAAXhW,QAAmD,MAAzBgW,EAAKhW,OAAOC,WAA2C,MAAtB+V,EAAK,cAA8B,OAAAzqB,MAAMkV,KAAKuV,EAAI,EAEvFb,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIlG,6DAAA,SAIU4qB,GAKTC,EAAA7qB,QAHP,WACQ,MAAA,IAAIwW,UAAU,uIAAsI,EAEvHoU,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIpG,mEAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAIyrB,EAA+BzrB,EAA6D,yEAgBzF4qB,EAAA7qB,QAfE,SAAyBmP,EAAQwc,GACpC,GAAU,MAAVxc,EAAgB,MAAO,CAAC,EACxB,IACAnQ,EAAKjB,EADLmR,EAASwc,EAA6Bvc,EAAQwc,GAElD,GAAIxrB,OAAO2O,sBAAuB,CAC5B,IAAA8c,EAAmBzrB,OAAO2O,sBAAsBK,GACpD,IAAKpR,EAAI,EAAGA,EAAI6tB,EAAiB9qB,OAAQ/C,IACvCiB,EAAM4sB,EAAiB7tB,GACnB4tB,EAAS/f,QAAQ5M,IAAQ,GACxBmB,OAAOgE,UAAU0nB,qBAAqBxnB,KAAK8K,EAAQnQ,KACjDkQ,EAAAlQ,GAAOmQ,EAAOnQ,GACvB,CAEK,OAAAkQ,CAAA,EAEkC0b,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAI1G,wEAAA,SAIU4qB,GAcTC,EAAA7qB,QAZE,SAA8BmP,EAAQwc,GACzC,GAAU,MAAVxc,EAAgB,MAAO,CAAC,EAC5B,IAEInQ,EAAKjB,EAFLmR,EAAS,CAAC,EACV4c,EAAa3rB,OAAO0O,KAAKM,GAE7B,IAAKpR,EAAI,EAAGA,EAAI+tB,EAAWhrB,OAAQ/C,IACjCiB,EAAM8sB,EAAW/tB,GACb4tB,EAAS/f,QAAQ5M,IAAQ,IACtBkQ,EAAAlQ,GAAOmQ,EAAOnQ,IAEhB,OAAAkQ,CAAA,EAEuC0b,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAI/G,6DAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAI8rB,EAAoB9rB,EAAkD,8DACtE+rB,EAAkB/rB,EAAgD,4DAClEgsB,EAA6BhsB,EAA2D,uEACxFisB,EAAoBjsB,EAAkD,8DAInE4qB,EAAA7qB,QAHP,SAA4B+W,GACnB,OAAAgV,EAAkBhV,IAAQiV,EAAgBjV,IAAQkV,EAA2BlV,IAAQmV,GAAkB,EAE3EtB,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIpG,uDAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAIqD,EAAWrD,EAAuC,mDAA4D,QAW3G4qB,EAAA7qB,QAVE,SAAauiB,EAAO4J,GAC3B,GAAuB,WAAnB7oB,EAAQif,IAAiC,OAAVA,EAAuB,OAAAA,EACtD,IAAA6J,EAAO7J,EAAM9M,OAAO4W,aACxB,QAAa,IAATD,EAAoB,CACtB,IAAIvhB,EAAMuhB,EAAK/nB,KAAKke,EAAO4J,GAAQ,WACnC,GAAqB,WAAjB7oB,EAAQuH,GAA0B,OAAAA,EAChC,MAAA,IAAI2L,UAAU,+CAA8C,CAEpE,OAAiB,WAAT2V,EAAoBG,OAASC,QAAQhK,EAAK,EAErBqI,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAI9F,yDAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAIqD,EAAWrD,EAAuC,mDAA4D,QAC9GosB,EAAcpsB,EAA4C,wDAKvD4qB,EAAA7qB,QAJP,SAAwBuR,GAClB,IAAAvS,EAAMqtB,EAAY9a,EAAK,UAC3B,MAAwB,WAAjBjO,EAAQtE,GAAoBA,EAAMstB,OAAOttB,EAAG,EAEpB4rB,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIhG,kDAAA,SAIU4qB,GAEhB,SAAStnB,EAAQI,GAGPknB,OAAAA,EAAO5qB,QAAUsD,EAAU,mBAAqBmS,QAAU,iBAAmBA,OAAOC,SAAW,SAAUhS,GAC/G,cAAcA,CAChB,EAAI,SAAUA,GACLA,OAAAA,GAAO,mBAAqB+R,QAAU/R,EAAIuS,cAAgBR,QAAU/R,IAAQ+R,OAAOtR,UAAY,gBAAkBT,CACvHknB,EAAAA,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,QAAUsD,EAAQI,EAAG,CAExFmnB,EAAA7qB,QAAUsD,EAASsnB,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,EAIzF,sEAAA,SAIU4qB,EAAQE,EAA0B7qB,GAElD,IAAI8qB,EAAmB9qB,EAAiD,6DASjE4qB,EAAA7qB,QARE,SAA4BsV,EAAGM,GACtC,GAAKN,EAAL,CACA,GAAiB,iBAANA,EAAuB,OAAAyV,EAAiBzV,EAAGM,GAClD,IAAAE,EAAI3V,OAAOgE,UAAU4R,SAAS1R,KAAKiR,GAAGU,MAAM,GAAK,GAErD,MADU,WAANF,GAAkBR,EAAEW,cAAaH,EAAIR,EAAEW,YAAY/S,MAC7C,QAAN4S,GAAqB,QAANA,EAAoB9U,MAAMkV,KAAKZ,GACxC,cAANQ,GAAqB,2CAA2CK,KAAKL,GAAWiV,EAAiBzV,EAAGM,QAApG,CALI,CAKsG,EAElEgV,EAAO5qB,QAAQ4D,YAAa,EAAMgnB,EAAO5qB,QAAiB,QAAI4qB,EAAO5qB,OAAA,GAOrGwsB,EAA2B,CAAC,EAGhC,SAASvsB,EAAoBwsB,GAExB,IAAAC,EAAeF,EAAyBC,GAC5C,QAAqB,IAAjBC,EACH,OAAOA,EAAa1sB,QAGjB4qB,IAAAA,EAAS4B,EAAyBC,GAAY,CAGjDzsB,QAAS,CAAA,GAOV,OAHAF,EAAoB2sB,GAAU7B,EAAQA,EAAO5qB,QAASC,GAG/C2qB,EAAO5qB,OAAA,CAMdC,EAAoBqF,EAAK,WACpB,GAAsB,iBAAfqnB,WAAgC,OAAAA,WACvC,IACH,OAAOjnB,MAAQ,IAAIknB,SAAS,cAAb,SACP7lB,GACJ,GAAkB,iBAAXrJ,OAA4B,OAAAA,MAAA,CACxC,CANwB,GAWpC,IAAImvB,EAAsB,CAAC,EAqBV,OAnBhB,WAED,IAAI7sB,EAAU6sB,EAMV3sB,EAAyBD,EAAwE,kEAC9FE,OAAAC,eAAeJ,EAAS,aAAe,CAC5Cf,OAAO,IAETe,EAAiB,aAAI,EACrB,IAEIK,GADA,EADSH,EAAuBD,EAAmC,sCAC7CK,WAE1BN,EAAiB,QAAIK,CAAA,CAhBpB,GAkBDwsB,EAAsBA,EAA6B,OAEvC,CAnxHZ,EAqxHA,IA7xHE7sB,QAAiBH,SAF8BA", "x_google_ignoreList": [0, 1, 2]}
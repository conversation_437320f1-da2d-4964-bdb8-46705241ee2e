/**
 * 🎯 CSS GESTOS MOBILE
 */

.mobile-gesture-area {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.mobile-swipeable {
  touch-action: pan-x pan-y;
  -webkit-user-select: none;
  user-select: none;
}

.mobile-tappable {
  touch-action: manipulation;
  cursor: pointer;
}

.mobile-tappable:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-out;
}

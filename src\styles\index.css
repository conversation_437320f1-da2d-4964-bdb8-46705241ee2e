/**
 * 🎯 CSS MODULAR - IMPORTAÇÃO ÚNICA
 *
 * Sistema de CSS modular unificado
 * Importação única para todo o projeto
 * Organizado por prioridade e funcionalidade
 */

/* ===== 1. DESIGN SYSTEM (PRIORIDADE MÁXIMA) ===== */
@import './design-system/index.css';

/* ===== 2. COMPONENTES MODULARES ===== */
@import './components/index.css';

/* ===== 3. UTILITIES E HELPERS ===== */
@import './utilities/index.css';

/* ===== 4. RESPONSIVIDADE ===== */
@import './responsive/index.css';

/* ===== 5. TAILWIND CSS ===== */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== ESTILOS BASE GLOBAIS ===== */
body {
  background: var(--color-bg);
  color: var(--color-text);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  transition: background var(--transition), color var(--transition);
  margin: 0;
  padding: 0;
}

/* Reset básico */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Otimizações de performance */
img {
  max-width: 100%;
  height: auto;
  contain-intrinsic-size: attr(width) attr(height);
}

/* Acessibilidade */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible */
.focus-visible:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent layout shifts */
.container {
  contain: layout style;
}

/* Optimize animations */
.animate-element {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/**
 * 🎯 UTILITIES E HELPERS
 * 
 * Classes utilitárias e helpers
 * Acessibilidade e performance
 */

/* ===== IMPORTAÇÕES ===== */
@import './accessibility.css';
@import './animations.css';
@import './performance.css';
@import './themes.css';

/* ===== UTILITIES BÁSICAS ===== */

/* Visibilidade */
.hidden { display: none !important; }
.visible { display: block !important; }
.invisible { visibility: hidden !important; }

/* Posicionamento */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* Overflow */
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-scroll { overflow: scroll !important; }

/* Display */
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

/* Flex utilities */
.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

.flex-row { flex-direction: row !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

/* Spacing */
.m-0 { margin: 0 !important; }
.m-auto { margin: auto !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }
.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

.p-0 { padding: 0 !important; }

/* Width & Height */
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }

.min-w-0 { min-width: 0 !important; }
.min-h-0 { min-height: 0 !important; }
.max-w-none { max-width: none !important; }
.max-h-none { max-height: none !important; }

/* Text utilities */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }

.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.break-words { word-wrap: break-word !important; }
.break-all { word-break: break-all !important; }

/* Background utilities */
.bg-transparent { background-color: transparent !important; }
.bg-current { background-color: currentColor !important; }

/* Border utilities */
.border-0 { border-width: 0 !important; }
.border { border-width: var(--border-width) !important; }
.border-2 { border-width: var(--border-width-thick) !important; }

.border-solid { border-style: solid !important; }
.border-dashed { border-style: dashed !important; }
.border-dotted { border-style: dotted !important; }
.border-none { border-style: none !important; }

/* Cursor utilities */
.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-help { cursor: help !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* User select */
.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }
.select-auto { user-select: auto !important; }

/* Pointer events */
.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }

/* Z-index utilities */
.z-0 { z-index: 0 !important; }
.z-10 { z-index: var(--z-dropdown) !important; }
.z-20 { z-index: var(--z-sticky) !important; }
.z-30 { z-index: var(--z-fixed) !important; }
.z-40 { z-index: var(--z-modal-backdrop) !important; }
.z-50 { z-index: var(--z-modal) !important; }
.z-auto { z-index: auto !important; }

/* Opacity */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* Transform utilities */
.transform { transform: translateZ(0) !important; }
.transform-none { transform: none !important; }

/* Transition utilities */
.transition-none { transition: none !important; }
.transition-all { transition: all var(--transition) !important; }
.transition-colors { transition: color var(--transition), background-color var(--transition), border-color var(--transition) !important; }
.transition-opacity { transition: opacity var(--transition) !important; }
.transition-shadow { transition: box-shadow var(--transition) !important; }
.transition-transform { transition: transform var(--transition) !important; }

/* Animation utilities */
.animate-none { animation: none !important; }
.animate-spin { animation: spin 1s linear infinite !important; }
.animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite !important; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important; }
.animate-bounce { animation: bounce 1s infinite !important; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes pulse {
  50% { opacity: .5; }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

/* Focus utilities */
.focus\:outline-none:focus { outline: 2px solid transparent !important; outline-offset: 2px !important; }
.focus\:ring:focus { box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1) !important; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.5) !important; }

/* Hover utilities */
.hover\:opacity-75:hover { opacity: 0.75 !important; }
.hover\:opacity-100:hover { opacity: 1 !important; }

/* Group utilities */
.group:hover .group-hover\:opacity-100 { opacity: 1 !important; }
.group:hover .group-hover\:opacity-75 { opacity: 0.75 !important; }

/* Print utilities */
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }
}

/* Dark mode utilities */
.dark .dark\:hidden { display: none !important; }
.dark .dark\:block { display: block !important; }

/* Responsive utilities */
@media (max-width: 640px) {
  .sm\:hidden { display: none !important; }
  .sm\:block { display: block !important; }
  .sm\:flex { display: flex !important; }
  .sm\:grid { display: grid !important; }
}

@media (max-width: 768px) {
  .md\:hidden { display: none !important; }
  .md\:block { display: block !important; }
  .md\:flex { display: flex !important; }
  .md\:grid { display: grid !important; }
}

@media (max-width: 1024px) {
  .lg\:hidden { display: none !important; }
  .lg\:block { display: block !important; }
  .lg\:flex { display: flex !important; }
  .lg\:grid { display: grid !important; }
}

import{j as e,m as t,A as s}from"./chunk-L3uak9dD.js";import{r as a}from"./chunk-D3Ns84uO.js";import{S as r}from"./chunk-8nFA7SZG.js";import{e as c}from"./chunk-BQYJeaTK.js";import{u as i}from"./chunk-Bthhypug.js";import{u as o}from"./chunk-CS50z45r.js";import{j as n,E as l}from"./chunk-Bc9oLI49.js";import"./chunk-DJqUFuPP.js";import"./chunk-Bt4ub4Kz.js";import"./index-B8F6PHpu.js";import"./chunk-DylKGAq6.js";const p=({src:t,alt:s,className:c="",loading:i="lazy",width:o,height:n,priority:l=!1})=>{const[p,j]=a.useState(!1),[d,m]=a.useState(!1),h=e=>r.validateUrl(e),x=(e=>{if(!h(e))return e;try{const t=new URL(e);return t.searchParams.set("fm","webp"),t.searchParams.set("q","80"),o&&t.searchParams.set("w",o.toString()),n&&t.searchParams.set("h",n.toString()),t.toString()}catch{return e}})(t),u=(e=>{if(!h(e))return e;try{const t=new URL(e);return t.searchParams.set("fm","jpg"),t.searchParams.set("q","80"),o&&t.searchParams.set("w",o.toString()),n&&t.searchParams.set("h",n.toString()),t.toString()}catch{return e}})(t);return e.jsxs("div",{className:`relative ${c}`,children:[!d&&!p&&e.jsx("div",{className:"absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded",style:{width:o,height:n}}),e.jsxs("picture",{children:[e.jsx("source",{srcSet:x,type:"image/webp"}),e.jsx("img",{src:u,alt:s,loading:l?"eager":i,width:o,height:n,className:`${c} ${d?"opacity-100":"opacity-0"} transition-opacity duration-300`,onLoad:()=>{m(!0)},onError:()=>{j(!0)},decoding:"async"})]})]})},j=a.memo((({projects:r})=>{const[j,d]=a.useState(null),{t:m}=o(),{playExpand:h,playCollapse:x}=i(),u=a.useCallback((e=>{const t=j!==e;d(j===e?null:e),t?h():x()}),[j,h,x]);return e.jsxs("section",{className:"w-full",children:[e.jsx(t.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},className:"mb-16",children:e.jsxs("div",{className:"max-w-[1200px] mx-auto text-left px-4 sm:px-6 lg:px-8",children:[e.jsx("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-text)] mb-4",children:m("projects.title")}),e.jsx("p",{className:"text-lg md:text-xl text-[var(--color-text-secondary)] leading-relaxed mb-4",children:m("projects.description")}),e.jsx(t.div,{initial:{width:0},animate:{width:"120px"},transition:{duration:.8,delay:.6},className:"h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"})]})}),e.jsx("div",{className:"projects-grid",children:r.map(((a,r)=>e.jsxs(t.article,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.15*r,ease:"easeOut"},className:"project-card",tabIndex:0,role:"article","aria-labelledby":`project-title-${r}`,"aria-describedby":`project-overview-${r}`,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),u(r))},children:[e.jsx("div",{className:"project-card-image-container",children:e.jsx(p,{src:a.imageUrl,alt:`${m(`projects.${a.projectKey}.title`)} - ${m("projects.projectImage")}`,className:"project-card-image",loading:"lazy",width:600,height:300})}),e.jsxs("div",{className:"project-card-content",children:[e.jsx("h2",{id:`project-title-${r}`,className:"project-card-title",children:m(`projects.${a.projectKey}.title`)}),e.jsx("p",{className:"project-card-description",children:m(`projects.${a.projectKey}.overview`)}),e.jsx("div",{className:"project-card-tags",children:(()=>{let t={fgvLaw:[m("projects.badges.usability"),m("projects.badges.informationArchitecture"),m("projects.badges.userTesting")],direitoGV:[m("projects.badges.uxResearch"),m("projects.badges.journeyMapping"),m("projects.badges.stakeholderManagement")],taliparts:[m("projects.badges.productStrategy"),m("projects.badges.seo"),m("projects.badges.productValidation")],tvInstitucional:[m("projects.badges.visualDesign"),m("projects.badges.communication"),m("projects.badges.engagement")]}[a.projectKey]||[];if(t.length>3)t=t.slice(0,3);else if(t.length<3){const e=[m("projects.badges.design"),m("projects.badges.strategy"),m("projects.badges.research")];for(;t.length<3;)t.push(e[t.length]||"UX Design")}return t.map(((t,s)=>e.jsx("span",{className:"project-card-tag",children:t},s)))})()}),e.jsx("div",{className:"project-card-actions",children:e.jsxs("button",{onClick:()=>u(r),className:"project-card-button group","aria-label":m(j===r?"projects.seeLess":"projects.seeMore"),children:[m(j===r?"projects.seeLess":"projects.seeMore"),j===r?e.jsx(n,{size:14,style:{marginLeft:"8px"},className:"transition-all duration-300 group-hover:scale-125"}):e.jsx(l,{size:14,style:{marginLeft:"8px"},className:"transition-all duration-300 group-hover:scale-125"})]})}),e.jsx(s,{children:j===r&&e.jsx(t.div,{id:`project-details-${r}`,initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3,ease:"easeOut"},className:"overflow-hidden",children:e.jsxs("div",{className:"project-expanded-content",children:[e.jsxs("div",{className:"project-section",children:[e.jsx("h4",{className:"project-section-title",children:m("projects.overview")}),e.jsx("p",{className:"project-section-content",children:m(`projects.${a.projectKey}.overview`)})]}),e.jsxs("div",{className:"project-section",children:[e.jsx("h4",{className:"project-section-title",children:m("projects.discovery")}),e.jsx("p",{className:"project-section-content",children:m(`projects.${a.projectKey}.discovery`)})]}),e.jsxs("div",{className:"project-section",children:[e.jsx("h4",{className:"project-section-title",children:m("projects.solution")}),e.jsx("p",{className:"project-section-content",children:m(`projects.${a.projectKey}.solution`)})]}),e.jsxs("div",{className:"project-section",children:[e.jsx("h4",{className:"project-section-title",children:m("projects.iteration")}),e.jsx("p",{className:"project-section-content",children:m(`projects.${a.projectKey}.iteration`)})]}),e.jsxs("div",{className:"project-section",children:[e.jsx("h4",{className:"project-section-title",children:m("projects.outcomes")}),e.jsx("ul",{className:"project-outcomes-list",children:(()=>{const t=m(`projects.${a.projectKey}.outcomes`,{returnObjects:!0});return c(t).map(((t,s)=>e.jsxs("li",{className:"project-outcome-item",children:[e.jsx("div",{className:"project-outcome-bullet"}),e.jsx("span",{children:t})]},s)))})()})]}),e.jsxs("div",{className:"project-section",children:[e.jsx("h4",{className:"project-section-title",children:m("projects.insights")}),e.jsx("p",{className:"project-section-content",style:{fontStyle:"italic"},children:m(`projects.${a.projectKey}.insights`)})]})]})})})]})]},a.projectKey)))})]})}));j.displayName="ProjectShowcase";export{j as default};
//# sourceMappingURL=ProjectShowcase-Dubp6LtH.js.map

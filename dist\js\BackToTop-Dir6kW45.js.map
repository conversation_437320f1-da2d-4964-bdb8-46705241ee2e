{"version": 3, "file": "BackToTop-Dir6kW45.js", "sources": ["../../src/components/ui/BackToTop.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { ChevronUp } from 'lucide-react';\r\n\r\ninterface BackToTopProps {\r\n  showAfter?: number;\r\n  className?: string;\r\n}\r\n\r\nexport const BackToTop: React.FC<BackToTopProps> = ({\r\n  showAfter = 300,\r\n  className = ''\r\n}) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const toggleVisibility = () => {\r\n      setIsVisible(window.pageYOffset > showAfter);\r\n    };\r\n\r\n    const handleScroll = () => {\r\n      requestAnimationFrame(toggleVisibility);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll, { passive: true });\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [showAfter]);\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' || e.key === ' ') {\r\n      e.preventDefault();\r\n      scrollToTop();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isVisible && (\r\n        <motion.button\r\n          initial={{ opacity: 0, scale: 0.8, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          exit={{ opacity: 0, scale: 0.8, y: 20 }}\r\n          whileHover={{ scale: 1.1 }}\r\n          whileTap={{ scale: 0.9 }}\r\n          onClick={scrollToTop}\r\n          onKeyDown={handleKeyDown}\r\n          className={`\r\n            fixed bottom-20 right-20 z-40\r\n            p-4 md:p-5 rounded-full shadow-lg hover:shadow-xl\r\n            bg-[var(--color-primary)] text-white\r\n            transition-all duration-300\r\n            focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:ring-offset-2\r\n            ${className}\r\n          `}\r\n          aria-label=\"Voltar ao topo da página\"\r\n          title=\"Voltar ao topo\"\r\n        >\r\n          <ChevronUp className=\"w-6 h-6 md:w-7 md:h-7\" />\r\n        </motion.button>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default BackToTop;\r\n"], "names": ["BackToTop", "showAfter", "className", "isVisible", "setIsVisible", "useState", "useEffect", "toggleVisibility", "window", "pageYOffset", "handleScroll", "requestAnimationFrame", "addEventListener", "passive", "removeEventListener", "scrollToTop", "scrollTo", "top", "behavior", "AnimatePresence", "motion", "button", "initial", "opacity", "scale", "y", "animate", "exit", "whileHover", "whileTap", "onClick", "onKeyDown", "e", "key", "preventDefault", "aria-label", "title", "children", "ChevronUp"], "mappings": "kKASO,MAAMA,EAAsC,EACjDC,YAAY,IACZC,YAAY,OAEZ,MAAOC,EAAWC,GAAgBC,EAAAA,UAAS,GAE3CC,EAAAA,WAAU,KACR,MAAMC,EAAmB,KACVC,EAAAA,OAAOC,YAAcR,EAAAA,EAG9BS,EAAe,KACnBC,sBAAsBJ,EAAAA,EAIxB,OADOK,OAAAA,iBAAiB,SAAUF,EAAc,CAAEG,SAAS,IACpD,IAAML,OAAOM,oBAAoB,SAAUJ,EAAAA,GACjD,CAACT,IAEJ,MAAMc,EAAc,KAClBP,OAAOQ,SAAS,CACdC,IAAK,EACLC,SAAU,UACZ,eAWCC,EAAAA,UACEhB,SACEiB,EAAOC,OAAM,CACZC,QAAS,CAAEC,QAAS,EAAGC,MAAO,GAAKC,EAAG,IACtCC,QAAS,CAAEH,QAAS,EAAGC,MAAO,EAAGC,EAAG,GACpCE,KAAM,CAAEJ,QAAS,EAAGC,MAAO,GAAKC,EAAG,IACnCG,WAAY,CAAEJ,MAAO,KACrBK,SAAU,CAAEL,MAAO,IACnBM,QAASf,EACTgB,UAjBeC,IACP,UAAVA,EAAEC,KAA6B,MAAVD,EAAEC,MACzBD,EAAEE,iBACFnB,IAAAA,EAeIb,UAAW,yTAMPA,gBAEJiC,aAAW,2BACXC,MAAM,iBAENC,eAACC,EAAAA,CAAUpC,UAAU"}
{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://tbisp0.github.io/portfolio", "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d dist", "dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "homepage": "https://tarcisiobispo.github.io/portfolio"}, "dependencies": {"@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.0", "@microsoft/clarity": "^1.0.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@types/react-window": "^1.8.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "emailjs-com": "^3.2.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.10.5", "gsap": "^3.13.0", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "input-otp": "^1.2.4", "lenis": "^1.3.3", "logrocket": "^10.0.0", "lucide-react": "^0.462.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.2", "react-loading-skeleton": "^3.5.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-type-animation": "^3.2.0", "react-window": "^1.8.11", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "vite-plugin-sitemap": "^0.8.2", "web-vitals": "^5.0.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.26.0", "@fullhuman/postcss-purgecss": "^7.0.2", "@swc/plugin-remove-console": "^7.0.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.17", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "cssnano": "^7.0.7", "eslint": "^9.26.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "gh-pages": "^6.3.0", "globals": "^15.15.0", "npm-check-updates": "^18.0.1", "postcss": "^8.5.3", "purgecss": "^7.0.2", "tailwindcss": "^3.4.17", "terser": "^5.39.2", "typescript": "^5.8.3", "vite": "^6.3.5"}}
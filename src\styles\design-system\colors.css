/**
 * 🎯 SISTEMA DE CORES
 * 
 * Cores semânticas baseadas nos tokens
 * WCAG 2.2 AA compliant
 * Suporte completo ao modo escuro
 */

/* ===== CORES SEMÂNTICAS - MODO CLARO ===== */
:root {
  /* ===== CORES PRIMÁRIAS ===== */
  --color-primary: var(--blue-700);
  --color-primary-rgb: 29, 78, 216;
  --color-primary-hover: var(--blue-800);
  --color-primary-active: var(--blue-900);
  --color-primary-light: var(--blue-100);
  --color-primary-dark: var(--blue-900);
  
  /* ===== CORES SECUNDÁRIAS ===== */
  --color-secondary: var(--green-600);
  --color-secondary-rgb: 5, 150, 105;
  --color-secondary-hover: var(--green-700);
  --color-secondary-active: var(--green-800);
  --color-secondary-light: var(--green-100);
  --color-secondary-dark: var(--green-900);
  
  /* ===== CORES DE TEXTO ===== */
  --color-text: var(--gray-900);
  --color-text-muted: var(--gray-700);
  --color-text-light: var(--gray-500);
  --color-text-inverse: var(--gray-50);
  --color-text-disabled: var(--gray-400);
  
  /* ===== CORES DE BACKGROUND ===== */
  --color-bg: var(--gray-50);
  --color-bg-alt: #ffffff;
  --color-surface: #ffffff;
  --color-surface-hover: var(--gray-50);
  --color-surface-active: var(--gray-100);
  --color-surface-disabled: var(--gray-100);
  
  /* ===== CORES DE BORDA ===== */
  --color-border: var(--gray-300);
  --color-border-hover: var(--gray-400);
  --color-border-active: var(--gray-500);
  --color-border-disabled: var(--gray-200);
  --color-border-focus: var(--blue-500);
  
  /* ===== CORES DE STATUS ===== */
  
  /* Success */
  --color-success: var(--green-600);
  --color-success-bg: var(--green-50);
  --color-success-border: var(--green-200);
  --color-success-text: var(--green-800);
  --color-success-hover: var(--green-700);
  
  /* Error */
  --color-error: var(--red-600);
  --color-error-bg: var(--red-50);
  --color-error-border: var(--red-200);
  --color-error-text: var(--red-800);
  --color-error-hover: var(--red-700);
  
  /* Warning */
  --color-warning: var(--yellow-600);
  --color-warning-bg: var(--yellow-50);
  --color-warning-border: var(--yellow-200);
  --color-warning-text: var(--yellow-800);
  --color-warning-hover: var(--yellow-700);
  
  /* Info */
  --color-info: var(--blue-600);
  --color-info-bg: var(--blue-50);
  --color-info-border: var(--blue-200);
  --color-info-text: var(--blue-800);
  --color-info-hover: var(--blue-700);
  
  /* ===== CORES DE OVERLAY ===== */
  --color-overlay: rgba(0, 0, 0, 0.5);
  --color-overlay-light: rgba(0, 0, 0, 0.25);
  --color-overlay-heavy: rgba(0, 0, 0, 0.75);
  
  /* ===== CORES DE FOCO ===== */
  --color-focus-ring: rgba(59, 130, 246, 0.5);
  --color-focus-ring-offset: #ffffff;
}

/* ===== CORES SEMÂNTICAS - MODO ESCURO ===== */
[data-theme="dark"] {
  /* ===== CORES PRIMÁRIAS ===== */
  --color-primary: var(--blue-400);
  --color-primary-rgb: 96, 165, 250;
  --color-primary-hover: var(--blue-300);
  --color-primary-active: var(--blue-200);
  --color-primary-light: var(--blue-900);
  --color-primary-dark: var(--blue-100);
  
  /* ===== CORES SECUNDÁRIAS ===== */
  --color-secondary: var(--green-400);
  --color-secondary-rgb: 52, 211, 153;
  --color-secondary-hover: var(--green-300);
  --color-secondary-active: var(--green-200);
  --color-secondary-light: var(--green-900);
  --color-secondary-dark: var(--green-100);
  
  /* ===== CORES DE TEXTO ===== */
  --color-text: var(--gray-50);
  --color-text-muted: var(--gray-300);
  --color-text-light: var(--gray-400);
  --color-text-inverse: var(--gray-900);
  --color-text-disabled: var(--gray-500);
  
  /* ===== CORES DE BACKGROUND ===== */
  --color-bg: var(--gray-900);
  --color-bg-alt: var(--gray-800);
  --color-surface: var(--gray-800);
  --color-surface-hover: var(--gray-700);
  --color-surface-active: var(--gray-600);
  --color-surface-disabled: var(--gray-800);
  
  /* ===== CORES DE BORDA ===== */
  --color-border: var(--gray-600);
  --color-border-hover: var(--gray-500);
  --color-border-active: var(--gray-400);
  --color-border-disabled: var(--gray-700);
  --color-border-focus: var(--blue-400);
  
  /* ===== CORES DE STATUS ===== */
  
  /* Success */
  --color-success: var(--green-400);
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-success-border: var(--green-800);
  --color-success-text: var(--green-300);
  --color-success-hover: var(--green-300);
  
  /* Error */
  --color-error: var(--red-400);
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-error-border: var(--red-800);
  --color-error-text: var(--red-300);
  --color-error-hover: var(--red-300);
  
  /* Warning */
  --color-warning: var(--yellow-400);
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-warning-border: var(--yellow-800);
  --color-warning-text: var(--yellow-300);
  --color-warning-hover: var(--yellow-300);
  
  /* Info */
  --color-info: var(--blue-400);
  --color-info-bg: rgba(59, 130, 246, 0.1);
  --color-info-border: var(--blue-800);
  --color-info-text: var(--blue-300);
  --color-info-hover: var(--blue-300);
  
  /* ===== CORES DE OVERLAY ===== */
  --color-overlay: rgba(0, 0, 0, 0.75);
  --color-overlay-light: rgba(0, 0, 0, 0.5);
  --color-overlay-heavy: rgba(0, 0, 0, 0.9);
  
  /* ===== CORES DE FOCO ===== */
  --color-focus-ring: rgba(96, 165, 250, 0.5);
  --color-focus-ring-offset: var(--gray-800);
}

/* ===== PREFERÊNCIA DO SISTEMA ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Aplicar cores escuras automaticamente */
    --color-primary: var(--blue-400);
    --color-primary-rgb: 96, 165, 250;
    --color-secondary: var(--green-400);
    --color-secondary-rgb: 52, 211, 153;
    --color-text: var(--gray-50);
    --color-text-muted: var(--gray-300);
    --color-bg: var(--gray-900);
    --color-surface: var(--gray-800);
    --color-border: var(--gray-600);
  }
}

/* ===== CLASSES UTILITÁRIAS DE COR ===== */

/* Text Colors */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-muted { color: var(--color-text-muted) !important; }
.text-light { color: var(--color-text-light) !important; }
.text-inverse { color: var(--color-text-inverse) !important; }

.text-success { color: var(--color-success) !important; }
.text-error { color: var(--color-error) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-info { color: var(--color-info) !important; }

/* Background Colors */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-surface { background-color: var(--color-surface) !important; }
.bg-surface-hover { background-color: var(--color-surface-hover) !important; }

.bg-success { background-color: var(--color-success-bg) !important; }
.bg-error { background-color: var(--color-error-bg) !important; }
.bg-warning { background-color: var(--color-warning-bg) !important; }
.bg-info { background-color: var(--color-info-bg) !important; }

/* Border Colors */
.border-primary { border-color: var(--color-primary) !important; }
.border-secondary { border-color: var(--color-secondary) !important; }
.border-default { border-color: var(--color-border) !important; }

.border-success { border-color: var(--color-success-border) !important; }
.border-error { border-color: var(--color-error-border) !important; }
.border-warning { border-color: var(--color-warning-border) !important; }
.border-info { border-color: var(--color-info-border) !important; }

/* Hover States */
.hover\:text-primary:hover { color: var(--color-primary-hover) !important; }
.hover\:text-secondary:hover { color: var(--color-secondary-hover) !important; }

.hover\:bg-primary:hover { background-color: var(--color-primary-hover) !important; }
.hover\:bg-secondary:hover { background-color: var(--color-secondary-hover) !important; }
.hover\:bg-surface:hover { background-color: var(--color-surface-hover) !important; }

.hover\:border-primary:hover { border-color: var(--color-primary-hover) !important; }
.hover\:border-secondary:hover { border-color: var(--color-secondary-hover) !important; }

/* Focus States */
.focus\:ring-primary:focus {
  box-shadow: 0 0 0 3px var(--color-focus-ring) !important;
}

.focus\:border-primary:focus {
  border-color: var(--color-border-focus) !important;
}

/* Gradients */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)) !important;
}

.bg-gradient-surface {
  background: linear-gradient(135deg, var(--color-surface), var(--color-surface-hover)) !important;
}

.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

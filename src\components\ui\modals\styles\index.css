/**
 * 🎯 CSS MODULAR PARA MODAIS
 * 
 * Estilos unificados para sistema de modais
 * Suporte completo ao modo escuro
 * WCAG 2.2 compliant
 */

/* ===== IMPORTAÇÕES ===== */
@import './base.css';
@import './feedback.css';
@import './confirm.css';
@import './animations.css';

/* ===== DESIGN TOKENS PARA MODAIS ===== */
:root {
  /* Modal Spacing */
  --modal-padding: 1.5rem;
  --modal-padding-sm: 1rem;
  --modal-gap: 1rem;
  
  /* Modal Borders */
  --modal-border-radius: 1rem;
  --modal-border-width: 1px;
  
  /* Modal Shadows */
  --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --modal-shadow-dark: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  
  /* Modal Backdrop */
  --modal-backdrop: rgba(0, 0, 0, 0.3);
  --modal-backdrop-dark: rgba(0, 0, 0, 0.5);
  
  /* Modal Z-Index */
  --modal-z-index: 50;
  --modal-backdrop-z-index: 40;
  
  /* Modal Transitions */
  --modal-transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  --modal-transition-fast: all 0.15s ease-out;
}

/* ===== CLASSES BASE ===== */
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: var(--modal-backdrop-z-index);
  background: var(--modal-backdrop);
  backdrop-filter: blur(4px);
  transition: var(--modal-transition-fast);
}

.modal-container {
  position: fixed;
  inset: 0;
  z-index: var(--modal-z-index);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-y: auto;
}

.modal-content {
  position: relative;
  width: 100%;
  background: white;
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-shadow);
  border: var(--modal-border-width) solid var(--color-border);
  transition: var(--modal-transition);
}

/* ===== MODO ESCURO ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --modal-backdrop: var(--modal-backdrop-dark);
    --modal-shadow: var(--modal-shadow-dark);
  }
}

.dark .modal-content {
  background: var(--color-surface-dark, #1f2937);
  border-color: var(--color-border-dark, #374151);
  box-shadow: var(--modal-shadow-dark);
}

.dark .modal-overlay {
  background: var(--modal-backdrop-dark);
}

/* ===== TAMANHOS ===== */
.modal-sm {
  max-width: 24rem; /* 384px */
}

.modal-md {
  max-width: 28rem; /* 448px */
}

.modal-lg {
  max-width: 32rem; /* 512px */
}

.modal-xl {
  max-width: 42rem; /* 672px */
}

.modal-full {
  max-width: 90vw;
  max-height: 90vh;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 640px) {
  .modal-container {
    padding: 0.5rem;
  }
  
  .modal-content {
    margin: 0;
    border-radius: 0.75rem;
  }
  
  .modal-sm,
  .modal-md,
  .modal-lg,
  .modal-xl {
    max-width: 100%;
  }
}

/* ===== ACESSIBILIDADE ===== */
.modal-content:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Reduzir movimento para usuários que preferem */
@media (prefers-reduced-motion: reduce) {
  .modal-overlay,
  .modal-content {
    transition: opacity 0.1s ease;
  }
}

/* ===== ESTADOS ===== */
.modal-loading {
  pointer-events: none;
  opacity: 0.7;
}

.modal-error {
  border-color: var(--color-error, #ef4444);
}

.modal-success {
  border-color: var(--color-success, #10b981);
}

.modal-warning {
  border-color: var(--color-warning, #f59e0b);
}

import{r as e,a as t,v as n,R as r}from"./chunk-D3Ns84uO.js";import{j as o}from"./chunk-L3uak9dD.js";function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1;const r=e.map((e=>{const r=s(e,t);return n||"function"!=typeof r||(n=!0),r}));if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():s(e[t],null)}}}}function a(...t){return e.useCallback(l(...t),t)}function c(t,n=[]){let r=[];const i=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return i.scopeName=t,[function(n,i){const s=e.createContext(i),l=r.length;r=[...r,i];const a=n=>{var r;const{scope:i,children:a,...c}=n,u=(null==(r=null==i?void 0:i[t])?void 0:r[l])||s,f=e.useMemo((()=>c),Object.values(c));return o.jsx(u.Provider,{value:f,children:a})};return a.displayName=n+"Provider",[a,function(r,o){var a;const c=(null==(a=null==o?void 0:o[t])?void 0:a[l])||s,u=e.useContext(c);if(u)return u;if(void 0!==i)return i;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},u(i,...n)]}function u(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function f(t){const n=p(t),r=e.forwardRef(((t,r)=>{const{children:i,...s}=t,l=e.Children.toArray(i),a=l.find(g);if(a){const t=a.props.children,i=l.map((n=>n===a?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n));return o.jsx(n,{...s,ref:r,children:e.isValidElement(t)?e.cloneElement(t,void 0,i):null})}return o.jsx(n,{...s,ref:r,children:i})}));return r.displayName=`${t}.Slot`,r}var d=f("Slot");function p(t){const n=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(r),i=function(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{const t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,r.props);return r.type!==e.Fragment&&(i.ref=n?l(n,t):t),e.cloneElement(r,i)}return e.Children.count(r)>1?e.Children.only(null):null}));return n.displayName=`${t}.SlotClone`,n}var h=Symbol("radix.slottable");function m(e){const t=({children:e})=>o.jsx(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=h,t}function g(t){return e.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===h}var y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce(((t,n)=>{const r=f(`Primitive.${n}`),i=e.forwardRef(((e,t)=>{const{asChild:i,...s}=e,l=i?r:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),o.jsx(l,{...s,ref:t})}));return i.displayName=`Primitive.${n}`,{...t,[n]:i}}),{});function v(e,n){e&&t.flushSync((()=>e.dispatchEvent(n)))}function w(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>{var t;return null==(t=n.current)?void 0:t.call(n,...e)}),[])}var x,b="dismissableLayer.update",E="dismissableLayer.pointerDownOutside",R="dismissableLayer.focusOutside",T=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),C=e.forwardRef(((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:c,onInteractOutside:u,onDismiss:f,...d}=t,p=e.useContext(T),[h,m]=e.useState(null),g=(null==h?void 0:h.ownerDocument)??(null==globalThis?void 0:globalThis.document),[,v]=e.useState({}),C=a(n,(e=>m(e))),P=Array.from(p.layers),[L]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),D=P.indexOf(L),S=h?P.indexOf(h):-1,N=p.layersWithOutsidePointerEventsDisabled.size>0,k=S>=D,j=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=w(t),o=e.useRef(!1),i=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){A(E,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...p.branches].some((e=>e.contains(t)));k&&!n&&(null==l||l(e),null==u||u(e),e.defaultPrevented||null==f||f())}),g),M=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=w(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){A(R,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...p.branches].some((e=>e.contains(t)))||(null==c||c(e),null==u||u(e),e.defaultPrevented||null==f||f())}),g);return function(t,n=(null==globalThis?void 0:globalThis.document)){const r=w(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{S===p.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))}),g),e.useEffect((()=>{if(h)return r&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(x=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(h)),p.layers.add(h),O(),()=>{r&&1===p.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=x)}}),[h,g,r,p]),e.useEffect((()=>()=>{h&&(p.layers.delete(h),p.layersWithOutsidePointerEventsDisabled.delete(h),O())}),[h,p]),e.useEffect((()=>{const e=()=>v({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)}),[]),o.jsx(y.div,{...d,ref:C,style:{pointerEvents:N?k?"auto":"none":void 0,...t.style},onFocusCapture:i(t.onFocusCapture,M.onFocusCapture),onBlurCapture:i(t.onBlurCapture,M.onBlurCapture),onPointerDownCapture:i(t.onPointerDownCapture,j.onPointerDownCapture)})}));C.displayName="DismissableLayer";var P=e.forwardRef(((t,n)=>{const r=e.useContext(T),i=e.useRef(null),s=a(n,i);return e.useEffect((()=>{const e=i.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}}),[r.branches]),o.jsx(y.div,{...t,ref:s})}));function O(){const e=new CustomEvent(b);document.dispatchEvent(e)}function A(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?v(o,i):o.dispatchEvent(i)}P.displayName="DismissableLayerBranch";var L=C,D=P,S=(null==globalThis?void 0:globalThis.document)?e.useLayoutEffect:()=>{},N=e.forwardRef(((t,r)=>{var i;const{container:s,...l}=t,[a,c]=e.useState(!1);S((()=>c(!0)),[]);const u=s||a&&(null==(i=null==globalThis?void 0:globalThis.document)?void 0:i.body);return u?n.createPortal(o.jsx(y.div,{...l,ref:r}),u):null}));N.displayName="Portal";var k=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef(null),i=e.useRef(t),s=e.useRef("none"),l=t?"mounted":"unmounted",[a,c]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=j(o.current);s.current="mounted"===a?e:"none"}),[a]),S((()=>{const e=o.current,n=i.current;if(n!==t){const r=s.current,o=j(e);if(t)c("MOUNT");else if("none"===o||"none"===(null==e?void 0:e.display))c("UNMOUNT");else{c(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}}),[t,c]),S((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const s=j(o.current).includes(r.animationName);if(r.target===n&&s&&(c("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},l=e=>{e.target===n&&(s.current=j(o.current))};return n.addEventListener("animationstart",l),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",l),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}c("ANIMATION_END")}),[n,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:e.useCallback((e=>{o.current=e?getComputedStyle(e):null,r(e)}),[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),s=a(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:s}):null};function j(e){return(null==e?void 0:e.animationName)||"none"}k.displayName="Presence";var M=r[" useInsertionEffect ".trim().toString()]||S;function _({prop:t,defaultProp:n,onChange:r=()=>{},caller:o}){const[i,s,l]=function({defaultProp:t,onChange:n}){const[r,o]=e.useState(t),i=e.useRef(r),s=e.useRef(n);return M((()=>{s.current=n}),[n]),e.useEffect((()=>{var e;i.current!==r&&(null==(e=s.current)||e.call(s,r),i.current=r)}),[r,i]),[r,o,s]}({defaultProp:n,onChange:r}),a=void 0!==t,c=a?t:i;{const n=e.useRef(void 0!==t);e.useEffect((()=>{const e=n.current;if(e!==a){const t=e?"controlled":"uncontrolled",n=a?"controlled":"uncontrolled";console.warn(`${o} is changing from ${t} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=a}),[a,o])}const u=e.useCallback((e=>{var n;if(a){const r=function(e){return"function"==typeof e}(e)?e(t):e;r!==t&&(null==(n=l.current)||n.call(l,r))}else s(e)}),[a,t,s,l]);return[c,u]}var W=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),F=e.forwardRef(((e,t)=>o.jsx(y.span,{...e,ref:t,style:{...W,...e.style}})));F.displayName="VisuallyHidden";var I=F;function H(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=H(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function B(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=H(e))&&(r&&(r+=" "),r+=t);return r}var $=r[" useId ".trim().toString()]||(()=>{}),z=0;function V(t){const[n,r]=e.useState($());return S((()=>{r((e=>e??String(z++)))}),[t]),n?`radix-${n}`:""}const U=["top","right","bottom","left"],Y=Math.min,X=Math.max,K=Math.round,q=Math.floor,Z=e=>({x:e,y:e}),G={left:"right",right:"left",bottom:"top",top:"bottom"},J={start:"end",end:"start"};function Q(e,t,n){return X(e,Y(t,n))}function ee(e,t){return"function"==typeof e?e(t):e}function te(e){return e.split("-")[0]}function ne(e){return e.split("-")[1]}function re(e){return"x"===e?"y":"x"}function oe(e){return"y"===e?"height":"width"}function ie(e){return["top","bottom"].includes(te(e))?"y":"x"}function se(e){return re(ie(e))}function le(e){return e.replace(/start|end/g,(e=>J[e]))}function ae(e){return e.replace(/left|right|bottom|top/g,(e=>G[e]))}function ce(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function ue(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function fe(e,t,n){let{reference:r,floating:o}=e;const i=ie(t),s=se(t),l=oe(s),a=te(t),c="y"===i,u=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,d=r[l]/2-o[l]/2;let p;switch(a){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:f};break;case"left":p={x:r.x-o.width,y:f};break;default:p={x:r.x,y:r.y}}switch(ne(t)){case"start":p[s]-=d*(n&&c?-1:1);break;case"end":p[s]+=d*(n&&c?-1:1)}return p}async function de(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=ee(t,e),h=ce(p),m=l[d?"floating"===f?"reference":"floating":f],g=ue(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:a})),y="floating"===f?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,v=await(null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await(null==i.isElement?void 0:i.isElement(v))&&await(null==i.getScale?void 0:i.getScale(v))||{x:1,y:1},x=ue(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:v,strategy:a}):y);return{top:(g.top-x.top+h.top)/w.y,bottom:(x.bottom-g.bottom+h.bottom)/w.y,left:(g.left-x.left+h.left)/w.x,right:(x.right-g.right+h.right)/w.x}}function pe(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function he(e){return U.some((t=>e[t]>=0))}function me(){return"undefined"!=typeof window}function ge(e){return we(e)?(e.nodeName||"").toLowerCase():"#document"}function ye(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ve(e){var t;return null==(t=(we(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function we(e){return!!me()&&(e instanceof Node||e instanceof ye(e).Node)}function xe(e){return!!me()&&(e instanceof Element||e instanceof ye(e).Element)}function be(e){return!!me()&&(e instanceof HTMLElement||e instanceof ye(e).HTMLElement)}function Ee(e){return!(!me()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ye(e).ShadowRoot)}function Re(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Le(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Te(e){return["table","td","th"].includes(ge(e))}function Ce(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function Pe(e){const t=Oe(),n=xe(e)?Le(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Oe(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Ae(e){return["html","body","#document"].includes(ge(e))}function Le(e){return ye(e).getComputedStyle(e)}function De(e){return xe(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Se(e){if("html"===ge(e))return e;const t=e.assignedSlot||e.parentNode||Ee(e)&&e.host||ve(e);return Ee(t)?t.host:t}function Ne(e){const t=Se(e);return Ae(t)?e.ownerDocument?e.ownerDocument.body:e.body:be(t)&&Re(t)?t:Ne(t)}function ke(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Ne(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),s=ye(o);if(i){const e=je(s);return t.concat(s,s.visualViewport||[],Re(o)?o:[],e&&n?ke(e):[])}return t.concat(o,ke(o,[],n))}function je(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Me(e){const t=Le(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=be(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,l=K(n)!==i||K(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function _e(e){return xe(e)?e:e.contextElement}function We(e){const t=_e(e);if(!be(t))return Z(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Me(t);let s=(i?K(n.width):n.width)/r,l=(i?K(n.height):n.height)/o;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}const Fe=Z(0);function Ie(e){const t=ye(e);return Oe()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Fe}function He(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=_e(e);let s=Z(1);t&&(r?xe(r)&&(s=We(r)):s=We(e));const l=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ye(e))&&t}(i,n,r)?Ie(i):Z(0);let a=(o.left+l.x)/s.x,c=(o.top+l.y)/s.y,u=o.width/s.x,f=o.height/s.y;if(i){const e=ye(i),t=r&&xe(r)?ye(r):r;let n=e,o=je(n);for(;o&&r&&t!==n;){const e=We(o),t=o.getBoundingClientRect(),r=Le(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;a*=e.x,c*=e.y,u*=e.x,f*=e.y,a+=i,c+=s,n=ye(o),o=je(n)}}return ue({width:u,height:f,x:a,y:c})}function Be(e,t){const n=De(e).scrollLeft;return t?t.left+n:He(ve(e)).left+n}function $e(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Be(e,r)),y:r.top+t.scrollTop}}function ze(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ye(e),r=ve(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,l=0,a=0;if(o){i=o.width,s=o.height;const e=Oe();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,a=o.offsetTop)}return{width:i,height:s,x:l,y:a}}(e,n);else if("document"===t)r=function(e){const t=ve(e),n=De(e),r=e.ownerDocument.body,o=X(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=X(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Be(e);const l=-n.scrollTop;return"rtl"===Le(r).direction&&(s+=X(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:l}}(ve(e));else if(xe(t))r=function(e,t){const n=He(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=be(e)?We(e):Z(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=Ie(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ue(r)}function Ve(e,t){const n=Se(e);return!(n===t||!xe(n)||Ae(n))&&("fixed"===Le(n).position||Ve(n,t))}function Ue(e,t,n){const r=be(t),o=ve(t),i="fixed"===n,s=He(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const a=Z(0);function c(){a.x=Be(o)}if(r||!r&&!i)if(("body"!==ge(t)||Re(o))&&(l=De(t)),r){const e=He(t,!0,i,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else o&&c();i&&!r&&o&&c();const u=!o||r||i?Z(0):$e(o,l);return{x:s.left+l.scrollLeft-a.x-u.x,y:s.top+l.scrollTop-a.y-u.y,width:s.width,height:s.height}}function Ye(e){return"static"===Le(e).position}function Xe(e,t){if(!be(e)||"fixed"===Le(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ve(e)===n&&(n=n.ownerDocument.body),n}function Ke(e,t){const n=ye(e);if(Ce(e))return n;if(!be(e)){let t=Se(e);for(;t&&!Ae(t);){if(xe(t)&&!Ye(t))return t;t=Se(t)}return n}let r=Xe(e,t);for(;r&&Te(r)&&Ye(r);)r=Xe(r,t);return r&&Ae(r)&&Ye(r)&&!Pe(r)?n:r||function(e){let t=Se(e);for(;be(t)&&!Ae(t);){if(Pe(t))return t;if(Ce(t))return null;t=Se(t)}return null}(e)||n}const qe={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,s=ve(r),l=!!t&&Ce(t.floating);if(r===s||l&&i)return n;let a={scrollLeft:0,scrollTop:0},c=Z(1);const u=Z(0),f=be(r);if((f||!f&&!i)&&(("body"!==ge(r)||Re(s))&&(a=De(r)),be(r))){const e=He(r);c=We(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}const d=!s||f||i?Z(0):$e(s,a,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+u.x+d.x,y:n.y*c.y-a.scrollTop*c.y+u.y+d.y}},getDocumentElement:ve,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?Ce(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=ke(e,[],!1).filter((e=>xe(e)&&"body"!==ge(e))),o=null;const i="fixed"===Le(e).position;let s=i?Se(e):e;for(;xe(s)&&!Ae(s);){const t=Le(s),n=Pe(s);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Re(s)&&!n&&Ve(e,s))?r=r.filter((e=>e!==s)):o=t,s=Se(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=i[0],l=i.reduce(((e,n)=>{const r=ze(t,n,o);return e.top=X(r.top,e.top),e.right=Y(r.right,e.right),e.bottom=Y(r.bottom,e.bottom),e.left=X(r.left,e.left),e}),ze(t,s,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:Ke,getElementRects:async function(e){const t=this.getOffsetParent||Ke,n=this.getDimensions,r=await n(e.floating);return{reference:Ue(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Me(e);return{width:t,height:n}},getScale:We,isElement:xe,isRTL:function(e){return"rtl"===Le(e).direction}};function Ze(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ge(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:a=!1}=r,c=_e(e),u=o||i?[...c?ke(c):[],...ke(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const f=c&&l?function(e,t){let n,r=null;const o=ve(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function s(l,a){void 0===l&&(l=!1),void 0===a&&(a=1),i();const c=e.getBoundingClientRect(),{left:u,top:f,width:d,height:p}=c;if(l||t(),!d||!p)return;const h={rootMargin:-q(f)+"px "+-q(o.clientWidth-(u+d))+"px "+-q(o.clientHeight-(f+p))+"px "+-q(u)+"px",threshold:X(0,Y(1,a))||1};let m=!0;function g(t){const r=t[0].intersectionRatio;if(r!==a){if(!m)return s();r?s(!1,r):n=setTimeout((()=>{s(!1,1e-7)}),1e3)}1!==r||Ze(c,e.getBoundingClientRect())||s(),m=!1}try{r=new IntersectionObserver(g,{...h,root:o.ownerDocument})}catch(y){r=new IntersectionObserver(g,h)}r.observe(e)}(!0),i}(c,n):null;let d,p=-1,h=null;s&&(h=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=h)||e.observe(t)}))),n()})),c&&!a&&h.observe(c),h.observe(t));let m=a?He(e):null;return a&&function t(){const r=He(e);m&&!Ze(m,r)&&n();m=r,d=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==f||f(),null==(e=h)||e.disconnect(),h=null,a&&cancelAnimationFrame(d)}}const Je=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:l}=t,a=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),s=te(n),l=ne(n),a="y"===ie(n),c=["left","top"].includes(s)?-1:1,u=i&&a?-1:1,f=ee(t,e);let{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),a?{x:p*u,y:d*c}:{x:d*c,y:p*u}}(t,e);return s===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:s}}}}},Qe=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...a}=ee(e,t),c={x:n,y:r},u=await de(t,a),f=ie(te(o)),d=re(f);let p=c[d],h=c[f];if(i){const e="y"===d?"bottom":"right";p=Q(p+u["y"===d?"top":"left"],p,p-u[e])}if(s){const e="y"===f?"bottom":"right";h=Q(h+u["y"===f?"top":"left"],h,h-u[e])}const m=l.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:s}}}}}},et=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:c}=t,{mainAxis:u=!0,crossAxis:f=!0,fallbackPlacements:d,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:m=!0,...g}=ee(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const y=te(o),v=ie(l),w=te(l)===l,x=await(null==a.isRTL?void 0:a.isRTL(c.floating)),b=d||(w||!m?[ae(l)]:function(e){const t=ae(e);return[le(e),t,le(t)]}(l)),E="none"!==h;!d&&E&&b.push(...function(e,t,n,r){const o=ne(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}(te(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(le)))),i}(l,m,h,x));const R=[l,...b],T=await de(t,g),C=[];let P=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&C.push(T[y]),f){const e=function(e,t,n){void 0===n&&(n=!1);const r=ne(e),o=se(e),i=oe(o);let s="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=ae(s)),[s,ae(s)]}(o,s,x);C.push(T[e[0]],T[e[1]])}if(P=[...P,{placement:o,overflows:C}],!C.every((e=>e<=0))){var O,A;const e=((null==(O=i.flip)?void 0:O.index)||0)+1,t=R[e];if(t){var L;const n="alignment"===f&&v!==ie(t),r=(null==(L=P[0])?void 0:L.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:P},reset:{placement:t}}}let n=null==(A=P.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:A.placement;if(!n)switch(p){case"bestFit":{var D;const e=null==(D=P.filter((e=>{if(E){const t=ie(e.placement);return t===v||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:D[0];e&&(n=e);break}case"initialPlacement":n=l}if(o!==n)return{reset:{placement:n}}}return{}}}},tt=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:l}=t,{apply:a=()=>{},...c}=ee(e,t),u=await de(t,c),f=te(o),d=ne(o),p="y"===ie(o),{width:h,height:m}=i.floating;let g,y;"top"===f||"bottom"===f?(g=f,y=d===(await(null==s.isRTL?void 0:s.isRTL(l.floating))?"start":"end")?"left":"right"):(y=f,g="end"===d?"top":"bottom");const v=m-u.top-u.bottom,w=h-u.left-u.right,x=Y(m-u[g],v),b=Y(h-u[y],w),E=!t.middlewareData.shift;let R=x,T=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(T=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=v),E&&!d){const e=X(u.left,0),t=X(u.right,0),n=X(u.top,0),r=X(u.bottom,0);p?T=h-2*(0!==e||0!==t?e+t:X(u.left,u.right)):R=m-2*(0!==n||0!==r?n+r:X(u.top,u.bottom))}await a({...t,availableWidth:T,availableHeight:R});const C=await s.getDimensions(l.floating);return h!==C.width||m!==C.height?{reset:{rects:!0}}:{}}}},nt=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ee(e,t);switch(r){case"referenceHidden":{const e=pe(await de(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:he(e)}}}case"escaped":{const e=pe(await de(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:he(e)}}}default:return{}}}}},rt=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:l,middlewareData:a}=t,{element:c,padding:u=0}=ee(e,t)||{};if(null==c)return{};const f=ce(u),d={x:n,y:r},p=se(o),h=oe(p),m=await s.getDimensions(c),g="y"===p,y=g?"top":"left",v=g?"bottom":"right",w=g?"clientHeight":"clientWidth",x=i.reference[h]+i.reference[p]-d[p]-i.floating[h],b=d[p]-i.reference[p],E=await(null==s.getOffsetParent?void 0:s.getOffsetParent(c));let R=E?E[w]:0;R&&await(null==s.isElement?void 0:s.isElement(E))||(R=l.floating[w]||i.floating[h]);const T=x/2-b/2,C=R/2-m[h]/2-1,P=Y(f[y],C),O=Y(f[v],C),A=P,L=R-m[h]-O,D=R/2-m[h]/2+T,S=Q(A,D,L),N=!a.arrow&&null!=ne(o)&&D!==S&&i.reference[h]/2-(D<A?P:O)-m[h]/2<0,k=N?D<A?D-A:D-L:0;return{[p]:d[p]+k,data:{[p]:S,centerOffset:D-S-k,...N&&{alignmentOffset:k}},reset:N}}}),ot=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:l=0,mainAxis:a=!0,crossAxis:c=!0}=ee(e,t),u={x:n,y:r},f=ie(o),d=re(f);let p=u[d],h=u[f];const m=ee(l,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(a){const e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,n=i.reference[d]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var y,v;const e="y"===d?"width":"height",t=["top","left"].includes(te(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(y=s.offset)?void 0:y[f])||0)+(t?0:g.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(v=s.offset)?void 0:v[f])||0)-(t?g.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}},it=(e,t,n)=>{const r=new Map,o={platform:qe,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,l=i.filter(Boolean),a=await(null==s.isRTL?void 0:s.isRTL(t));let c=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:f}=fe(c,r,a),d=r,p={},h=0;for(let m=0;m<l.length;m++){const{name:n,fn:i}=l[m],{x:g,y:y,data:v,reset:w}=await i({x:u,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:s,elements:{reference:e,floating:t}});u=null!=g?g:u,f=null!=y?y:f,p={...p,[n]:{...p[n],...v}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await s.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:u,y:f}=fe(c,d,a))),m=-1)}return{x:u,y:f,placement:d,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var st="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function lt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!lt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!lt(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function at(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ct(e,t){const n=at(e);return Math.round(t*n)/n}function ut(t){const n=e.useRef(t);return st((()=>{n.current=t})),n}const ft=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?rt({element:n.current,padding:r}).fn(t):{}:n?rt({element:n,padding:r}).fn(t):{};var o}}),dt=(e,t)=>({...Qe(e),options:[e,t]}),pt=(e,t)=>({...ot(e),options:[e,t]}),ht=(e,t)=>({...et(e),options:[e,t]}),mt=(e,t)=>({...tt(e),options:[e,t]}),gt=(e,t)=>({...nt(e),options:[e,t]}),yt=(e,t)=>({...ft(e),options:[e,t]});var vt=e.forwardRef(((e,t)=>{const{children:n,width:r=10,height:i=5,...s}=e;return o.jsx(y.svg,{...s,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:o.jsx("polygon",{points:"0,0 30,0 15,10"})})}));vt.displayName="Arrow";var wt=vt;var xt="Popper",[bt,Et]=c(xt),[Rt,Tt]=bt(xt),Ct="PopperAnchor",Pt=e.forwardRef(((t,n)=>{const{__scopePopper:r,virtualRef:i,...s}=t,l=Tt(Ct,r),c=e.useRef(null),u=a(n,c);return e.useEffect((()=>{l.onAnchorChange((null==i?void 0:i.current)||c.current)})),i?null:o.jsx(y.div,{...s,ref:u})}));Pt.displayName=Ct;var Ot="PopperContent",[At,Lt]=bt(Ot),Dt=e.forwardRef(((n,r)=>{var i,s,l,c,u,f;const{__scopePopper:d,side:p="bottom",sideOffset:h=0,align:m="center",alignOffset:g=0,arrowPadding:v=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:T=!1,updatePositionStrategy:C="optimized",onPlaced:P,...O}=n,A=Tt(Ot,d),[L,D]=e.useState(null),N=a(r,(e=>D(e))),[k,j]=e.useState(null),M=function(t){const[n,r]=e.useState(void 0);return S((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}(k),_=(null==M?void 0:M.width)??0,W=(null==M?void 0:M.height)??0,F=p+("center"!==m?"-"+m:""),I="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},H=Array.isArray(b)?b:[b],B=H.length>0,$={padding:I,boundary:H.filter(jt),altBoundary:B},{refs:z,floatingStyles:V,placement:U,isPositioned:Y,middlewareData:X}=function(n){void 0===n&&(n={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s,elements:{reference:l,floating:a}={},transform:c=!0,whileElementsMounted:u,open:f}=n,[d,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[h,m]=e.useState(i);lt(h,i)||m(i);const[g,y]=e.useState(null),[v,w]=e.useState(null),x=e.useCallback((e=>{e!==T.current&&(T.current=e,y(e))}),[]),b=e.useCallback((e=>{e!==C.current&&(C.current=e,w(e))}),[]),E=l||g,R=a||v,T=e.useRef(null),C=e.useRef(null),P=e.useRef(d),O=null!=u,A=ut(u),L=ut(s),D=ut(f),S=e.useCallback((()=>{if(!T.current||!C.current)return;const e={placement:r,strategy:o,middleware:h};L.current&&(e.platform=L.current),it(T.current,C.current,e).then((e=>{const n={...e,isPositioned:!1!==D.current};N.current&&!lt(P.current,n)&&(P.current=n,t.flushSync((()=>{p(n)})))}))}),[h,r,o,L,D]);st((()=>{!1===f&&P.current.isPositioned&&(P.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[f]);const N=e.useRef(!1);st((()=>(N.current=!0,()=>{N.current=!1})),[]),st((()=>{if(E&&(T.current=E),R&&(C.current=R),E&&R){if(A.current)return A.current(E,R,S);S()}}),[E,R,S,A,O]);const k=e.useMemo((()=>({reference:T,floating:C,setReference:x,setFloating:b})),[x,b]),j=e.useMemo((()=>({reference:E,floating:R})),[E,R]),M=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!j.floating)return e;const t=ct(j.floating,d.x),n=ct(j.floating,d.y);return c?{...e,transform:"translate("+t+"px, "+n+"px)",...at(j.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,c,j.floating,d.x,d.y]);return e.useMemo((()=>({...d,update:S,refs:k,elements:j,floatingStyles:M})),[d,S,k,j,M])}({strategy:"fixed",placement:F,whileElementsMounted:(...e)=>Ge(...e,{animationFrame:"always"===C}),elements:{reference:A.anchor},middleware:[(K={mainAxis:h+W,alignmentAxis:g},{...Je(K),options:[K,q]}),x&&dt({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?pt():void 0,...$}),x&&ht({...$}),mt({...$,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${n}px`),s.setProperty("--radix-popper-available-height",`${r}px`),s.setProperty("--radix-popper-anchor-width",`${o}px`),s.setProperty("--radix-popper-anchor-height",`${i}px`)}}),k&&yt({element:k,padding:v}),Mt({arrowWidth:_,arrowHeight:W}),T&&gt({strategy:"referenceHidden",...$})]});var K,q;const[Z,G]=_t(U),J=w(P);S((()=>{Y&&(null==J||J())}),[Y,J]);const Q=null==(i=X.arrow)?void 0:i.x,ee=null==(s=X.arrow)?void 0:s.y,te=0!==(null==(l=X.arrow)?void 0:l.centerOffset),[ne,re]=e.useState();return S((()=>{L&&re(window.getComputedStyle(L).zIndex)}),[L]),o.jsx("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:Y?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ne,"--radix-popper-transform-origin":[null==(c=X.transformOrigin)?void 0:c.x,null==(u=X.transformOrigin)?void 0:u.y].join(" "),...(null==(f=X.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:o.jsx(At,{scope:d,placedSide:Z,onArrowChange:j,arrowX:Q,arrowY:ee,shouldHideArrow:te,children:o.jsx(y.div,{"data-side":Z,"data-align":G,...O,ref:N,style:{...O.style,animation:Y?void 0:"none"}})})})}));Dt.displayName=Ot;var St="PopperArrow",Nt={top:"bottom",right:"left",bottom:"top",left:"right"},kt=e.forwardRef((function(e,t){const{__scopePopper:n,...r}=e,i=Lt(St,n),s=Nt[i.placedSide];return o.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:o.jsx(wt,{...r,ref:t,style:{...r.style,display:"block"}})})}));function jt(e){return null!==e}kt.displayName=St;var Mt=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o;const{placement:i,rects:s,middlewareData:l}=t,a=0!==(null==(n=l.arrow)?void 0:n.centerOffset),c=a?0:e.arrowWidth,u=a?0:e.arrowHeight,[f,d]=_t(i),p={start:"0%",center:"50%",end:"100%"}[d],h=((null==(r=l.arrow)?void 0:r.x)??0)+c/2,m=((null==(o=l.arrow)?void 0:o.y)??0)+u/2;let g="",y="";return"bottom"===f?(g=a?p:`${h}px`,y=-u+"px"):"top"===f?(g=a?p:`${h}px`,y=`${s.floating.height+u}px`):"right"===f?(g=-u+"px",y=a?p:`${m}px`):"left"===f&&(g=`${s.floating.width+u}px`,y=a?p:`${m}px`),{data:{x:g,y:y}}}});function _t(e){const[t,n="center"]=e.split("-");return[t,n]}var Wt=Pt,Ft=Dt,It=kt,[Ht,Bt]=c("Tooltip",[Et]),$t=Et(),zt="TooltipProvider",Vt=700,Ut="tooltip.open",[Yt,Xt]=Ht(zt),Kt=t=>{const{__scopeTooltip:n,delayDuration:r=Vt,skipDelayDuration:i=300,disableHoverableContent:s=!1,children:l}=t,a=e.useRef(!0),c=e.useRef(!1),u=e.useRef(0);return e.useEffect((()=>{const e=u.current;return()=>window.clearTimeout(e)}),[]),o.jsx(Yt,{scope:n,isOpenDelayedRef:a,delayDuration:r,onOpen:e.useCallback((()=>{window.clearTimeout(u.current),a.current=!1}),[]),onClose:e.useCallback((()=>{window.clearTimeout(u.current),u.current=window.setTimeout((()=>a.current=!0),i)}),[i]),isPointerInTransitRef:c,onPointerInTransitChange:e.useCallback((e=>{c.current=e}),[]),disableHoverableContent:s,children:l})};Kt.displayName=zt;var qt="Tooltip",[Zt,Gt]=Ht(qt),Jt="TooltipTrigger";e.forwardRef(((t,n)=>{const{__scopeTooltip:r,...s}=t,l=Gt(Jt,r),c=Xt(Jt,r),u=$t(r),f=a(n,e.useRef(null),l.onTriggerChange),d=e.useRef(!1),p=e.useRef(!1),h=e.useCallback((()=>d.current=!1),[]);return e.useEffect((()=>()=>document.removeEventListener("pointerup",h)),[h]),o.jsx(Wt,{asChild:!0,...u,children:o.jsx(y.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...s,ref:f,onPointerMove:i(t.onPointerMove,(e=>{"touch"!==e.pointerType&&(p.current||c.isPointerInTransitRef.current||(l.onTriggerEnter(),p.current=!0))})),onPointerLeave:i(t.onPointerLeave,(()=>{l.onTriggerLeave(),p.current=!1})),onPointerDown:i(t.onPointerDown,(()=>{l.open&&l.onClose(),d.current=!0,document.addEventListener("pointerup",h,{once:!0})})),onFocus:i(t.onFocus,(()=>{d.current||l.onOpen()})),onBlur:i(t.onBlur,l.onClose),onClick:i(t.onClick,l.onClose)})})})).displayName=Jt;var[Qt,en]=Ht("TooltipPortal",{forceMount:void 0}),tn="TooltipContent",nn=e.forwardRef(((e,t)=>{const n=en(tn,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...s}=e,l=Gt(tn,e.__scopeTooltip);return o.jsx(k,{present:r||l.open,children:l.disableHoverableContent?o.jsx(an,{side:i,...s,ref:t}):o.jsx(rn,{side:i,...s,ref:t})})})),rn=e.forwardRef(((t,n)=>{const r=Gt(tn,t.__scopeTooltip),i=Xt(tn,t.__scopeTooltip),s=e.useRef(null),l=a(n,s),[c,u]=e.useState(null),{trigger:f,onClose:d}=r,p=s.current,{onPointerInTransitChange:h}=i,m=e.useCallback((()=>{u(null),h(!1)}),[h]),g=e.useCallback(((e,t)=>{const n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,function(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}(r,n.getBoundingClientRect())),i=function(e){const t=e.slice();return t.sort(((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0)),function(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const n=e[r];for(;t.length>=2;){const e=t[t.length-1],r=t[t.length-2];if(!((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x)))break;t.pop()}t.push(n)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const t=e[r];for(;n.length>=2;){const e=n[n.length-1],r=n[n.length-2];if(!((e.x-r.x)*(t.y-r.y)>=(e.y-r.y)*(t.x-r.x)))break;n.pop()}n.push(t)}return n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}(t)}([...o,...function(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())]);u(i),h(!0)}),[h]);return e.useEffect((()=>()=>m()),[m]),e.useEffect((()=>{if(f&&p){const e=e=>g(e,p),t=e=>g(e,f);return f.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{f.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}}),[f,p,g,m]),e.useEffect((()=>{if(c){const e=e=>{const t=e.target,n={x:e.clientX,y:e.clientY},r=(null==f?void 0:f.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const e=t[i],l=t[s],a=e.x,c=e.y,u=l.x,f=l.y;c>r!=f>r&&n<(u-a)*(r-c)/(f-c)+a&&(o=!o)}return o}(n,c);r?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}}),[f,p,c,d,m]),o.jsx(an,{...t,ref:l})})),[on,sn]=Ht(qt,{isInside:!1}),ln=m("TooltipContent"),an=e.forwardRef(((t,n)=>{const{__scopeTooltip:r,children:i,"aria-label":s,onEscapeKeyDown:l,onPointerDownOutside:a,...c}=t,u=Gt(tn,r),f=$t(r),{onClose:d}=u;return e.useEffect((()=>(document.addEventListener(Ut,d),()=>document.removeEventListener(Ut,d))),[d]),e.useEffect((()=>{if(u.trigger){const e=e=>{const t=e.target;(null==t?void 0:t.contains(u.trigger))&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}}),[u.trigger,d]),o.jsx(C,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:o.jsxs(Ft,{"data-state":u.stateAttribute,...f,...c,ref:n,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[o.jsx(ln,{children:i}),o.jsx(on,{scope:r,isInside:!0,children:o.jsx(I,{id:u.contentId,role:"tooltip",children:s||i})})]})})}));nn.displayName=tn;var cn="TooltipArrow";e.forwardRef(((e,t)=>{const{__scopeTooltip:n,...r}=e,i=$t(n);return sn(cn,n).isInside?null:o.jsx(It,{...i,...r,ref:t})})).displayName=cn;var un=Kt,fn=nn;export{D as B,fn as C,y as P,L as R,d as S,F as V,f as a,_ as b,c,k as d,i as e,w as f,N as g,S as h,v as i,B as j,un as k,V as l,a as u};
//# sourceMappingURL=chunk-Bt4ub4Kz.js.map

{"version": 3, "file": "chunk-CS50z45r.js", "sources": ["../../node_modules/react-i18next/dist/es/utils.js", "../../node_modules/react-i18next/dist/es/unescape.js", "../../node_modules/react-i18next/dist/es/defaults.js", "../../node_modules/react-i18next/dist/es/i18nInstance.js", "../../node_modules/react-i18next/dist/es/initReactI18next.js", "../../node_modules/react-i18next/dist/es/context.js", "../../node_modules/react-i18next/dist/es/useTranslation.js", "../../node_modules/i18next/dist/esm/i18next.js", "../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["export const warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nexport const warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport const loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nexport const loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nexport const hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nexport const getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nexport const isString = obj => typeof obj === 'string';\nexport const isObject = obj => typeof obj === 'object' && obj !== null;", "const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);", "import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport const setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nexport const getDefaults = () => defaultOptions;", "let i18nInstance;\nexport const setI18n = instance => {\n  i18nInstance = instance;\n};\nexport const getI18n = () => i18nInstance;", "import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};", "import { createContext } from 'react';\nimport { getDefaults, setDefaults } from './defaults.js';\nimport { getI18n, setI18n } from './i18nInstance.js';\nimport { initReactI18next } from './initReactI18next.js';\nexport { getDefaults, setDefaults, getI18n, setI18n, initReactI18next };\nexport const I18nContext = createContext();\nexport class ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nexport const composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nexport const getInitialProps = () => {\n  const i18n = getI18n();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};", "import { useState, useEffect, useContext, useRef, useCallback } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace, isString, isObject } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => useCallback(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nexport const useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (isString(optsOrDefaultValue)) return optsOrDefaultValue;\n      if (isObject(optsOrDefaultValue) && isString(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) warnOnce(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  useEffect(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n};", "const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n", "const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "names": ["alreadyWarned", "warnOnce", "i18n", "code", "msg", "rest", "isString", "Date", "args", "_b", "_a", "services", "logger", "forward", "_d", "_c", "warn", "console", "loadedClb", "cb", "isInitialized", "initialized", "setTimeout", "off", "on", "loadNamespaces", "ns", "loadLanguages", "lng", "options", "preload", "indexOf", "for<PERSON>ach", "n", "push", "obj", "matchHtmlEntity", "htmlEntities", "unescapeHtmlEntity", "m", "defaultOptions", "bindI18n", "bindI18nStore", "transEmptyNodeValue", "transSupportBasicHtmlNodes", "transWrapTextNodes", "transKeepBasicHtmlNodesFor", "useSuspense", "unescape", "text", "replace", "i18nInstance", "initReactI18next", "type", "init", "instance", "react", "setI18n", "I18nContext", "createContext", "ReportNamespaces", "constructor", "this", "usedNamespaces", "addUsedNamespaces", "namespaces", "getUsedNamespaces", "Object", "keys", "alwaysNewT", "language", "namespace", "keyPrefix", "getFixedT", "useTranslation", "props", "i18nFromProps", "i18nFromContext", "defaultNS", "defaultNSFromContext", "useContext", "reportNamespaces", "notReadyT", "k", "optsOrDefaultValue", "defaultValue", "Array", "isArray", "length", "retNotReady", "t", "ready", "wait", "i18nOptions", "call", "initializedStoreOnce", "every", "languages", "hasLoadedNamespace", "precheck", "loadNotPending", "backendConnector", "backend", "isLanguageChangingTo", "memoGetT", "useCallback", "useMemoizedT", "nsMode", "getT", "getNewT", "setT", "useState", "joinedNS", "join", "previousJoinedNS", "value", "ignore", "ref", "useRef", "useEffect", "current", "usePrevious", "isMounted", "boundReset", "store", "split", "e", "ret", "Promise", "resolve", "defer", "res", "rej", "promise", "reject", "makeString", "object", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "key", "canNotTraverseDeeper", "getLastOfPath", "path", "Empty", "stack", "stackIndex", "prototype", "hasOwnProperty", "set<PERSON>ath", "newValue", "p", "slice", "last", "<PERSON><PERSON><PERSON>", "deepExtend", "target", "source", "overwrite", "prop", "String", "regexEscape", "str", "_entityMap", "escape", "data", "s", "chars", "looksLikeObjectPathRegExpCache", "capacity", "regExpMap", "Map", "regExpQueue", "getRegExp", "pattern", "regExpFromCache", "get", "regExpNew", "RegExp", "delete", "shift", "set", "deepFind", "keySeparator", "tokens", "i", "next", "nextPath", "j", "getCleanedCode", "consoleLogger", "log", "output", "error", "apply", "<PERSON><PERSON>", "concreteLogger", "prefix", "debug", "deprecate", "lvl", "debugOnly", "create", "moduleName", "clone", "baseLogger", "EventEmitter", "observers", "events", "listener", "event", "numListeners", "emit", "from", "entries", "observer", "numTimesAdded", "ResourceStore", "super", "ignoreJSONStructure", "addNamespaces", "removeNamespaces", "index", "splice", "getResource", "result", "addResource", "silent", "concat", "addResources", "resources", "addResourceBundle", "deep", "skipCopy", "pack", "JSON", "parse", "stringify", "removeResourceBundle", "hasResourceBundle", "getResourceBundle", "getDataByLanguage", "hasLanguageSomeTranslations", "find", "v", "toJSON", "postProcessor", "processors", "addPostProcessor", "module", "name", "handle", "translator", "processor", "process", "checkedLoadedFor", "shouldHandleAsObject", "Translator", "changeLanguage", "exists", "o", "interpolation", "opt", "resolved", "extractFromKey", "nsSeparator", "wouldCheckForNsInKey", "seemsNaturalLanguage", "userDefinedKeySeparator", "userDefinedNsSeparator", "possibleChars", "filter", "c", "r", "map", "matched", "test", "ki", "substring", "looksLikeObjectPath", "match", "interpolator", "nestingRegexp", "parts", "translate", "last<PERSON>ey", "overloadTranslationOptionHandler", "arguments", "returnDetails", "appendNamespaceToCIMode", "toLowerCase", "usedKey", "exactUsed<PERSON>ey", "usedLng", "usedNS", "usedParams", "getUsedParamsDetails", "resUsed<PERSON><PERSON>", "resExactUsedKey", "joinArrays", "handleAsObjectInI18nFormat", "i18nFormat", "handleAsObject", "needsPluralHandling", "count", "hasDefaultValue", "defaultValueSuffix", "pluralResolver", "getSuffix", "defaultValueSuffixOrdinalFallback", "ordinal", "needsZeroSuffixLookup", "pluralSeparator", "resForObjHndl", "resType", "toString", "extendTranslation", "usedDefault", "isValidLookup", "resForMissing", "missingKeyNoValueFallbackToKey", "updateMissing", "fk", "lngs", "fallbackLngs", "languageUtils", "getFallbackCodes", "fallbackLng", "saveMissingTo", "toResolveHierarchy", "send", "l", "specificDefaultValue", "defaultForMissing", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saveMissing", "saveMissingPlurals", "suffixes", "getSuffixes", "suffix", "appendNamespaceToMissingKey", "parseMissingKeyHandler", "returnObjects", "returnedObjectHandler", "resTypeIsArray", "copy", "newKeyToUse", "<PERSON><PERSON><PERSON>", "defaultVariables", "skipInterpolation", "skipOnVariables", "nestBef", "nb", "interpolate", "na", "nest", "context", "reset", "postProcess", "postProcessorNames", "applyPostProcessor", "postProcessPassResolved", "i18nResolved", "found", "extracted", "fallbackNS", "needsContextHandling", "codes", "utils", "finalKeys", "addLookupKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "contextSeparator", "<PERSON><PERSON><PERSON>", "pop", "returnNull", "returnEmptyString", "resourceStore", "optionsKeys", "useOptionsReplaceForData", "option", "LanguageUtil", "supportedLngs", "getScriptPartFromCode", "formatLanguageCode", "getLanguagePartFromCode", "formattedCode", "Intl", "getCanonicalLocales", "lowerCaseLng", "cleanCode", "isSupportedCode", "load", "nonExplicitSupportedLngs", "getBestMatchFromCodes", "cleanedLng", "lngScOnly", "lngOnly", "supportedLng", "fallbacks", "default", "fallbackCode", "fallbackCodes", "addCode", "fc", "suffixesOrder", "zero", "one", "two", "few", "many", "other", "dummyRule", "select", "resolvedOptions", "pluralCategories", "PluralResolver", "pluralRulesCache", "addRule", "rules", "clearCache", "getRule", "cleanedCode", "cache<PERSON>ey", "rule", "PluralRules", "err", "lngPart", "needsPlural", "getPluralFormsOfKey", "sort", "pluralCategory1", "pluralCategory2", "pluralCategory", "prepend", "deepFindWithDefaults", "defaultData", "getPathWithDefaults", "regexSafe", "val", "Interpolator", "format", "escapeValue", "escape$1", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "resetRegExp", "getOrResetRegExp", "existingRegExp", "lastIndex", "regexp", "regexpUnescape", "replaces", "handleFormat", "interpolationkey", "trim", "f", "missingInterpolationHandler", "regex", "safeValue", "todo", "exec", "matchedVar", "temp", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "doReduce", "elem", "reduce", "createCachedFormatter", "fn", "cache", "optForCache", "formatParams", "frm", "createNonCachedFormatter", "<PERSON><PERSON><PERSON>", "cf", "cacheInBuiltFormats", "formats", "number", "formatter", "NumberFormat", "currency", "style", "datetime", "DateTimeFormat", "relativetime", "RelativeTimeFormat", "range", "list", "ListFormat", "add", "addCached", "findIndex", "mem", "formatName", "formatOptions", "formatStr", "optStr", "<PERSON><PERSON><PERSON>", "isNaN", "parseInt", "parseFormatStr", "formatted", "valOptions", "locale", "Connector", "waitingReads", "maxP<PERSON>llelReads", "readingCalls", "maxRetries", "retryTimeout", "state", "queue", "queueLoad", "callback", "toLoad", "pending", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "reload", "pendingCount", "loaded", "errors", "q", "push<PERSON><PERSON>", "removePending", "done", "loadedKeys", "read", "fcName", "tried", "resolver", "bind", "then", "catch", "prepareLoading", "loadOne", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "_e", "opts", "initAsync", "simplifyPluralSuffix", "partialBundledLanguages", "tDescription", "transformOptions", "initImmediate", "noop", "I18n", "inst", "modules", "external", "getOwnPropertyNames", "getPrototypeOf", "isClone", "isInitializing", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "languageDetector", "detection", "deferred", "finish", "loadResources", "usedCallback", "append", "resolvedLanguage", "setResolvedLanguage", "reloadResources", "use", "Error", "li", "lngInLngs", "unshift", "setLngProps", "setLng", "fl", "cacheUserLanguage", "async", "detect", "fixedT", "<PERSON><PERSON><PERSON>", "setDefaultNamespace", "lastLng", "loadState", "preResult", "preloaded", "newLngs", "dir", "createInstance", "cloneInstance", "forkResourceStore", "mergedOptions", "clonedData", "prev", "acc", "fieldContentRegExp", "cookie", "minutes", "domain", "cookieOptions", "sameSite", "expires", "setTime", "getTime", "document", "encodeURIComponent", "maxAge", "Number", "Math", "floor", "TypeError", "toUTCString", "httpOnly", "secure", "partitioned", "serializeCookie", "nameEQ", "ca", "char<PERSON>t", "remove", "cookie$1", "lookup", "_ref", "lookup<PERSON><PERSON><PERSON>", "_ref2", "cookieMinutes", "cookieDomain", "querystring", "lookupQuerystring", "window", "search", "location", "hash", "params", "pos", "hasLocalStorageSupport", "localStorageAvailable", "localStorage", "<PERSON><PERSON><PERSON>", "setItem", "removeItem", "lookupLocalStorage", "getItem", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "navigator", "userLanguage", "htmlTag", "internalHtmlTag", "documentElement", "getAttribute", "lookupFromPathIndex", "pathname", "subdomain", "lookupFromSubdomainIndex", "internalLookupFromSubdomainIndex", "hostname", "canCookies", "order", "Browser", "detectors", "defaults", "caches", "excludeCache<PERSON>or", "convertDetectedLanguage", "lookupFromUrlIndex", "addDetector", "detector", "detectionOrder", "detected", "detectorName", "d", "input", "some", "cacheName"], "mappings": "wCAAO,MAeDA,EAAgB,CAAE,EACXC,EAAW,CAACC,EAAMC,EAAMC,EAAKC,KACpCC,EAASF,IAAQJ,EAAcI,KAC/BE,EAASF,OAAoBA,OAAWG,MAlB1B,EAACL,EAAMC,EAAMC,EAAKC,iBAC9B,MAAAG,EAAO,CAACJ,EAAK,CACjBD,UACIE,GAAQ,CAAE,IAEhB,GAAI,OAAAI,EAAA,OAAMC,EAAA,MAAAR,OAAA,EAAAA,EAAAS,eAAU,EAAAD,EAAAE,iBAAQC,QAC1B,OAAOX,EAAKS,SAASC,OAAOC,QAAQL,EAAM,OAAQ,mBAAmB,GAEnEF,EAASE,EAAK,MAAUA,EAAA,GAAK,mBAAmBA,EAAK,OACrD,OAAAM,EAAA,OAAMC,EAAA,MAAAb,OAAA,EAAAA,EAAAS,eAAU,EAAAI,EAAAH,iBAAQI,MAC1Bd,EAAKS,SAASC,OAAOI,QAAQR,UACpBS,uBAASD,OACVC,QAAAD,QAAQR,EACpB,EAMOQ,CAAAd,EAAMC,EAAMC,EAAKC,GAAI,EAEtBa,EAAY,CAAChB,EAAMiB,IAAO,KAC9B,GAAIjB,EAAKkB,cACHD,QACC,CACL,MAAME,EAAc,KAClBC,YAAW,KACJpB,EAAAqB,IAAI,cAAeF,EAAW,GAClC,GACCF,GAAA,EAEDjB,EAAAsB,GAAG,cAAeH,EAC3B,GAEaI,EAAiB,CAACvB,EAAMwB,EAAIP,KACvCjB,EAAKuB,eAAeC,EAAIR,EAAUhB,EAAMiB,GAAG,EAEhCQ,EAAgB,CAACzB,EAAM0B,EAAKF,EAAIP,KAE3C,GADIb,EAASoB,KAAKA,EAAK,CAACA,IACpBxB,EAAK2B,QAAQC,SAAW5B,EAAK2B,QAAQC,QAAQC,QAAQH,IAAW,EAAA,OAAOH,EAAevB,EAAMwB,EAAIP,GACpGO,EAAGM,SAAaC,IACV/B,EAAK2B,QAAQH,GAAGK,QAAQE,GAAK,GAAQ/B,EAAA2B,QAAQH,GAAGQ,KAAKD,EAAC,IAE5D/B,EAAKyB,cAAcC,EAAKV,EAAUhB,EAAMiB,GAAG,EAiBhCb,EAAkB6B,GAAe,iBAARA,EC5DhCC,EAAkB,oGAClBC,EAAe,CACnB,QAAS,IACT,QAAS,IACT,OAAQ,IACR,QAAS,IACT,OAAQ,IACR,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,QAAS,IACT,SAAU,IACV,SAAU,IACV,SAAU,IACV,SAAU,IACV,QAAS,IACT,SAAU,IACV,WAAY,IACZ,UAAW,IACX,SAAU,IACV,QAAS,KAELC,EAA0BC,GAAAF,EAAaE,GCtB7C,IAAIC,EAAiB,CACnBC,SAAU,kBACVC,cAAe,GACfC,oBAAqB,GACrBC,4BAA4B,EAC5BC,mBAAoB,GACpBC,2BAA4B,CAAC,KAAM,SAAU,IAAK,KAClDC,aAAa,EACbC,SDesBC,GAAQA,EAAKC,QAAQd,EAAiBE,IExB9D,IAAIa,EACG,MCCMC,EAAmB,CAC9BC,KAAM,WACN,IAAAC,CAAKC,GFOoB,EAAC1B,EAAU,MACnBW,EAAA,IACZA,KACAX,EACJ,EEVa0B,CAAAA,EAAS1B,QAAQ2B,ODJV,CAAAD,IACNA,EAAAA,CAAAA,ECIbE,CAAQF,EACZ,GCFaG,EAAcC,EAAAA,gBACpB,MAAMC,EACX,WAAAC,GACEC,KAAKC,eAAiB,CAAE,CAC5B,CACE,iBAAAC,CAAkBC,GAChBA,EAAWjC,SAAcN,IAClBoC,KAAKC,eAAerC,KAAUoC,KAAAC,eAAerC,IAAM,EAAA,GAE9D,CACE,iBAAAwC,GACS,OAAAC,OAAOC,KAAKN,KAAKC,eAC5B,ECdA,MAOMM,EAAa,CAACnE,EAAMoE,EAAUC,EAAWC,IAActE,EAAKuE,UAAUH,EAAUC,EAAWC,GAEpFE,EAAiB,CAAChD,EAAIiD,EAAQ,kBACnC,MACJzE,KAAM0E,GACJD,GAEFzE,KAAM2E,EACNC,UAAWC,GACTC,EAAUA,WAACtB,IAAgB,CAAE,EAC3BxD,EAAO0E,GAAiBC,GHhBH1B,EGkB3B,GADIjD,IAASA,EAAK+E,mBAAuB/E,EAAA+E,iBAAmB,IAAIrB,IAC3D1D,EAAM,CACAD,EAAAC,EAAM,sBAAuB,0FAChC,MAAAgF,EAAY,CAACC,EAAGC,KAChB9E,OAAAA,EAAS8E,GAA4BA,ENoCD,iBAAtBjD,EMnCLiD,INmC+C,OAARjD,GMnChB7B,EAAS8E,EAAmBC,cAAsBD,EAAmBC,aAClGC,MAAMC,QAAQJ,GAAKA,EAAEA,EAAEK,OAAS,GAAKL,ENkC1B,IAAAhD,CMlC0B,EAExCsD,EAAc,CAACP,EAAW,CAAA,GAAI,GAI7B,OAHPO,EAAYC,EAAIR,EAChBO,EAAYvF,KAAO,CAAE,EACrBuF,EAAYE,OAAQ,EACbF,CACX,EACM,OAAA/E,EAAAR,EAAK2B,QAAQ2B,YAAb,EAAA9C,EAAoBkF,OAAe3F,EAAAC,EAAM,oBAAqB,uHAClE,MAAM2F,EAAc,IJnBWrD,KIqB1BtC,EAAK2B,QAAQ2B,SACbmB,IAEC5B,YACJA,EAAAyB,UACAA,GACEqB,EACJ,IAAI5B,EAAmBc,IAAwB,OAAAtE,EAAKP,EAAA2B,cAAS,EAAApB,EAAAqE,WAChDxE,EAAAA,EAAS2D,GAAc,CAACA,GAAcA,GAAc,CAAC,eAClE,OAAKnD,GAAAC,EAAAb,EAAA+E,kBAAiBjB,oBAAoBlD,EAAAgF,KAAA/E,EAAAkD,GAC1C,MAAM0B,GAASzF,EAAKkB,eAAiBlB,EAAK6F,uBAAyB9B,EAAW+B,OAAM/D,GNHpD,EAACP,EAAIxB,EAAM2B,EAAU,CAAA,IAChD3B,EAAK+F,WAAc/F,EAAK+F,UAAUT,OAMhCtF,EAAKgG,mBAAmBxE,EAAI,CACjCE,IAAKC,EAAQD,IACbuE,SAAU,CAAChD,EAAciD,WACvB,IAAI,OAAA1F,EAAAmB,EAAQY,mBAAUV,QAAQ,yBAA4BoB,EAAaxC,SAAS0F,iBAAiBC,SAAWnD,EAAaoD,uBAAyBH,EAAejD,EAAaoD,qBAAsB7E,GAAY,OAAA,CAAA,KARzMzB,EAAAC,EAAM,eAAgB,yCAA0C,CACvE+F,UAAW/F,EAAK+F,aAEX,GMFgFC,CAAmBjE,EAAG/B,EAAM2F,KAC/GW,EAtCa,EAACtG,EAAMoE,EAAUC,EAAWC,IAAciC,EAAWA,YAACpC,EAAWnE,EAAMoE,EAAUC,EAAWC,GAAY,CAACtE,EAAMoE,EAAUC,EAAWC,IAsCtIkC,CAAaxG,EAAMyE,EAAM/C,KAAO,KAA6B,aAAvBiE,EAAYc,OAAwB1C,EAAaA,EAAW,GAAIO,GACjHoC,EAAO,IAAMJ,EACbK,EAAU,IAAMxC,EAAWnE,EAAMyE,EAAM/C,KAAO,KAA6B,aAAvBiE,EAAYc,OAAwB1C,EAAaA,EAAW,GAAIO,IACnHkB,EAAGoB,GAAQC,EAAAA,SAASH,GACvB,IAAAI,EAAW/C,EAAWgD,OACtBtC,EAAM/C,MAAKoF,EAAW,GAAGrC,EAAM/C,MAAMoF,KACnC,MAAAE,EApDY,EAACC,EAAOC,KACpB,MAAAC,EAAMC,EAAAA,SAIZ,OAHAC,EAAAA,WAAU,KACRF,EAAIG,QAAiCL,CAAA,GACpC,CAACA,EAAOC,IACJC,EAAIG,OAAA,EA+CcC,CAAYT,GAC/BU,EAAYJ,EAAMA,QAAC,GACzBC,EAAAA,WAAU,KACF,MAAA9E,SACJA,EAAAC,cACAA,GACEmD,EACJ6B,EAAUF,SAAU,EACf7B,GAAU5C,IACT4B,EAAM/C,IACRD,EAAczB,EAAMyE,EAAM/C,IAAKqC,GAAY,KACrCyD,EAAUF,SAASV,EAAKD,EAAO,IAGtBpF,EAAAvB,EAAM+D,GAAY,KAC3ByD,EAAUF,SAASV,EAAKD,EAAO,KAIrClB,GAASuB,GAAoBA,IAAqBF,GAAYU,EAAUF,SAC1EV,EAAKD,GAEP,MAAMc,EAAa,KACbD,EAAUF,SAASV,EAAKD,EAAO,EAIrC,OAFIpE,IAAgB,MAAAvC,GAAAA,EAAAsB,GAAGiB,EAAUkF,IAC7BjF,IAAe,MAAAxC,GAAAA,EAAM0H,MAAMpG,GAAGkB,EAAeiF,IAC1C,KACLD,EAAUF,SAAU,EAChBtH,eAAgB2H,MAAM,KAAK7F,SAAa8F,GAAA5H,EAAKqB,IAAIuG,EAAGH,MACpDjF,GAAiBxC,GAAoBwC,EAAAmF,MAAM,KAAK7F,SAAQ8F,GAAK5H,EAAK0H,MAAMrG,IAAIuG,EAAGH,IAAW,CAC/F,GACA,CAACzH,EAAM8G,IACVO,EAAAA,WAAU,KACJG,EAAUF,SAAW7B,GACvBmB,EAAKF,EACX,GACK,CAAC1G,EAAMsE,EAAWmB,IACrB,MAAMoC,EAAM,CAACrC,EAAGxF,EAAMyF,GAItB,GAHAoC,EAAIrC,EAAIA,EACRqC,EAAI7H,KAAOA,EACX6H,EAAIpC,MAAQA,EACRA,EAAc,OAAAoC,EAClB,IAAKpC,IAAU5C,EAAoB,OAAAgF,EAC7B,MAAA,IAAIC,SAAmBC,IACvBtD,EAAM/C,IACRD,EAAczB,EAAMyE,EAAM/C,IAAKqC,GAAY,IAAMgE,MAEjDxG,EAAevB,EAAM+D,GAAY,IAAMgE,KAC7C,GACG,ECzGG3H,EAAkB6B,GAAe,iBAARA,EACzB+F,EAAQ,KACR,IAAAC,EACAC,EACJ,MAAMC,EAAU,IAAIL,SAAQ,CAACC,EAASK,KAC9BH,EAAAF,EACAG,EAAAE,CAAA,IAID,OAFPD,EAAQJ,QAAUE,EAClBE,EAAQC,OAASF,EACVC,CAAA,EAEHE,EAAuBC,GACb,MAAVA,EAAuB,GACpB,GAAKA,EAORC,EAA4B,OAC5BC,EAAWC,GAAOA,GAAOA,EAAI5G,QAAQ,QAAS,EAAK4G,EAAIzF,QAAQuF,EAA2B,KAAOE,EACjGC,EAAuBJ,IAAWA,GAAUlI,EAASkI,GACrDK,EAAgB,CAACL,EAAQM,EAAMC,KAC7B,MAAAC,EAAS1I,EAASwI,GAAeA,EAAKjB,MAAM,KAAlBiB,EAChC,IAAIG,EAAa,EACV,KAAAA,EAAaD,EAAMxD,OAAS,GAAG,CACpC,GAAIoD,EAAqBJ,GAAS,MAAO,CAAE,EAC3C,MAAMG,EAAMD,EAASM,EAAMC,KACtBT,EAAOG,IAAQI,IAAcP,EAAAG,GAAO,IAAII,GAE3CP,EADErE,OAAO+E,UAAUC,eAAerD,KAAK0C,EAAQG,GACtCH,EAAOG,GAEP,CAAE,IAEXM,CACN,CACE,OAAIL,EAAqBJ,GAAgB,CAAE,EACpC,CACLrG,IAAKqG,EACLrD,EAAGuD,EAASM,EAAMC,IACnB,EAEGG,EAAU,CAACZ,EAAQM,EAAMO,KACvB,MAAAlH,IACJA,EAAAgD,EACAA,GACE0D,EAAcL,EAAQM,EAAM3E,QAChC,QAAY,IAARhC,GAAqC,IAAhB2G,EAAKtD,OAE5B,YADArD,EAAIgD,GAAKkE,GAGX,IAAIvB,EAAIgB,EAAKA,EAAKtD,OAAS,GACvB8D,EAAIR,EAAKS,MAAM,EAAGT,EAAKtD,OAAS,GAChCgE,EAAOX,EAAcL,EAAQc,EAAGnF,QACpC,UAAoB,IAAbqF,EAAKrH,KAAqBmH,EAAE9D,QACjCsC,EAAI,GAAGwB,EAAEA,EAAE9D,OAAS,MAAMsC,IAC1BwB,EAAIA,EAAEC,MAAM,EAAGD,EAAE9D,OAAS,GACnBgE,EAAAX,EAAcL,EAAQc,EAAGnF,SACtB,MAANqF,OAAM,EAAAA,EAAArH,WAA6C,IAA/BqH,EAAKrH,IAAI,GAAGqH,EAAKrE,KAAK2C,OAC5C0B,EAAKrH,SAAM,GAGfqH,EAAKrH,IAAI,GAAGqH,EAAKrE,KAAK2C,KAAOuB,CAAA,EAUzBI,EAAU,CAACjB,EAAQM,KACjB,MAAA3G,IACJA,EAAAgD,EACAA,GACE0D,EAAcL,EAAQM,GACtB,GAAC3G,GACAgC,OAAO+E,UAAUC,eAAerD,KAAK3D,EAAKgD,GAC/C,OAAOhD,EAAIgD,EAAC,EASRuE,EAAa,CAACC,EAAQC,EAAQC,KAClC,IAAA,MAAWC,KAAQF,EACJ,cAATE,GAAiC,gBAATA,IACtBA,KAAQH,EACNrJ,EAASqJ,EAAOG,KAAUH,EAAOG,aAAiBC,QAAUzJ,EAASsJ,EAAOE,KAAUF,EAAOE,aAAiBC,OAC5GF,IAAWF,EAAOG,GAAQF,EAAOE,IAErCJ,EAAWC,EAAOG,GAAOF,EAAOE,GAAOD,GAGlCF,EAAAG,GAAQF,EAAOE,IAIrB,OAAAH,CAAA,EAEHK,EAAcC,GAAOA,EAAI/G,QAAQ,sCAAuC,QAC9E,IAAIgH,EAAa,CACf,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,QACL,IAAK,UAEP,MAAMC,EAAiBC,GACjB9J,EAAS8J,GACJA,EAAKlH,QAAQ,cAAmBmH,GAAAH,EAAWG,KAE7CD,EAsBT,MAAME,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,KAC7BC,EAAiC,IArBvC,MACE,WAAA1G,CAAY2G,GACV1G,KAAK0G,SAAWA,EACX1G,KAAA2G,cAAgBC,IACrB5G,KAAK6G,YAAc,EACvB,CACE,SAAAC,CAAUC,GACR,MAAMC,EAAkBhH,KAAK2G,UAAUM,IAAIF,GAC3C,QAAwB,IAApBC,EACK,OAAAA,EAEH,MAAAE,EAAY,IAAIC,OAAOJ,GAMtB,OALH/G,KAAK6G,YAAYnF,SAAW1B,KAAK0G,UACnC1G,KAAK2G,UAAUS,OAAOpH,KAAK6G,YAAYQ,SAEpCrH,KAAA2G,UAAUW,IAAIP,EAASG,GACvBlH,KAAA6G,YAAYzI,KAAK2I,GACfG,CACX,GAGuD,IAgBjDK,EAAW,CAAClJ,EAAK2G,EAAMwC,EAAe,OACtC,IAACnJ,EAAY,OACb,GAAAA,EAAI2G,GAAO,CACT,IAAC3E,OAAO+E,UAAUC,eAAerD,KAAK3D,EAAK2G,GAAc,OAC7D,OAAO3G,EAAI2G,EACf,CACQ,MAAAyC,EAASzC,EAAKjB,MAAMyD,GAC1B,IAAI9D,EAAUrF,EACd,IAAA,IAASqJ,EAAI,EAAGA,EAAID,EAAO/F,QAAS,CAClC,IAAKgC,GAA8B,iBAAZA,EACd,OAEL,IAAAiE,EACAC,EAAW,GACf,IAAA,IAASC,EAAIH,EAAGG,EAAIJ,EAAO/F,SAAUmG,EAMnC,GALIA,IAAMH,IACIE,GAAAJ,GAEdI,GAAYH,EAAOI,GACnBF,EAAOjE,EAAQkE,QACF,IAATD,EAAoB,CACtB,GAAI,CAAC,SAAU,SAAU,WAAW1J,eAAe0J,IAAQ,GAAME,EAAIJ,EAAO/F,OAAS,EACnF,SAEFgG,GAAKG,EAAIH,EAAI,EACb,KACR,CAEchE,EAAAiE,CACd,CACS,OAAAjE,CAAA,EAEHoE,EAAiBzL,GAAc,MAANA,OAAM,EAAAA,EAAA+C,QAAQ,IAAK,KAE5C2I,EAAgB,CACpBxI,KAAM,SACN,GAAAyI,CAAItL,GACGsD,KAAAiI,OAAO,MAAOvL,EACpB,EACD,IAAAQ,CAAKR,GACEsD,KAAAiI,OAAO,OAAQvL,EACrB,EACD,KAAAwL,CAAMxL,GACCsD,KAAAiI,OAAO,QAASvL,EACtB,EACD,MAAAuL,CAAO1I,EAAM7C,WACX,OAAAC,EAAA,OAAAC,EAAA,MAAAO,aAAA,EAAAA,QAAUoC,SAAV,EAAA3C,EAAiBuL,QAAjBxL,EAAAqF,KAAApF,EAAyBO,QAAST,EACtC,GAEA,MAAM0L,EACJ,WAAArI,CAAYsI,EAAgBtK,EAAU,IAC/BiC,KAAAR,KAAK6I,EAAgBtK,EAC9B,CACE,IAAAyB,CAAK6I,EAAgBtK,EAAU,IACxBiC,KAAAsI,OAASvK,EAAQuK,QAAU,WAChCtI,KAAKlD,OAASuL,GAAkBN,EAChC/H,KAAKjC,QAAUA,EACfiC,KAAKuI,MAAQxK,EAAQwK,KACzB,CACE,GAAAP,IAAOtL,GACL,OAAOsD,KAAKjD,QAAQL,EAAM,MAAO,IAAI,EACzC,CACE,IAAAQ,IAAQR,GACN,OAAOsD,KAAKjD,QAAQL,EAAM,OAAQ,IAAI,EAC1C,CACE,KAAAwL,IAASxL,GACP,OAAOsD,KAAKjD,QAAQL,EAAM,QAAS,GACvC,CACE,SAAA8L,IAAa9L,GACX,OAAOsD,KAAKjD,QAAQL,EAAM,OAAQ,wBAAwB,EAC9D,CACE,OAAAK,CAAQL,EAAM+L,EAAKH,EAAQI,GACzB,OAAIA,IAAc1I,KAAKuI,MAAc,MACjC/L,EAASE,EAAK,QAAU,GAAK,GAAG4L,IAAStI,KAAKsI,UAAU5L,EAAK,MAC1DsD,KAAKlD,OAAO2L,GAAK/L,GAC5B,CACE,MAAAiM,CAAOC,GACE,OAAA,IAAIR,EAAOpI,KAAKlD,OAAQ,CAE3BwL,OAAQ,GAAGtI,KAAKsI,UAAUM,QAEzB5I,KAAKjC,SAEd,CACE,KAAA8K,CAAM9K,GAGJ,OAFAA,EAAUA,GAAWiC,KAAKjC,SAClBuK,OAASvK,EAAQuK,QAAUtI,KAAKsI,OACjC,IAAIF,EAAOpI,KAAKlD,OAAQiB,EACnC,EAEA,IAAI+K,EAAa,IAAIV,EAErB,MAAMW,EACJ,WAAAhJ,GACEC,KAAKgJ,UAAY,CAAE,CACvB,CACE,EAAAtL,CAAGuL,EAAQC,GAMF,OALPD,EAAOlF,MAAM,KAAK7F,SAAiBiL,IAC5BnJ,KAAKgJ,UAAUG,UAAaH,UAAUG,GAAS,IAAIvC,KACxD,MAAMwC,EAAepJ,KAAKgJ,UAAUG,GAAOlC,IAAIiC,IAAa,EAC5DlJ,KAAKgJ,UAAUG,GAAO7B,IAAI4B,EAAUE,EAAe,EAAC,IAE/CpJ,IACX,CACE,GAAAvC,CAAI0L,EAAOD,GACJlJ,KAAKgJ,UAAUG,KACfD,EAILlJ,KAAKgJ,UAAUG,GAAO/B,OAAO8B,UAHpBlJ,KAAKgJ,UAAUG,GAI5B,CACE,IAAAE,CAAKF,KAAUzM,GACT,GAAAsD,KAAKgJ,UAAUG,GAAQ,CACV3H,MAAM8H,KAAKtJ,KAAKgJ,UAAUG,GAAOI,WACzCrL,SAAQ,EAAEsL,EAAUC,MACzB,IAAA,IAAS/B,EAAI,EAAGA,EAAI+B,EAAe/B,IACjC8B,KAAY9M,EACtB,GAEA,CACQ,GAAAsD,KAAKgJ,UAAU,KAAM,CACRxH,MAAM8H,KAAKtJ,KAAKgJ,UAAU,KAAKO,WACvCrL,SAAQ,EAAEsL,EAAUC,MACzB,IAAA,IAAS/B,EAAI,EAAGA,EAAI+B,EAAe/B,IACjC8B,EAASrB,MAAMqB,EAAU,CAACL,KAAUzM,GAC9C,GAEA,CACA,EAGA,MAAMgN,UAAsBX,EAC1B,WAAAhJ,CAAYuG,EAAMvI,EAAU,CAC1BH,GAAI,CAAC,eACLoD,UAAW,gBAEJ2I,QACF3J,KAAAsG,KAAOA,GAAQ,CAAE,EACtBtG,KAAKjC,QAAUA,OACmB,IAA9BiC,KAAKjC,QAAQyJ,eACfxH,KAAKjC,QAAQyJ,aAAe,UAEW,IAArCxH,KAAKjC,QAAQ6L,sBACf5J,KAAKjC,QAAQ6L,qBAAsB,EAEzC,CACE,aAAAC,CAAcjM,GACRoC,KAAKjC,QAAQH,GAAGK,QAAQL,GAAM,GAC3BoC,KAAAjC,QAAQH,GAAGQ,KAAKR,EAE3B,CACE,gBAAAkM,CAAiBlM,GACf,MAAMmM,EAAQ/J,KAAKjC,QAAQH,GAAGK,QAAQL,GAClCmM,GAAY,GACd/J,KAAKjC,QAAQH,GAAGoM,OAAOD,EAAO,EAEpC,CACE,WAAAE,CAAYnM,EAAKF,EAAIiH,EAAK9G,EAAU,CAAA,WAClC,MAAMyJ,OAAwC,IAAzBzJ,EAAQyJ,aAA6BzJ,EAAQyJ,aAAexH,KAAKjC,QAAQyJ,aACxFoC,OAAsD,IAAhC7L,EAAQ6L,oBAAoC7L,EAAQ6L,oBAAsB5J,KAAKjC,QAAQ6L,oBAC/G5E,IAAAA,EACAlH,EAAIG,QAAQ,MAAW,EACzB+G,EAAOlH,EAAIiG,MAAM,MAEjBiB,EAAO,CAAClH,EAAKF,GACTiH,IACErD,MAAMC,QAAQoD,GAChBG,EAAK5G,QAAQyG,GACJrI,EAASqI,IAAQ2C,EAC1BxC,EAAK5G,QAAQyG,EAAId,MAAMyD,IAEvBxC,EAAK5G,KAAKyG,KAIhB,MAAMqF,EAASvE,EAAQ3F,KAAKsG,KAAMtB,GAMlC,OALKkF,IAAWtM,IAAOiH,GAAO/G,EAAIG,QAAQ,MAAW,IACnDH,EAAMkH,EAAK,GACXpH,EAAKoH,EAAK,GACVH,EAAMG,EAAKS,MAAM,GAAGtC,KAAK,OAEvB+G,GAAWN,GAAwBpN,EAASqI,GACzC0C,EAAS,OAAA5K,EAAA,cAAK2J,WAAL,EAAA1J,EAAYkB,SAAO,EAAAnB,EAAAiB,GAAKiH,EAAK2C,GADgB0C,CAEjE,CACE,WAAAC,CAAYrM,EAAKF,EAAIiH,EAAKxB,EAAOtF,EAAU,CACzCqM,QAAQ,IAER,MAAM5C,OAAwC,IAAzBzJ,EAAQyJ,aAA6BzJ,EAAQyJ,aAAexH,KAAKjC,QAAQyJ,aAC1FxC,IAAAA,EAAO,CAAClH,EAAKF,GACbiH,IAAKG,EAAOA,EAAKqF,OAAO7C,EAAe3C,EAAId,MAAMyD,GAAgB3C,IACjE/G,EAAIG,QAAQ,MAAW,IACzB+G,EAAOlH,EAAIiG,MAAM,KACTV,EAAAzF,EACRA,EAAKoH,EAAK,IAEZhF,KAAK6J,cAAcjM,GACX0H,EAAAtF,KAAKsG,KAAMtB,EAAM3B,GACpBtF,EAAQqM,QAAQpK,KAAKqJ,KAAK,QAASvL,EAAKF,EAAIiH,EAAKxB,EAC1D,CACE,YAAAiH,CAAaxM,EAAKF,EAAI2M,EAAWxM,EAAU,CACzCqM,QAAQ,IAER,IAAA,MAAW3L,KAAK8L,GACV/N,EAAS+N,EAAU9L,KAAO+C,MAAMC,QAAQ8I,EAAU9L,WAAU0L,YAAYrM,EAAKF,EAAIa,EAAG8L,EAAU9L,GAAI,CACpG2L,QAAQ,IAGPrM,EAAQqM,QAAQpK,KAAKqJ,KAAK,QAASvL,EAAKF,EAAI2M,EACrD,CACE,iBAAAC,CAAkB1M,EAAKF,EAAI2M,EAAWE,EAAM1E,EAAWhI,EAAU,CAC/DqM,QAAQ,EACRM,UAAU,IAEN1F,IAAAA,EAAO,CAAClH,EAAKF,GACbE,EAAIG,QAAQ,MAAW,IACzB+G,EAAOlH,EAAIiG,MAAM,KACV0G,EAAAF,EACKA,EAAA3M,EACZA,EAAKoH,EAAK,IAEZhF,KAAK6J,cAAcjM,GACnB,IAAI+M,EAAOhF,EAAQ3F,KAAKsG,KAAMtB,IAAS,CAAE,EACpCjH,EAAQ2M,WAAUH,EAAYK,KAAKC,MAAMD,KAAKE,UAAUP,KACzDE,EACS7E,EAAA+E,EAAMJ,EAAWxE,GAErB4E,EAAA,IACFA,KACAJ,GAGCjF,EAAAtF,KAAKsG,KAAMtB,EAAM2F,GACpB5M,EAAQqM,QAAQpK,KAAKqJ,KAAK,QAASvL,EAAKF,EAAI2M,EACrD,CACE,oBAAAQ,CAAqBjN,EAAKF,GACpBoC,KAAKgL,kBAAkBlN,EAAKF,WACvBoC,KAAKsG,KAAKxI,GAAKF,GAExBoC,KAAK8J,iBAAiBlM,GACjBoC,KAAAqJ,KAAK,UAAWvL,EAAKF,EAC9B,CACE,iBAAAoN,CAAkBlN,EAAKF,GACrB,YAAqC,IAA9BoC,KAAKiK,YAAYnM,EAAKF,EACjC,CACE,iBAAAqN,CAAkBnN,EAAKF,GAEd,OADFA,IAASA,EAAAoC,KAAKjC,QAAQiD,WACpBhB,KAAKiK,YAAYnM,EAAKF,EACjC,CACE,iBAAAsN,CAAkBpN,GACT,OAAAkC,KAAKsG,KAAKxI,EACrB,CACE,2BAAAqN,CAA4BrN,GACpB,MAAAwI,EAAOtG,KAAKkL,kBAAkBpN,GAEpC,SADUwI,GAAQjG,OAAOC,KAAKgG,IAAS,IAC5B8E,SAAU9E,EAAK+E,IAAMhL,OAAOC,KAAKgG,EAAK+E,IAAI3J,OAAS,GAClE,CACE,MAAA4J,GACE,OAAOtL,KAAKsG,IAChB,EAGA,IAAIiF,EAAgB,CAClBC,WAAY,CAAE,EACd,gBAAAC,CAAiBC,GACV1L,KAAAwL,WAAWE,EAAOC,MAAQD,CAChC,EACD,MAAAE,CAAOJ,EAAYnI,EAAOwB,EAAK9G,EAAS8N,GAI/B,OAHPL,EAAWtN,SAAqB4N,UACtBzI,GAAA,OAAAzG,EAAAoD,KAAKwL,WAAWM,aAAYC,QAAQ1I,EAAOwB,EAAK9G,EAAS8N,KAAexI,CAAA,IAE3EA,CACX,GAGA,MAAM2I,EAAmB,CAAE,EACrBC,EAA8B5H,IAAC7H,EAAS6H,IAAuB,kBAARA,GAAoC,iBAARA,EACzF,MAAM6H,UAAmBnD,EACvB,WAAAhJ,CAAYlD,EAAUkB,EAAU,IAparB,IAAIwI,EAAG3E,EAqaT+H,QAraMpD,EAsayG1J,EAtatG+E,EAsagH5B,KAA3H,CAAC,gBAAiB,gBAAiB,iBAAkB,eAAgB,mBAAoB,aAAc,SAra5G9B,SAAaO,IACT8H,EAAE9H,OAAMA,GAAK8H,EAAE9H,GAAC,IAqapBuB,KAAKjC,QAAUA,OACmB,IAA9BiC,KAAKjC,QAAQyJ,eACfxH,KAAKjC,QAAQyJ,aAAe,KAEzBxH,KAAAlD,OAASgM,EAAWH,OAAO,aACpC,CACE,cAAAwD,CAAerO,GACTA,SAAU0C,SAAW1C,EAC7B,CACE,MAAAsO,CAAOvH,EAAKwH,EAAI,CACdC,cAAe,CAAA,IAEf,MAAMC,EAAM,IACPF,GAED,GAAO,MAAPxH,EAAoB,OAAA,EACxB,MAAM2H,EAAWxM,KAAKmE,QAAQU,EAAK0H,GACnC,YAAyB,WAAlBC,WAAUnI,IACrB,CACE,cAAAoI,CAAe5H,EAAK0H,GAClB,IAAIG,OAAkC,IAApBH,EAAIG,YAA4BH,EAAIG,YAAc1M,KAAKjC,QAAQ2O,iBAC7D,IAAhBA,IAAyCA,EAAA,KAC7C,MAAMlF,OAAoC,IAArB+E,EAAI/E,aAA6B+E,EAAI/E,aAAexH,KAAKjC,QAAQyJ,aACtF,IAAIrH,EAAaoM,EAAI3O,IAAMoC,KAAKjC,QAAQiD,WAAa,GACrD,MAAM2L,EAAuBD,GAAe7H,EAAI5G,QAAQyO,IAAe,EACjEE,IAAwB5M,KAAKjC,QAAQ8O,yBAA4BN,EAAI/E,cAAiBxH,KAAKjC,QAAQ+O,wBAA2BP,EAAIG,aAjUhH,EAAC7H,EAAK6H,EAAalF,KAC7CkF,EAAcA,GAAe,GAC7BlF,EAAeA,GAAgB,GAC/B,MAAMuF,EAAgBvG,EAAMwG,QAAOC,GAAKP,EAAYzO,QAAQgP,GAAK,GAAKzF,EAAavJ,QAAQgP,GAAK,IAC5F,GAAyB,IAAzBF,EAAcrL,OAAqB,OAAA,EACvC,MAAMwL,EAAIzG,EAA+BK,UAAU,IAAIiG,EAAcI,KAAIF,GAAW,MAANA,EAAY,MAAQA,IAAG9J,KAAK,SAC1G,IAAIiK,GAAWF,EAAEG,KAAKxI,GACtB,IAAKuI,EAAS,CACN,MAAAE,EAAKzI,EAAI5G,QAAQuJ,GACnB8F,EAAK,IAAMJ,EAAEG,KAAKxI,EAAI0I,UAAU,EAAGD,MAC3BF,GAAA,EAEhB,CACS,OAAAA,CAAA,EAoTmJI,CAAoB3I,EAAK6H,EAAalF,IAC1L,GAAAmF,IAAyBC,EAAsB,CACjD,MAAMnO,EAAIoG,EAAI4I,MAAMzN,KAAK0N,aAAaC,eAClC,GAAAlP,GAAKA,EAAEiD,OAAS,EACX,MAAA,CACLmD,MACA1E,WAAY3D,EAAS2D,GAAc,CAACA,GAAcA,GAGhD,MAAAyN,EAAQ/I,EAAId,MAAM2I,IACpBA,IAAgBlF,GAAgBkF,IAAgBlF,GAAgBxH,KAAKjC,QAAQH,GAAGK,QAAQ2P,EAAM,KAAU,KAAAzN,EAAayN,EAAMvG,SACzHxC,EAAA+I,EAAMzK,KAAKqE,EACvB,CACW,MAAA,CACL3C,MACA1E,WAAY3D,EAAS2D,GAAc,CAACA,GAAcA,EAExD,CACE,SAAA0N,CAAUvN,EAAM+L,EAAGyB,GACb,IAAAvB,EAAmB,iBAANF,EAAiB,IAC7BA,GACDA,EAQA,GAPe,iBAARE,GAAoBvM,KAAKjC,QAAQgQ,mCACpCxB,EAAAvM,KAAKjC,QAAQgQ,iCAAiCC,YAE/B,iBAAZjQ,UAA4BwO,EAAA,IAClCA,IAEAA,IAAKA,EAAM,CAAE,GACN,MAARjM,EAAqB,MAAA,GACpBkB,MAAMC,QAAQnB,KAAcA,EAAA,CAAC2F,OAAO3F,KACzC,MAAM2N,OAAsC,IAAtB1B,EAAI0B,cAA8B1B,EAAI0B,cAAgBjO,KAAKjC,QAAQkQ,cACnFzG,OAAoC,IAArB+E,EAAI/E,aAA6B+E,EAAI/E,aAAexH,KAAKjC,QAAQyJ,cAChF3C,IACJA,EAAA1E,WACAA,GACEH,KAAKyM,eAAenM,EAAKA,EAAKoB,OAAS,GAAI6K,GACzC9L,EAAYN,EAAWA,EAAWuB,OAAS,GACjD,IAAIgL,OAAkC,IAApBH,EAAIG,YAA4BH,EAAIG,YAAc1M,KAAKjC,QAAQ2O,iBAC7D,IAAhBA,IAAyCA,EAAA,KACvC,MAAA5O,EAAMyO,EAAIzO,KAAOkC,KAAKQ,SACtB0N,EAA0B3B,EAAI2B,yBAA2BlO,KAAKjC,QAAQmQ,wBACxE,GAAuB,YAAvB,MAAApQ,OAAA,EAAAA,EAAKqQ,eACP,OAAID,EACED,EACK,CACL5J,IAAK,GAAG5D,IAAYiM,IAAc7H,IAClCuJ,QAASvJ,EACTwJ,aAAcxJ,EACdyJ,QAASxQ,EACTyQ,OAAQ9N,EACR+N,WAAYxO,KAAKyO,qBAAqBlC,IAGnC,GAAG9L,IAAYiM,IAAc7H,IAElCoJ,EACK,CACL5J,IAAKQ,EACLuJ,QAASvJ,EACTwJ,aAAcxJ,EACdyJ,QAASxQ,EACTyQ,OAAQ9N,EACR+N,WAAYxO,KAAKyO,qBAAqBlC,IAGnC1H,EAET,MAAM2H,EAAWxM,KAAKmE,QAAQ7D,EAAMiM,GACpC,IAAIlI,EAAgB,MAAVmI,OAAU,EAAAA,EAAAnI,IACd,MAAAqK,SAAalC,WAAU4B,UAAWvJ,EAClC8J,SAAkBnC,WAAU6B,eAAgBxJ,EAE5C+J,OAAgC,IAAnBrC,EAAIqC,WAA2BrC,EAAIqC,WAAa5O,KAAKjC,QAAQ6Q,WAC1EC,GAA8B7O,KAAK8O,YAAc9O,KAAK8O,WAAWC,eACjEC,OAAoC,IAAdzC,EAAI0C,QAAwBzS,EAAS+P,EAAI0C,OAC/DC,EAAkBhD,EAAWgD,gBAAgB3C,GAC7C4C,EAAqBH,EAAsBhP,KAAKoP,eAAeC,UAAUvR,EAAKyO,EAAI0C,MAAO1C,GAAO,GAChG+C,EAAoC/C,EAAIgD,SAAWP,EAAsBhP,KAAKoP,eAAeC,UAAUvR,EAAKyO,EAAI0C,MAAO,CAC3HM,SAAS,IACN,GACCC,EAAwBR,IAAwBzC,EAAIgD,SAAyB,IAAdhD,EAAI0C,MACnE1N,EAAeiO,GAAyBjD,EAAI,eAAevM,KAAKjC,QAAQ0R,wBAA0BlD,EAAI,eAAe4C,MAAyB5C,EAAI,eAAe+C,MAAwC/C,EAAIhL,aACnN,IAAImO,EAAgBrL,EAChBwK,IAA+BxK,GAAO6K,IACxBQ,EAAAnO,GAEZ,MAAAwN,EAAiB9C,EAAqByD,GACtCC,EAAUtP,OAAO+E,UAAUwK,SAASzH,MAAMuH,GAChD,KAAIb,GAA8Ba,GAAiBX,GAjBlC,CAAC,kBAAmB,oBAAqB,mBAiBoB9Q,QAAQ0R,GAAW,IAAOnT,EAASoS,IAAepN,MAAMC,QAAQiO,GA8ClJ,GAAeb,GAA8BrS,EAASoS,IAAepN,MAAMC,QAAQ4C,GACvEA,EAAAA,EAAIlB,KAAKyL,GACXvK,IAAWA,EAAArE,KAAK6P,kBAAkBxL,EAAK/D,EAAMiM,EAAKuB,QACjD,CACL,IAAIgC,GAAc,EACd1B,GAAU,GACTpO,KAAK+P,cAAc1L,IAAQ6K,IAChBY,GAAA,EACRzL,EAAA9C,GAEHvB,KAAK+P,cAAc1L,KACZ+J,GAAA,EACJ/J,EAAAQ,GAER,MACMmL,GADiCzD,EAAI0D,gCAAkCjQ,KAAKjC,QAAQkS,iCAClC7B,OAAU,EAAY/J,EACxE6L,EAAgBhB,GAAmB3N,IAAiB8C,GAAOrE,KAAKjC,QAAQmS,cAC1E,GAAA9B,GAAW0B,GAAeI,EAAe,CAE3C,GADKlQ,KAAAlD,OAAOkL,IAAIkI,EAAgB,YAAc,aAAcpS,EAAK2C,EAAWoE,EAAKqL,EAAgB3O,EAAe8C,GAC5GmD,EAAc,CACV,MAAA2I,EAAKnQ,KAAKmE,QAAQU,EAAK,IACxB0H,EACH/E,cAAc,IAEZ2I,GAAMA,EAAG9L,KAAUrE,KAAAlD,OAAOI,KAAK,kLAC7C,CACQ,IAAIkT,EAAO,GACL,MAAAC,EAAerQ,KAAKsQ,cAAcC,iBAAiBvQ,KAAKjC,QAAQyS,YAAajE,EAAIzO,KAAOkC,KAAKQ,UACnG,GAAmC,aAA/BR,KAAKjC,QAAQ0S,eAAgCJ,GAAgBA,EAAa,GAC5E,IAAA,IAAS3I,EAAI,EAAGA,EAAI2I,EAAa3O,OAAQgG,IAClC0I,EAAAhS,KAAKiS,EAAa3I,QAEe,QAA/B1H,KAAKjC,QAAQ0S,cACtBL,EAAOpQ,KAAKsQ,cAAcI,mBAAmBnE,EAAIzO,KAAOkC,KAAKQ,UAE7D4P,EAAKhS,KAAKmO,EAAIzO,KAAOkC,KAAKQ,UAE5B,MAAMmQ,EAAO,CAACC,EAAGvP,EAAGwP,WAClB,MAAMC,EAAoB5B,GAAmB2B,IAAyBxM,EAAMwM,EAAuBb,EAC/FhQ,KAAKjC,QAAQgT,kBACf/Q,KAAKjC,QAAQgT,kBAAkBH,EAAGnQ,EAAWY,EAAGyP,EAAmBZ,EAAe3D,IACzE,OAAA3P,EAAAoD,KAAKuC,uBAAL,EAAA3F,EAAuBoU,cAChChR,KAAKuC,iBAAiByO,YAAYJ,EAAGnQ,EAAWY,EAAGyP,EAAmBZ,EAAe3D,GAEvFvM,KAAKqJ,KAAK,aAAcuH,EAAGnQ,EAAWY,EAAGgD,EAAG,EAE1CrE,KAAKjC,QAAQiT,cACXhR,KAAKjC,QAAQkT,oBAAsBjC,EACrCoB,EAAKlS,SAAoBsC,IACvB,MAAM0Q,EAAWlR,KAAKoP,eAAe+B,YAAY3Q,EAAU+L,GACvDiD,GAAyBjD,EAAI,eAAevM,KAAKjC,QAAQ0R,wBAA0ByB,EAASjT,QAAQ,GAAG+B,KAAKjC,QAAQ0R,uBAAyB,GAC/IyB,EAAS9S,KAAK,GAAG4B,KAAKjC,QAAQ0R,uBAEhCyB,EAAShT,SAAkBkT,IACpBT,EAAA,CAACnQ,GAAWqE,EAAMuM,EAAQ7E,EAAI,eAAe6E,MAAa7P,EAAY,GAC5E,IAGEoP,EAAAP,EAAMvL,EAAKtD,GAG5B,CACM8C,EAAMrE,KAAK6P,kBAAkBxL,EAAK/D,EAAMiM,EAAKC,EAAUsB,GACnDM,GAAW/J,IAAQQ,GAAO7E,KAAKjC,QAAQsT,8BACzChN,EAAM,GAAG5D,IAAYiM,IAAc7H,MAEhCuJ,GAAW0B,IAAgB9P,KAAKjC,QAAQuT,yBAC3CjN,EAAMrE,KAAKjC,QAAQuT,uBAAuBtR,KAAKjC,QAAQsT,4BAA8B,GAAG5Q,IAAYiM,IAAc7H,IAAQA,EAAKiL,EAAczL,OAAM,EAAWkI,GAEtK,KAnHmK,CAC7J,IAAKA,EAAIgF,gBAAkBvR,KAAKjC,QAAQwT,cAAe,CAChDvR,KAAKjC,QAAQyT,uBACXxR,KAAAlD,OAAOI,KAAK,mEAEb,MAAAgQ,EAAIlN,KAAKjC,QAAQyT,sBAAwBxR,KAAKjC,QAAQyT,sBAAsB9C,EAAYgB,EAAe,IACxGnD,EACH3O,GAAIuC,IACD,QAAQ0E,MAAQ7E,KAAKQ,mDAC1B,OAAIyN,GACFzB,EAASnI,IAAM6I,EACNV,EAAAgC,WAAaxO,KAAKyO,qBAAqBlC,GACzCC,GAEFU,CACf,CACM,GAAI1F,EAAc,CACV,MAAAiK,EAAiBjQ,MAAMC,QAAQiO,GAC/BgC,EAAOD,EAAiB,GAAK,CAAE,EAC/BE,EAAcF,EAAiB9C,EAAkBD,EACvD,IAAA,MAAWjQ,KAAKiR,EACd,GAAIrP,OAAO+E,UAAUC,eAAerD,KAAK0N,EAAejR,GAAI,CAC1D,MAAMmT,EAAU,GAAGD,IAAcnK,IAAe/I,IAE9CiT,EAAKjT,GADHyQ,IAAoB7K,EACZrE,KAAK6N,UAAU+D,EAAS,IAC7BrF,EACHhL,aAAc0K,EAAqB1K,GAAgBA,EAAa9C,QAAK,EAEnEmQ,YAAY,EACZhR,GAAIuC,IAIEH,KAAK6N,UAAU+D,EAAS,IAC7BrF,EAEDqC,YAAY,EACZhR,GAAIuC,IAINuR,EAAKjT,KAAOmT,IAASF,EAAKjT,GAAKiR,EAAcjR,GAC7D,CAEciT,EAAAA,CACd,CACA,CAsEI,OAAIzD,GACFzB,EAASnI,IAAMA,EACNmI,EAAAgC,WAAaxO,KAAKyO,qBAAqBlC,GACzCC,GAEFnI,CACX,CACE,iBAAAwL,CAAkBxL,EAAKQ,EAAK0H,EAAKC,EAAUsB,WACrC,GAAA,OAAAlR,EAAAoD,KAAK8O,iBAAL,EAAAlS,EAAiBiO,MACbxG,EAAArE,KAAK8O,WAAWjE,MAAMxG,EAAK,IAC5BrE,KAAKjC,QAAQuO,cAAcuF,oBAC3BtF,GACFA,EAAIzO,KAAOkC,KAAKQ,UAAYgM,EAAS8B,QAAS9B,EAAS+B,OAAQ/B,EAAS4B,QAAS,CAClF5B,kBAER,IAAgBD,EAAIuF,kBAAmB,CAC7BvF,EAAID,eAAoBtM,KAAA0N,aAAalO,KAAK,IACzC+M,EAEDD,cAAe,IACVtM,KAAKjC,QAAQuO,iBACbC,EAAID,iBAIb,MAAMyF,EAAkBvV,EAAS6H,UAAiD,KAAxC,OAAA1H,mBAAK2P,oBAAL,EAAA3P,EAAoBoV,iBAAgCxF,EAAID,cAAcyF,gBAAkB/R,KAAKjC,QAAQuO,cAAcyF,iBACzJ,IAAAC,EACJ,GAAID,EAAiB,CACnB,MAAME,EAAK5N,EAAIoJ,MAAMzN,KAAK0N,aAAaC,eACvCqE,EAAUC,GAAMA,EAAGvQ,MAC3B,CACU,IAAA4E,EAAOiG,EAAInN,UAAY5C,EAAS+P,EAAInN,SAAWmN,EAAInN,QAAUmN,EAMjE,GALIvM,KAAKjC,QAAQuO,cAAcuF,mBAAyBvL,EAAA,IACnDtG,KAAKjC,QAAQuO,cAAcuF,oBAC3BvL,IAECjC,EAAArE,KAAK0N,aAAawE,YAAY7N,EAAKiC,EAAMiG,EAAIzO,KAAOkC,KAAKQ,UAAYgM,EAAS8B,QAAS/B,GACzFwF,EAAiB,CACnB,MAAMI,EAAK9N,EAAIoJ,MAAMzN,KAAK0N,aAAaC,eAEnCqE,GADYG,GAAMA,EAAGzQ,UACF6K,EAAI6F,MAAO,EAC1C,EACW7F,EAAIzO,KAAO0O,GAAYA,EAASnI,MAASkI,EAAAzO,IAAMkC,KAAKQ,UAAYgM,EAAS8B,UAC7D,IAAb/B,EAAI6F,OAAgB/N,EAAMrE,KAAK0N,aAAa0E,KAAK/N,GAAK,IAAI3H,WACxDoR,WAAU,MAAOpR,EAAK,IAAO6P,EAAI8F,QAI9BrS,KAAK6N,aAAanR,EAAMmI,IAHxB7E,KAAAlD,OAAOI,KAAK,6CAA6CR,EAAK,cAAcmI,EAAI,MAC9E,OAGR0H,IACCA,EAAID,eAAoBtM,KAAA0N,aAAa4E,OAC/C,CACI,MAAMC,EAAchG,EAAIgG,aAAevS,KAAKjC,QAAQwU,YAC9CC,EAAqBhW,EAAS+V,GAAe,CAACA,GAAeA,EAU5D,OATI,MAAPlO,IAAe,MAAAmO,OAAA,EAAAA,EAAoB9Q,UAAqC,IAA3B6K,EAAIkG,qBAC7CpO,EAAAkH,EAAcK,OAAO4G,EAAoBnO,EAAKQ,EAAK7E,KAAKjC,SAAWiC,KAAKjC,QAAQ2U,wBAA0B,CAC9GC,aAAc,IACTnG,EACHgC,WAAYxO,KAAKyO,qBAAqBlC,OAErCA,GACDA,EAAKvM,OAEJqE,CACX,CACE,OAAAF,CAAQ7D,EAAMiM,EAAM,IACd,IAAAqG,EACAxE,EACAC,EACAC,EACAC,EAgEG,OA/DH/R,EAAS8D,KAAOA,EAAO,CAACA,IAC5BA,EAAKpC,SAAamD,IACZ,GAAArB,KAAK+P,cAAc6C,GAAQ,OAC/B,MAAMC,EAAY7S,KAAKyM,eAAepL,EAAGkL,GACnC1H,EAAMgO,EAAUhO,IACZuJ,EAAAvJ,EACV,IAAI1E,EAAa0S,EAAU1S,WACvBH,KAAKjC,QAAQ+U,aAAY3S,EAAaA,EAAWkK,OAAOrK,KAAKjC,QAAQ+U,aACzE,MAAM9D,OAAoC,IAAdzC,EAAI0C,QAAwBzS,EAAS+P,EAAI0C,OAC/DO,EAAwBR,IAAwBzC,EAAIgD,SAAyB,IAAdhD,EAAI0C,MACnE8D,OAAuC,IAAhBxG,EAAI8F,UAA0B7V,EAAS+P,EAAI8F,UAAmC,iBAAhB9F,EAAI8F,UAAyC,KAAhB9F,EAAI8F,QACtHW,EAAQzG,EAAI6D,KAAO7D,EAAI6D,KAAOpQ,KAAKsQ,cAAcI,mBAAmBnE,EAAIzO,KAAOkC,KAAKQ,SAAU+L,EAAIiE,aACxGrQ,EAAWjC,SAAcN,YACnBoC,KAAK+P,cAAc6C,KACdrE,EAAA3Q,EACJoO,EAAiB,GAAGgH,EAAM,MAAMpV,QAAS,OAAAhB,EAAKoD,KAAAiT,gBAAO7Q,sBAAuB,OAAAzF,OAAKsW,YAAL,EAAAtW,EAAYyF,mBAAmBmM,MAC9GvC,EAAiB,GAAGgH,EAAM,MAAMpV,MAAQ,EACxCoC,KAAKlD,OAAOI,KAAK,QAAQkR,qBAA2B4E,EAAM7P,KAAK,2CAA2CoL,wBAA8B,6NAE1IyE,EAAM9U,SAAgB7B,UAChB,GAAA2D,KAAK+P,cAAc6C,GAAQ,OACrBtE,EAAAjS,EACJ,MAAA6W,EAAY,CAACrO,GACnB,GAAI,OAAAjI,EAAAoD,KAAK8O,iBAAL,EAAAlS,EAAiBuW,cACnBnT,KAAK8O,WAAWqE,cAAcD,EAAWrO,EAAKxI,EAAMuB,EAAI2O,OACnD,CACD,IAAA6G,EACApE,MAAoChP,KAAKoP,eAAeC,UAAUhT,EAAMkQ,EAAI0C,MAAO1C,IACvF,MAAM8G,EAAa,GAAGrT,KAAKjC,QAAQ0R,sBAC7B6D,EAAgB,GAAGtT,KAAKjC,QAAQ0R,yBAAyBzP,KAAKjC,QAAQ0R,kBAU5E,GATIT,IACQkE,EAAA9U,KAAKyG,EAAMuO,GACjB7G,EAAIgD,SAAmD,IAAxC6D,EAAanV,QAAQqV,IAC5BJ,EAAA9U,KAAKyG,EAAMuO,EAAahU,QAAQkU,EAAetT,KAAKjC,QAAQ0R,kBAEpED,GACQ0D,EAAA9U,KAAKyG,EAAMwO,IAGrBN,EAAsB,CAClB,MAAAQ,EAAa,GAAG1O,IAAM7E,KAAKjC,QAAQyV,mBAAmBjH,EAAI8F,UAChEa,EAAU9U,KAAKmV,GACXvE,IACQkE,EAAA9U,KAAKmV,EAAaH,GACxB7G,EAAIgD,SAAmD,IAAxC6D,EAAanV,QAAQqV,IAC5BJ,EAAA9U,KAAKmV,EAAaH,EAAahU,QAAQkU,EAAetT,KAAKjC,QAAQ0R,kBAE3ED,GACQ0D,EAAA9U,KAAKmV,EAAaF,GAG9C,CACA,CACc,IAAAI,EACG,KAAAA,EAAcP,EAAUQ,OACxB1T,KAAK+P,cAAc6C,KACPvE,EAAAoF,EACfb,EAAQ5S,KAAKiK,YAAY5N,EAAMuB,EAAI6V,EAAalH,GAE9D,IACS,GACF,IAEI,CACLlI,IAAKuO,EACLxE,UACAC,eACAC,UACAC,SAEN,CACE,aAAAwB,CAAc1L,GACZ,aAAe,IAARA,IAAwBrE,KAAKjC,QAAQ4V,YAAsB,OAARtP,IAAoBrE,KAAKjC,QAAQ6V,mBAA6B,KAARvP,EACpH,CACE,WAAA4F,CAAY5N,EAAMuB,EAAIiH,EAAK9G,EAAU,CAAA,SAC/B,OAAA,OAAAnB,EAAAoD,KAAK8O,iBAAL,EAAAlS,EAAiBqN,aAAoBjK,KAAK8O,WAAW7E,YAAY5N,EAAMuB,EAAIiH,EAAK9G,GAC7EiC,KAAK6T,cAAc5J,YAAY5N,EAAMuB,EAAIiH,EAAK9G,EACzD,CACE,oBAAA0Q,CAAqB1Q,EAAU,IAC7B,MAAM+V,EAAc,CAAC,eAAgB,UAAW,UAAW,UAAW,MAAO,OAAQ,cAAe,KAAM,eAAgB,cAAe,gBAAiB,gBAAiB,aAAc,cAAe,iBAClMC,EAA2BhW,EAAQqB,UAAY5C,EAASuB,EAAQqB,SAClE,IAAAkH,EAAOyN,EAA2BhW,EAAQqB,QAAUrB,EAUxD,GATIgW,QAAqD,IAAlBhW,EAAQkR,QAC7C3I,EAAK2I,MAAQlR,EAAQkR,OAEnBjP,KAAKjC,QAAQuO,cAAcuF,mBACtBvL,EAAA,IACFtG,KAAKjC,QAAQuO,cAAcuF,oBAC3BvL,KAGFyN,EAA0B,CACtBzN,EAAA,IACFA,GAEL,IAAA,MAAWzB,KAAOiP,SACTxN,EAAKzB,EAEpB,CACW,OAAAyB,CACX,CACE,sBAAO4I,CAAgBnR,GACrB,MAAMuK,EAAS,eACf,IAAA,MAAW0L,KAAUjW,EACnB,GAAIsC,OAAO+E,UAAUC,eAAerD,KAAKjE,EAASiW,IAAW1L,IAAW0L,EAAOzG,UAAU,EAAGjF,UAAkB,IAAcvK,EAAQiW,GAC3H,OAAA,EAGJ,OAAA,CACX,EAGA,MAAMC,EACJ,WAAAlU,CAAYhC,GACViC,KAAKjC,QAAUA,EACViC,KAAAkU,cAAgBlU,KAAKjC,QAAQmW,gBAAiB,EAC9ClU,KAAAlD,OAASgM,EAAWH,OAAO,gBACpC,CACE,qBAAAwL,CAAsB9X,GAEpB,KADAA,EAAOyL,EAAezL,KACTA,EAAK4B,QAAQ,KAAO,EAAU,OAAA,KACrC,MAAAuH,EAAInJ,EAAK0H,MAAM,KACjB,OAAa,IAAbyB,EAAE9D,OAAqB,MAC3B8D,EAAEkO,MACoC,MAAlClO,EAAEA,EAAE9D,OAAS,GAAGyM,cAA8B,KAC3CnO,KAAKoU,mBAAmB5O,EAAErC,KAAK,MAC1C,CACE,uBAAAkR,CAAwBhY,GAEtB,KADAA,EAAOyL,EAAezL,KACTA,EAAK4B,QAAQ,KAAO,EAAU,OAAA5B,EACrC,MAAAmJ,EAAInJ,EAAK0H,MAAM,KACrB,OAAO/D,KAAKoU,mBAAmB5O,EAAE,GACrC,CACE,kBAAA4O,CAAmB/X,GACjB,GAAIG,EAASH,IAASA,EAAK4B,QAAQ,MAAW,EAAA,CACxC,IAAAqW,EACA,IACFA,EAAgBC,KAAKC,oBAAoBnY,GAAM,EAChD,OAAQ2H,GAAG,CAIZ,OAHIsQ,GAAiBtU,KAAKjC,QAAQ0W,eAChCH,EAAgBA,EAAcnG,eAE5BmG,IACAtU,KAAKjC,QAAQ0W,aACRpY,EAAK8R,cAEP9R,EACb,CACW,OAAA2D,KAAKjC,QAAQ2W,WAAa1U,KAAKjC,QAAQ0W,aAAepY,EAAK8R,cAAgB9R,CACtF,CACE,eAAAsY,CAAgBtY,GAIP,OAHmB,iBAAtB2D,KAAKjC,QAAQ6W,MAA2B5U,KAAKjC,QAAQ8W,4BAChDxY,EAAA2D,KAAKqU,wBAAwBhY,KAE9B2D,KAAKkU,gBAAkBlU,KAAKkU,cAAcxS,QAAU1B,KAAKkU,cAAcjW,QAAQ5B,IAAQ,CACnG,CACE,qBAAAyY,CAAsB9B,GAChB,IAACA,EAAc,OAAA,KACf,IAAAJ,EAsBG,OArBPI,EAAM9U,SAAgB7B,IACpB,GAAIuW,EAAO,OACL,MAAAmC,EAAa/U,KAAKoU,mBAAmB/X,GACtC2D,KAAKjC,QAAQmW,gBAAiBlU,KAAK2U,gBAAgBI,KAAqBnC,EAAAmC,EAAA,KAE1EnC,GAAS5S,KAAKjC,QAAQmW,eACzBlB,EAAM9U,SAAgB7B,IACpB,GAAIuW,EAAO,OACL,MAAAoC,EAAYhV,KAAKmU,sBAAsB9X,GAC7C,GAAI2D,KAAK2U,gBAAgBK,UAAmBpC,EAAQoC,EAC9C,MAAAC,EAAUjV,KAAKqU,wBAAwBhY,GAC7C,GAAI2D,KAAK2U,gBAAgBM,UAAiBrC,EAAQqC,EAClDrC,EAAQ5S,KAAKjC,QAAQmW,cAAc9I,MAAqB8J,GAClDA,IAAiBD,EAAgBC,EACjCA,EAAajX,QAAQ,KAAO,GAAKgX,EAAQhX,QAAQ,KAAO,OAAxD,EACAiX,EAAajX,QAAQ,KAAO,GAAKgX,EAAQhX,QAAQ,KAAO,GAAKiX,EAAa3H,UAAU,EAAG2H,EAAajX,QAAQ,QAAUgX,GACpF,IAAlCC,EAAajX,QAAQgX,IAAkBA,EAAQvT,OAAS,EAD8EwT,OACtI,GACL,IAGAtC,IAAeA,EAAA5S,KAAKuQ,iBAAiBvQ,KAAKjC,QAAQyS,aAAa,IAC7DoC,CACX,CACE,gBAAArC,CAAiB4E,EAAW9Y,GACtB,IAAC8Y,EAAW,MAAO,GAGvB,GAFyB,mBAAdA,IAA0BA,EAAYA,EAAU9Y,IACvDG,EAAS2Y,KAAYA,EAAY,CAACA,IAClC3T,MAAMC,QAAQ0T,GAAmB,OAAAA,EACrC,IAAK9Y,EAAa,OAAA8Y,EAAUC,SAAW,GACnC,IAAAxC,EAAQuC,EAAU9Y,GAKtB,OAJKuW,IAAOA,EAAQuC,EAAUnV,KAAKmU,sBAAsB9X,KACpDuW,IAAOA,EAAQuC,EAAUnV,KAAKoU,mBAAmB/X,KACjDuW,IAAOA,EAAQuC,EAAUnV,KAAKqU,wBAAwBhY,KACtDuW,IAAOA,EAAQuC,EAAUC,SACvBxC,GAAS,EACpB,CACE,kBAAAlC,CAAmBrU,EAAMgZ,GACjB,MAAAC,EAAgBtV,KAAKuQ,iBAAiB8E,GAAgBrV,KAAKjC,QAAQyS,aAAe,GAAInU,GACtF2W,EAAQ,GACRuC,EAAetI,IACdA,IACDjN,KAAK2U,gBAAgB1H,GACvB+F,EAAM5U,KAAK6O,GAEXjN,KAAKlD,OAAOI,KAAK,uDAAuD+P,KAChF,EAYW,OAVHzQ,EAASH,KAAUA,EAAK4B,QAAQ,MAAO,GAAM5B,EAAK4B,QAAQ,MAAY,IAC9C,iBAAtB+B,KAAKjC,QAAQ6W,QAAiC5U,KAAKoU,mBAAmB/X,IAChD,iBAAtB2D,KAAKjC,QAAQ6W,MAAiD,gBAAtB5U,KAAKjC,QAAQ6W,MAAgCW,EAAAvV,KAAKmU,sBAAsB9X,IAC1F,gBAAtB2D,KAAKjC,QAAQ6W,QAAgC5U,KAAKqU,wBAAwBhY,KACrEG,EAASH,IACVkZ,EAAAvV,KAAKoU,mBAAmB/X,IAElCiZ,EAAcpX,SAAcsX,IACtBxC,EAAM/U,QAAQuX,GAAM,GAAWD,EAAAvV,KAAKoU,mBAAmBoB,GAAG,IAEzDxC,CACX,EAGA,MAAMyC,EAAgB,CACpBC,KAAM,EACNC,IAAK,EACLC,IAAK,EACLC,IAAK,EACLC,KAAM,EACNC,MAAO,GAEHC,EAAY,CAChBC,OAAQhH,GAAmB,IAAVA,EAAc,MAAQ,QACvCiH,gBAAiB,KAAO,CACtBC,iBAAkB,CAAC,MAAO,YAG9B,MAAMC,EACJ,WAAArW,CAAYuQ,EAAevS,EAAU,IACnCiC,KAAKsQ,cAAgBA,EACrBtQ,KAAKjC,QAAUA,EACViC,KAAAlD,OAASgM,EAAWH,OAAO,kBAChC3I,KAAKqW,iBAAmB,CAAE,CAC9B,CACE,OAAAC,CAAQxY,EAAKO,GACN2B,KAAAuW,MAAMzY,GAAOO,CACtB,CACE,UAAAmY,GACExW,KAAKqW,iBAAmB,CAAE,CAC9B,CACE,OAAAI,CAAQpa,EAAM0B,EAAU,IACtB,MAAM2Y,EAAc5O,EAAwB,QAATzL,EAAiB,KAAOA,GACrDkD,EAAOxB,EAAQwR,QAAU,UAAY,WACrCoH,EAAW/L,KAAKE,UAAU,CAC9B4L,cACAnX,SAEE,GAAAoX,KAAY3W,KAAKqW,iBACZ,OAAArW,KAAKqW,iBAAiBM,GAE3B,IAAAC,EACA,IACKA,EAAA,IAAIrC,KAAKsC,YAAYH,EAAa,CACvCnX,QAEH,OAAQuX,GACP,IAAKvC,KAEI,OADFvU,KAAAlD,OAAOoL,MAAM,iDACX8N,EAET,IAAK3Z,EAAKoR,MAAM,OAAe,OAAAuI,EAC/B,MAAMe,EAAU/W,KAAKsQ,cAAc+D,wBAAwBhY,GACpDua,EAAA5W,KAAKyW,QAAQM,EAAShZ,EACnC,CAEW,OADFiC,KAAAqW,iBAAiBM,GAAYC,EAC3BA,CACX,CACE,WAAAI,CAAY3a,EAAM0B,EAAU,IAC1B,IAAI6Y,EAAO5W,KAAKyW,QAAQpa,EAAM0B,GAEvB,OADF6Y,IAAMA,EAAO5W,KAAKyW,QAAQ,MAAO1Y,KAC/B,MAAA6Y,OAAA,EAAAA,EAAMV,kBAAkBC,iBAAiBzU,QAAS,CAC7D,CACE,mBAAAuV,CAAoB5a,EAAMwI,EAAK9G,EAAU,CAAA,GAChC,OAAAiC,KAAKmR,YAAY9U,EAAM0B,GAASoP,KAAIiE,GAAU,GAAGvM,IAAMuM,KAClE,CACE,WAAAD,CAAY9U,EAAM0B,EAAU,IAC1B,IAAI6Y,EAAO5W,KAAKyW,QAAQpa,EAAM0B,GAE1B,OADC6Y,IAAMA,EAAO5W,KAAKyW,QAAQ,MAAO1Y,IACjC6Y,EACEA,EAAKV,kBAAkBC,iBAAiBe,MAAK,CAACC,EAAiBC,IAAoB3B,EAAc0B,GAAmB1B,EAAc2B,KAAkBjK,KAAsBkK,GAAA,GAAGrX,KAAKjC,QAAQuZ,UAAUvZ,EAAQwR,QAAU,UAAUvP,KAAKjC,QAAQuZ,UAAY,KAAKD,MADnP,EAEtB,CACE,SAAAhI,CAAUhT,EAAM4S,EAAOlR,EAAU,CAAA,GAC/B,MAAM6Y,EAAO5W,KAAKyW,QAAQpa,EAAM0B,GAChC,OAAI6Y,EACK,GAAG5W,KAAKjC,QAAQuZ,UAAUvZ,EAAQwR,QAAU,UAAUvP,KAAKjC,QAAQuZ,UAAY,KAAKV,EAAKX,OAAOhH,MAEzGjP,KAAKlD,OAAOI,KAAK,6BAA6Bb,KACvC2D,KAAKqP,UAAU,MAAOJ,EAAOlR,GACxC,EAGA,MAAMwZ,EAAuB,CAACjR,EAAMkR,EAAa3S,EAAK2C,EAAe,IAAKoC,GAAsB,KAC9F,IAAI5E,EA57BsB,EAACsB,EAAMkR,EAAa3S,KACxC,MAAAxB,EAAQsC,EAAQW,EAAMzB,GAC5B,YAAc,IAAVxB,EACKA,EAEFsC,EAAQ6R,EAAa3S,EAAG,EAu7BpB4S,CAAoBnR,EAAMkR,EAAa3S,GAK3CG,OAJFA,GAAQ4E,GAAuBpN,EAASqI,KAC3CG,EAAOuC,EAASjB,EAAMzB,EAAK2C,QACd,IAATxC,IAAoBA,EAAOuC,EAASiQ,EAAa3S,EAAK2C,KAErDxC,CAAAA,EAEH0S,EAAYC,GAAOA,EAAIvY,QAAQ,MAAO,QAC5C,MAAMwY,EACJ,WAAA7X,CAAYhC,EAAU,UACfiC,KAAAlD,OAASgM,EAAWH,OAAO,gBAChC3I,KAAKjC,QAAUA,EACViC,KAAA6X,QAAS,OAAAjb,EAAA,MAAAmB,OAAA,EAAAA,EAASuO,oBAAT,EAAA1P,EAAwBib,UAAoBxU,GAAAA,GAC1DrD,KAAKR,KAAKzB,EACd,CACE,IAAAyB,CAAKzB,EAAU,IACRA,EAAQuO,gBAAevO,EAAQuO,cAAgB,CAClDwL,aAAa,IAET,MACJzR,OAAQ0R,EAAAD,YACRA,EAAAE,oBACAA,EAAA1P,OACAA,EAAA2P,cACAA,EAAA7G,OACAA,EAAA8G,cACAA,EAAAC,gBACAA,EAAAC,eACAA,EAAAC,eACAA,EAAAC,cACAA,EAAAC,qBACAA,EAAAC,cACAA,EAAAC,qBACAA,EAAAC,wBACAA,EAAAC,YACAA,EAAAC,aACAA,GACE7a,EAAQuO,cACPtM,KAAAqG,YAAsB,IAAb0R,EAAyBA,EAAW1R,EAC7CrG,KAAA8X,iBAA8B,IAAhBA,GAA4BA,EAC1C9X,KAAAgY,yBAA8C,IAAxBA,GAAoCA,EAC/DhY,KAAKsI,OAASA,EAASpC,EAAYoC,GAAU2P,GAAiB,KAC9DjY,KAAKoR,OAASA,EAASlL,EAAYkL,GAAU8G,GAAiB,KAC9DlY,KAAKmY,gBAAkBA,GAAmB,IACrCnY,KAAAqY,eAAiBD,EAAiB,GAAKC,GAAkB,IAC9DrY,KAAKoY,eAAiBpY,KAAKqY,eAAiB,GAAKD,GAAkB,GACnEpY,KAAKsY,cAAgBA,EAAgBpS,EAAYoS,GAAiBC,GAAwBrS,EAAY,OACtGlG,KAAKwY,cAAgBA,EAAgBtS,EAAYsS,GAAiBC,GAAwBvS,EAAY,KACtGlG,KAAK0Y,wBAA0BA,GAA2B,IAC1D1Y,KAAK2Y,YAAcA,GAAe,IAC7B3Y,KAAA4Y,kBAAgC,IAAjBA,GAA6BA,EACjD5Y,KAAK6Y,aACT,CACE,KAAAvG,GACMtS,KAAKjC,SAAciC,KAAAR,KAAKQ,KAAKjC,QACrC,CACE,WAAA8a,GACQ,MAAAC,EAAmB,CAACC,EAAgBhS,KACpC,MAAAgS,OAAA,EAAAA,EAAgBjT,UAAWiB,GAC7BgS,EAAeC,UAAY,EACpBD,GAEF,IAAI5R,OAAOJ,EAAS,KAExB/G,KAAAiZ,OAASH,EAAiB9Y,KAAKiZ,OAAQ,GAAGjZ,KAAKsI,cAActI,KAAKoR,UACvEpR,KAAKkZ,eAAiBJ,EAAiB9Y,KAAKkZ,eAAgB,GAAGlZ,KAAKsI,SAAStI,KAAKqY,sBAAsBrY,KAAKoY,iBAAiBpY,KAAKoR,UAC9HpR,KAAA2N,cAAgBmL,EAAiB9Y,KAAK2N,cAAe,GAAG3N,KAAKsY,qBAAqBtY,KAAKwY,gBAChG,CACE,WAAAtG,CAAY/L,EAAKG,EAAMxI,EAAKC,SACtB,IAAA0P,EACApK,EACA8V,EACE,MAAA3B,EAAcxX,KAAKjC,SAAWiC,KAAKjC,QAAQuO,eAAiBtM,KAAKjC,QAAQuO,cAAcuF,kBAAoB,CAAE,EAC7GuH,EAAsBvU,IAC1B,GAAIA,EAAI5G,QAAQ+B,KAAKmY,iBAAmB,EAAG,CACnCnT,MAAAA,EAAOuS,EAAqBjR,EAAMkR,EAAa3S,EAAK7E,KAAKjC,QAAQyJ,aAAcxH,KAAKjC,QAAQ6L,qBAClG,OAAO5J,KAAK4Y,aAAe5Y,KAAK6X,OAAO7S,OAAM,EAAWlH,EAAK,IACxDC,KACAuI,EACH+S,iBAAkBxU,IACfG,CACb,CACM,MAAMQ,EAAIX,EAAId,MAAM/D,KAAKmY,iBACnB9W,EAAImE,EAAE6B,QAAQiS,OACdC,EAAI/T,EAAErC,KAAKnD,KAAKmY,iBAAiBmB,OACvC,OAAOtZ,KAAK6X,OAAON,EAAqBjR,EAAMkR,EAAanW,EAAGrB,KAAKjC,QAAQyJ,aAAcxH,KAAKjC,QAAQ6L,qBAAsB2P,EAAGzb,EAAK,IAC/HC,KACAuI,EACH+S,iBAAkBhY,GACnB,EAEHrB,KAAK6Y,cACL,MAAMW,GAA8B,MAAAzb,OAAA,EAAAA,EAASyb,8BAA+BxZ,KAAKjC,QAAQyb,4BACnFzH,OAA8D,KAA5C,OAAAnV,EAAA,MAAAmB,OAAA,EAAAA,EAASuO,oBAAT,EAAA1P,EAAwBmV,iBAAgChU,EAAQuO,cAAcyF,gBAAkB/R,KAAKjC,QAAQuO,cAAcyF,gBA2C5I,MA1CO,CAAC,CACb0H,MAAOzZ,KAAKkZ,eACZQ,UAAkB/B,GAAAD,EAAUC,IAC3B,CACD8B,MAAOzZ,KAAKiZ,OACZS,UAAkB/B,GAAA3X,KAAK8X,YAAcJ,EAAU1X,KAAKqG,OAAOsR,IAAQD,EAAUC,KAEzEzZ,SAAgByb,IAEpB,IADWR,EAAA,EACJ1L,EAAQkM,EAAKF,MAAMG,KAAKzT,IAAM,CACnC,MAAM0T,EAAapM,EAAM,GAAG6L,OAE5B,GADAjW,EAAQ+V,EAAaS,QACP,IAAVxW,EACE,GAAuC,mBAAhCmW,EAA4C,CACrD,MAAMM,EAAON,EAA4BrT,EAAKsH,EAAO1P,GAC7CsF,EAAA7G,EAASsd,GAAQA,EAAO,EAC5C,MAAA,GAAqB/b,GAAWsC,OAAO+E,UAAUC,eAAerD,KAAKjE,EAAS8b,GAC1DxW,EAAA,WACC0O,EAAiB,CAC1B1O,EAAQoK,EAAM,GACd,QACZ,CACYzN,KAAKlD,OAAOI,KAAK,8BAA8B2c,uBAAgC1T,KACvE9C,EAAA,EACpB,MACoB7G,EAAS6G,IAAWrD,KAAKgY,sBACnC3U,EAAQoB,EAAWpB,IAEf,MAAAqW,EAAYC,EAAKD,UAAUrW,GAS7B,GARJ8C,EAAMA,EAAI/G,QAAQqO,EAAM,GAAIiM,GACxB3H,GACG4H,EAAAF,MAAMT,WAAa3V,EAAM3B,OAC9BiY,EAAKF,MAAMT,WAAavL,EAAM,GAAG/L,QAEjCiY,EAAKF,MAAMT,UAAY,EAEzBG,IACIA,GAAYnZ,KAAK2Y,YACnB,KAEV,KAEWxS,CACX,CACE,IAAAiM,CAAKjM,EAAKqP,EAAIzX,EAAU,CAAA,GAClB,IAAA0P,EACApK,EACA0W,EACE,MAAAC,EAAmB,CAACnV,EAAKoV,KAC7B,MAAMC,EAAMla,KAAK0Y,wBACjB,GAAI7T,EAAI5G,QAAQic,GAAO,EAAU,OAAArV,EAC3B,MAAAoI,EAAIpI,EAAId,MAAM,IAAIoD,OAAO,GAAG+S,WAClC,IAAIC,EAAgB,IAAIlN,EAAE,KAC1BpI,EAAMoI,EAAE,GACQkN,EAAAna,KAAKkS,YAAYiI,EAAeJ,GAC1C,MAAAK,EAAsBD,EAAc1M,MAAM,MAC1C4M,EAAsBF,EAAc1M,MAAM,SAC3C,MAAA2M,OAAA,EAAAA,EAAqB1Y,SAAU,GAAK,GAAM,IAAM2Y,GAAuBA,EAAoB3Y,OAAS,GAAM,KAC7FyY,EAAAA,EAAc/a,QAAQ,KAAM,MAE1C,IACc2a,EAAAnP,KAAKC,MAAMsP,GACvBF,IAAkCF,EAAA,IACjCE,KACAF,GAEN,OAAQ/V,GAEP,OADAhE,KAAKlD,OAAOI,KAAK,oDAAoD2H,IAAOb,GACrE,GAAGa,IAAMqV,IAAMC,GAC9B,CAEa,OADHJ,EAAcxY,cAAgBwY,EAAcxY,aAAatD,QAAQ+B,KAAKsI,SAAc,UAAOyR,EAAcxY,aACtGsD,CAAA,EAET,KAAO4I,EAAQzN,KAAK2N,cAAciM,KAAKzT,IAAM,CAC3C,IAAImU,EAAa,GACDP,EAAA,IACXhc,GAEWgc,EAAAA,EAAc3a,UAAY5C,EAASud,EAAc3a,SAAW2a,EAAc3a,QAAU2a,EACpGA,EAActH,oBAAqB,SAC5BsH,EAAcxY,aACrB,IAAIgZ,GAAW,EACf,IAAqD,IAAjD9M,EAAM,GAAGxP,QAAQ+B,KAAKmY,mBAA4B,OAAO9K,KAAKI,EAAM,IAAK,CAC3E,MAAMP,EAAIO,EAAM,GAAG1J,MAAM/D,KAAKmY,iBAAiBhL,KAAIqN,GAAQA,EAAKlB,SAC1D7L,EAAA,GAAKP,EAAE7F,QACAiT,EAAApN,EACFqN,GAAA,CACnB,CAEU,GADIlX,EAAAmS,EAAGwE,EAAiBhY,KAAKhC,KAAMyN,EAAM,GAAG6L,OAAQS,GAAgBA,GACpE1W,GAASoK,EAAM,KAAOtH,IAAQ3J,EAAS6G,GAAe,OAAAA,EACrD7G,EAAS6G,KAAQA,EAAQoB,EAAWpB,IACpCA,IACErD,KAAAlD,OAAOI,KAAK,qBAAqBuQ,EAAM,kBAAkBtH,KACtD9C,EAAA,IAENkX,IACMlX,EAAAiX,EAAWG,QAAO,CAACpP,EAAGkO,IAAMvZ,KAAK6X,OAAOxM,EAAGkO,EAAGxb,EAAQD,IAAK,IAC9DC,EACHsb,iBAAkB5L,EAAM,GAAG6L,UACzBjW,EAAMiW,SAEZnT,EAAMA,EAAI/G,QAAQqO,EAAM,GAAIpK,GAC5BrD,KAAKiZ,OAAOD,UAAY,CAC9B,CACW,OAAA7S,CACX,EAGA,MA+BMuU,EAA8BC,IAClC,MAAMC,EAAQ,CAAE,EACT,MAAA,CAACvP,EAAGuF,EAAGvE,KACZ,IAAIwO,EAAcxO,EACdA,GAAKA,EAAEgN,kBAAoBhN,EAAEyO,cAAgBzO,EAAEyO,aAAazO,EAAEgN,mBAAqBhN,EAAEA,EAAEgN,oBAC3EwB,EAAA,IACTA,EACH,CAACxO,EAAEgN,uBAAmB,IAG1B,MAAMxU,EAAM+L,EAAIhG,KAAKE,UAAU+P,GAC3B,IAAAE,EAAMH,EAAM/V,GAKhB,OAJKkW,IACHA,EAAMJ,EAAG7S,EAAe8I,GAAIvE,GAC5BuO,EAAM/V,GAAOkW,GAERA,EAAI1P,EAAC,CACb,EAEG2P,EAA2BL,GAAM,CAACtP,EAAGuF,EAAGvE,IAAMsO,EAAG7S,EAAe8I,GAAIvE,EAAtBsO,CAAyBtP,GAC7E,MAAM4P,EACJ,WAAAlb,CAAYhC,EAAU,IACfiC,KAAAlD,OAASgM,EAAWH,OAAO,aAChC3I,KAAKjC,QAAUA,EACfiC,KAAKR,KAAKzB,EACd,CACE,IAAAyB,CAAK3C,EAAUkB,EAAU,CACvBuO,cAAe,CAAA,IAEVtM,KAAAmY,gBAAkBpa,EAAQuO,cAAc6L,iBAAmB,IAC1D,MAAA+C,EAAKnd,EAAQod,oBAAsBT,EAAwBM,EACjEhb,KAAKob,QAAU,CACbC,OAAQH,GAAG,CAACpd,EAAKyO,KACf,MAAM+O,EAAY,IAAI/G,KAAKgH,aAAazd,EAAK,IACxCyO,IAEE,OAAAoL,GAAO2D,EAAUzD,OAAOF,EAAG,IAEpC6D,SAAUN,GAAG,CAACpd,EAAKyO,KACjB,MAAM+O,EAAY,IAAI/G,KAAKgH,aAAazd,EAAK,IACxCyO,EACHkP,MAAO,aAEF,OAAA9D,GAAO2D,EAAUzD,OAAOF,EAAG,IAEpC+D,SAAUR,GAAG,CAACpd,EAAKyO,KACjB,MAAM+O,EAAY,IAAI/G,KAAKoH,eAAe7d,EAAK,IAC1CyO,IAEE,OAAAoL,GAAO2D,EAAUzD,OAAOF,EAAG,IAEpCiE,aAAcV,GAAG,CAACpd,EAAKyO,KACrB,MAAM+O,EAAY,IAAI/G,KAAKsH,mBAAmB/d,EAAK,IAC9CyO,IAEL,UAAc+O,EAAUzD,OAAOF,EAAKpL,EAAIuP,OAAS,MAAK,IAExDC,KAAMb,GAAG,CAACpd,EAAKyO,KACb,MAAM+O,EAAY,IAAI/G,KAAKyH,WAAWle,EAAK,IACtCyO,IAEE,OAAAoL,GAAO2D,EAAUzD,OAAOF,EAAG,IAG1C,CACE,GAAAsE,CAAItQ,EAAM6J,GACRxV,KAAKob,QAAQzP,EAAKwC,cAAcmL,QAAU9D,CAC9C,CACE,SAAA0G,CAAUvQ,EAAM6J,GACTxV,KAAAob,QAAQzP,EAAKwC,cAAcmL,QAAUoB,EAAsBlF,EACpE,CACE,MAAAqC,CAAOxU,EAAOwU,EAAQ/Z,EAAKC,EAAU,CAAA,GACnC,MAAMqd,EAAUvD,EAAO9T,MAAM/D,KAAKmY,iBAC9B,GAAAiD,EAAQ1Z,OAAS,GAAK0Z,EAAQ,GAAGnd,QAAQ,KAAO,GAAKmd,EAAQ,GAAGnd,QAAQ,KAAO,GAAKmd,EAAQhQ,MAAUmO,GAAAA,EAAEtb,QAAQ,MAAO,IAAK,CACxH,MAAA+a,EAAYoC,EAAQe,WAAU5C,GAAKA,EAAEtb,QAAQ,MAAS,IAC5Dmd,EAAQ,GAAK,CAACA,EAAQ,MAAOA,EAAQpR,OAAO,EAAGgP,IAAY7V,KAAKnD,KAAKmY,gBAC3E,CAyBW,OAxBQiD,EAAQX,QAAO,CAAC2B,EAAK7C,WAC5B,MAAA8C,WACJA,EAAAC,cACAA,GA/Ge,CAAaC,IAClC,IAAIF,EAAaE,EAAUpO,cAAcmL,OACzC,MAAMgD,EAAgB,CAAE,EACxB,GAAIC,EAAUte,QAAQ,MAAW,EAAA,CACzB,MAAAuH,EAAI+W,EAAUxY,MAAM,KAC1BsY,EAAa7W,EAAE,GAAG2I,cAAcmL,OAC1B,MAAAkD,EAAShX,EAAE,GAAG+H,UAAU,EAAG/H,EAAE,GAAG9D,OAAS,GAC5B,aAAf2a,GAA6BG,EAAOve,QAAQ,KAAO,EAChDqe,EAAcd,WAAwBc,EAAAd,SAAWgB,EAAOlD,QACrC,iBAAf+C,GAAiCG,EAAOve,QAAQ,KAAO,EAC3Dqe,EAAcR,QAAqBQ,EAAAR,MAAQU,EAAOlD,QAE1CkD,EAAOzY,MAAM,KACrB7F,SAAeqO,IAClB,GAAIA,EAAK,CACP,MAAO1H,KAAQtI,GAAQgQ,EAAIxI,MAAM,KAC3B4T,EAAMpb,EAAK4G,KAAK,KAAKmW,OAAOla,QAAQ,WAAY,IAChDqd,EAAa5X,EAAIyU,OAClBgD,EAAcG,KAAaH,EAAcG,GAAc9E,GAChD,UAARA,IAA+B2E,EAAAG,IAAc,GACrC,SAAR9E,IAA8B2E,EAAAG,IAAc,GAC3CC,MAAM/E,OAAoB8E,GAAcE,SAAShF,EAAK,IACrE,IAGA,CACS,MAAA,CACL0E,aACAC,gBACD,EAmFOM,CAAerD,GACf,GAAAvZ,KAAKob,QAAQiB,GAAa,CAC5B,IAAIQ,EAAYT,EACZ,IACI,MAAAU,GAAa,OAAAlgB,EAAA,MAAAmB,OAAA,EAAAA,EAAS+c,mBAAT,EAAAle,EAAwBmB,EAAQsb,oBAAqB,CAAE,EACpEzI,EAAIkM,EAAWC,QAAUD,EAAWhf,KAAOC,EAAQgf,QAAUhf,EAAQD,KAAOA,EAClF+e,EAAY7c,KAAKob,QAAQiB,GAAYD,EAAKxL,EAAG,IACxC0L,KACAve,KACA+e,GAEN,OAAQ5U,GACFlI,KAAAlD,OAAOI,KAAKgL,EAC3B,CACe,OAAA2U,CACf,CAGa,OAFL7c,KAAKlD,OAAOI,KAAK,oCAAoCmf,KAEhDD,CAAA,GACN/Y,EAEP,EASA,MAAM2Z,UAAkBjU,EACtB,WAAAhJ,CAAYyC,EAASsB,EAAOjH,EAAUkB,EAAU,CAAA,WACvC4L,QACP3J,KAAKwC,QAAUA,EACfxC,KAAK8D,MAAQA,EACb9D,KAAKnD,SAAWA,EAChBmD,KAAKsQ,cAAgBzT,EAASyT,cAC9BtQ,KAAKjC,QAAUA,EACViC,KAAAlD,OAASgM,EAAWH,OAAO,oBAChC3I,KAAKid,aAAe,GACfjd,KAAAkd,iBAAmBnf,EAAQmf,kBAAoB,GACpDld,KAAKmd,aAAe,EACpBnd,KAAKod,WAAarf,EAAQqf,YAAc,EAAIrf,EAAQqf,WAAa,EACjEpd,KAAKqd,aAAetf,EAAQsf,cAAgB,EAAItf,EAAQsf,aAAe,IACvErd,KAAKsd,MAAQ,CAAE,EACftd,KAAKud,MAAQ,GACb,OAAA5gB,EAAA,OAAAC,EAAAoD,KAAKwC,cAAS,EAAA5F,EAAA4C,OAAO7C,EAAAqF,KAAApF,EAAAC,EAAUkB,EAAQyE,QAASzE,EACpD,CACE,SAAAyf,CAAUrb,EAAWhC,EAAYpC,EAAS0f,GACxC,MAAMC,EAAS,CAAE,EACXC,EAAU,CAAE,EACZC,EAAkB,CAAE,EACpBC,EAAmB,CAAE,EA4BpB,OA3BP1b,EAAUjE,SAAeJ,IACvB,IAAIggB,GAAmB,EACvB3d,EAAWjC,SAAcN,IACvB,MAAM+N,EAAO,GAAG7N,KAAOF,KAClBG,EAAQggB,QAAU/d,KAAK8D,MAAMkH,kBAAkBlN,EAAKF,GAClDoC,KAAAsd,MAAM3R,GAAQ,EACV3L,KAAKsd,MAAM3R,GAAQ,IAAmC,IAArB3L,KAAKsd,MAAM3R,QAC/B,IAAlBgS,EAAQhS,KAAqBgS,EAAQhS,IAAQ,IAE5C3L,KAAAsd,MAAM3R,GAAQ,EACAmS,GAAA,OACG,IAAlBH,EAAQhS,KAAqBgS,EAAQhS,IAAQ,QAC5B,IAAjB+R,EAAO/R,KAAqB+R,EAAO/R,IAAQ,QAClB,IAAzBkS,EAAiBjgB,KAAmBigB,EAAiBjgB,IAAM,IACzE,IAEWkgB,IAAkCF,EAAA9f,IAAO,EAAA,KAE5CuC,OAAOC,KAAKod,GAAQhc,QAAUrB,OAAOC,KAAKqd,GAASjc,SACrD1B,KAAKud,MAAMnf,KAAK,CACduf,UACAK,aAAc3d,OAAOC,KAAKqd,GAASjc,OACnCuc,OAAQ,CAAE,EACVC,OAAQ,GACRT,aAGG,CACLC,OAAQrd,OAAOC,KAAKod,GACpBC,QAAStd,OAAOC,KAAKqd,GACrBC,gBAAiBvd,OAAOC,KAAKsd,GAC7BC,iBAAkBxd,OAAOC,KAAKud,GAEpC,CACE,MAAAI,CAAOtS,EAAMmL,EAAKxQ,GACV,MAAAC,EAAIoF,EAAK5H,MAAM,KACfjG,EAAMyI,EAAE,GACR3I,EAAK2I,EAAE,GACTuQ,GAAU9W,KAAAqJ,KAAK,gBAAiBvL,EAAKF,EAAIkZ,IACxCA,GAAOxQ,GACVtG,KAAK8D,MAAM0G,kBAAkB1M,EAAKF,EAAI0I,OAAM,OAAW,EAAW,CAChEoE,UAAU,IAGd1K,KAAKsd,MAAM3R,GAAQmL,GAAW,EAAA,EAC1BA,GAAOxQ,IAAWtG,KAAAsd,MAAM3R,GAAQ,GACpC,MAAMsS,EAAS,CAAE,EACZje,KAAAud,MAAMrf,SAAaigB,IA32CX,EAACzZ,EAAQM,EAAMO,KACxB,MAAAlH,IACJA,EAAAgD,EACAA,GACE0D,EAAcL,EAAQM,EAAM3E,QAChChC,EAAIgD,GAAKhD,EAAIgD,IAAM,GACfhD,EAAAgD,GAAGjD,KAAKmH,EAAQ,EAs2ChB6Y,CAASD,EAAEF,OAAQ,CAACngB,GAAMF,GA7EV,EAACugB,EAAGxS,UACA,IAApBwS,EAAER,QAAQhS,YACLwS,EAAER,QAAQhS,GACfwS,EAAAH,eACN,EA0EMK,CAAcF,EAAGxS,GACbmL,GAAKqH,EAAED,OAAO9f,KAAK0Y,GACA,IAAnBqH,EAAEH,cAAuBG,EAAEG,OAC7Bje,OAAOC,KAAK6d,EAAEF,QAAQ/f,SAAa0S,IAC5BqN,EAAOrN,KAAWqN,EAAArN,GAAK,CAAE,GACxB,MAAA2N,EAAaJ,EAAEF,OAAOrN,GACxB2N,EAAW7c,QACb6c,EAAWrgB,SAAaC,SACD,IAAjB8f,EAAOrN,GAAGzS,KAAyB8f,EAAArN,GAAGzS,IAAK,EAAA,GAE7D,IAEQggB,EAAEG,MAAO,EACLH,EAAED,OAAOxc,OACTyc,EAAAV,SAASU,EAAED,QAEbC,EAAEV,WAEZ,IAESzd,KAAAqJ,KAAK,SAAU4U,GACpBje,KAAKud,MAAQvd,KAAKud,MAAMvQ,QAAYmR,IAACA,EAAEG,MAC3C,CACE,IAAAE,CAAK1gB,EAAKF,EAAI6gB,EAAQC,EAAQ,EAAG5c,EAAO9B,KAAKqd,aAAcI,GACzD,IAAK3f,EAAI4D,cAAe+b,EAAS,KAAM,CAAA,GACnC,GAAAzd,KAAKmd,cAAgBnd,KAAKkd,iBAS5B,YARAld,KAAKid,aAAa7e,KAAK,CACrBN,MACAF,KACA6gB,SACAC,QACA5c,OACA2b,aAICzd,KAAAmd,eACC,MAAAwB,EAAW,CAAC7H,EAAKxQ,KAEjB,GADCtG,KAAAmd,eACDnd,KAAKid,aAAavb,OAAS,EAAG,CAC1B,MAAAiG,EAAO3H,KAAKid,aAAa5V,QAC/BrH,KAAKwe,KAAK7W,EAAK7J,IAAK6J,EAAK/J,GAAI+J,EAAK8W,OAAQ9W,EAAK+W,MAAO/W,EAAK7F,KAAM6F,EAAK8V,SAC9E,CACU3G,GAAOxQ,GAAQoY,EAAQ1e,KAAKod,WAC9B5f,YAAW,KACJwC,KAAAwe,KAAKxc,KAAKhC,KAAMlC,EAAKF,EAAI6gB,EAAQC,EAAQ,EAAU,EAAP5c,EAAU2b,EAAQ,GAClE3b,GAGL2b,EAAS3G,EAAKxQ,EAAI,EAEdkP,EAAKxV,KAAKwC,QAAQic,GAAQG,KAAK5e,KAAKwC,SACtC,GAAc,IAAdgT,EAAG9T,OAaA,OAAA8T,EAAG1X,EAAKF,EAAI+gB,GAZb,IACI,MAAAzR,EAAIsI,EAAG1X,EAAKF,GACdsP,GAAuB,mBAAXA,EAAE2R,KACd3R,EAAA2R,SAAaF,EAAS,KAAMrY,KAAOwY,MAAMH,GAE3CA,EAAS,KAAMzR,EAElB,OAAQ4J,GACP6H,EAAS7H,EACjB,CAIA,CACE,cAAAiI,CAAe5c,EAAWhC,EAAYpC,EAAU,CAAA,EAAI0f,GAC9C,IAACzd,KAAKwC,QAER,OADKxC,KAAAlD,OAAOI,KAAK,kEACVugB,GAAYA,IAEjBjhB,EAAS2F,OAAwBnC,KAAKsQ,cAAcI,mBAAmBvO,IACvE3F,EAAS2D,KAAaA,EAAa,CAACA,IACxC,MAAMud,EAAS1d,KAAKwd,UAAUrb,EAAWhC,EAAYpC,EAAS0f,GAC1D,IAACC,EAAOA,OAAOhc,OAEV,OADFgc,EAAOC,QAAQjc,QAAkB+b,IAC/B,KAEFC,EAAAA,OAAOxf,SAAgByN,IAC5B3L,KAAKgf,QAAQrT,EAAI,GAEvB,CACE,IAAAiJ,CAAKzS,EAAWhC,EAAYsd,GAC1Bzd,KAAK+e,eAAe5c,EAAWhC,EAAY,CAAA,EAAIsd,EACnD,CACE,MAAAM,CAAO5b,EAAWhC,EAAYsd,GACvBzd,KAAA+e,eAAe5c,EAAWhC,EAAY,CACzC4d,QAAQ,GACPN,EACP,CACE,OAAAuB,CAAQrT,EAAMrD,EAAS,IACf,MAAA/B,EAAIoF,EAAK5H,MAAM,KACfjG,EAAMyI,EAAE,GACR3I,EAAK2I,EAAE,GACRvG,KAAAwe,KAAK1gB,EAAKF,EAAI,YAAQ,OAAW,GAAW,CAACkZ,EAAKxQ,KACjDwQ,GAAU9W,KAAAlD,OAAOI,KAAK,GAAGoL,sBAA2B1K,kBAAmBE,WAAcgZ,IACpFA,GAAOxQ,GAAMtG,KAAKlD,OAAOkL,IAAI,GAAGM,qBAA0B1K,kBAAmBE,IAAOwI,GACpFtG,KAAAie,OAAOtS,EAAMmL,EAAKxQ,EAAI,GAEjC,CACE,WAAA0K,CAAY7O,EAAW1B,EAAWoE,EAAKoa,EAAeC,EAAUnhB,EAAU,CAAE,EAAEohB,EAAM,sBAClF,KAAI,OAAAxiB,EAAA,OAAKC,EAAAoD,KAAAnD,eAAU,EAAAD,EAAAqW,YAAO,EAAAtW,EAAAyF,sBAAuB,OAAApF,EAAA,OAAAC,EAAA+C,KAAKnD,eAAL,EAAAI,EAAegW,YAAf,EAAAjW,EAAsBoF,mBAAmB3B,KAI1F,GAAIoE,SAA6C,KAARA,EAAzC,CACI,GAAA,OAAAua,EAAApf,KAAKwC,cAAL,EAAA4c,EAAczW,OAAQ,CACxB,MAAM0W,EAAO,IACRthB,EACHmhB,YAEI1J,EAAKxV,KAAKwC,QAAQmG,OAAOiW,KAAK5e,KAAKwC,SACrC,GAAAgT,EAAG9T,OAAS,EACV,IACE,IAAAwL,EAEFA,EADgB,IAAdsI,EAAG9T,OACD8T,EAAGrT,EAAW1B,EAAWoE,EAAKoa,EAAeI,GAE7C7J,EAAGrT,EAAW1B,EAAWoE,EAAKoa,GAEhC/R,GAAuB,mBAAXA,EAAE2R,KACd3R,EAAA2R,SAAaM,EAAI,KAAM7Y,KAAOwY,MAAMK,GAEtCA,EAAI,KAAMjS,EAEb,OAAQ4J,GACPqI,EAAIrI,EACd,MAEQtB,EAAGrT,EAAW1B,EAAWoE,EAAKoa,EAAeE,EAAKE,EAE1D,CACSld,GAAcA,EAAU,IAC7BnC,KAAK8D,MAAMqG,YAAYhI,EAAU,GAAI1B,EAAWoE,EAAKoa,EA5BA,OAHnDjf,KAAKlD,OAAOI,KAAK,qBAAqB2H,wBAA0BpE,wBAAiC,2NAgCvG,EAGA,MAAMwG,GAAM,KAAO,CACjBsB,OAAO,EACP+W,WAAW,EACX1hB,GAAI,CAAC,eACLoD,UAAW,CAAC,eACZwP,YAAa,CAAC,OACdsC,YAAY,EACZoB,eAAe,EACfW,0BAA0B,EAC1BD,KAAM,MACN5W,SAAS,EACTuhB,sBAAsB,EACtB/X,aAAc,IACdkF,YAAa,IACb+C,gBAAiB,IACjB+D,iBAAkB,IAClBgM,yBAAyB,EACzBxO,aAAa,EACbd,eAAe,EACfO,cAAe,WACfQ,oBAAoB,EACpBF,mBAAmB,EACnByI,6BAA6B,EAC7BjH,aAAa,EACbG,yBAAyB,EACzBiB,YAAY,EACZC,mBAAmB,EACnBrC,eAAe,EACf3C,YAAY,EACZ4C,uBAAuB,EACvBF,wBAAwB,EACxBD,6BAA6B,EAC7BnD,yBAAyB,EACzBH,iCAA0CrR,IACxC,IAAIuH,EAAM,CAAE,EAIR,GAHmB,iBAAZvH,EAAK,KAAiBuH,EAAMvH,EAAK,IACxCF,EAASE,EAAK,MAASuH,EAAA1C,aAAe7E,EAAK,IAC3CF,EAASE,EAAK,MAASuH,EAAAwb,aAAe/iB,EAAK,IACxB,iBAAZA,EAAK,IAAsC,iBAAZA,EAAK,GAAiB,CAC9D,MAAMqB,EAAUrB,EAAK,IAAMA,EAAK,GAChC2D,OAAOC,KAAKvC,GAASG,SAAe2G,IAC9BZ,EAAAY,GAAO9G,EAAQ8G,EAAG,GAE9B,CACW,OAAAZ,CAAA,EAETqI,cAAe,CACbwL,aAAa,EACbD,OAAiBxU,GAAAA,EACjBiF,OAAQ,KACR8I,OAAQ,KACR+G,gBAAiB,IACjBE,eAAgB,IAChBC,cAAe,MACfE,cAAe,IACfE,wBAAyB,IACzBC,YAAa,IACb5G,iBAAiB,GAEnBoJ,qBAAqB,IAEjBuE,GAAmB3hB,YAQhBA,OAPHvB,EAASuB,EAAQH,MAAKG,EAAQH,GAAK,CAACG,EAAQH,KAC5CpB,EAASuB,EAAQyS,eAAczS,EAAQyS,YAAc,CAACzS,EAAQyS,cAC9DhU,EAASuB,EAAQ+U,cAAa/U,EAAQ+U,WAAa,CAAC/U,EAAQ+U,cAC5D,OAAAnW,EAAA,SAAAoB,EAAQmW,wBAAejW,cAAvB,EAAAtB,EAAAqF,KAAApF,EAAiC,WAAY,IAC/CmB,EAAQmW,cAAgBnW,EAAQmW,cAAc7J,OAAO,CAAC,YAEnB,kBAA1BtM,EAAQ4hB,gBAA6B5hB,EAAQuhB,UAAYvhB,EAAQ4hB,eACrE5hB,CAAAA,EAGH6hB,GAAO,OASb,MAAMC,WAAa9W,EACjB,WAAAhJ,CAAYhC,EAAU,CAAE,EAAE0f,GATA,IAAQqC,EAkBhC,GAROnW,QACF3J,KAAAjC,QAAU2hB,GAAiB3hB,GAChCiC,KAAKnD,SAAW,CAAE,EAClBmD,KAAKlD,OAASgM,EACd9I,KAAK+f,QAAU,CACbC,SAAU,IAfoBF,EAiBZ9f,KAhBTK,OAAO4f,oBAAoB5f,OAAO6f,eAAeJ,IACzD5hB,SAAeke,IACO,mBAAd0D,EAAK1D,KACd0D,EAAK1D,GAAO0D,EAAK1D,GAAKwC,KAAKkB,GACjC,IAaQrC,IAAazd,KAAK1C,gBAAkBS,EAAQoiB,QAAS,CACnD,IAACngB,KAAKjC,QAAQuhB,UAET,OADFtf,KAAAR,KAAKzB,EAAS0f,GACZzd,KAETxC,YAAW,KACJwC,KAAAR,KAAKzB,EAAS0f,EAAQ,GAC1B,EACT,CACA,CACE,IAAAje,CAAKzB,EAAU,CAAE,EAAE0f,GACjBzd,KAAKogB,gBAAiB,EACC,mBAAZriB,IACEA,EAAAA,EACXA,EAAU,CAAE,GAEW,MAArBA,EAAQiD,WAAqBjD,EAAQH,KACnCpB,EAASuB,EAAQH,IACnBG,EAAQiD,UAAYjD,EAAQH,GACnBG,EAAQH,GAAGK,QAAQ,eAAiB,IAC7CF,EAAQiD,UAAYjD,EAAQH,GAAG,KAGnC,MAAMyiB,EAAUpZ,KAChBjH,KAAKjC,QAAU,IACVsiB,KACArgB,KAAKjC,WACL2hB,GAAiB3hB,IAEtBiC,KAAKjC,QAAQuO,cAAgB,IACxB+T,EAAQ/T,iBACRtM,KAAKjC,QAAQuO,oBAEW,IAAzBvO,EAAQyJ,eACLxH,KAAAjC,QAAQ8O,wBAA0B9O,EAAQyJ,mBAErB,IAAxBzJ,EAAQ2O,cACL1M,KAAAjC,QAAQ+O,uBAAyB/O,EAAQ2O,aAEhD,MAAM4T,EAAuCC,GACtCA,EACwB,mBAAlBA,EAAqC,IAAIA,EAC7CA,EAFoB,KAIzB,IAACvgB,KAAKjC,QAAQoiB,QAAS,CAMrB,IAAA7E,EALAtb,KAAK+f,QAAQjjB,OACfgM,EAAWtJ,KAAK8gB,EAAoBtgB,KAAK+f,QAAQjjB,QAASkD,KAAKjC,SAEpD+K,EAAAtJ,KAAK,KAAMQ,KAAKjC,SAI3Bud,EADEtb,KAAK+f,QAAQzE,UACHtb,KAAK+f,QAAQzE,UAEbL,EAEd,MAAMuF,EAAK,IAAIvM,EAAajU,KAAKjC,SACjCiC,KAAK8D,MAAQ,IAAI4F,EAAc1J,KAAKjC,QAAQwM,UAAWvK,KAAKjC,SAC5D,MAAMwI,EAAIvG,KAAKnD,SACf0J,EAAEzJ,OAASgM,EACXvC,EAAEsN,cAAgB7T,KAAK8D,MACvByC,EAAE+J,cAAgBkQ,EAChBja,EAAA6I,eAAiB,IAAIgH,EAAeoK,EAAI,CACxClJ,QAAStX,KAAKjC,QAAQ0R,gBACtB8P,qBAAsBvf,KAAKjC,QAAQwhB,wBAEjCjE,GAAetb,KAAKjC,QAAQuO,cAAcuL,QAAU7X,KAAKjC,QAAQuO,cAAcuL,SAAWwI,EAAQ/T,cAAcuL,SAChHtR,EAAA+U,UAAYgF,EAAoBhF,GAClC/U,EAAE+U,UAAU9b,KAAK+G,EAAGvG,KAAKjC,SACpBiC,KAAAjC,QAAQuO,cAAcuL,OAAStR,EAAE+U,UAAUzD,OAAO+G,KAAKrY,EAAE+U,YAEhE/U,EAAEmH,aAAe,IAAIkK,EAAa5X,KAAKjC,SACvCwI,EAAE0M,MAAQ,CACR7Q,mBAAoBpC,KAAKoC,mBAAmBwc,KAAK5e,OAEnDuG,EAAEhE,iBAAmB,IAAIya,EAAUsD,EAAoBtgB,KAAK+f,QAAQvd,SAAU+D,EAAEsN,cAAetN,EAAGvG,KAAKjC,SACvGwI,EAAEhE,iBAAiB7E,GAAG,KAAK,CAACyL,KAAUzM,KAC/BsD,KAAAqJ,KAAKF,KAAUzM,EAAI,IAEtBsD,KAAK+f,QAAQU,mBACfla,EAAEka,iBAAmBH,EAAoBtgB,KAAK+f,QAAQU,kBAClDla,EAAEka,iBAAiBjhB,MAAQ+G,EAAAka,iBAAiBjhB,KAAK+G,EAAGvG,KAAKjC,QAAQ2iB,UAAW1gB,KAAKjC,UAEnFiC,KAAK+f,QAAQjR,aACfvI,EAAEuI,WAAawR,EAAoBtgB,KAAK+f,QAAQjR,YAC5CvI,EAAEuI,WAAWtP,MAAQ+G,EAAAuI,WAAWtP,KAAKQ,OAE3CA,KAAK6L,WAAa,IAAIK,EAAWlM,KAAKnD,SAAUmD,KAAKjC,SACrDiC,KAAK6L,WAAWnO,GAAG,KAAK,CAACyL,KAAUzM,KAC5BsD,KAAAqJ,KAAKF,KAAUzM,EAAI,IAErBsD,KAAA+f,QAAQC,SAAS9hB,SAAaO,IAC7BA,EAAEe,MAAQf,EAAAe,KAAKQ,KAAI,GAE/B,CAGQ,GAFCA,KAAA6X,OAAS7X,KAAKjC,QAAQuO,cAAcuL,OACpC4F,IAAqBA,EAAAmC,IACtB5f,KAAKjC,QAAQyS,cAAgBxQ,KAAKnD,SAAS4jB,mBAAqBzgB,KAAKjC,QAAQD,IAAK,CACpF,MAAMkV,EAAQhT,KAAKnD,SAASyT,cAAcC,iBAAiBvQ,KAAKjC,QAAQyS,aACpEwC,EAAMtR,OAAS,GAAkB,QAAbsR,EAAM,KAAmBhT,KAAAjC,QAAQD,IAAMkV,EAAM,GAC3E,CACShT,KAAKnD,SAAS4jB,kBAAqBzgB,KAAKjC,QAAQD,KAC9CkC,KAAAlD,OAAOI,KAAK,2DAEF,CAAC,cAAe,oBAAqB,oBAAqB,qBAClEgB,SAAkBugB,IACpBze,KAAAye,GAAU,IAAI/hB,IAASsD,KAAK8D,MAAM2a,MAAW/hB,EAAI,IAEhC,CAAC,cAAe,eAAgB,oBAAqB,wBAC7DwB,SAAkBugB,IAC3Bze,KAAAye,GAAU,IAAI/hB,KACjBsD,KAAK8D,MAAM2a,MAAW/hB,GACfsD,KACR,IAEH,MAAM2gB,EAAWvc,IACXwQ,EAAO,KACL,MAAAgM,EAAS,CAAC9J,EAAKlV,KACnB5B,KAAKogB,gBAAiB,EAClBpgB,KAAK1C,gBAAkB0C,KAAKiC,sBAA2BjC,KAAAlD,OAAOI,KAAK,yEACvE8C,KAAK1C,eAAgB,EAChB0C,KAAKjC,QAAQoiB,cAAcrjB,OAAOkL,IAAI,cAAehI,KAAKjC,SAC1DiC,KAAAqJ,KAAK,cAAerJ,KAAKjC,SAC9B4iB,EAASxc,QAAQvC,GACjB6b,EAAS3G,EAAKlV,EAAC,EAEjB,GAAI5B,KAAKmC,YAAcnC,KAAK1C,cAAe,OAAOsjB,EAAO,KAAM5gB,KAAK4B,EAAEgd,KAAK5e,OAC3EA,KAAKmM,eAAenM,KAAKjC,QAAQD,IAAK8iB,EAAM,EAOvC,OALH5gB,KAAKjC,QAAQwM,YAAcvK,KAAKjC,QAAQuhB,UACpC1K,IAENpX,WAAWoX,EAAM,GAEZ+L,CACX,CACE,aAAAE,CAAcrgB,EAAUid,EAAWmC,YACjC,IAAIkB,EAAerD,EACnB,MAAMnP,EAAU9R,EAASgE,GAAYA,EAAWR,KAAKQ,SAErD,GADwB,mBAAbA,IAAwCsgB,EAAAtgB,IAC9CR,KAAKjC,QAAQwM,WAAavK,KAAKjC,QAAQyhB,wBAAyB,CACnE,GAA+B,YAAlB,MAATlR,OAAS,EAAAA,EAAAH,kBAAgCnO,KAAKjC,QAAQC,SAA2C,IAAhCgC,KAAKjC,QAAQC,QAAQ0D,eAAsBof,IAChH,MAAMpD,EAAS,GACTqD,EAAgBjjB,IACpB,IAAKA,EAAK,OACV,GAAY,WAARA,EAAkB,OACTkC,KAAKnD,SAASyT,cAAcI,mBAAmB5S,GACvDI,SAAa0S,IACN,WAANA,GACA8M,EAAOzf,QAAQ2S,GAAK,GAAG8M,EAAOtf,KAAKwS,EAAC,GACzC,EAEH,GAAKtC,EAIHyS,EAAOzS,OAJK,CACMtO,KAAKnD,SAASyT,cAAcC,iBAAiBvQ,KAAKjC,QAAQyS,aAClEtS,SAAQ0S,GAAKmQ,EAAOnQ,IACtC,CAGM,OAAAjU,EAAA,OAAAC,EAAAoD,KAAKjC,QAAQC,cAAb,EAAApB,EAAsBsB,UAAUvB,EAAAqF,KAAApF,GAAAgU,GAAKmQ,EAAOnQ,KAC5C5Q,KAAKnD,SAAS0F,iBAAiBqS,KAAK8I,EAAQ1d,KAAKjC,QAAQH,IAASoG,IAC3DA,GAAMhE,KAAKghB,mBAAoBhhB,KAAKQ,UAAUR,KAAKihB,oBAAoBjhB,KAAKQ,UACjFsgB,EAAa9c,EAAC,GAEtB,MACM8c,EAAa,KAEnB,CACE,eAAAI,CAAgB9Q,EAAMxS,EAAI6f,GACxB,MAAMkD,EAAWvc,IAgBV,MAfa,mBAATgM,IACEqN,EAAArN,EACJA,OAAA,GAES,mBAAPxS,IACE6f,EAAA7f,EACNA,OAAA,GAEFwS,IAAMA,EAAOpQ,KAAKmC,WAClBvE,IAASA,EAAAoC,KAAKjC,QAAQH,IACtB6f,IAAqBA,EAAAmC,IAC1B5f,KAAKnD,SAAS0F,iBAAiBwb,OAAO3N,EAAMxS,GAAWkZ,IACrD6J,EAASxc,UACTsZ,EAAS3G,EAAG,IAEP6J,CACX,CACE,GAAAQ,CAAIzV,GACF,IAAKA,EAAc,MAAA,IAAI0V,MAAM,iGAC7B,IAAK1V,EAAOnM,KAAY,MAAA,IAAI6hB,MAAM,4FAsB3B,MArBa,YAAhB1V,EAAOnM,OACTS,KAAK+f,QAAQvd,QAAUkJ,IAEL,WAAhBA,EAAOnM,MAAqBmM,EAAO1D,KAAO0D,EAAOxO,MAAQwO,EAAOxD,SAClElI,KAAK+f,QAAQjjB,OAAS4O,GAEJ,qBAAhBA,EAAOnM,OACTS,KAAK+f,QAAQU,iBAAmB/U,GAEd,eAAhBA,EAAOnM,OACTS,KAAK+f,QAAQjR,WAAapD,GAER,kBAAhBA,EAAOnM,MACTgM,EAAcE,iBAAiBC,GAEb,cAAhBA,EAAOnM,OACTS,KAAK+f,QAAQzE,UAAY5P,GAEP,aAAhBA,EAAOnM,MACJS,KAAA+f,QAAQC,SAAS5hB,KAAKsN,GAEtB1L,IACX,CACE,mBAAAihB,CAAoBrQ,GAClB,GAAKA,GAAM5Q,KAAKmC,aACZ,CAAC,SAAU,OAAOlE,QAAQ2S,IAAS,GAAvC,CACA,IAAA,IAASyQ,EAAK,EAAGA,EAAKrhB,KAAKmC,UAAUT,OAAQ2f,IAAM,CAC3C,MAAAC,EAAYthB,KAAKmC,UAAUkf,GACjC,KAAI,CAAC,SAAU,OAAOpjB,QAAQqjB,IAAiB,IAC3CthB,KAAK8D,MAAMqH,4BAA4BmW,GAAY,CACrDthB,KAAKghB,iBAAmBM,EACxB,KACR,CACA,EACSthB,KAAKghB,kBAAoBhhB,KAAKmC,UAAUlE,QAAQ2S,GAAK,GAAK5Q,KAAK8D,MAAMqH,4BAA4ByF,KACpG5Q,KAAKghB,iBAAmBpQ,EACnB5Q,KAAAmC,UAAUof,QAAQ3Q,GAXc,CAa3C,CACE,cAAAzE,CAAerO,EAAK2f,GAClBzd,KAAKyC,qBAAuB3E,EAC5B,MAAM6iB,EAAWvc,IACZpE,KAAAqJ,KAAK,mBAAoBvL,GAC9B,MAAM0jB,EAAmB5Q,IACvB5Q,KAAKQ,SAAWoQ,EAChB5Q,KAAKmC,UAAYnC,KAAKnD,SAASyT,cAAcI,mBAAmBE,GAChE5Q,KAAKghB,sBAAmB,EACxBhhB,KAAKihB,oBAAoBrQ,EAAC,EAEtB0N,EAAO,CAACxH,EAAKlG,KACbA,EACE5Q,KAAKyC,uBAAyB3E,IAChC0jB,EAAY5Q,GACP5Q,KAAA6L,WAAWM,eAAeyE,GAC/B5Q,KAAKyC,0BAAuB,EACvBzC,KAAAqJ,KAAK,kBAAmBuH,GACxB5Q,KAAAlD,OAAOkL,IAAI,kBAAmB4I,IAGrC5Q,KAAKyC,0BAAuB,EAE9Bke,EAASxc,SAAQ,IAAIzH,IAASsD,KAAK4B,KAAKlF,KACpC+gB,KAAmB3G,GAAK,IAAIpa,IAASsD,KAAK4B,KAAKlF,IAAK,EAEpD+kB,EAAiBrR,YAChBtS,GAAQsS,IAAQpQ,KAAKnD,SAAS4jB,qBAAyB,IAC5D,MAAMiB,EAAKllB,EAAS4T,GAAQA,EAAOA,GAAQA,EAAK,GAC1CQ,EAAI5Q,KAAK8D,MAAMqH,4BAA4BuW,GAAMA,EAAK1hB,KAAKnD,SAASyT,cAAcwE,sBAAsBtY,EAAS4T,GAAQ,CAACA,GAAQA,GACpIQ,IACG5Q,KAAKQ,UACRghB,EAAY5Q,GAET5Q,KAAK6L,WAAWrL,UAAeR,KAAA6L,WAAWM,eAAeyE,GAC9D,OAAAjU,EAAA,OAAAC,EAAAoD,KAAKnD,SAAS4jB,uBAAkB,EAAA7jB,EAAA+kB,oBAAoBhlB,EAAAqF,KAAApF,EAAAgU,IAEjD5Q,KAAA6gB,cAAcjQ,GAAUkG,IAC3BwH,EAAKxH,EAAKlG,EAAC,GACZ,EAaI,OAXF9S,IAAOkC,KAAKnD,SAAS4jB,kBAAqBzgB,KAAKnD,SAAS4jB,iBAAiBmB,OAElE9jB,GAAOkC,KAAKnD,SAAS4jB,kBAAoBzgB,KAAKnD,SAAS4jB,iBAAiBmB,MAC7B,IAAjD5hB,KAAKnD,SAAS4jB,iBAAiBoB,OAAOngB,OACxC1B,KAAKnD,SAAS4jB,iBAAiBoB,SAAShD,KAAK4C,GAExCzhB,KAAAnD,SAAS4jB,iBAAiBoB,OAAOJ,GAGxCA,EAAO3jB,GARP2jB,EAAOzhB,KAAKnD,SAAS4jB,iBAAiBoB,UAUjClB,CACX,CACE,SAAAhgB,CAAU7C,EAAKF,EAAI8C,GACjB,MAAMohB,EAAS,CAACjd,EAAKwa,KAAS9iB,KACxB,IAAA8P,EAEEA,EADc,iBAATgT,EACLrf,KAAKjC,QAAQgQ,iCAAiC,CAAClJ,EAAKwa,GAAMhV,OAAO9N,IAEjE,IACC8iB,GAGLhT,EAAAvO,IAAMuO,EAAEvO,KAAOgkB,EAAOhkB,IACtBuO,EAAA+D,KAAO/D,EAAE+D,MAAQ0R,EAAO1R,KACxB/D,EAAAzO,GAAKyO,EAAEzO,IAAMkkB,EAAOlkB,GACF,KAAhByO,EAAE3L,YAAkB2L,EAAE3L,UAAY2L,EAAE3L,WAAaA,GAAaohB,EAAOphB,WACnE,MAAA8G,EAAexH,KAAKjC,QAAQyJ,cAAgB,IAC9C,IAAAua,EAMG,OAJOA,EADV1V,EAAE3L,WAAac,MAAMC,QAAQoD,GACnBA,EAAIsI,KAAI9L,GAAK,GAAGgL,EAAE3L,YAAY8G,IAAenG,MAE7CgL,EAAE3L,UAAY,GAAG2L,EAAE3L,YAAY8G,IAAe3C,IAAQA,EAE7D7E,KAAK4B,EAAEmgB,EAAW1V,EAAC,EASrB,OAPH7P,EAASsB,GACXgkB,EAAOhkB,IAAMA,EAEbgkB,EAAO1R,KAAOtS,EAEhBgkB,EAAOlkB,GAAKA,EACZkkB,EAAOphB,UAAYA,EACZohB,CACX,CACE,CAAAlgB,IAAKlF,SACH,OAAO,OAAAE,EAAKoD,KAAA6L,iBAAY,EAAAjP,EAAAiR,aAAanR,EACzC,CACE,MAAA0P,IAAU1P,SACR,OAAO,OAAAE,EAAKoD,KAAA6L,iBAAY,EAAAjP,EAAAwP,UAAU1P,EACtC,CACE,mBAAAslB,CAAoBpkB,GAClBoC,KAAKjC,QAAQiD,UAAYpD,CAC7B,CACE,kBAAAwE,CAAmBxE,EAAIG,EAAU,IAC3B,IAACiC,KAAK1C,cAED,OADP0C,KAAKlD,OAAOI,KAAK,kDAAmD8C,KAAKmC,YAClE,EAET,IAAKnC,KAAKmC,YAAcnC,KAAKmC,UAAUT,OAE9B,OADP1B,KAAKlD,OAAOI,KAAK,6DAA8D8C,KAAKmC,YAC7E,EAET,MAAMrE,EAAMC,EAAQD,KAAOkC,KAAKghB,kBAAoBhhB,KAAKmC,UAAU,GAC7DqO,IAAcxQ,KAAKjC,SAAUiC,KAAKjC,QAAQyS,YAC1CyR,EAAUjiB,KAAKmC,UAAUnC,KAAKmC,UAAUT,OAAS,GACvD,GAA0B,WAAtB5D,EAAIqQ,cAAmC,OAAA,EACrC,MAAA7L,EAAiB,CAACsO,EAAGzS,KACnB,MAAA+jB,EAAYliB,KAAKnD,SAAS0F,iBAAiB+a,MAAM,GAAG1M,KAAKzS,KAC/D,OAAqB,IAAd+jB,GAAkC,IAAdA,GAAiC,IAAdA,CAAc,EAE9D,GAAInkB,EAAQsE,SAAU,CACpB,MAAM8f,EAAYpkB,EAAQsE,SAASrC,KAAMsC,GACrC,QAAc,IAAd6f,EAAgC,OAAAA,CAC1C,CACI,QAAIniB,KAAKgL,kBAAkBlN,EAAKF,OAC3BoC,KAAKnD,SAAS0F,iBAAiBC,WAAWxC,KAAKjC,QAAQwM,WAAcvK,KAAKjC,QAAQyhB,8BACnFld,EAAexE,EAAKF,IAAS4S,IAAelO,EAAe2f,EAASrkB,IAE5E,CACE,cAAAD,CAAeC,EAAI6f,GACjB,MAAMkD,EAAWvc,IACb,OAACpE,KAAKjC,QAAQH,IAIdpB,EAASoB,KAAKA,EAAK,CAACA,IACxBA,EAAGM,SAAaC,IACV6B,KAAKjC,QAAQH,GAAGK,QAAQE,GAAK,GAAQ6B,KAAAjC,QAAQH,GAAGQ,KAAKD,EAAC,IAE5D6B,KAAK6gB,eAAqB/J,IACxB6J,EAASxc,UACLsZ,KAAmB3G,EAAG,IAErB6J,IAXDlD,GAAoBA,IACjBvZ,QAAQC,UAWrB,CACE,aAAAtG,CAAcuS,EAAMqN,GAClB,MAAMkD,EAAWvc,IACb5H,EAAS4T,KAAOA,EAAO,CAACA,IAC5B,MAAMgS,EAAYpiB,KAAKjC,QAAQC,SAAW,GACpCqkB,EAAUjS,EAAKpD,QAAOlP,GAAOskB,EAAUnkB,QAAQH,GAAO,GAAKkC,KAAKnD,SAASyT,cAAcqE,gBAAgB7W,KACzG,OAACukB,EAAQ3gB,QAIb1B,KAAKjC,QAAQC,QAAUokB,EAAU/X,OAAOgY,GACxCriB,KAAK6gB,eAAqB/J,IACxB6J,EAASxc,UACLsZ,KAAmB3G,EAAG,IAErB6J,IARDlD,GAAoBA,IACjBvZ,QAAQC,UAQrB,CACE,GAAAme,CAAIxkB,WAEE,GADCA,IAAWA,EAAAkC,KAAKghB,oBAAqB,OAAApkB,EAAKoD,KAAAmC,gBAAW,EAAAvF,EAAA8E,QAAS,EAAI1B,KAAKmC,UAAU,GAAKnC,KAAKQ,YAC3F1C,EAAY,MAAA,MACjB,MACMwS,GAAgB,OAAA3T,OAAKE,eAAL,EAAAF,EAAe2T,gBAAiB,IAAI2D,EAAahN,MACvE,MAFgB,CAAC,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,OAEnahJ,QAAQqS,EAAc+D,wBAAwBvW,KAAc,GAAAA,EAAIqQ,cAAclQ,QAAQ,SAAW,EAAI,MAAQ,KAChI,CACE,qBAAOskB,CAAexkB,EAAU,CAAE,EAAE0f,GAC3B,OAAA,IAAIoC,GAAK9hB,EAAS0f,EAC7B,CACE,aAAA+E,CAAczkB,EAAU,GAAI0f,EAAWmC,IACrC,MAAM6C,EAAoB1kB,EAAQ0kB,kBAC9BA,UAA0B1kB,EAAQ0kB,kBACtC,MAAMC,EAAgB,IACjB1iB,KAAKjC,WACLA,EAEDoiB,SAAS,GAGPtX,EAAQ,IAAIgX,GAAK6C,QACD,IAAlB3kB,EAAQwK,YAA0C,IAAnBxK,EAAQuK,SACzCO,EAAM/L,OAAS+L,EAAM/L,OAAO+L,MAAM9K,IAYpC,GAVsB,CAAC,QAAS,WAAY,YAC9BG,SAAaO,IACnBoK,EAAApK,GAAKuB,KAAKvB,EAAC,IAEnBoK,EAAMhM,SAAW,IACZmD,KAAKnD,UAEVgM,EAAMhM,SAASoW,MAAQ,CACrB7Q,mBAAoByG,EAAMzG,mBAAmBwc,KAAK/V,IAEhD4Z,EAAmB,CACf,MAAAE,EAAatiB,OAAOC,KAAKN,KAAK8D,MAAMwC,MAAMmU,QAAO,CAACmI,EAAMhS,KAC5DgS,EAAKhS,GAAK,IACL5Q,KAAK8D,MAAMwC,KAAKsK,IAEhBgS,EAAAhS,GAAKvQ,OAAOC,KAAKsiB,EAAKhS,IAAI6J,QAAO,CAACoI,EAAK1kB,KAC1C0kB,EAAI1kB,GAAK,IACJykB,EAAKhS,GAAGzS,IAEN0kB,IACND,EAAKhS,IACDgS,IACN,IACH/Z,EAAM/E,MAAQ,IAAI4F,EAAciZ,EAAYD,GACtC7Z,EAAAhM,SAASgX,cAAgBhL,EAAM/E,KAC3C,CAUW,OATP+E,EAAMgD,WAAa,IAAIK,EAAWrD,EAAMhM,SAAU6lB,GAClD7Z,EAAMgD,WAAWnO,GAAG,KAAK,CAACyL,KAAUzM,KAC5BmM,EAAAQ,KAAKF,KAAUzM,EAAI,IAErBmM,EAAArJ,KAAKkjB,EAAejF,GAC1B5U,EAAMgD,WAAW9N,QAAU2kB,EACrB7Z,EAAAgD,WAAWtJ,iBAAiB1F,SAASoW,MAAQ,CACjD7Q,mBAAoByG,EAAMzG,mBAAmBwc,KAAK/V,IAE7CA,CACX,CACE,MAAAyC,GACS,MAAA,CACLvN,QAASiC,KAAKjC,QACd+F,MAAO9D,KAAK8D,MACZtD,SAAUR,KAAKQ,SACf2B,UAAWnC,KAAKmC,UAChB6e,iBAAkBhhB,KAAKghB,iBAE7B,EAEK,MAACvhB,GAAWogB,GAAK0C,iBACtB9iB,GAAS8iB,eAAiB1C,GAAK0C,eCxlE/B,MAAM9c,MACJA,GAAAvH,QACAA,IACE,GAoBJ,MAAM4kB,GAAqB,wCAuDrBC,GAAS,CACb,MAAApa,CAAOgD,EAAMtI,EAAO2f,EAASC,GACvB,IAAAC,EAAgBlV,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAK,CACtFhJ,KAAM,IACNme,SAAU,UAERH,IACYE,EAAAE,YAAc3mB,KACdymB,EAAAE,QAAQC,QAAQH,EAAcE,QAAQE,UAAsB,GAAVN,EAAe,MAE7EC,MAAsBA,OAASA,GACnCM,SAASR,OAjEW,SAAUpX,EAAMgM,GAItC,MAAMpL,EAHQyB,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAK,CAChFhJ,KAAM,KAIR,IAAImB,EAAM,GAAGwF,KADC6X,mBAAmB7L,KAE7B,GAAApL,EAAIkX,OAAS,EAAG,CACZ,MAAAA,EAASlX,EAAIkX,OAAS,EAC5B,GAAIC,OAAOhH,MAAM+G,GAAe,MAAA,IAAIrC,MAAM,6BAC1Cjb,GAAO,aAAawd,KAAKC,MAAMH,IACnC,CACE,GAAIlX,EAAI0W,OAAQ,CACd,IAAKH,GAAmBzV,KAAKd,EAAI0W,QACzB,MAAA,IAAIY,UAAU,4BAEf1d,GAAA,YAAYoG,EAAI0W,QAC3B,CACE,GAAI1W,EAAIvH,KAAM,CACZ,IAAK8d,GAAmBzV,KAAKd,EAAIvH,MACzB,MAAA,IAAI6e,UAAU,0BAEf1d,GAAA,UAAUoG,EAAIvH,MACzB,CACE,GAAIuH,EAAI6W,QAAS,CACf,GAAuC,mBAA5B7W,EAAI6W,QAAQU,YACf,MAAA,IAAID,UAAU,6BAEtB1d,GAAO,aAAaoG,EAAI6W,QAAQU,eACpC,CAGE,GAFIvX,EAAIwX,WAAiB5d,GAAA,cACrBoG,EAAIyX,SAAe7d,GAAA,YACnBoG,EAAI4W,SAEN,OADyC,iBAAjB5W,EAAI4W,SAAwB5W,EAAI4W,SAAShV,cAAgB5B,EAAI4W,UAEnF,KAAK,EACIhd,GAAA,oBACP,MACF,IAAK,MACIA,GAAA,iBACP,MACF,IAAK,SACIA,GAAA,oBACP,MACF,IAAK,OACIA,GAAA,kBACP,MACF,QACQ,MAAA,IAAI0d,UAAU,8BAInB,OADHtX,EAAI0X,cAAoB9d,GAAA,iBACrBA,CACT,CAYsB+d,CAAgBvY,EAAM6X,mBAAmBngB,GAAQ6f,EACpE,EACD,IAAA1E,CAAK7S,GACG,MAAAwY,EAAS,GAAGxY,KACZyY,EAAKb,SAASR,OAAOhf,MAAM,KACjC,IAAA,IAAS2D,EAAI,EAAGA,EAAI0c,EAAG1iB,OAAQgG,IAAK,CAC9B,IAAAuF,EAAImX,EAAG1c,GACJ,KAAgB,MAAhBuF,EAAEoX,OAAO,MAAgBpX,EAAEM,UAAU,EAAGN,EAAEvL,QAC7C,GAAsB,IAAtBuL,EAAEhP,QAAQkmB,GAAsB,OAAAlX,EAAEM,UAAU4W,EAAOziB,OAAQuL,EAAEvL,OACvE,CACW,OAAA,IACR,EACD,MAAA4iB,CAAO3Y,GACA3L,KAAA2I,OAAOgD,EAAM,IAAM,EAC5B,GAEA,IAAI4Y,GAAW,CACb5Y,KAAM,SAEN,MAAA6Y,CAAOC,GACD,IAAAC,aACFA,GACED,EACA,GAAAC,GAAoC,oBAAbnB,SAClB,OAAAR,GAAOvE,KAAKkG,SAAiB,CAGvC,EAED,iBAAA/C,CAAkB7jB,EAAK6mB,GACjB,IAAAD,aACFA,EAAAE,cACAA,EAAAC,aACAA,EAAA3B,cACAA,GACEyB,EACAD,GAAoC,oBAAbnB,UACzBR,GAAOpa,OAAO+b,EAAc5mB,EAAK8mB,EAAeC,EAAc3B,EAEpE,GAGI4B,GAAc,CAChBnZ,KAAM,cAEN,MAAA6Y,CAAOC,SACD,IAGA7R,GAHAmS,kBACFA,GACEN,EAEA,GAAkB,oBAAXO,OAAwB,CAC7B,IAAAC,OACFA,GACED,OAAOE,UACNF,OAAOE,SAASD,SAAU,OAAAroB,EAAAooB,OAAOE,SAASC,WAAhB,EAAAvoB,EAAsBqB,QAAQ,OAAW,IAC7DgnB,EAAAD,OAAOE,SAASC,KAAK5X,UAAUyX,OAAOE,SAASC,KAAKlnB,QAAQ,OAEjE,MACAmnB,EADQH,EAAO1X,UAAU,GACVxJ,MAAM,KAC3B,IAAA,IAAS2D,EAAI,EAAGA,EAAI0d,EAAO1jB,OAAQgG,IAAK,CACtC,MAAM2d,EAAMD,EAAO1d,GAAGzJ,QAAQ,KAC9B,GAAIonB,EAAM,EAAG,CACCD,EAAO1d,GAAG6F,UAAU,EAAG8X,KACvBN,IACVnS,EAAQwS,EAAO1d,GAAG6F,UAAU8X,EAAM,GAE9C,CACA,CACA,CACW,OAAAzS,CACX,GAGA,IAAI0S,GAAyB,KAC7B,MAAMC,GAAwB,KACxB,GAA2B,OAA3BD,GAAwC,OAAAA,GACxC,IAEF,GADAA,GAA2C,oBAAXN,QAAkD,OAAxBA,OAAOQ,cAC5DF,GACI,OAAA,EAET,MAAMG,EAAU,wBACTT,OAAAQ,aAAaE,QAAQD,EAAS,OAC9BT,OAAAQ,aAAaG,WAAWF,EAChC,OAAQzhB,GACkBshB,IAAA,CAC7B,CACS,OAAAA,EAAA,EAET,IAAIE,GAAe,CACjB7Z,KAAM,eAEN,MAAA6Y,CAAOC,GACD,IAAAmB,mBACFA,GACEnB,EACA,GAAAmB,GAAsBL,KACxB,OAAOP,OAAOQ,aAAaK,QAAQD,SAAuB,CAG7D,EAED,iBAAAjE,CAAkB7jB,EAAK6mB,GACjB,IAAAiB,mBACFA,GACEjB,EACAiB,GAAsBL,MACjBP,OAAAQ,aAAaE,QAAQE,EAAoB9nB,EAEtD,GAGA,IAAIgoB,GAA2B,KAC/B,MAAMC,GAA0B,KAC1B,GAA6B,OAA7BD,GAA0C,OAAAA,GAC1C,IAEF,GADAA,GAA6C,oBAAXd,QAAoD,OAA1BA,OAAOgB,gBAC9DF,GACI,OAAA,EAET,MAAML,EAAU,wBACTT,OAAAgB,eAAeN,QAAQD,EAAS,OAChCT,OAAAgB,eAAeL,WAAWF,EAClC,OAAQzhB,GACoB8hB,IAAA,CAC/B,CACS,OAAAA,EAAA,EAET,IAAIE,GAAiB,CACnBra,KAAM,iBACN,MAAA6Y,CAAOC,GACD,IAAAwB,qBACFA,GACExB,EACA,GAAAwB,GAAwBF,KAC1B,OAAOf,OAAOgB,eAAeH,QAAQI,SAAyB,CAGjE,EACD,iBAAAtE,CAAkB7jB,EAAK6mB,GACjB,IAAAsB,qBACFA,GACEtB,EACAsB,GAAwBF,MACnBf,OAAAgB,eAAeN,QAAQO,EAAsBnoB,EAE1D,GAGIooB,GAAc,CAChBva,KAAM,YACN,MAAA6Y,CAAOzmB,GACL,MAAM6U,EAAQ,GACV,GAAqB,oBAAduT,UAA2B,CAC9B,MAAAhkB,UACJA,EAAAikB,aACAA,EAAA5lB,SACAA,GACE2lB,UACJ,GAAIhkB,EAEF,IAAA,IAASuF,EAAI,EAAGA,EAAIvF,EAAUT,OAAQgG,IAC9BkL,EAAAxU,KAAK+D,EAAUuF,IAGrB0e,GACFxT,EAAMxU,KAAKgoB,GAET5lB,GACFoS,EAAMxU,KAAKoC,EAEnB,CACW,OAAAoS,EAAMlR,OAAS,EAAIkR,OAAQ,CACtC,GAGIyT,GAAU,CACZ1a,KAAM,UAEN,MAAA6Y,CAAOC,GACD,IAGA7R,GAFFyT,QAAAA,GACE5B,EAEJ,MAAM6B,EAAkBD,IAAgC,oBAAb9C,SAA2BA,SAASgD,gBAAkB,MAI1F,OAHHD,GAA2D,mBAAjCA,EAAgBE,eACpC5T,EAAA0T,EAAgBE,aAAa,SAEhC5T,CACX,GAGI5N,GAAO,CACT2G,KAAM,OAEN,MAAA6Y,CAAOC,SACD,IAAAgC,oBACFA,GACEhC,EACA,GAAkB,oBAAXO,OAA+B,OAC1C,MAAMxkB,EAAWwkB,OAAOE,SAASwB,SAASjZ,MAAM,mBAChD,IAAKjM,MAAMC,QAAQjB,GAAkB,OAErC,OAAO,OAAA5D,EAAS4D,EAD6B,iBAAxBimB,EAAmCA,EAAsB,SACvE,EAAA7pB,EAAiBwC,QAAQ,IAAK,GACzC,GAGIunB,GAAY,CACdhb,KAAM,YACN,MAAA6Y,CAAOC,WACD,IAAAmC,yBACFA,GACEnC,EAEJ,MAAMoC,EAAuE,iBAA7BD,EAAwCA,EAA2B,EAAI,EAIjHpmB,EAA6B,oBAAXwkB,SAA0B,OAAAroB,EAAA,gBAAOuoB,eAAP,EAAAtoB,EAAiBkqB,eAAjB,EAAAnqB,EAA2B8Q,MAAM,2DAG/E,GAACjN,EAEL,OAAOA,EAASqmB,EACpB,GAIA,IAAIE,IAAa,EACjB,IAEWxD,SAAAR,OACIgE,IAAA,CAEf,CAAA,MAAS/iB,IAAG,CACZ,MAAMgjB,GAAQ,CAAC,cAAe,SAAU,eAAgB,iBAAkB,YAAa,WAClFD,IAAkBC,GAAAhd,OAAO,EAAG,GAejC,MAAMid,GACJ,WAAAlnB,CAAYlD,GACNkB,IAAAA,EAAUiQ,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACpFhO,KAAKT,KAAO,mBACZS,KAAKknB,UAAY,CAAE,EACdlnB,KAAAR,KAAK3C,EAAUkB,EACxB,CACE,IAAAyB,GACM,IAAA3C,EAAWmR,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAK,CACjFsC,cAAe,CAAA,GAEbvS,EAAUiQ,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAK,CAAE,EAChFjM,EAAciM,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAK,CAAE,EACxFhO,KAAKnD,SAAWA,EACXmD,KAAAjC,QA9VT,SAAkBM,GAQT,OAPPH,GAAQ8D,KAAKyD,GAAMzD,KAAKgM,UAAW,IAAclI,IAC/C,GAAIA,EACF,IAAA,MAAWE,KAAQF,OACC,IAAdzH,EAAI2H,OAAyBA,GAAQF,EAAOE,GAExD,IAES3H,CACT,CAqVmB8oB,CAASppB,EAASiC,KAAKjC,SAAW,CAAA,EA5B1B,CACzBipB,SACAjC,kBAAmB,MACnBL,aAAc,UACdkB,mBAAoB,aACpBK,qBAAsB,aAEtBmB,OAAQ,CAAC,gBACTC,gBAAiB,CAAC,UAIlBC,wBAA8B1W,GAAAA,IAiBwB,iBAAzC5Q,KAAKjC,QAAQupB,yBAAwCtnB,KAAKjC,QAAQupB,wBAAwBrpB,QAAQ,UAAe,IAC1H+B,KAAKjC,QAAQupB,wBAA0B1W,GAAKA,EAAExR,QAAQ,IAAK,MAIzDY,KAAKjC,QAAQwpB,0BAAyBxpB,QAAQ0oB,oBAAsBzmB,KAAKjC,QAAQwpB,oBACrFvnB,KAAK+B,YAAcA,EACnB/B,KAAKwnB,YAAYjD,IACjBvkB,KAAKwnB,YAAY1C,IACjB9kB,KAAKwnB,YAAYhC,IACjBxlB,KAAKwnB,YAAYxB,IACjBhmB,KAAKwnB,YAAYtB,IACjBlmB,KAAKwnB,YAAYnB,IACjBrmB,KAAKwnB,YAAYxiB,IACjBhF,KAAKwnB,YAAYb,GACrB,CACE,WAAAa,CAAYC,GAEH,OADFznB,KAAAknB,UAAUO,EAAS9b,MAAQ8b,EACzBznB,IACX,CACE,MAAA6hB,GACE,IAAI6F,EAAiB1Z,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAKhO,KAAKjC,QAAQipB,MAClGW,EAAW,GASX,OARJD,EAAexpB,SAAwB0pB,IACjC,GAAA5nB,KAAKknB,UAAUU,GAAe,CAChC,IAAIpD,EAASxkB,KAAKknB,UAAUU,GAAcpD,OAAOxkB,KAAKjC,SAClDymB,GAA4B,iBAAXA,IAAqBA,EAAS,CAACA,IAChDA,IAAQmD,EAAWA,EAAStd,OAAOma,GAC/C,KAEImD,EAAWA,EAAS3a,QAAO6a,IAAKA,kBAlXb,iBADPC,EAmX6DD,IA/WvD,CAAC,kBAAmB,uBAAwB,uBAAwB,2BAA4B,kBAAmB,gBAAiB,mBAAoB,aAAc,cAAe,oBAAqB,wBAAyB,oBAAqB,cACzPE,MAAKhhB,GAAWA,EAAQsG,KAAKya,MALlD,IAAgBA,CAmX+D,IAAE3a,KAAI0a,GAAK7nB,KAAKjC,QAAQupB,wBAAwBO,KACvH7nB,KAAKnD,UAAYmD,KAAKnD,SAASyT,eAAiBtQ,KAAKnD,SAASyT,cAAcwE,sBAA8B6S,EACvGA,EAASjmB,OAAS,EAAIimB,EAAS,GAAK,IAC/C,CACE,iBAAAhG,CAAkB7jB,GAChB,IAAIspB,EAASpZ,UAAUtM,OAAS,QAAsB,IAAjBsM,UAAU,GAAmBA,UAAU,GAAKhO,KAAKjC,QAAQqpB,OACzFA,IACDpnB,KAAKjC,QAAQspB,iBAAmBrnB,KAAKjC,QAAQspB,gBAAgBppB,QAAQH,IAAW,GACpFspB,EAAOlpB,SAAqB8pB,IACtBhoB,KAAKknB,UAAUc,IAAYhoB,KAAKknB,UAAUc,GAAWrG,kBAAkB7jB,EAAKkC,KAAKjC,QAAO,IAElG,EAEAkpB,GAAQ1nB,KAAO", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}
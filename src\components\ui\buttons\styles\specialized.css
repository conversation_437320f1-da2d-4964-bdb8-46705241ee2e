/* ===== SPECIALIZED BUTTON STYLES ===== */
/* Estilos para BackToTopButton.tsx, ContentButtons.tsx, PaginationButton.tsx */

/* ===== BACK TO TOP BUTTON ===== */
.button-back-to-top {
  /* Dimensions */
  width: 48px;
  height: 48px;
  
  /* Position */
  position: fixed;
  bottom: 24px;
  left: 24px;
  z-index: 40;
  
  /* Appearance */
  border-radius: 50%;
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  color: var(--color-text);
  
  /* Effects */
  box-shadow: var(--button-shadow-hover);
  transition: var(--button-transition);
}

.button-back-to-top:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 8px 16px rgba(var(--primary), 0.3);
}

.button-back-to-top:active {
  transform: translateY(0) scale(1);
  box-shadow: var(--button-shadow);
}

/* ===== EXPAND/COLLAPSE BUTTONS ===== */
.button-expand,
.button-collapse {
  /* Dimensions */
  min-height: var(--button-height-md);
  padding: var(--button-padding-md);
  gap: 8px;
  
  /* Typography */
  font-size: 14px;
  font-weight: 500;
  
  /* Appearance */
  border-radius: var(--button-border-radius);
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  color: var(--color-text);
  
  /* Effects */
  box-shadow: var(--button-shadow);
  transition: var(--button-transition);
}

.button-expand:hover,
.button-collapse:hover {
  background: var(--color-accent);
  border-color: var(--color-primary);
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--button-shadow-hover);
}

.button-expand:active,
.button-collapse:active {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--button-shadow-active);
}

/* Expand/Collapse Icon Animations */
.button-expand .chevron-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-expand:hover .chevron-icon {
  transform: translateY(2px);
}

.button-collapse .chevron-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-collapse:hover .chevron-icon {
  transform: translateY(-2px);
}

/* ===== PAGINATION BUTTONS ===== */
.button-pagination {
  /* Dimensions */
  min-height: var(--button-height-md);
  padding: var(--button-padding-md);
  gap: 8px;
  
  /* Typography */
  font-size: 14px;
  font-weight: 500;
  
  /* Appearance */
  border-radius: var(--button-border-radius);
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  color: var(--color-text);
  
  /* Effects */
  box-shadow: var(--button-shadow);
  transition: var(--button-transition);
}

.button-pagination:hover:not(:disabled) {
  background: var(--color-accent);
  border-color: var(--color-primary);
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--button-shadow-hover);
}

.button-pagination:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--button-shadow-active);
}

.button-pagination:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--button-shadow) !important;
}

/* Pagination Direction Styles */
.button-pagination.direction-previous .chevron-icon {
  transform: rotate(180deg);
}

.button-pagination.direction-next .chevron-icon {
  transform: rotate(0deg);
}

/* Pagination Icon Animations */
.button-pagination:hover:not(:disabled) .chevron-icon {
  animation: pagination-bounce 0.6s ease-in-out;
}

@keyframes pagination-bounce {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(4px); }
}

.button-pagination.direction-previous:hover:not(:disabled) .chevron-icon {
  animation: pagination-bounce-left 0.6s ease-in-out;
}

@keyframes pagination-bounce-left {
  0%, 100% { transform: rotate(180deg) translateX(0); }
  50% { transform: rotate(180deg) translateX(4px); }
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
  .button-back-to-top {
    bottom: 80px; /* Above mobile navigation */
    left: 16px;
    width: 44px;
    height: 44px;
  }
  
  .button-expand,
  .button-collapse,
  .button-pagination {
    min-height: 44px; /* Touch target minimum */
  }
}

/* ===== DARK MODE ===== */
.dark .button-back-to-top,
.dark .button-expand,
.dark .button-collapse,
.dark .button-pagination {
  border-color: var(--color-border);
  background: var(--color-surface);
}

.dark .button-back-to-top:hover,
.dark .button-expand:hover,
.dark .button-collapse:hover,
.dark .button-pagination:hover:not(:disabled) {
  background: var(--color-accent);
  border-color: var(--color-primary);
}

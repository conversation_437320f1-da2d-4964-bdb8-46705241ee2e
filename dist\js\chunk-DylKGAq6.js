var t,e,s,i,n,r,a,o,u,l,h,c,d,f,p,y,v,m,g,b,w,O,M,P,S,q,k,F,C,E,A,Q,D,x,R,W,K,j,T,U,H,G=t=>{throw TypeError(t)},L=(t,e,s)=>e.has(t)||G("Cannot "+s),I=(t,e,s)=>(L(t,e,"read from private field"),s?s.call(t):e.get(t)),_=(t,e,s)=>e.has(t)?G("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),N=(t,e,s,i)=>(L(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),B=(t,e,s)=>(L(t,e,"access private method"),s),$=(t,e,s,i)=>({set _(i){N(t,e,i,s)},get _(){return I(t,e,i)}});import{r as z}from"./chunk-D3Ns84uO.js";import{j as J}from"./chunk-L3uak9dD.js";var V=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},X="undefined"==typeof window||"Deno"in globalThis;function Y(){}function Z(t,e){return"function"==typeof t?t(e):t}function tt(t,e){const{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==st(a,e.options))return!1}else if(!nt(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!n||n===e.state.fetchStatus)&&!(r&&!r(e)))}function et(t,e){const{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(it(e.options.mutationKey)!==it(r))return!1}else if(!nt(e.options.mutationKey,r))return!1}return(!i||e.state.status===i)&&!(n&&!n(e))}function st(t,e){return((null==e?void 0:e.queryKeyHashFn)||it)(t)}function it(t){return JSON.stringify(t,((t,e)=>ot(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function nt(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every((s=>nt(t[s],e[s]))))}function rt(t,e){if(t===e)return t;const s=at(t)&&at(e);if(s||ot(t)&&ot(e)){const i=s?t:Object.keys(t),n=i.length,r=s?e:Object.keys(e),a=r.length,o=s?[]:{};let u=0;for(let l=0;l<a;l++){const n=s?l:r[l];(!s&&i.includes(n)||s)&&void 0===t[n]&&void 0===e[n]?(o[n]=void 0,u++):(o[n]=rt(t[n],e[n]),o[n]===t[n]&&void 0!==t[n]&&u++)}return n===a&&u===n?t:o}return e}function at(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function ot(t){if(!ut(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!ut(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function ut(t){return"[object Object]"===Object.prototype.toString.call(t)}function lt(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?rt(t,e):e}function ht(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function ct(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var dt=Symbol();function ft(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==dt?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var pt=new(i=class extends V{constructor(){super(),_(this,t),_(this,e),_(this,s),N(this,s,(t=>{if(!X&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){I(this,e)||this.setEventListener(I(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=I(this,e))||t.call(this),N(this,e,void 0))}setEventListener(t){var i;N(this,s,t),null==(i=I(this,e))||i.call(this),N(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){I(this,t)!==e&&(N(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){var e;return"boolean"==typeof I(this,t)?I(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,e=new WeakMap,s=new WeakMap,i),yt=new(o=class extends V{constructor(){super(),_(this,n,!0),_(this,r),_(this,a),N(this,a,(t=>{if(!X&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}))}onSubscribe(){I(this,r)||this.setEventListener(I(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=I(this,r))||t.call(this),N(this,r,void 0))}setEventListener(t){var e;N(this,a,t),null==(e=I(this,r))||e.call(this),N(this,r,t(this.setOnline.bind(this)))}setOnline(t){I(this,n)!==t&&(N(this,n,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return I(this,n)}},n=new WeakMap,r=new WeakMap,a=new WeakMap,o);function vt(t){return Math.min(1e3*2**t,3e4)}function mt(t){return"online"!==(t??"online")||yt.isOnline()}var gt=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function bt(t){return t instanceof gt}function wt(t){let e,s=!1,i=0,n=!1;const r=function(){let t,e;const s=new Promise(((s,i)=>{t=s,e=i}));function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}(),a=()=>pt.isFocused()&&("always"===t.networkMode||yt.isOnline())&&t.canRun(),o=()=>mt(t.networkMode)&&t.canRun(),u=s=>{var i;n||(n=!0,null==(i=t.onSuccess)||i.call(t,s),null==e||e(),r.resolve(s))},l=s=>{var i;n||(n=!0,null==(i=t.onError)||i.call(t,s),null==e||e(),r.reject(s))},h=()=>new Promise((s=>{var i;e=t=>{(n||a())&&s(t)},null==(i=t.onPause)||i.call(t)})).then((()=>{var s;e=void 0,n||null==(s=t.onContinue)||s.call(t)})),c=()=>{if(n)return;let e;const r=0===i?t.initialPromise:void 0;try{e=r??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(u).catch((e=>{var r;if(n)return;const o=t.retry??(X?0:3),u=t.retryDelay??vt,d="function"==typeof u?u(i,e):u,f=!0===o||"number"==typeof o&&i<o||"function"==typeof o&&o(i,e);var p;!s&&f?(i++,null==(r=t.onFail)||r.call(t,i,e),(p=d,new Promise((t=>{setTimeout(t,p)}))).then((()=>a()?void 0:h())).then((()=>{s?l(e):c()}))):l(e)}))};return{promise:r,cancel:e=>{var s;n||(l(new gt(e)),null==(s=t.abort)||s.call(t))},continue:()=>(null==e||e(),r),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:o,start:()=>(o()?c():h().then(c),r)}}var Ot=t=>setTimeout(t,0);var Mt=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=Ot;const r=i=>{e?t.push(i):n((()=>{s(i)}))};return{batch:r=>{let a;e++;try{a=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),Pt=(l=class{constructor(){_(this,u)}destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&N(this,u,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(X?1/0:3e5))}clearGcTimeout(){I(this,u)&&(clearTimeout(I(this,u)),N(this,u,void 0))}},u=new WeakMap,l),St=(b=class extends Pt{constructor(t){super(),_(this,m),_(this,h),_(this,c),_(this,d),_(this,f),_(this,p),_(this,y),_(this,v),N(this,v,!1),N(this,y,t.defaultOptions),this.setOptions(t.options),this.observers=[],N(this,f,t.client),N(this,d,I(this,f).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,N(this,h,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??I(this,h),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return null==(t=I(this,p))?void 0:t.promise}setOptions(t){this.options={...I(this,y),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||I(this,d).remove(this)}setData(t,e){const s=lt(this.state.data,t,this.options);return B(this,m,g).call(this,{data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),s}setState(t,e){B(this,m,g).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,s;const i=null==(e=I(this,p))?void 0:e.promise;return null==(s=I(this,p))||s.cancel(t),i?i.then(Y).catch(Y):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(I(this,h))}isActive(){return this.observers.some((t=>{return!1!==(e=t.options.enabled,s=this,"function"==typeof e?e(s):e);var e,s}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===dt||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!function(t,e){return Math.max(t+(e||0)-Date.now(),0)}(this.state.dataUpdatedAt,t)}onFocus(){var t;const e=this.observers.find((t=>t.shouldFetchOnWindowFocus()));null==e||e.refetch({cancelRefetch:!1}),null==(t=I(this,p))||t.continue()}onOnline(){var t;const e=this.observers.find((t=>t.shouldFetchOnReconnect()));null==e||e.refetch({cancelRefetch:!1}),null==(t=I(this,p))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),I(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(I(this,p)&&(I(this,v)?I(this,p).cancel({revert:!0}):I(this,p).cancelRetry()),this.scheduleGc()),I(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||B(this,m,g).call(this,{type:"invalidate"})}fetch(t,e){var s,i,n;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(I(this,p))return I(this,p).continueRetry(),I(this,p).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const r=new AbortController,a=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(N(this,v,!0),r.signal)})},o={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:I(this,f),state:this.state,fetchFn:()=>{const t=ft(this.options,e),s={client:I(this,f),queryKey:this.queryKey,meta:this.meta};return a(s),N(this,v,!1),this.options.persister?this.options.persister(t,s,this):t(s)}};a(o),null==(s=this.options.behavior)||s.onFetch(o,this),N(this,c,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(i=o.fetchOptions)?void 0:i.meta)||B(this,m,g).call(this,{type:"fetch",meta:null==(n=o.fetchOptions)?void 0:n.meta});const u=t=>{var e,s,i,n;bt(t)&&t.silent||B(this,m,g).call(this,{type:"error",error:t}),bt(t)||(null==(s=(e=I(this,d).config).onError)||s.call(e,t,this),null==(n=(i=I(this,d).config).onSettled)||n.call(i,this.state.data,t,this)),this.scheduleGc()};return N(this,p,wt({initialPromise:null==e?void 0:e.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{var e,s,i,n;if(void 0!==t){try{this.setData(t)}catch(r){return void u(r)}null==(s=(e=I(this,d).config).onSuccess)||s.call(e,t,this),null==(n=(i=I(this,d).config).onSettled)||n.call(i,t,this.state.error,this),this.scheduleGc()}else u(new Error(`${this.queryHash} data is undefined`))},onError:u,onFail:(t,e)=>{B(this,m,g).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{B(this,m,g).call(this,{type:"pause"})},onContinue:()=>{B(this,m,g).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),I(this,p).start()}},h=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,y=new WeakMap,v=new WeakMap,m=new WeakSet,g=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...(s=e.data,i=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:mt(i.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return bt(n)&&n.revert&&I(this,c)?{...I(this,c),fetchStatus:"idle"}:{...e,error:n,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}var s,i})(this.state),Mt.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),I(this,d).notify({query:this,type:"updated",action:t})}))},b);var qt=(O=class extends V{constructor(t={}){super(),_(this,w),this.config=t,N(this,w,new Map)}build(t,e,s){const i=e.queryKey,n=e.queryHash??st(i,e);let r=this.get(n);return r||(r=new St({client:t,queryKey:i,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(r)),r}add(t){I(this,w).has(t.queryHash)||(I(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=I(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&I(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Mt.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return I(this,w).get(t)}getAll(){return[...I(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>tt(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>tt(t,e))):e}notify(t){Mt.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){Mt.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){Mt.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,O),kt=(F=class extends Pt{constructor(t){super(),_(this,q),_(this,M),_(this,P),_(this,S),this.mutationId=t.mutationId,N(this,P,t.mutationCache),N(this,M,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){I(this,M).includes(t)||(I(this,M).push(t),this.clearGcTimeout(),I(this,P).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){N(this,M,I(this,M).filter((e=>e!==t))),this.scheduleGc(),I(this,P).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){I(this,M).length||("pending"===this.state.status?this.scheduleGc():I(this,P).remove(this))}continue(){var t;return(null==(t=I(this,S))?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var e,s,i,n,r,a,o,u,l,h,c,d,f,p,y,v,m,g,b,w;const O=()=>{B(this,q,k).call(this,{type:"continue"})};N(this,S,wt({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{B(this,q,k).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{B(this,q,k).call(this,{type:"pause"})},onContinue:O,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>I(this,P).canRun(this)}));const M="pending"===this.state.status,F=!I(this,S).canStart();try{if(M)O();else{B(this,q,k).call(this,{type:"pending",variables:t,isPaused:F}),await(null==(s=(e=I(this,P).config).onMutate)?void 0:s.call(e,t,this));const r=await(null==(n=(i=this.options).onMutate)?void 0:n.call(i,t));r!==this.state.context&&B(this,q,k).call(this,{type:"pending",context:r,variables:t,isPaused:F})}const f=await I(this,S).start();return await(null==(a=(r=I(this,P).config).onSuccess)?void 0:a.call(r,f,t,this.state.context,this)),await(null==(u=(o=this.options).onSuccess)?void 0:u.call(o,f,t,this.state.context)),await(null==(h=(l=I(this,P).config).onSettled)?void 0:h.call(l,f,null,this.state.variables,this.state.context,this)),await(null==(d=(c=this.options).onSettled)?void 0:d.call(c,f,null,t,this.state.context)),B(this,q,k).call(this,{type:"success",data:f}),f}catch(C){try{throw await(null==(p=(f=I(this,P).config).onError)?void 0:p.call(f,C,t,this.state.context,this)),await(null==(v=(y=this.options).onError)?void 0:v.call(y,C,t,this.state.context)),await(null==(g=(m=I(this,P).config).onSettled)?void 0:g.call(m,void 0,C,this.state.variables,this.state.context,this)),await(null==(w=(b=this.options).onSettled)?void 0:w.call(b,void 0,C,t,this.state.context)),C}finally{B(this,q,k).call(this,{type:"error",error:C})}}finally{I(this,P).runNext(this)}}},M=new WeakMap,P=new WeakMap,S=new WeakMap,q=new WeakSet,k=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),Mt.batch((()=>{I(this,M).forEach((e=>{e.onMutationUpdate(t)})),I(this,P).notify({mutation:this,type:"updated",action:t})}))},F);var Ft=(Q=class extends V{constructor(t={}){super(),_(this,C),_(this,E),_(this,A),this.config=t,N(this,C,new Set),N(this,E,new Map),N(this,A,0)}build(t,e,s){const i=new kt({mutationCache:this,mutationId:++$(this,A)._,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){I(this,C).add(t);const e=Ct(t);if("string"==typeof e){const s=I(this,E).get(e);s?s.push(t):I(this,E).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(I(this,C).delete(t)){const e=Ct(t);if("string"==typeof e){const s=I(this,E).get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&I(this,E).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=Ct(t);if("string"==typeof e){const s=I(this,E).get(e),i=null==s?void 0:s.find((t=>"pending"===t.state.status));return!i||i===t}return!0}runNext(t){var e;const s=Ct(t);if("string"==typeof s){const i=null==(e=I(this,E).get(s))?void 0:e.find((e=>e!==t&&e.state.isPaused));return(null==i?void 0:i.continue())??Promise.resolve()}return Promise.resolve()}clear(){Mt.batch((()=>{I(this,C).forEach((t=>{this.notify({type:"removed",mutation:t})})),I(this,C).clear(),I(this,E).clear()}))}getAll(){return Array.from(I(this,C))}find(t){const e={exact:!0,...t};return this.getAll().find((t=>et(e,t)))}findAll(t={}){return this.getAll().filter((e=>et(t,e)))}notify(t){Mt.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return Mt.batch((()=>Promise.all(t.map((t=>t.continue().catch(Y))))))}},C=new WeakMap,E=new WeakMap,A=new WeakMap,Q);function Ct(t){var e;return null==(e=t.options.scope)?void 0:e.id}function Et(t){return{onFetch:(e,s)=>{var i,n,r,a,o;const u=e.options,l=null==(r=null==(n=null==(i=e.fetchOptions)?void 0:i.meta)?void 0:n.fetchMore)?void 0:r.direction,h=(null==(a=e.state.data)?void 0:a.pages)||[],c=(null==(o=e.state.data)?void 0:o.pageParams)||[];let d={pages:[],pageParams:[]},f=0;const p=async()=>{let s=!1;const i=ft(e.options,e.fetchOptions),n=async(t,n,r)=>{if(s)return Promise.reject();if(null==n&&t.pages.length)return Promise.resolve(t);const a={client:e.client,queryKey:e.queryKey,pageParam:n,direction:r?"backward":"forward",meta:e.options.meta};var o;o=a,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",(()=>{s=!0})),e.signal)});const u=await i(a),{maxPages:l}=e.options,h=r?ct:ht;return{pages:h(t.pages,u,l),pageParams:h(t.pageParams,n,l)}};if(l&&h.length){const t="backward"===l,e={pages:h,pageParams:c},s=(t?Qt:At)(u,e);d=await n(e,s,t)}else{const e=t??h.length;do{const t=0===f?c[0]??u.initialPageParam:At(u,d);if(f>0&&null==t)break;d=await n(d,t),f++}while(f<e)}return d};e.options.persister?e.fetchFn=()=>{var t,i;return null==(i=(t=e.options).persister)?void 0:i.call(t,p,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=p}}}function At(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function Qt(t,{pages:e,pageParams:s}){var i;return e.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,e[0],e,s[0],s):void 0}var Dt=(H=class{constructor(t={}){_(this,D),_(this,x),_(this,R),_(this,W),_(this,K),_(this,j),_(this,T),_(this,U),N(this,D,t.queryCache||new qt),N(this,x,t.mutationCache||new Ft),N(this,R,t.defaultOptions||{}),N(this,W,new Map),N(this,K,new Map),N(this,j,0)}mount(){$(this,j)._++,1===I(this,j)&&(N(this,T,pt.subscribe((async t=>{t&&(await this.resumePausedMutations(),I(this,D).onFocus())}))),N(this,U,yt.subscribe((async t=>{t&&(await this.resumePausedMutations(),I(this,D).onOnline())}))))}unmount(){var t,e;$(this,j)._--,0===I(this,j)&&(null==(t=I(this,T))||t.call(this),N(this,T,void 0),null==(e=I(this,U))||e.call(this),N(this,U,void 0))}isFetching(t){return I(this,D).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return I(this,x).findAll({...t,status:"pending"}).length}getQueryData(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=I(this,D).get(s.queryHash))?void 0:e.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=I(this,D).build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(Z(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return I(this,D).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),n=I(this,D).get(i.queryHash),r=function(t,e){return"function"==typeof t?t(e):t}(e,null==n?void 0:n.state.data);if(void 0!==r)return I(this,D).build(this,i).setData(r,{...s,manual:!0})}setQueriesData(t,e,s){return Mt.batch((()=>I(this,D).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=I(this,D).get(s.queryHash))?void 0:e.state}removeQueries(t){const e=I(this,D);Mt.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=I(this,D);return Mt.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries({type:"active",...t},e))))}cancelQueries(t,e={}){const s={revert:!0,...e},i=Mt.batch((()=>I(this,D).findAll(t).map((t=>t.cancel(s)))));return Promise.all(i).then(Y).catch(Y)}invalidateQueries(t,e={}){return Mt.batch((()=>(I(this,D).findAll(t).forEach((t=>{t.invalidate()})),"none"===(null==t?void 0:t.refetchType)?Promise.resolve():this.refetchQueries({...t,type:(null==t?void 0:t.refetchType)??(null==t?void 0:t.type)??"active"},e))))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},i=Mt.batch((()=>I(this,D).findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(Y)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(Y)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=I(this,D).build(this,e);return s.isStaleByTime(Z(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Y).catch(Y)}fetchInfiniteQuery(t){return t.behavior=Et(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Y).catch(Y)}ensureInfiniteQueryData(t){return t.behavior=Et(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return yt.isOnline()?I(this,x).resumePausedMutations():Promise.resolve()}getQueryCache(){return I(this,D)}getMutationCache(){return I(this,x)}getDefaultOptions(){return I(this,R)}setDefaultOptions(t){N(this,R,t)}setQueryDefaults(t,e){I(this,W).set(it(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...I(this,W).values()],s={};return e.forEach((e=>{nt(t,e.queryKey)&&Object.assign(s,e.defaultOptions)})),s}setMutationDefaults(t,e){I(this,K).set(it(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...I(this,K).values()],s={};return e.forEach((e=>{nt(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...I(this,R).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=st(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===dt&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:{...I(this,R).mutations,...(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){I(this,D).clear(),I(this,x).clear()}},D=new WeakMap,x=new WeakMap,R=new WeakMap,W=new WeakMap,K=new WeakMap,j=new WeakMap,T=new WeakMap,U=new WeakMap,H),xt=z.createContext(void 0),Rt=({client:t,children:e})=>(z.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),J.jsx(xt.Provider,{value:t,children:e}));export{Dt as Q,Rt as a};
//# sourceMappingURL=chunk-DylKGAq6.js.map

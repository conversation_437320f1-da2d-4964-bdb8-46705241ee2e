const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/chunk-C9WIJbrP.js","js/chunk-DJqUFuPP.js","js/chunk-DzkTCSaD.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,o,i)=>((t,o,i)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[o]=i)(t,"symbol"!=typeof o?o+"":o,i);import{j as o}from"./chunk-L3uak9dD.js";import{r as i}from"./chunk-D3Ns84uO.js";import{_ as n,S as r,A as s}from"./index-C6NRgFfu.js";import{C as c}from"./chunk-DzkTCSaD.js";import"./chunk-DJqUFuPP.js";import"./chunk-DU4M193A.js";import"./chunk-BxTnlCnP.js";import"./chunk-DylKGAq6.js";import"./chunk-CS50z45r.js";class a{constructor(){t(this,"originalXMLHttpRequest"),t(this,"originalFetch"),this.originalXMLHttpRequest=window.XMLHttpRequest,this.originalFetch=window.fetch}install(){this.interceptXMLHttpRequest(),this.interceptFetch()}uninstall(){this.originalXMLHttpRequest&&(window.XMLHttpRequest=this.originalXMLHttpRequest),this.originalFetch&&(window.fetch=this.originalFetch)}interceptXMLHttpRequest(){const e=this.originalXMLHttpRequest,t=r;window.XMLHttpRequest=function(...o){const i=new e(...o),n=i.open;return i.open=function(e,o,...i){if("string"==typeof o)try{if(t.matchesAnalyticsPattern(o,"wootric")){if(!t.validateAnalyticsUrl(o))return void console.warn("SecureLogRocket: Blocked potentially unsafe Wootric URL:",o)}else if(t.matchesAnalyticsPattern(o,"delighted")){if(!t.validateAnalyticsUrl(o))return void console.warn("SecureLogRocket: Blocked potentially unsafe Delighted URL:",o)}else if(t.matchesAnalyticsPattern(o,"logrocket")&&!t.validateAnalyticsUrl(o))return void console.warn("SecureLogRocket: Blocked potentially unsafe LogRocket URL:",o)}catch(r){return void console.warn("SecureLogRocket: Error validating URL:",r)}return n.call(this,e,o,...i)},i},Object.setPrototypeOf(window.XMLHttpRequest,e),Object.defineProperty(window.XMLHttpRequest,"prototype",{value:e.prototype,writable:!1})}interceptFetch(){const e=this.originalFetch,t=r;window.fetch=function(o,i){let n;try{if(n="string"==typeof o?o:o instanceof URL?o.href:o instanceof Request?o.url:String(o),(t.matchesAnalyticsPattern(n,"wootric")||t.matchesAnalyticsPattern(n,"delighted")||t.matchesAnalyticsPattern(n,"logrocket"))&&!t.validateAnalyticsUrl(n))return console.warn("SecureLogRocket: Blocked potentially unsafe analytics URL:",n),Promise.reject(new Error("URL validation failed"))}catch(r){console.warn("SecureLogRocket: Error validating fetch URL:",r)}return e.call(this,o,i)}}}const l=new class{constructor(){t(this,"logRocket",null),t(this,"interceptor"),t(this,"isInitialized",!1),this.interceptor=new a}async init(e,t={}){if(this.isInitialized)console.warn("SecureLogRocket: Already initialized");else try{this.interceptor.install(),this.preventDeprecatedEventListeners();const o=await n((()=>import("./chunk-C9WIJbrP.js").then((e=>e.b))),__vite__mapDeps([0,1,2]));this.logRocket=o.default;const i={shouldAugmentNPS:!1,shouldParseXHRBlob:!1,network:{isEnabled:!0},...t};this.logRocket.init(e,i),this.isInitialized=!0}catch(o){throw console.error("SecureLogRocket: Failed to initialize:",o),this.interceptor.uninstall(),o}}identify(e,t){if(this.logRocket)try{const o=t?this.sanitizeUserInfo(t):void 0;this.logRocket.identify(e,o)}catch(o){console.error("SecureLogRocket: Error identifying user:",o)}else console.warn("SecureLogRocket: Not initialized")}track(e,t){if(this.logRocket)try{const o=t?this.sanitizeUserInfo(t):void 0;this.logRocket.track(e,o)}catch(o){console.error("SecureLogRocket: Error tracking event:",o)}else console.warn("SecureLogRocket: Not initialized")}getSessionURL(e){if(!this.logRocket)return console.warn("SecureLogRocket: Not initialized"),void e("");try{this.logRocket.getSessionURL((t=>{if(t&&"string"==typeof t&&t.length>0)try{const o=new URL(t);o.hostname.includes("logrocket.com")||o.hostname.includes("logrocket.io")?e(t):(console.warn("SecureLogRocket: Invalid session URL domain:",o.hostname),e(""))}catch(o){console.warn("SecureLogRocket: Invalid session URL format:",t),e("")}else console.warn("SecureLogRocket: Empty or invalid session URL received"),e("")}))}catch(t){console.error("SecureLogRocket: Error getting session URL:",t),e("")}}captureMessage(e,t){if(this.logRocket)try{const o=r.sanitizeString(e),i=t?this.sanitizeUserInfo(t):void 0;this.logRocket.captureMessage(o,i)}catch(o){console.error("SecureLogRocket: Error capturing message:",o)}else console.warn("SecureLogRocket: Not initialized")}captureException(e,t){if(this.logRocket)try{const o=t?this.sanitizeUserInfo(t):void 0;this.logRocket.captureException(e,o)}catch(o){console.error("SecureLogRocket: Error capturing exception:",o)}else console.warn("SecureLogRocket: Not initialized")}preventDeprecatedEventListeners(){const e=window.addEventListener;window.addEventListener=function(t,o,i){return"unload"===t?(console.warn('SecureLogRocket: Replacing deprecated "unload" event with "pagehide"'),e.call(this,"pagehide",o,i)):"beforeunload"===t?(console.warn('SecureLogRocket: "beforeunload" event should be used sparingly'),e.call(this,t,o,i)):e.call(this,t,o,i)}}uninstall(){try{this.logRocket&&"function"==typeof this.logRocket.uninstall&&this.logRocket.uninstall(),this.interceptor.uninstall(),this.logRocket=null,this.isInitialized=!1}catch(e){console.error("SecureLogRocket: Error during uninstall:",e)}}sanitizeUserInfo(e){const t={};for(const[o,i]of Object.entries(e)){const e=r.sanitizeString(String(o));t[e]="string"==typeof i?r.sanitizeString(i):"number"==typeof i||"boolean"==typeof i||null==i?i:r.sanitizeString(JSON.stringify(i))}return t}get isReady(){return this.isInitialized&&null!==this.logRocket}get instance(){return this.logRocket}},d=()=>(i.useEffect((()=>{try{const e="rp64ayubme";c.init(e),c.identify("tarcisio-portfolio",void 0,void 0,"Tarcísio Bispo Portfolio"),c.setTag("portfolio_owner","Tarcisio Bispo de Araujo"),c.setTag("portfolio_type","UX/Product Designer"),c.setTag("contact_email","<EMAIL>"),c.setTag("portfolio_version","2024"),c.setTag("site_language","multi"),c.event("portfolio_initialized")}catch(e){console.error("Failed to initialize Microsoft Clarity:",e)}}),[]),null),u=({gtmId:e})=>(i.useEffect((()=>{if("undefined"!=typeof window&&e){const t=document.createElement("script");return t.innerHTML=`\n        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n        })(window,document,'script','dataLayer','${e}');\n      `,document.head.appendChild(t),()=>{document.head.contains(t)&&document.head.removeChild(t)}}}),[e]),null),h=({gtmId:e})=>(i.useEffect((()=>{if("undefined"!=typeof window&&e){const t=document.createElement("noscript");return t.innerHTML=`\n        <iframe src="https://www.googletagmanager.com/ns.html?id=${e}"\n        height="0" width="0" style="display:none;visibility:hidden"></iframe>\n      `,document.body.insertBefore(t,document.body.firstChild),()=>{document.body.contains(t)&&document.body.removeChild(t)}}}),[e]),null),g=({children:e})=>(i.useEffect((()=>{try{l.init("fatqpp/portfolio-kbfin",{shouldAugmentNPS:!1,shouldParseXHRBlob:!1,network:{isEnabled:!0}}).then((()=>{l.getSessionURL((e=>{})),l.track("Portfolio Visit",{portfolioOwner:"Tarcisio Bispo de Araujo",ownerEmail:"<EMAIL>",portfolioType:"UX/Product Designer",version:"2024"})})).catch((e=>{console.error("Failed to initialize secure LogRocket:",e)}))}catch(e){}}),[]),o.jsxs(o.Fragment,{children:[o.jsx(u,{gtmId:s.GTM_ID}),o.jsx(h,{gtmId:s.GTM_ID}),o.jsx(d,{}),e]}));export{g as default};
//# sourceMappingURL=AnalyticsProvider-Da6roXPw.js.map

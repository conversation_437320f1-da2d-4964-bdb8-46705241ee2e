/**
 * 🎯 SISTEMA RESPONSIVO
 * 
 * Media queries e breakpoints unificados
 * Mobile-first approach
 * Performance otimizada
 */

/* ===== BREAKPOINTS ===== */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== MOBILE FIRST (BASE) ===== */
/* Estilos base para mobile (< 640px) */

.container {
  width: 100%;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

.section {
  padding: var(--space-2xl) 0;
}

.section-title {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-lg);
}

.grid-responsive {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-md);
}

/* ===== SMALL DEVICES (≥640px) ===== */
@media (min-width: 640px) {
  .container {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }
  
  .section-title {
    font-size: var(--font-size-3xl);
  }
  
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
  }
  
  /* SM utilities */
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
  .sm\:inline { display: inline; }
  
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
  
  .sm\:text-left { text-align: left; }
  .sm\:text-center { text-align: center; }
  .sm\:text-right { text-align: right; }
  
  .sm\:w-auto { width: auto; }
  .sm\:w-full { width: 100%; }
  
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
}

/* ===== MEDIUM DEVICES (≥768px) ===== */
@media (min-width: 768px) {
  .container {
    max-width: 768px;
    margin: 0 auto;
    padding-left: var(--space-xl);
    padding-right: var(--space-xl);
  }
  
  .section {
    padding: var(--space-3xl) 0;
  }
  
  .section-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-xl);
  }
  
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-xl);
  }
  
  /* MD utilities */
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:inline { display: inline; }
  
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
  
  .md\:text-left { text-align: left; }
  .md\:text-center { text-align: center; }
  .md\:text-right { text-align: right; }
  
  .md\:w-auto { width: auto; }
  .md\:w-full { width: 100%; }
  .md\:w-1\/2 { width: 50%; }
  .md\:w-1\/3 { width: 33.333333%; }
  .md\:w-2\/3 { width: 66.666667%; }
  
  .md\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

/* ===== LARGE DEVICES (≥1024px) ===== */
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: var(--space-2xl);
    padding-right: var(--space-2xl);
  }
  
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  /* LG utilities */
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:inline { display: inline; }
  
  .lg\:flex-row { flex-direction: row; }
  .lg\:flex-col { flex-direction: column; }
  
  .lg\:text-left { text-align: left; }
  .lg\:text-center { text-align: center; }
  .lg\:text-right { text-align: right; }
  
  .lg\:w-auto { width: auto; }
  .lg\:w-full { width: 100%; }
  .lg\:w-1\/2 { width: 50%; }
  .lg\:w-1\/3 { width: 33.333333%; }
  .lg\:w-2\/3 { width: 66.666667%; }
  .lg\:w-1\/4 { width: 25%; }
  .lg\:w-3\/4 { width: 75%; }
  
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* ===== EXTRA LARGE DEVICES (≥1280px) ===== */
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
  
  /* XL utilities */
  .xl\:block { display: block; }
  .xl\:hidden { display: none; }
  .xl\:flex { display: flex; }
  .xl\:grid { display: grid; }
  
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* ===== 2XL DEVICES (≥1536px) ===== */
@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
  
  /* 2XL utilities */
  .2xl\:block { display: block; }
  .2xl\:hidden { display: none; }
  .2xl\:flex { display: flex; }
  .2xl\:grid { display: grid; }
  
  .2xl\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .2xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .2xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .2xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .2xl\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .2xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

/* ===== ORIENTAÇÃO ===== */
@media (orientation: portrait) {
  .portrait\:hidden { display: none; }
  .portrait\:block { display: block; }
}

@media (orientation: landscape) {
  .landscape\:hidden { display: none; }
  .landscape\:block { display: block; }
}

/* ===== DENSIDADE DE PIXELS ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina\:hidden { display: none; }
  .retina\:block { display: block; }
}

/* ===== HOVER SUPPORT ===== */
@media (hover: hover) {
  .hover-supported\:hover\:opacity-75:hover { opacity: 0.75; }
  .hover-supported\:hover\:scale-105:hover { transform: scale(1.05); }
}

@media (hover: none) {
  .no-hover\:active\:opacity-75:active { opacity: 0.75; }
  .no-hover\:active\:scale-105:active { transform: scale(1.05); }
}

/* ===== PRINT STYLES ===== */
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }
  .print\:text-black { color: #000 !important; }
  .print\:bg-white { background: #fff !important; }
  
  /* Remove shadows and effects for print */
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  /* Ensure good contrast for print */
  .container {
    max-width: none !important;
    padding: 0 !important;
  }
}

/* ===== MOTION PREFERENCES ===== */
@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:transition-none { transition: none !important; }
  .motion-reduce\:animate-none { animation: none !important; }
  
  /* Disable all animations and transitions */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== CONTRAST PREFERENCES ===== */
@media (prefers-contrast: high) {
  .high-contrast\:border-2 { border-width: 2px !important; }
  .high-contrast\:font-bold { font-weight: 700 !important; }
  .high-contrast\:shadow-none { box-shadow: none !important; }
}

/* ===== COLOR SCHEME PREFERENCES ===== */
@media (prefers-color-scheme: dark) {
  .dark-scheme\:hidden { display: none; }
  .dark-scheme\:block { display: block; }
}

@media (prefers-color-scheme: light) {
  .light-scheme\:hidden { display: none; }
  .light-scheme\:block { display: block; }
}

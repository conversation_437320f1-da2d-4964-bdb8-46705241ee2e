/**
 * 🎯 TOAST OPTIMIZATIONS
 * 
 * Otimizações para toasts e notificações
 */

/* ===== TOAST CONTAINER ===== */
.toast-container {
  position: fixed;
  top: var(--space-lg);
  right: var(--space-lg);
  z-index: var(--z-toast);
  max-width: 400px;
}

/* ===== TOAST STYLES ===== */
.toast {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  margin-bottom: var(--space-sm);
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease-out;
}

.toast.success {
  border-left: 4px solid var(--color-secondary);
}

.toast.error {
  border-left: 4px solid #ef4444;
}

.toast.warning {
  border-left: 4px solid #f59e0b;
}

.toast.info {
  border-left: 4px solid var(--color-primary);
}

/* ===== ANIMATIONS ===== */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .toast-container {
    top: var(--space-sm);
    right: var(--space-sm);
    left: var(--space-sm);
    max-width: none;
  }
}

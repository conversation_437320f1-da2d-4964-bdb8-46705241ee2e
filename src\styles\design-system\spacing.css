/**
 * 🎯 SISTEMA DE ESPAÇAMENTO
 * 
 * Espaçamentos baseados nos tokens
 * Escala consistente de 8px
 */

/* ===== ESPAÇAMENTOS SEMÂNTICOS ===== */
:root {
  --space-xs: var(--spacing-1);    /* 4px */
  --space-sm: var(--spacing-2);    /* 8px */
  --space-md: var(--spacing-4);    /* 16px */
  --space-lg: var(--spacing-6);    /* 24px */
  --space-xl: var(--spacing-8);    /* 32px */
  --space-2xl: var(--spacing-12);  /* 48px */
  --space-3xl: var(--spacing-16);  /* 64px */
}

/* ===== MARGIN UTILITIES ===== */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }
.m-10 { margin: var(--spacing-10) !important; }
.m-12 { margin: var(--spacing-12) !important; }
.m-16 { margin: var(--spacing-16) !important; }
.m-20 { margin: var(--spacing-20) !important; }
.m-24 { margin: var(--spacing-24) !important; }
.m-32 { margin: var(--spacing-32) !important; }

.m-auto { margin: auto !important; }

/* Margin X */
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-1) !important; margin-right: var(--spacing-1) !important; }
.mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
.mx-3 { margin-left: var(--spacing-3) !important; margin-right: var(--spacing-3) !important; }
.mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
.mx-6 { margin-left: var(--spacing-6) !important; margin-right: var(--spacing-6) !important; }
.mx-8 { margin-left: var(--spacing-8) !important; margin-right: var(--spacing-8) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

/* Margin Y */
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-1) !important; margin-bottom: var(--spacing-1) !important; }
.my-2 { margin-top: var(--spacing-2) !important; margin-bottom: var(--spacing-2) !important; }
.my-3 { margin-top: var(--spacing-3) !important; margin-bottom: var(--spacing-3) !important; }
.my-4 { margin-top: var(--spacing-4) !important; margin-bottom: var(--spacing-4) !important; }
.my-6 { margin-top: var(--spacing-6) !important; margin-bottom: var(--spacing-6) !important; }
.my-8 { margin-top: var(--spacing-8) !important; margin-bottom: var(--spacing-8) !important; }
.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

/* ===== PADDING UTILITIES ===== */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }
.p-10 { padding: var(--spacing-10) !important; }
.p-12 { padding: var(--spacing-12) !important; }
.p-16 { padding: var(--spacing-16) !important; }
.p-20 { padding: var(--spacing-20) !important; }
.p-24 { padding: var(--spacing-24) !important; }

/* Padding X */
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-1) !important; padding-right: var(--spacing-1) !important; }
.px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.px-6 { padding-left: var(--spacing-6) !important; padding-right: var(--spacing-6) !important; }
.px-8 { padding-left: var(--spacing-8) !important; padding-right: var(--spacing-8) !important; }

/* Padding Y */
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-1) !important; padding-bottom: var(--spacing-1) !important; }
.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }
.py-6 { padding-top: var(--spacing-6) !important; padding-bottom: var(--spacing-6) !important; }
.py-8 { padding-top: var(--spacing-8) !important; padding-bottom: var(--spacing-8) !important; }

/* ===== GAP UTILITIES ===== */
.gap-0 { gap: 0 !important; }
.gap-1 { gap: var(--spacing-1) !important; }
.gap-2 { gap: var(--spacing-2) !important; }
.gap-3 { gap: var(--spacing-3) !important; }
.gap-4 { gap: var(--spacing-4) !important; }
.gap-5 { gap: var(--spacing-5) !important; }
.gap-6 { gap: var(--spacing-6) !important; }
.gap-8 { gap: var(--spacing-8) !important; }
.gap-10 { gap: var(--spacing-10) !important; }
.gap-12 { gap: var(--spacing-12) !important; }
.gap-16 { gap: var(--spacing-16) !important; }

/* Gap X */
.gap-x-0 { column-gap: 0 !important; }
.gap-x-1 { column-gap: var(--spacing-1) !important; }
.gap-x-2 { column-gap: var(--spacing-2) !important; }
.gap-x-3 { column-gap: var(--spacing-3) !important; }
.gap-x-4 { column-gap: var(--spacing-4) !important; }
.gap-x-6 { column-gap: var(--spacing-6) !important; }
.gap-x-8 { column-gap: var(--spacing-8) !important; }

/* Gap Y */
.gap-y-0 { row-gap: 0 !important; }
.gap-y-1 { row-gap: var(--spacing-1) !important; }
.gap-y-2 { row-gap: var(--spacing-2) !important; }
.gap-y-3 { row-gap: var(--spacing-3) !important; }
.gap-y-4 { row-gap: var(--spacing-4) !important; }
.gap-y-6 { row-gap: var(--spacing-6) !important; }
.gap-y-8 { row-gap: var(--spacing-8) !important; }

/* ===== SPACE UTILITIES ===== */
.space-x-0 > * + * { margin-left: 0 !important; }
.space-x-1 > * + * { margin-left: var(--spacing-1) !important; }
.space-x-2 > * + * { margin-left: var(--spacing-2) !important; }
.space-x-3 > * + * { margin-left: var(--spacing-3) !important; }
.space-x-4 > * + * { margin-left: var(--spacing-4) !important; }
.space-x-6 > * + * { margin-left: var(--spacing-6) !important; }
.space-x-8 > * + * { margin-left: var(--spacing-8) !important; }

.space-y-0 > * + * { margin-top: 0 !important; }
.space-y-1 > * + * { margin-top: var(--spacing-1) !important; }
.space-y-2 > * + * { margin-top: var(--spacing-2) !important; }
.space-y-3 > * + * { margin-top: var(--spacing-3) !important; }
.space-y-4 > * + * { margin-top: var(--spacing-4) !important; }
.space-y-6 > * + * { margin-top: var(--spacing-6) !important; }
.space-y-8 > * + * { margin-top: var(--spacing-8) !important; }

/* ===== RESPONSIVE SPACING ===== */
@media (max-width: 768px) {
  .sm\:m-0 { margin: 0 !important; }
  .sm\:m-2 { margin: var(--spacing-2) !important; }
  .sm\:m-4 { margin: var(--spacing-4) !important; }
  .sm\:mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
  .sm\:mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
  
  .sm\:p-0 { padding: 0 !important; }
  .sm\:p-2 { padding: var(--spacing-2) !important; }
  .sm\:p-4 { padding: var(--spacing-4) !important; }
  .sm\:px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
  .sm\:px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
  
  .sm\:gap-2 { gap: var(--spacing-2) !important; }
  .sm\:gap-4 { gap: var(--spacing-4) !important; }
}

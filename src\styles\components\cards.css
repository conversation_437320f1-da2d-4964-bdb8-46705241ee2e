/**
 * 🎯 SISTEMA DE CARDS UNIFICADO
 * 
 * Cards modulares com design system consistente
 * Suporte completo ao modo escuro
 * WCAG 2.2 compliant
 */

/* ===== CARD BASE ===== */
.card {
  background: var(--color-surface);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  position: relative;
  contain: layout style paint;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

/* ===== CARD VARIANTS ===== */
.card-elevated {
  box-shadow: var(--shadow-lg);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
}

.card-flat {
  box-shadow: none;
  border: var(--border-width-thick) solid var(--color-border);
}

.card-glass {
  background: rgba(var(--color-primary-rgb), 0.05);
  backdrop-filter: blur(20px);
  border: var(--border-width) solid rgba(var(--color-primary-rgb), 0.1);
}

/* ===== PROJECT CARDS ===== */
.project-card {
  background: var(--color-surface);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  max-width: 100%;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.project-card-content {
  padding: var(--space-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-sm);
  line-height: var(--line-height-tight);
}

.project-card-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  line-height: var(--line-height-base);
  margin-bottom: var(--space-md);
  flex: 1;
}

.project-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
}

.project-card-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background: var(--color-surface-hover);
  color: var(--color-text-muted);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border: var(--border-width) solid var(--color-border);
  transition: var(--transition-fast);
}

.project-card-tag:hover {
  background: var(--color-surface-active);
  color: var(--color-text);
}

.project-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: var(--space-md);
  border-top: var(--border-width) solid var(--color-border);
}

/* ===== PROFILE CARD ===== */
.profile-card {
  background: var(--color-surface);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius-xl);
  padding: var(--space-2xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: visible;
  backdrop-filter: blur(20px);
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(var(--color-primary-rgb), 0.05) 0%,
    rgba(var(--color-secondary-rgb), 0.05) 100%);
  pointer-events: none;
  border-radius: inherit;
}

.profile-card-avatar {
  width: 128px;
  height: 128px;
  border-radius: var(--border-radius-full);
  margin: 0 auto var(--space-lg);
  border: 4px solid var(--color-primary);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.profile-card-avatar:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.profile-card-name {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-sm);
}

.profile-card-title {
  font-size: var(--font-size-lg);
  color: var(--color-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-medium);
}

.profile-card-bio {
  font-size: var(--font-size-base);
  color: var(--color-text-muted);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

/* ===== CONTACT CARD ===== */
.contact-card {
  background: var(--color-surface);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.contact-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.contact-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-lg);
  text-align: center;
}

/* ===== BACKLOG CARD ===== */
.backlog-card {
  background: var(--color-surface);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.backlog-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-secondary);
}

.backlog-card-header {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: white;
  padding: var(--space-lg);
  text-align: center;
}

.backlog-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-sm);
}

.backlog-card-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.backlog-card-content {
  padding: var(--space-lg);
}

/* ===== CARD GRIDS ===== */
.cards-grid {
  display: grid;
  gap: var(--space-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.projects-grid {
  display: grid;
  gap: var(--space-lg);
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  .card {
    padding: var(--space-md);
  }
  
  .project-card-content {
    padding: var(--space-md);
  }
  
  .project-card-title {
    font-size: var(--font-size-lg);
  }
  
  .project-card-description {
    font-size: var(--font-size-xs);
  }
  
  .profile-card {
    padding: var(--space-lg);
  }
  
  .profile-card-avatar {
    width: 100px;
    height: 100px;
  }
  
  .profile-card-name {
    font-size: var(--font-size-xl);
  }
  
  .contact-card {
    padding: var(--space-lg);
  }
  
  .backlog-card-header,
  .backlog-card-content {
    padding: var(--space-md);
  }
  
  .cards-grid,
  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-contrast: high) {
  .card,
  .project-card,
  .profile-card,
  .contact-card,
  .backlog-card {
    border: var(--border-width-thick) solid var(--color-text);
    box-shadow: none;
  }
  
  .project-card-title,
  .profile-card-name,
  .contact-card-title,
  .backlog-card-title {
    font-weight: var(--font-weight-bold);
    color: var(--color-text);
  }
  
  .project-card-tag {
    border: var(--border-width) solid var(--color-text);
    background: var(--color-bg);
  }
}

/* ===== MOVIMENTO REDUZIDO ===== */
@media (prefers-reduced-motion: reduce) {
  .card,
  .project-card,
  .profile-card,
  .contact-card,
  .backlog-card,
  .profile-card-avatar {
    transition: none;
  }
  
  .card:hover,
  .project-card:hover,
  .profile-card-avatar:hover {
    transform: none;
  }
}

/**
 * 🎯 CSS COMPONENTES MOBILE
 */

.mobile-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: var(--shadow-sm);
  transition: var(--mobile-transition);
}

.mobile-card.interactive:active {
  transform: scale(0.98);
}

.mobile-modal {
  position: fixed;
  inset: 0;
  z-index: var(--z-mobile-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.mobile-sheet {
  position: fixed;
  z-index: var(--z-mobile-modal);
  background: var(--color-surface);
  border-radius: 1rem 1rem 0 0;
  max-height: 90vh;
}

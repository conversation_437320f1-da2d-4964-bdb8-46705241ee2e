const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/Index-C7GLQPZa.js","js/chunk-L3uak9dD.js","js/chunk-D3Ns84uO.js","js/chunk-DJqUFuPP.js","js/chunk-CS50z45r.js","js/chunk-BxTnlCnP.js","js/chunk-DU4M193A.js","js/chunk-DylKGAq6.js","js/NotFound-BjHS4ktM.js","js/PrivacyPolicy-BjMS_s1l.js","js/AnalyticsProvider-Da6roXPw.js","js/chunk-DzkTCSaD.js","js/BackToTop-Dir6kW45.js","js/FluidGradientBackground-fz9tBY22.js","js/LazyScripts-Chw9cytQ.js","js/CookieConsent-ek5hpc7h.js"])))=>i.map(i=>d[i]);
var e,t,a=Object.defineProperty,o=(e,t,o)=>((e,t,o)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o)(e,"symbol"!=typeof t?t+"":t,o);import{j as r,m as i,A as n}from"./chunk-L3uak9dD.js";import{R as s,r as l,b as c,a as d,v as u,u as p,B as m,c as g,d as f}from"./chunk-D3Ns84uO.js";import{a as h,g as b}from"./chunk-DJqUFuPP.js";import{c as v,u as y,a as x,B as w,P as k,b as E,d as C,e as j,f as T,V as S,R as A,g as I,h as N,i as P,j as D,C as R,k as M,S as L,L as z}from"./chunk-DU4M193A.js";import{X as O,S as U,a as _,M as B,V,b as F,G as $,C as q,A as G,E as X,T as H,B as W,c as K,d as Y,L as Q,e as J,f as Z,g as ee,h as te,U as ae,F as oe,R as re,i as ie}from"./chunk-BxTnlCnP.js";import{Q as ne,a as se}from"./chunk-DylKGAq6.js";import{i as le,B as ce,a as de,u as ue}from"./chunk-CS50z45r.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}(),"undefined"!=typeof window&&(window.React=s,window.react=s),"undefined"!=typeof globalThis&&(globalThis.React=s,globalThis.react=s);var pe,me={};var ge=function(){if(pe)return me;pe=1;var e=h();return me.createRoot=e.createRoot,me.hydrateRoot=e.hydrateRoot,me}();const fe={},he=function(e,t,a){let o=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),r=(null==a?void 0:a.nonce)||(null==a?void 0:a.getAttribute("nonce"));o=e(t.map((e=>{if((e=function(e){return"/portfolio/"+e}(e))in fe)return;fe[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${a}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script"),o.crossOrigin="",o.href=e,r&&o.setAttribute("nonce",r),document.head.appendChild(o),t?new Promise(((t,a)=>{o.addEventListener("load",t),o.addEventListener("error",(()=>a(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function r(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return o.then((t=>{for(const e of t||[])"rejected"===e.status&&r(e.reason);return e().catch(r)}))};let be=0;const ve=new Map,ye=e=>{if(ve.has(e))return;const t=setTimeout((()=>{ve.delete(e),Ee({type:"REMOVE_TOAST",toastId:e})}),1e6);ve.set(e,t)},xe=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case"DISMISS_TOAST":{const{toastId:a}=t;return a?ye(a):e.toasts.forEach((e=>{ye(e.id)})),{...e,toasts:e.toasts.map((e=>e.id===a||void 0===a?{...e,open:!1}:e))}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))}}},we=[];let ke={toasts:[]};function Ee(e){ke=xe(ke,e),we.forEach((e=>{e(ke)}))}function Ce({...e}){const t=(be=(be+1)%Number.MAX_SAFE_INTEGER,be.toString()),a=()=>Ee({type:"DISMISS_TOAST",toastId:t});return Ee({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>Ee({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function je(e){const t=e+"CollectionProvider",[a,o]=v(t),[i,n]=a(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{const{scope:t,children:a}=e,o=c.useRef(null),n=c.useRef(new Map).current;return r.jsx(i,{scope:t,itemMap:n,collectionRef:o,children:a})};s.displayName=t;const l=e+"CollectionSlot",d=x(l),u=c.forwardRef(((e,t)=>{const{scope:a,children:o}=e,i=n(l,a),s=y(t,i.collectionRef);return r.jsx(d,{ref:s,children:o})}));u.displayName=l;const p=e+"CollectionItemSlot",m="data-radix-collection-item",g=x(p),f=c.forwardRef(((e,t)=>{const{scope:a,children:o,...i}=e,s=c.useRef(null),l=y(t,s),d=n(p,a);return c.useEffect((()=>(d.itemMap.set(s,{ref:s,...i}),()=>{d.itemMap.delete(s)}))),r.jsx(g,{[m]:"",ref:l,children:o})}));return f.displayName=p,[{Provider:s,Slot:u,ItemSlot:f},function(t){const a=n(e+"CollectionConsumer",t);return c.useCallback((()=>{const e=a.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(a.itemMap.values()).sort(((e,a)=>t.indexOf(e.ref.current)-t.indexOf(a.ref.current)))}),[a.collectionRef,a.itemMap])},o]}var Te="ToastProvider",[Se,Ae,Ie]=je("Toast"),[Ne,Pe]=v("Toast",[Ie]),[De,Re]=Ne(Te),Me=e=>{const{__scopeToast:t,label:a="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:n=50,children:s}=e,[c,d]=l.useState(null),[u,p]=l.useState(0),m=l.useRef(!1),g=l.useRef(!1);return a.trim()||console.error(`Invalid prop \`label\` supplied to \`${Te}\`. Expected non-empty \`string\`.`),r.jsx(Se.Provider,{scope:t,children:r.jsx(De,{scope:t,label:a,duration:o,swipeDirection:i,swipeThreshold:n,toastCount:u,viewport:c,onViewportChange:d,onToastAdd:l.useCallback((()=>p((e=>e+1))),[]),onToastRemove:l.useCallback((()=>p((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:g,children:s})})};Me.displayName=Te;var Le="ToastViewport",ze=["F8"],Oe="toast.viewportPause",Ue="toast.viewportResume",_e=l.forwardRef(((e,t)=>{const{__scopeToast:a,hotkey:o=ze,label:i="Notifications ({hotkey})",...n}=e,s=Re(Le,a),c=Ae(a),d=l.useRef(null),u=l.useRef(null),p=l.useRef(null),m=l.useRef(null),g=y(t,m,s.onViewportChange),f=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),h=s.toastCount>0;l.useEffect((()=>{const e=e=>{var t;0!==o.length&&o.every((t=>e[t]||e.code===t))&&(null==(t=m.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[o]),l.useEffect((()=>{const e=d.current,t=m.current;if(h&&e&&t){const a=()=>{if(!s.isClosePausedRef.current){const e=new CustomEvent(Oe);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},o=()=>{if(s.isClosePausedRef.current){const e=new CustomEvent(Ue);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},r=t=>{!e.contains(t.relatedTarget)&&o()},i=()=>{e.contains(document.activeElement)||o()};return e.addEventListener("focusin",a),e.addEventListener("focusout",r),e.addEventListener("pointermove",a),e.addEventListener("pointerleave",i),window.addEventListener("blur",a),window.addEventListener("focus",o),()=>{e.removeEventListener("focusin",a),e.removeEventListener("focusout",r),e.removeEventListener("pointermove",a),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",a),window.removeEventListener("focus",o)}}}),[h,s.isClosePausedRef]);const b=l.useCallback((({tabbingDirection:e})=>{const t=c().map((t=>{const a=t.ref.current,o=[a,...rt(a)];return"forwards"===e?o:o.reverse()}));return("forwards"===e?t.reverse():t).flat()}),[c]);return l.useEffect((()=>{const e=m.current;if(e){const t=t=>{var a,o,r;const i=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!i){const i=document.activeElement,n=t.shiftKey;if(t.target===e&&n)return void(null==(a=u.current)||a.focus());const s=b({tabbingDirection:n?"backwards":"forwards"}),l=s.findIndex((e=>e===i));it(s.slice(l+1))?t.preventDefault():n?null==(o=u.current)||o.focus():null==(r=p.current)||r.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[c,b]),r.jsxs(w,{ref:d,role:"region","aria-label":i.replace("{hotkey}",f),tabIndex:-1,style:{pointerEvents:h?void 0:"none"},children:[h&&r.jsx(Ve,{ref:u,onFocusFromOutsideViewport:()=>{it(b({tabbingDirection:"forwards"}))}}),r.jsx(Se.Slot,{scope:a,children:r.jsx(k.ol,{tabIndex:-1,...n,ref:g})}),h&&r.jsx(Ve,{ref:p,onFocusFromOutsideViewport:()=>{it(b({tabbingDirection:"backwards"}))}})]})}));_e.displayName=Le;var Be="ToastFocusProxy",Ve=l.forwardRef(((e,t)=>{const{__scopeToast:a,onFocusFromOutsideViewport:o,...i}=e,n=Re(Be,a);return r.jsx(S,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;const a=e.relatedTarget;!(null==(t=n.viewport)?void 0:t.contains(a))&&o()}})}));Ve.displayName=Be;var Fe="Toast",$e=l.forwardRef(((e,t)=>{const{forceMount:a,open:o,defaultOpen:i,onOpenChange:n,...s}=e,[l,c]=E({prop:o,defaultProp:i??!0,onChange:n,caller:Fe});return r.jsx(C,{present:a||l,children:r.jsx(Xe,{open:l,...s,ref:t,onClose:()=>c(!1),onPause:T(e.onPause),onResume:T(e.onResume),onSwipeStart:j(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:j(e.onSwipeMove,(e=>{const{x:t,y:a}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${a}px`)})),onSwipeCancel:j(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:j(e.onSwipeEnd,(e=>{const{x:t,y:a}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${a}px`),c(!1)}))})})}));$e.displayName=Fe;var[qe,Ge]=Ne(Fe,{onClose(){}}),Xe=l.forwardRef(((e,t)=>{const{__scopeToast:a,type:o="foreground",duration:i,open:n,onClose:s,onEscapeKeyDown:c,onPause:u,onResume:p,onSwipeStart:m,onSwipeMove:g,onSwipeCancel:f,onSwipeEnd:h,...b}=e,v=Re(Fe,a),[x,w]=l.useState(null),E=y(t,(e=>w(e))),C=l.useRef(null),S=l.useRef(null),I=i||v.duration,N=l.useRef(0),P=l.useRef(I),D=l.useRef(0),{onToastAdd:R,onToastRemove:M}=v,L=T((()=>{var e;(null==x?void 0:x.contains(document.activeElement))&&(null==(e=v.viewport)||e.focus()),s()})),z=l.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(D.current),N.current=(new Date).getTime(),D.current=window.setTimeout(L,e))}),[L]);l.useEffect((()=>{const e=v.viewport;if(e){const t=()=>{z(P.current),null==p||p()},a=()=>{const e=(new Date).getTime()-N.current;P.current=P.current-e,window.clearTimeout(D.current),null==u||u()};return e.addEventListener(Oe,a),e.addEventListener(Ue,t),()=>{e.removeEventListener(Oe,a),e.removeEventListener(Ue,t)}}}),[v.viewport,I,u,p,z]),l.useEffect((()=>{n&&!v.isClosePausedRef.current&&z(I)}),[n,I,v.isClosePausedRef,z]),l.useEffect((()=>(R(),()=>M())),[R,M]);const O=l.useMemo((()=>x?tt(x):null),[x]);return v.viewport?r.jsxs(r.Fragment,{children:[O&&r.jsx(He,{__scopeToast:a,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:O}),r.jsx(qe,{scope:a,onClose:L,children:d.createPortal(r.jsx(Se.ItemSlot,{scope:a,children:r.jsx(A,{asChild:!0,onEscapeKeyDown:j(c,(()=>{v.isFocusedToastEscapeKeyDownRef.current||L(),v.isFocusedToastEscapeKeyDownRef.current=!1})),children:r.jsx(k.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":n?"open":"closed","data-swipe-direction":v.swipeDirection,...b,ref:E,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:j(e.onKeyDown,(e=>{"Escape"===e.key&&(null==c||c(e.nativeEvent),e.nativeEvent.defaultPrevented||(v.isFocusedToastEscapeKeyDownRef.current=!0,L()))})),onPointerDown:j(e.onPointerDown,(e=>{0===e.button&&(C.current={x:e.clientX,y:e.clientY})})),onPointerMove:j(e.onPointerMove,(e=>{if(!C.current)return;const t=e.clientX-C.current.x,a=e.clientY-C.current.y,o=Boolean(S.current),r=["left","right"].includes(v.swipeDirection),i=["left","up"].includes(v.swipeDirection)?Math.min:Math.max,n=r?i(0,t):0,s=r?0:i(0,a),l="touch"===e.pointerType?10:2,c={x:n,y:s},d={originalEvent:e,delta:c};o?(S.current=c,at("toast.swipeMove",g,d,{discrete:!1})):ot(c,v.swipeDirection,l)?(S.current=c,at("toast.swipeStart",m,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(a)>l)&&(C.current=null)})),onPointerUp:j(e.onPointerUp,(e=>{const t=S.current,a=e.target;if(a.hasPointerCapture(e.pointerId)&&a.releasePointerCapture(e.pointerId),S.current=null,C.current=null,t){const a=e.currentTarget,o={originalEvent:e,delta:t};ot(t,v.swipeDirection,v.swipeThreshold)?at("toast.swipeEnd",h,o,{discrete:!0}):at("toast.swipeCancel",f,o,{discrete:!0}),a.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))})})}),v.viewport)})]}):null})),He=e=>{const{__scopeToast:t,children:a,...o}=e,i=Re(Fe,t),[n,s]=l.useState(!1),[c,d]=l.useState(!1);return function(e=()=>{}){const t=T(e);N((()=>{let e=0,a=0;return e=window.requestAnimationFrame((()=>a=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(a)}}),[t])}((()=>s(!0))),l.useEffect((()=>{const e=window.setTimeout((()=>d(!0)),1e3);return()=>window.clearTimeout(e)}),[]),c?null:r.jsx(I,{asChild:!0,children:r.jsx(S,{...o,children:n&&r.jsxs(r.Fragment,{children:[i.label," ",a]})})})},We=l.forwardRef(((e,t)=>{const{__scopeToast:a,...o}=e;return r.jsx(k.div,{...o,ref:t})}));We.displayName="ToastTitle";var Ke=l.forwardRef(((e,t)=>{const{__scopeToast:a,...o}=e;return r.jsx(k.div,{...o,ref:t})}));Ke.displayName="ToastDescription";var Ye="ToastAction",Qe=l.forwardRef(((e,t)=>{const{altText:a,...o}=e;return a.trim()?r.jsx(et,{altText:a,asChild:!0,children:r.jsx(Ze,{...o,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Ye}\`. Expected non-empty \`string\`.`),null)}));Qe.displayName=Ye;var Je="ToastClose",Ze=l.forwardRef(((e,t)=>{const{__scopeToast:a,...o}=e,i=Ge(Je,a);return r.jsx(et,{asChild:!0,children:r.jsx(k.button,{type:"button",...o,ref:t,onClick:j(e.onClick,i.onClose)})})}));Ze.displayName=Je;var et=l.forwardRef(((e,t)=>{const{__scopeToast:a,altText:o,...i}=e;return r.jsx(k.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...i,ref:t})}));function tt(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const a=e.ariaHidden||e.hidden||"none"===e.style.display,o=""===e.dataset.radixToastAnnounceExclude;if(!a)if(o){const a=e.dataset.radixToastAnnounceAlt;a&&t.push(a)}else t.push(...tt(e))}})),t}function at(e,t,a,{discrete:o}){const r=a.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:a});t&&r.addEventListener(e,t,{once:!0}),o?P(r,i):r.dispatchEvent(i)}var ot=(e,t,a=0)=>{const o=Math.abs(e.x),r=Math.abs(e.y),i=o>r;return"left"===t||"right"===t?i&&o>a:!i&&r>a};function rt(e){const t=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)t.push(a.currentNode);return t}function it(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var nt=Me,st=_e,lt=$e,ct=We,dt=Ke,ut=Qe,pt=Ze;const mt=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,gt=D,ft=(e,t)=>a=>{var o;if(null==(null==t?void 0:t.variants))return gt(e,null==a?void 0:a.class,null==a?void 0:a.className);const{variants:r,defaultVariants:i}=t,n=Object.keys(r).map((e=>{const t=null==a?void 0:a[e],o=null==i?void 0:i[e];if(null===t)return null;const n=mt(t)||mt(o);return r[e][n]})),s=a&&Object.entries(a).reduce(((e,t)=>{let[a,o]=t;return void 0===o||(e[a]=o),e}),{}),l=null==t||null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce(((e,t)=>{let{class:a,className:o,...r}=t;return Object.entries(r).every((e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...i,...s}[t]):{...i,...s}[t]===a}))?[...e,a,o]:e}),[]);return gt(e,n,l,null==a?void 0:a.class,null==a?void 0:a.className)},ht=e=>{const t=xt(e),{conflictingClassGroups:a,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{const a=e.split("-");return""===a[0]&&1!==a.length&&a.shift(),bt(a,t)||yt(e)},getConflictingClassGroupIds:(e,t)=>{const r=a[e]||[];return t&&o[e]?[...r,...o[e]]:r}}},bt=(e,t)=>{var a;if(0===e.length)return t.classGroupId;const o=e[0],r=t.nextPart.get(o),i=r?bt(e.slice(1),r):void 0;if(i)return i;if(0===t.validators.length)return;const n=e.join("-");return null==(a=t.validators.find((({validator:e})=>e(n))))?void 0:a.classGroupId},vt=/^\[(.+)\]$/,yt=e=>{if(vt.test(e)){const t=vt.exec(e)[1],a=null==t?void 0:t.substring(0,t.indexOf(":"));if(a)return"arbitrary.."+a}},xt=e=>{const{theme:t,prefix:a}=e,o={nextPart:new Map,validators:[]};return Ct(Object.entries(e.classGroups),a).forEach((([e,a])=>{wt(a,o,e,t)})),o},wt=(e,t,a,o)=>{e.forEach((e=>{if("string"!=typeof e){if("function"==typeof e)return Et(e)?void wt(e(o),t,a,o):void t.validators.push({validator:e,classGroupId:a});Object.entries(e).forEach((([e,r])=>{wt(r,kt(t,e),a,o)}))}else{(""===e?t:kt(t,e)).classGroupId=a}}))},kt=(e,t)=>{let a=e;return t.split("-").forEach((e=>{a.nextPart.has(e)||a.nextPart.set(e,{nextPart:new Map,validators:[]}),a=a.nextPart.get(e)})),a},Et=e=>e.isThemeGetter,Ct=(e,t)=>t?e.map((([e,a])=>[e,a.map((e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,a])=>[t+e,a]))):e))])):e,jt=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,a=new Map,o=new Map;const r=(r,i)=>{a.set(r,i),t++,t>e&&(t=0,o=a,a=new Map)};return{get(e){let t=a.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(r(e,t),t):void 0},set(e,t){a.has(e)?a.set(e,t):r(e,t)}}},Tt=e=>{const{separator:t,experimentalParseClassName:a}=e,o=1===t.length,r=t[0],i=t.length,n=e=>{const a=[];let n,s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===r&&(o||e.slice(u,u+i)===t)){a.push(e.slice(l,u)),l=u+i;continue}if("/"===c){n=u;continue}}"["===c?s++:"]"===c&&s--}const c=0===a.length?e:e.substring(l),d=c.startsWith("!");return{modifiers:a,hasImportantModifier:d,baseClassName:d?c.substring(1):c,maybePostfixModifierPosition:n&&n>l?n-l:void 0}};return a?e=>a({className:e,parseClassName:n}):n},St=e=>{if(e.length<=1)return e;const t=[];let a=[];return e.forEach((e=>{"["===e[0]?(t.push(...a.sort(),e),a=[]):a.push(e)})),t.push(...a.sort()),t},At=/\s+/;function It(){let e,t,a=0,o="";for(;a<arguments.length;)(e=arguments[a++])&&(t=Nt(e))&&(o&&(o+=" "),o+=t);return o}const Nt=e=>{if("string"==typeof e)return e;let t,a="";for(let o=0;o<e.length;o++)e[o]&&(t=Nt(e[o]))&&(a&&(a+=" "),a+=t);return a};function Pt(e,...t){let a,o,r,i=function(s){const l=t.reduce(((e,t)=>t(e)),e());return a=(e=>({cache:jt(e.cacheSize),parseClassName:Tt(e),...ht(e)}))(l),o=a.cache.get,r=a.cache.set,i=n,n(s)};function n(e){const t=o(e);if(t)return t;const i=((e,t)=>{const{parseClassName:a,getClassGroupId:o,getConflictingClassGroupIds:r}=t,i=[],n=e.trim().split(At);let s="";for(let l=n.length-1;l>=0;l-=1){const e=n[l],{modifiers:t,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=a(e);let p=Boolean(u),m=o(p?d.substring(0,u):d);if(!m){if(!p){s=e+(s.length>0?" "+s:s);continue}if(m=o(d),!m){s=e+(s.length>0?" "+s:s);continue}p=!1}const g=St(t).join(":"),f=c?g+"!":g,h=f+m;if(i.includes(h))continue;i.push(h);const b=r(m,p);for(let a=0;a<b.length;++a){const e=b[a];i.push(f+e)}s=e+(s.length>0?" "+s:s)}return s})(e,a);return r(e,i),i}return function(){return i(It.apply(null,arguments))}}const Dt=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},Rt=/^\[(?:([a-z-]+):)?(.+)\]$/i,Mt=/^\d+\/\d+$/,Lt=new Set(["px","full","screen"]),zt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ot=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ut=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,_t=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Bt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Vt=e=>$t(e)||Lt.has(e)||Mt.test(e),Ft=e=>aa(e,"length",oa),$t=e=>Boolean(e)&&!Number.isNaN(Number(e)),qt=e=>aa(e,"number",$t),Gt=e=>Boolean(e)&&Number.isInteger(Number(e)),Xt=e=>e.endsWith("%")&&$t(e.slice(0,-1)),Ht=e=>Rt.test(e),Wt=e=>zt.test(e),Kt=new Set(["length","size","percentage"]),Yt=e=>aa(e,Kt,ra),Qt=e=>aa(e,"position",ra),Jt=new Set(["image","url"]),Zt=e=>aa(e,Jt,na),ea=e=>aa(e,"",ia),ta=()=>!0,aa=(e,t,a)=>{const o=Rt.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):a(o[2]))},oa=e=>Ot.test(e)&&!Ut.test(e),ra=()=>!1,ia=e=>_t.test(e),na=e=>Bt.test(e),sa=Pt((()=>{const e=Dt("colors"),t=Dt("spacing"),a=Dt("blur"),o=Dt("brightness"),r=Dt("borderColor"),i=Dt("borderRadius"),n=Dt("borderSpacing"),s=Dt("borderWidth"),l=Dt("contrast"),c=Dt("grayscale"),d=Dt("hueRotate"),u=Dt("invert"),p=Dt("gap"),m=Dt("gradientColorStops"),g=Dt("gradientColorStopPositions"),f=Dt("inset"),h=Dt("margin"),b=Dt("opacity"),v=Dt("padding"),y=Dt("saturate"),x=Dt("scale"),w=Dt("sepia"),k=Dt("skew"),E=Dt("space"),C=Dt("translate"),j=()=>["auto",Ht,t],T=()=>[Ht,t],S=()=>["",Vt,Ft],A=()=>["auto",$t,Ht],I=()=>["","0",Ht],N=()=>[$t,Ht];return{cacheSize:500,separator:":",theme:{colors:[ta],spacing:[Vt,Ft],blur:["none","",Wt,Ht],brightness:N(),borderColor:[e],borderRadius:["none","","full",Wt,Ht],borderSpacing:T(),borderWidth:S(),contrast:N(),grayscale:I(),hueRotate:N(),invert:I(),gap:T(),gradientColorStops:[e],gradientColorStopPositions:[Xt,Ft],inset:j(),margin:j(),opacity:N(),padding:T(),saturate:N(),scale:N(),sepia:I(),skew:N(),space:T(),translate:T()},classGroups:{aspect:[{aspect:["auto","square","video",Ht]}],container:["container"],columns:[{columns:[Wt]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Ht]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Gt,Ht]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Ht]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",Gt,Ht]}],"grid-cols":[{"grid-cols":[ta]}],"col-start-end":[{col:["auto",{span:["full",Gt,Ht]},Ht]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[ta]}],"row-start-end":[{row:["auto",{span:[Gt,Ht]},Ht]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Ht]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Ht]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[h]}],mx:[{mx:[h]}],my:[{my:[h]}],ms:[{ms:[h]}],me:[{me:[h]}],mt:[{mt:[h]}],mr:[{mr:[h]}],mb:[{mb:[h]}],ml:[{ml:[h]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Ht,t]}],"min-w":[{"min-w":[Ht,t,"min","max","fit"]}],"max-w":[{"max-w":[Ht,t,"none","full","min","max","fit","prose",{screen:[Wt]},Wt]}],h:[{h:[Ht,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Ht,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Ht,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Ht,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Wt,Ft]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",qt]}],"font-family":[{font:[ta]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Ht]}],"line-clamp":[{"line-clamp":["none",$t,qt]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Vt,Ht]}],"list-image":[{"list-image":["none",Ht]}],"list-style-type":[{list:["none","disc","decimal",Ht]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Vt,Ft]}],"underline-offset":[{"underline-offset":["auto",Vt,Ht]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Ht]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Ht]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Qt]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Yt]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Zt]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[Vt,Ht]}],"outline-w":[{outline:[Vt,Ft]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:S()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[Vt,Ft]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Wt,ea]}],"shadow-color":[{shadow:[ta]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[a]}],brightness:[{brightness:[o]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Wt,Ht]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[y]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[a]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Ht]}],duration:[{duration:N()}],ease:[{ease:["linear","in","out","in-out",Ht]}],delay:[{delay:N()}],animate:[{animate:["none","spin","ping","pulse","bounce",Ht]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Gt,Ht]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Ht]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Ht]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Ht]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Vt,Ft,qt]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}));function la(...e){return sa(D(e))}const ca=nt,da=l.forwardRef((({className:e,...t},a)=>r.jsx(st,{ref:a,className:la("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t})));da.displayName=st.displayName;const ua=ft("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),pa=l.forwardRef((({className:e,variant:t,...a},o)=>r.jsx(lt,{ref:o,className:la(ua({variant:t}),e),...a})));pa.displayName=lt.displayName;l.forwardRef((({className:e,...t},a)=>r.jsx(ut,{ref:a,className:la("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}))).displayName=ut.displayName;const ma=l.forwardRef((({className:e,...t},a)=>r.jsx(pt,{ref:a,className:la("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:r.jsx(O,{className:"h-4 w-4"})})));ma.displayName=pt.displayName;const ga=l.forwardRef((({className:e,...t},a)=>r.jsx(ct,{ref:a,className:la("text-sm font-semibold",e),...t})));ga.displayName=ct.displayName;const fa=l.forwardRef((({className:e,...t},a)=>r.jsx(dt,{ref:a,className:la("text-sm opacity-90",e),...t})));function ha(){const{toasts:e}=function(){const[e,t]=l.useState(ke);return l.useEffect((()=>(we.push(t),()=>{const e=we.indexOf(t);e>-1&&we.splice(e,1)})),[e]),{...e,toast:Ce,dismiss:e=>Ee({type:"DISMISS_TOAST",toastId:e})}}();return r.jsxs(ca,{children:[e.map((function({id:e,title:t,description:a,action:o,...i}){return r.jsxs(pa,{...i,children:[r.jsxs("div",{className:"grid gap-1",children:[t&&r.jsx(ga,{children:t}),a&&r.jsx(fa,{children:a})]}),o,r.jsx(ma,{})]},e)})),r.jsx(da,{})]})}fa.displayName=dt.displayName;const ba=l.createContext(void 0),va=()=>{const e=l.useContext(ba);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e},ya=({children:e,defaultTheme:t="system",storageKey:a="portfolio-theme"})=>{const[o,i]=l.useState(t),[n,s]=l.useState("light"),[c,d]=l.useState(!0),u=()=>"undefined"!=typeof window&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",p=e=>"system"===e?u():e,m=e=>{const t=document.documentElement;t.classList.remove("light","dark"),t.classList.add(e),t.setAttribute("data-theme",e)};l.useEffect((()=>{if("undefined"!=typeof window){const e=localStorage.getItem(a),o=e&&["light","dark","system"].includes(e)?e:t;i(o);const r=p(o);s(r),m(r),d(!1)}}),[t,a]),l.useEffect((()=>{if("undefined"==typeof window)return;const e=window.matchMedia("(prefers-color-scheme: dark)"),t=()=>{if("system"===o){const e=u();s(e),m(e)}};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)}),[o]);const g={theme:o,setTheme:e=>{i(e),"undefined"!=typeof window&&localStorage.setItem(a,e);const t=p(e);s(t),m(t)},resolvedTheme:n,isLoading:c};return r.jsx(ba.Provider,{value:g,children:e})};var xa=Array(12).fill(0),wa=({visible:e,className:t})=>c.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},c.createElement("div",{className:"sonner-spinner"},xa.map(((e,t)=>c.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`}))))),ka=c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},c.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Ea=c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},c.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),Ca=c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},c.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),ja=c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},c.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Ta=c.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},c.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),c.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),Sa=1,Aa=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach((t=>t(e)))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:a,...o}=e,r="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:Sa++,i=this.toasts.find((e=>e.id===r)),n=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(r)&&this.dismissedToasts.delete(r),i?this.toasts=this.toasts.map((t=>t.id===r?(this.publish({...t,...e,id:r,title:a}),{...t,...e,id:r,dismissible:n,title:a}):t)):this.addToast({title:a,...o,dismissible:n,id:r}),r},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach((e=>{this.subscribers.forEach((t=>t({id:e.id,dismiss:!0})))})),this.subscribers.forEach((t=>t({id:e,dismiss:!0}))),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let a;void 0!==t.loading&&(a=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let o,r=e instanceof Promise?e:e(),i=void 0!==a,n=r.then((async e=>{if(o=["resolve",e],c.isValidElement(e))i=!1,this.create({id:a,type:"default",message:e});else if(Ia(e)&&!e.ok){i=!1;let o="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,r="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:a,type:"error",message:o,description:r})}else if(void 0!==t.success){i=!1;let o="function"==typeof t.success?await t.success(e):t.success,r="function"==typeof t.description?await t.description(e):t.description;this.create({id:a,type:"success",message:o,description:r})}})).catch((async e=>{if(o=["reject",e],void 0!==t.error){i=!1;let o="function"==typeof t.error?await t.error(e):t.error,r="function"==typeof t.description?await t.description(e):t.description;this.create({id:a,type:"error",message:o,description:r})}})).finally((()=>{var e;i&&(this.dismiss(a),a=void 0),null==(e=t.finally)||e.call(t)})),s=()=>new Promise(((e,t)=>n.then((()=>"reject"===o[0]?t(o[1]):e(o[1]))).catch(t)));return"string"!=typeof a&&"number"!=typeof a?{unwrap:s}:Object.assign(a,{unwrap:s})},this.custom=(e,t)=>{let a=(null==t?void 0:t.id)||Sa++;return this.create({jsx:e(a),id:a,...t}),a},this.getActiveToasts=()=>this.toasts.filter((e=>!this.dismissedToasts.has(e.id))),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Ia=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,Na=(e,t)=>{let a=(null==t?void 0:t.id)||Sa++;return Aa.addToast({title:e,...t,id:a}),a};function Pa(e){return void 0!==e.label}Object.assign(Na,{success:Aa.success,info:Aa.info,warning:Aa.warning,error:Aa.error,custom:Aa.custom,message:Aa.message,promise:Aa.promise,dismiss:Aa.dismiss,loading:Aa.loading},{getHistory:()=>Aa.toasts,getToasts:()=>Aa.getActiveToasts()}),function(e,{insertAt:t}={}){if("undefined"==typeof document)return;let a=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css","top"===t&&a.firstChild?a.insertBefore(o,a.firstChild):a.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}(':where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n');var Da=3,Ra=14;function Ma(...e){return e.filter(Boolean).join(" ")}var La=e=>{var t,a,o,r,i,n,s,d,u,p,m;let{invert:g,toast:f,unstyled:h,interacting:b,setHeights:v,visibleToasts:y,heights:x,index:w,toasts:k,expanded:E,removeToast:C,defaultRichColors:j,closeButton:T,style:S,cancelButtonStyle:A,actionButtonStyle:I,className:N="",descriptionClassName:P="",duration:D,position:R,gap:M,loadingIcon:L,expandByDefault:z,classNames:O,icons:U,closeButtonAriaLabel:_="Close toast",pauseWhenPageIsHidden:B}=e,[V,F]=c.useState(null),[$,q]=c.useState(null),[G,X]=c.useState(!1),[H,W]=c.useState(!1),[K,Y]=c.useState(!1),[Q,J]=c.useState(!1),[Z,ee]=c.useState(!1),[te,ae]=c.useState(0),[oe,re]=c.useState(0),ie=c.useRef(f.duration||D||4e3),ne=c.useRef(null),se=c.useRef(null),le=0===w,ce=w+1<=y,de=f.type,ue=!1!==f.dismissible,pe=f.className||"",me=f.descriptionClassName||"",ge=c.useMemo((()=>x.findIndex((e=>e.toastId===f.id))||0),[x,f.id]),fe=c.useMemo((()=>{var e;return null!=(e=f.closeButton)?e:T}),[f.closeButton,T]),he=c.useMemo((()=>f.duration||D||4e3),[f.duration,D]),be=c.useRef(0),ve=c.useRef(0),ye=c.useRef(0),xe=c.useRef(null),[we,ke]=R.split("-"),Ee=c.useMemo((()=>x.reduce(((e,t,a)=>a>=ge?e:e+t.height),0)),[x,ge]),Ce=(()=>{let[e,t]=c.useState(document.hidden);return c.useEffect((()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)}),[]),e})(),je=f.invert||g,Te="loading"===de;ve.current=c.useMemo((()=>ge*M+Ee),[ge,Ee]),c.useEffect((()=>{ie.current=he}),[he]),c.useEffect((()=>{X(!0)}),[]),c.useEffect((()=>{let e=se.current;if(e){let t=e.getBoundingClientRect().height;return re(t),v((e=>[{toastId:f.id,height:t,position:f.position},...e])),()=>v((e=>e.filter((e=>e.toastId!==f.id))))}}),[v,f.id]),c.useLayoutEffect((()=>{if(!G)return;let e=se.current,t=e.style.height;e.style.height="auto";let a=e.getBoundingClientRect().height;e.style.height=t,re(a),v((e=>e.find((e=>e.toastId===f.id))?e.map((e=>e.toastId===f.id?{...e,height:a}:e)):[{toastId:f.id,height:a,position:f.position},...e]))}),[G,f.title,f.description,v,f.id]);let Se=c.useCallback((()=>{W(!0),ae(ve.current),v((e=>e.filter((e=>e.toastId!==f.id)))),setTimeout((()=>{C(f)}),200)}),[f,C,v,ve]);return c.useEffect((()=>{if(f.promise&&"loading"===de||f.duration===1/0||"loading"===f.type)return;let e;return E||b||B&&Ce?(()=>{if(ye.current<be.current){let e=(new Date).getTime()-be.current;ie.current=ie.current-e}ye.current=(new Date).getTime()})():ie.current!==1/0&&(be.current=(new Date).getTime(),e=setTimeout((()=>{var e;null==(e=f.onAutoClose)||e.call(f,f),Se()}),ie.current)),()=>clearTimeout(e)}),[E,b,f,de,B,Ce,Se]),c.useEffect((()=>{f.delete&&Se()}),[Se,f.delete]),c.createElement("li",{tabIndex:0,ref:se,className:Ma(N,pe,null==O?void 0:O.toast,null==(t=null==f?void 0:f.classNames)?void 0:t.toast,null==O?void 0:O.default,null==O?void 0:O[de],null==(a=null==f?void 0:f.classNames)?void 0:a[de]),"data-sonner-toast":"","data-rich-colors":null!=(o=f.richColors)?o:j,"data-styled":!(f.jsx||f.unstyled||h),"data-mounted":G,"data-promise":!!f.promise,"data-swiped":Z,"data-removed":H,"data-visible":ce,"data-y-position":we,"data-x-position":ke,"data-index":w,"data-front":le,"data-swiping":K,"data-dismissible":ue,"data-type":de,"data-invert":je,"data-swipe-out":Q,"data-swipe-direction":$,"data-expanded":!!(E||z&&G),style:{"--index":w,"--toasts-before":w,"--z-index":k.length-w,"--offset":`${H?te:ve.current}px`,"--initial-height":z?"auto":`${oe}px`,...S,...f.style},onDragEnd:()=>{Y(!1),F(null),xe.current=null},onPointerDown:e=>{Te||!ue||(ne.current=new Date,ae(ve.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(Y(!0),xe.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,a,o;if(Q||!ue)return;xe.current=null;let r=Number((null==(e=se.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),i=Number((null==(t=se.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),n=(new Date).getTime()-(null==(a=ne.current)?void 0:a.getTime()),s="x"===V?r:i,l=Math.abs(s)/n;if(Math.abs(s)>=20||l>.11)return ae(ve.current),null==(o=f.onDismiss)||o.call(f,f),q("x"===V?r>0?"right":"left":i>0?"down":"up"),Se(),J(!0),void ee(!1);Y(!1),F(null)},onPointerMove:t=>{var a,o,r,i;if(!xe.current||!ue||(null==(a=window.getSelection())?void 0:a.toString().length)>0)return;let n=t.clientY-xe.current.y,s=t.clientX-xe.current.x,l=null!=(o=e.swipeDirections)?o:function(e){let[t,a]=e.split("-"),o=[];return t&&o.push(t),a&&o.push(a),o}(R);!V&&(Math.abs(s)>1||Math.abs(n)>1)&&F(Math.abs(s)>Math.abs(n)?"x":"y");let c={x:0,y:0};"y"===V?(l.includes("top")||l.includes("bottom"))&&(l.includes("top")&&n<0||l.includes("bottom")&&n>0)&&(c.y=n):"x"===V&&(l.includes("left")||l.includes("right"))&&(l.includes("left")&&s<0||l.includes("right")&&s>0)&&(c.x=s),(Math.abs(c.x)>0||Math.abs(c.y)>0)&&ee(!0),null==(r=se.current)||r.style.setProperty("--swipe-amount-x",`${c.x}px`),null==(i=se.current)||i.style.setProperty("--swipe-amount-y",`${c.y}px`)}},fe&&!f.jsx?c.createElement("button",{"aria-label":_,"data-disabled":Te,"data-close-button":!0,onClick:Te||!ue?()=>{}:()=>{var e;Se(),null==(e=f.onDismiss)||e.call(f,f)},className:Ma(null==O?void 0:O.closeButton,null==(r=null==f?void 0:f.classNames)?void 0:r.closeButton)},null!=(i=null==U?void 0:U.close)?i:Ta):null,f.jsx||l.isValidElement(f.title)?f.jsx?f.jsx:"function"==typeof f.title?f.title():f.title:c.createElement(c.Fragment,null,de||f.icon||f.promise?c.createElement("div",{"data-icon":"",className:Ma(null==O?void 0:O.icon,null==(n=null==f?void 0:f.classNames)?void 0:n.icon)},f.promise||"loading"===f.type&&!f.icon?f.icon||(null!=U&&U.loading?c.createElement("div",{className:Ma(null==O?void 0:O.loader,null==(Ae=null==f?void 0:f.classNames)?void 0:Ae.loader,"sonner-loader"),"data-visible":"loading"===de},U.loading):L?c.createElement("div",{className:Ma(null==O?void 0:O.loader,null==(Ie=null==f?void 0:f.classNames)?void 0:Ie.loader,"sonner-loader"),"data-visible":"loading"===de},L):c.createElement(wa,{className:Ma(null==O?void 0:O.loader,null==(Ne=null==f?void 0:f.classNames)?void 0:Ne.loader),visible:"loading"===de})):null,"loading"!==f.type?f.icon||(null==U?void 0:U[de])||(e=>{switch(e){case"success":return ka;case"info":return Ca;case"warning":return Ea;case"error":return ja;default:return null}})(de):null):null,c.createElement("div",{"data-content":"",className:Ma(null==O?void 0:O.content,null==(s=null==f?void 0:f.classNames)?void 0:s.content)},c.createElement("div",{"data-title":"",className:Ma(null==O?void 0:O.title,null==(d=null==f?void 0:f.classNames)?void 0:d.title)},"function"==typeof f.title?f.title():f.title),f.description?c.createElement("div",{"data-description":"",className:Ma(P,me,null==O?void 0:O.description,null==(u=null==f?void 0:f.classNames)?void 0:u.description)},"function"==typeof f.description?f.description():f.description):null),l.isValidElement(f.cancel)?f.cancel:f.cancel&&Pa(f.cancel)?c.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||A,onClick:e=>{var t,a;Pa(f.cancel)&&ue&&(null==(a=(t=f.cancel).onClick)||a.call(t,e),Se())},className:Ma(null==O?void 0:O.cancelButton,null==(p=null==f?void 0:f.classNames)?void 0:p.cancelButton)},f.cancel.label):null,l.isValidElement(f.action)?f.action:f.action&&Pa(f.action)?c.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||I,onClick:e=>{var t,a;Pa(f.action)&&(null==(a=(t=f.action).onClick)||a.call(t,e),!e.defaultPrevented&&Se())},className:Ma(null==O?void 0:O.actionButton,null==(m=null==f?void 0:f.classNames)?void 0:m.actionButton)},f.action.label):null));var Ae,Ie,Ne};function za(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}function Oa(e,t){let a={};return[e,t].forEach(((e,t)=>{let o=1===t,r=o?"--mobile-offset":"--offset",i=o?"16px":"32px";function n(e){["top","right","bottom","left"].forEach((t=>{a[`${r}-${t}`]="number"==typeof e?`${e}px`:e}))}"number"==typeof e||"string"==typeof e?n(e):"object"==typeof e?["top","right","bottom","left"].forEach((t=>{void 0===e[t]?a[`${r}-${t}`]=i:a[`${r}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]})):n(i)})),a}var Ua=l.forwardRef((function(e,t){let{invert:a,position:o="bottom-right",hotkey:r=["altKey","KeyT"],expand:i,closeButton:n,className:s,offset:l,mobileOffset:d,theme:p="light",richColors:m,duration:g,style:f,visibleToasts:h=Da,toastOptions:b,dir:v=za(),gap:y=Ra,loadingIcon:x,icons:w,containerAriaLabel:k="Notifications",pauseWhenPageIsHidden:E}=e,[C,j]=c.useState([]),T=c.useMemo((()=>Array.from(new Set([o].concat(C.filter((e=>e.position)).map((e=>e.position)))))),[C,o]),[S,A]=c.useState([]),[I,N]=c.useState(!1),[P,D]=c.useState(!1),[R,M]=c.useState("system"!==p?p:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),L=c.useRef(null),z=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),O=c.useRef(null),U=c.useRef(!1),_=c.useCallback((e=>{j((t=>{var a;return null!=(a=t.find((t=>t.id===e.id)))&&a.delete||Aa.dismiss(e.id),t.filter((({id:t})=>t!==e.id))}))}),[]);return c.useEffect((()=>Aa.subscribe((e=>{e.dismiss?j((t=>t.map((t=>t.id===e.id?{...t,delete:!0}:t)))):setTimeout((()=>{u.flushSync((()=>{j((t=>{let a=t.findIndex((t=>t.id===e.id));return-1!==a?[...t.slice(0,a),{...t[a],...e},...t.slice(a+1)]:[e,...t]}))}))}))}))),[]),c.useEffect((()=>{if("system"!==p)return void M(p);if("system"===p&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?M("dark"):M("light")),"undefined"==typeof window)return;let e=window.matchMedia("(prefers-color-scheme: dark)");try{e.addEventListener("change",(({matches:e})=>{M(e?"dark":"light")}))}catch(t){e.addListener((({matches:e})=>{try{M(e?"dark":"light")}catch(t){console.error(t)}}))}}),[p]),c.useEffect((()=>{C.length<=1&&N(!1)}),[C]),c.useEffect((()=>{let e=e=>{var t,a;r.every((t=>e[t]||e.code===t))&&(N(!0),null==(t=L.current)||t.focus()),"Escape"===e.code&&(document.activeElement===L.current||null!=(a=L.current)&&a.contains(document.activeElement))&&N(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[r]),c.useEffect((()=>{if(L.current)return()=>{O.current&&(O.current.focus({preventScroll:!0}),O.current=null,U.current=!1)}}),[L.current]),c.createElement("section",{ref:t,"aria-label":`${k} ${z}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},T.map(((t,o)=>{var r;let[u,p]=t.split("-");return C.length?c.createElement("ol",{key:t,dir:"auto"===v?za():v,tabIndex:-1,ref:L,className:s,"data-sonner-toaster":!0,"data-theme":R,"data-y-position":u,"data-lifted":I&&C.length>1&&!i,"data-x-position":p,style:{"--front-toast-height":`${(null==(r=S[0])?void 0:r.height)||0}px`,"--width":"356px","--gap":`${y}px`,...f,...Oa(l,d)},onBlur:e=>{U.current&&!e.currentTarget.contains(e.relatedTarget)&&(U.current=!1,O.current&&(O.current.focus({preventScroll:!0}),O.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||U.current||(U.current=!0,O.current=e.relatedTarget)},onMouseEnter:()=>N(!0),onMouseMove:()=>N(!0),onMouseLeave:()=>{P||N(!1)},onDragEnd:()=>N(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||D(!0)},onPointerUp:()=>D(!1)},C.filter((e=>!e.position&&0===o||e.position===t)).map(((o,r)=>{var s,l;return c.createElement(La,{key:o.id,icons:w,index:r,toast:o,defaultRichColors:m,duration:null!=(s=null==b?void 0:b.duration)?s:g,className:null==b?void 0:b.className,descriptionClassName:null==b?void 0:b.descriptionClassName,invert:a,visibleToasts:h,closeButton:null!=(l=null==b?void 0:b.closeButton)?l:n,interacting:P,position:t,style:null==b?void 0:b.style,unstyled:null==b?void 0:b.unstyled,classNames:null==b?void 0:b.classNames,cancelButtonStyle:null==b?void 0:b.cancelButtonStyle,actionButtonStyle:null==b?void 0:b.actionButtonStyle,removeToast:_,toasts:C.filter((e=>e.position==o.position)),heights:S.filter((e=>e.position==o.position)),setHeights:A,expandByDefault:i,gap:y,loadingIcon:x,expanded:I,pauseWhenPageIsHidden:E,swipeDirections:e.swipeDirections})}))):null})))}));const _a=({...e})=>{const{theme:t="system"}=va();return r.jsx(Ua,{theme:t,className:"toaster group",position:"top-center",expand:!1,richColors:!0,closeButton:!0,toastOptions:{duration:3e3,classNames:{toast:"group toast group-[.toaster]:bg-[var(--color-surface)] group-[.toaster]:text-[var(--color-text)] group-[.toaster]:border-[var(--color-border)] group-[.toaster]:shadow-xl group-[.toaster]:backdrop-blur-sm",description:"group-[.toast]:text-[var(--color-muted)]",actionButton:"group-[.toast]:bg-[var(--color-primary)] group-[.toast]:text-white",cancelButton:"group-[.toast]:bg-[var(--color-neutral)] group-[.toast]:text-[var(--color-text)]",closeButton:"group-[.toast]:bg-[var(--color-surface)] group-[.toast]:text-[var(--color-muted)] group-[.toast]:border-[var(--color-border)]",success:"group-[.toaster]:bg-green-50 group-[.toaster]:text-green-900 group-[.toaster]:border-green-200 dark:group-[.toaster]:bg-green-900/20 dark:group-[.toaster]:text-green-100 dark:group-[.toaster]:border-green-800",error:"group-[.toaster]:bg-red-50 group-[.toaster]:text-red-900 group-[.toaster]:border-red-200 dark:group-[.toaster]:bg-red-900/20 dark:group-[.toaster]:text-red-100 dark:group-[.toaster]:border-red-800",warning:"group-[.toaster]:bg-yellow-50 group-[.toaster]:text-yellow-900 group-[.toaster]:border-yellow-200 dark:group-[.toaster]:bg-yellow-900/20 dark:group-[.toaster]:text-yellow-100 dark:group-[.toaster]:border-yellow-800",info:"group-[.toaster]:bg-blue-50 group-[.toaster]:text-blue-900 group-[.toaster]:border-blue-200 dark:group-[.toaster]:bg-blue-900/20 dark:group-[.toaster]:text-blue-100 dark:group-[.toaster]:border-blue-800"},style:{borderRadius:"12px",padding:"16px",fontSize:"14px",fontWeight:"500"}},...e})},Ba=M;var Va,Fa;l.forwardRef((({className:e,sideOffset:t=4,...a},o)=>r.jsx(R,{ref:o,sideOffset:t,className:la("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}))).displayName=R.displayName;const $a=b(function(){if(Fa)return Va;Fa=1;var e="undefined"!=typeof Element,t="function"==typeof Map,a="function"==typeof Set,o="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function r(i,n){if(i===n)return!0;if(i&&n&&"object"==typeof i&&"object"==typeof n){if(i.constructor!==n.constructor)return!1;var s,l,c,d;if(Array.isArray(i)){if((s=i.length)!=n.length)return!1;for(l=s;0!==l--;)if(!r(i[l],n[l]))return!1;return!0}if(t&&i instanceof Map&&n instanceof Map){if(i.size!==n.size)return!1;for(d=i.entries();!(l=d.next()).done;)if(!n.has(l.value[0]))return!1;for(d=i.entries();!(l=d.next()).done;)if(!r(l.value[1],n.get(l.value[0])))return!1;return!0}if(a&&i instanceof Set&&n instanceof Set){if(i.size!==n.size)return!1;for(d=i.entries();!(l=d.next()).done;)if(!n.has(l.value[0]))return!1;return!0}if(o&&ArrayBuffer.isView(i)&&ArrayBuffer.isView(n)){if((s=i.length)!=n.length)return!1;for(l=s;0!==l--;)if(i[l]!==n[l])return!1;return!0}if(i.constructor===RegExp)return i.source===n.source&&i.flags===n.flags;if(i.valueOf!==Object.prototype.valueOf&&"function"==typeof i.valueOf&&"function"==typeof n.valueOf)return i.valueOf()===n.valueOf();if(i.toString!==Object.prototype.toString&&"function"==typeof i.toString&&"function"==typeof n.toString)return i.toString()===n.toString();if((s=(c=Object.keys(i)).length)!==Object.keys(n).length)return!1;for(l=s;0!==l--;)if(!Object.prototype.hasOwnProperty.call(n,c[l]))return!1;if(e&&i instanceof Element)return!1;for(l=s;0!==l--;)if(("_owner"!==c[l]&&"__v"!==c[l]&&"__o"!==c[l]||!i.$$typeof)&&!r(i[c[l]],n[c[l]]))return!1;return!0}return i!=i&&n!=n}return Va=function(e,t){try{return r(e,t)}catch(a){if((a.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw a}}}());var qa,Ga;const Xa=b(Ga?qa:(Ga=1,qa=function(e,t,a,o,r,i,n,s){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[a,o,r,i,n,s],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}));var Ha,Wa;const Ka=b(Wa?Ha:(Wa=1,Ha=function(e,t,a,o){var r=a?a.call(o,e,t):void 0;if(void 0!==r)return!!r;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var i=Object.keys(e),n=Object.keys(t);if(i.length!==n.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),l=0;l<i.length;l++){var c=i[l];if(!s(c))return!1;var d=e[c],u=t[c];if(!1===(r=a?a.call(o,d,u,c):void 0)||void 0===r&&d!==u)return!1}return!0}));var Ya=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(Ya||{}),Qa={rel:["amphtml","canonical","alternate"]},Ja={type:["application/ld+json"]},Za={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},eo=Object.values(Ya),to={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},ao=Object.entries(to).reduce(((e,[t,a])=>(e[a]=t,e)),{}),oo="data-rh",ro="defaultTitle",io="defer",no="encodeSpecialCharacters",so="onChangeClientState",lo="titleTemplate",co="prioritizeSeoTags",uo=(e,t)=>{for(let a=e.length-1;a>=0;a-=1){const o=e[a];if(Object.prototype.hasOwnProperty.call(o,t))return o[t]}return null},po=e=>{let t=uo(e,"title");const a=uo(e,lo);if(Array.isArray(t)&&(t=t.join("")),a&&t)return a.replace(/%s/g,(()=>t));const o=uo(e,ro);return t||o||void 0},mo=e=>uo(e,so)||(()=>{}),go=(e,t)=>t.filter((t=>void 0!==t[e])).map((t=>t[e])).reduce(((e,t)=>({...e,...t})),{}),fo=(e,t)=>t.filter((e=>void 0!==e.base)).map((e=>e.base)).reverse().reduce(((t,a)=>{if(!t.length){const o=Object.keys(a);for(let r=0;r<o.length;r+=1){const i=o[r].toLowerCase();if(-1!==e.indexOf(i)&&a[i])return t.concat(a)}}return t}),[]),ho=(e,t,a)=>{const o={};return a.filter((t=>{return!!Array.isArray(t[e])||(void 0!==t[e]&&(a=`Helmet: ${e} should be of type "Array". Instead found type "${typeof t[e]}"`,console&&"function"==typeof console.warn&&console.warn(a)),!1);var a})).map((t=>t[e])).reverse().reduce(((e,a)=>{const r={};a.filter((e=>{let a;const i=Object.keys(e);for(let o=0;o<i.length;o+=1){const r=i[o],n=r.toLowerCase();-1===t.indexOf(n)||"rel"===a&&"canonical"===e[a].toLowerCase()||"rel"===n&&"stylesheet"===e[n].toLowerCase()||(a=n),-1===t.indexOf(r)||"innerHTML"!==r&&"cssText"!==r&&"itemprop"!==r||(a=r)}if(!a||!e[a])return!1;const n=e[a].toLowerCase();return o[a]||(o[a]={}),r[a]||(r[a]={}),!o[a][n]&&(r[a][n]=!0,!0)})).reverse().forEach((t=>e.push(t)));const i=Object.keys(r);for(let t=0;t<i.length;t+=1){const e=i[t],a={...o[e],...r[e]};o[e]=a}return e}),[]).reverse()},bo=(e,t)=>{if(Array.isArray(e)&&e.length)for(let a=0;a<e.length;a+=1){if(e[a][t])return!0}return!1},vo=e=>Array.isArray(e)?e.join(""):e,yo=(e,t)=>Array.isArray(e)?e.reduce(((e,a)=>(((e,t)=>{const a=Object.keys(e);for(let o=0;o<a.length;o+=1)if(t[a[o]]&&t[a[o]].includes(e[a[o]]))return!0;return!1})(a,t)?e.priority.push(a):e.default.push(a),e)),{priority:[],default:[]}):{default:e,priority:[]},xo=(e,t)=>({...e,[t]:void 0}),wo=["noscript","script","style"],ko=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),Eo=e=>Object.keys(e).reduce(((t,a)=>{const o=void 0!==e[a]?`${a}="${e[a]}"`:`${a}`;return t?`${t} ${o}`:o}),""),Co=(e,t={})=>Object.keys(e).reduce(((t,a)=>(t[to[a]||a]=e[a],t)),t),jo=(e,t)=>t.map(((t,a)=>{const o={key:a,[oo]:!0};return Object.keys(t).forEach((e=>{const a=to[e]||e;if("innerHTML"===a||"cssText"===a){const e=t.innerHTML||t.cssText;o.dangerouslySetInnerHTML={__html:e}}else o[a]=t[e]})),c.createElement(e,o)})),To=(e,t,a=!0)=>{switch(e){case"title":return{toComponent:()=>((e,t,a)=>{const o=Co(a,{key:t,[oo]:!0});return[c.createElement("title",o,t)]})(0,t.title,t.titleAttributes),toString:()=>((e,t,a,o)=>{const r=Eo(a),i=vo(t);return r?`<${e} ${oo}="true" ${r}>${ko(i,o)}</${e}>`:`<${e} ${oo}="true">${ko(i,o)}</${e}>`})(e,t.title,t.titleAttributes,a)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>Co(t),toString:()=>Eo(t)};default:return{toComponent:()=>jo(e,t),toString:()=>((e,t,a=!0)=>t.reduce(((t,o)=>{const r=o,i=Object.keys(r).filter((e=>!("innerHTML"===e||"cssText"===e))).reduce(((e,t)=>{const o=void 0===r[t]?t:`${t}="${ko(r[t],a)}"`;return e?`${e} ${o}`:o}),""),n=r.innerHTML||r.cssText||"",s=-1===wo.indexOf(e);return`${t}<${e} ${oo}="true" ${i}${s?"/>":`>${n}</${e}>`}`}),""))(e,t,a)}}},So=e=>{const{baseTag:t,bodyAttributes:a,encode:o=!0,htmlAttributes:r,noscriptTags:i,styleTags:n,title:s="",titleAttributes:l,prioritizeSeoTags:c}=e;let{linkTags:d,metaTags:u,scriptTags:p}=e,m={toComponent:()=>{},toString:()=>""};return c&&({priorityMethods:m,linkTags:d,metaTags:u,scriptTags:p}=(({metaTags:e,linkTags:t,scriptTags:a,encode:o})=>{const r=yo(e,Za),i=yo(t,Qa),n=yo(a,Ja);return{priorityMethods:{toComponent:()=>[...jo("meta",r.priority),...jo("link",i.priority),...jo("script",n.priority)],toString:()=>`${To("meta",r.priority,o)} ${To("link",i.priority,o)} ${To("script",n.priority,o)}`},metaTags:r.default,linkTags:i.default,scriptTags:n.default}})(e)),{priority:m,base:To("base",t,o),bodyAttributes:To("bodyAttributes",a,o),htmlAttributes:To("htmlAttributes",r,o),link:To("link",d,o),meta:To("meta",u,o),noscript:To("noscript",i,o),script:To("script",p,o),style:To("style",n,o),title:To("title",{title:s,titleAttributes:l},o)}},Ao=[],Io=!("undefined"==typeof window||!window.document||!window.document.createElement),No=class{constructor(e,t){o(this,"instances",[]),o(this,"canUseDOM",Io),o(this,"context"),o(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?Ao:this.instances,add:e=>{(this.canUseDOM?Ao:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?Ao:this.instances).indexOf(e);(this.canUseDOM?Ao:this.instances).splice(t,1)}}}),this.context=e,this.canUseDOM=t||!1,t||(e.helmet=So({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},Po=c.createContext({}),Do=(e=class extends l.Component{constructor(t){super(t),o(this,"helmetData"),this.helmetData=new No(this.props.context||{},e.canUseDOM)}render(){return c.createElement(Po.Provider,{value:this.helmetData.value},this.props.children)}},o(e,"canUseDOM",Io),e),Ro=(e,t)=>{const a=document.head||document.querySelector("head"),o=a.querySelectorAll(`${e}[${oo}]`),r=[].slice.call(o),i=[];let n;return t&&t.length&&t.forEach((t=>{const a=document.createElement(e);for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))if("innerHTML"===e)a.innerHTML=t.innerHTML;else if("cssText"===e)a.styleSheet?a.styleSheet.cssText=t.cssText:a.appendChild(document.createTextNode(t.cssText));else{const o=e,r=void 0===t[o]?"":t[o];a.setAttribute(e,r)}a.setAttribute(oo,"true"),r.some(((e,t)=>(n=t,a.isEqualNode(e))))?r.splice(n,1):i.push(a)})),r.forEach((e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),i.forEach((e=>a.appendChild(e))),{oldTags:r,newTags:i}},Mo=(e,t)=>{const a=document.getElementsByTagName(e)[0];if(!a)return;const o=a.getAttribute(oo),r=o?o.split(","):[],i=[...r],n=Object.keys(t);for(const s of n){const e=t[s]||"";a.getAttribute(s)!==e&&a.setAttribute(s,e),-1===r.indexOf(s)&&r.push(s);const o=i.indexOf(s);-1!==o&&i.splice(o,1)}for(let s=i.length-1;s>=0;s-=1)a.removeAttribute(i[s]);r.length===i.length?a.removeAttribute(oo):a.getAttribute(oo)!==n.join(",")&&a.setAttribute(oo,n.join(","))},Lo=(e,t)=>{const{baseTag:a,bodyAttributes:o,htmlAttributes:r,linkTags:i,metaTags:n,noscriptTags:s,onChangeClientState:l,scriptTags:c,styleTags:d,title:u,titleAttributes:p}=e;Mo("body",o),Mo("html",r),((e,t)=>{void 0!==e&&document.title!==e&&(document.title=vo(e)),Mo("title",t)})(u,p);const m={baseTag:Ro("base",a),linkTags:Ro("link",i),metaTags:Ro("meta",n),noscriptTags:Ro("noscript",s),scriptTags:Ro("script",c),styleTags:Ro("style",d)},g={},f={};Object.keys(m).forEach((e=>{const{newTags:t,oldTags:a}=m[e];t.length&&(g[e]=t),a.length&&(f[e]=m[e].oldTags)})),t&&t(),l(e,g,f)},zo=null,Oo=e=>{zo&&cancelAnimationFrame(zo),e.defer?zo=requestAnimationFrame((()=>{Lo(e,(()=>{zo=null}))})):(Lo(e),zo=null)},Uo=class extends l.Component{constructor(){super(...arguments),o(this,"rendered",!1)}shouldComponentUpdate(e){return!Ka(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:t}=this.props.context;let a=null;const o=(r=e.get().map((e=>{const t={...e.props};return delete t.context,t})),{baseTag:fo(["href"],r),bodyAttributes:go("bodyAttributes",r),defer:uo(r,io),encode:uo(r,no),htmlAttributes:go("htmlAttributes",r),linkTags:ho("link",["rel","href"],r),metaTags:ho("meta",["name","charset","http-equiv","property","itemprop"],r),noscriptTags:ho("noscript",["innerHTML"],r),onChangeClientState:mo(r),scriptTags:ho("script",["src","innerHTML"],r),styleTags:ho("style",["cssText"],r),title:po(r),titleAttributes:go("titleAttributes",r),prioritizeSeoTags:bo(r,co)});var r;Do.canUseDOM?Oo(o):So&&(a=So(o)),t(a)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},_o=(t=class extends l.Component{shouldComponentUpdate(e){return!$a(xo(this.props,"helmetData"),xo(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,a,o){return{...t,[e.type]:[...t[e.type]||[],{...a,...this.mapNestedChildrenToProps(e,o)}]}}mapObjectTypeChildren(e,t,a,o){switch(e.type){case"title":return{...t,[e.type]:o,titleAttributes:{...a}};case"body":return{...t,bodyAttributes:{...a}};case"html":return{...t,htmlAttributes:{...a}};default:return{...t,[e.type]:{...a}}}}mapArrayTypeChildrenToProps(e,t){let a={...t};return Object.keys(e).forEach((t=>{a={...a,[t]:e[t]}})),a}warnOnInvalidChildren(e,t){return Xa(eo.some((t=>e.type===t)),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${eo.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),Xa(!t||"string"==typeof t||Array.isArray(t)&&!t.some((e=>"string"!=typeof e)),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,t){let a={};return c.Children.forEach(e,(e=>{if(!e||!e.props)return;const{children:o,...r}=e.props,i=Object.keys(r).reduce(((e,t)=>(e[ao[t]||t]=r[t],e)),{});let{type:n}=e;switch("symbol"==typeof n?n=n.toString():this.warnOnInvalidChildren(e,o),n){case"Symbol(react.fragment)":t=this.mapChildrenToProps(o,t);break;case"link":case"meta":case"noscript":case"script":case"style":a=this.flattenArrayTypeChildren(e,a,i,o);break;default:t=this.mapObjectTypeChildren(e,t,i,o)}})),this.mapArrayTypeChildrenToProps(a,t)}render(){const{children:e,...t}=this.props;let a={...t},{helmetData:o}=t;if(e&&(a=this.mapChildrenToProps(e,a)),o&&!(o instanceof No)){o=new No(o.context,!0),delete a.helmetData}return o?c.createElement(Uo,{...a,context:o.value}):c.createElement(Po.Consumer,null,(e=>c.createElement(Uo,{...a,context:e})))}},o(t,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),t);const Bo=()=>r.jsx("div",{className:"flex items-center justify-center py-12",children:r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"w-8 h-8 border-4 border-[var(--color-border)] border-t-[var(--color-primary)] rounded-full animate-spin"}),r.jsx("span",{className:"sr-only",children:"Carregando..."})]})}),Vo={"pt-BR":{translation:{navigation:{profile:"Perfil",projects:"Projetos",backlog:"Backlog",contact:"Contato",home:"Início",goToProfile:"Ir para sessão Perfil",goToProjetos:"Ir para sessão Projetos",goToBacklog:"Ir para sessão Backlog",goToContato:"Ir para sessão Contato",menu:{open:"Abrir menu de navegação",close:"Fechar menu de navegação",toggle:"Alternar menu de navegação"},settings:{title:"Configurações",open:"Abrir configurações",close:"Fechar configurações",description:"Personalize sua experiência",theme:{toggle:"Alternar tema",lightMode:"Modo claro ativado",darkMode:"Modo escuro ativado"},language:{select:"Selecionar idioma"},sound:{toggle:"Alternar som",enabled:"Som ativado",disabled:"Som desativado"},accessibility:{menu:"Menu de acessibilidade",description:"Recursos de acessibilidade"},feedback:{open:"Abrir feedback",description:"Compartilhe sua experiência e sugestões"}}},profile:{title:"UX/Product Designer com foco em estratégia, impacto e experiência",bio:"Sou UX/Product Designer com forte atuação no design de produtos digitais focados em experiência do usuário, conversão e impacto de negócio. Com background em Marketing Digital, SEO e IA, integro estratégia, design e usabilidade em processos contínuos de melhoria e inovação.",exploreProjects:"Explore projetos",letsChat:"Vamos Conversar",downloadCV:"Download CV",linkedin:"LinkedIn",name:"Tarcisio Bispo de Araujo",ixdf:"IxDF | Interaction Design Foundation",hero:{greeting:"Olá, eu sou",roles:{uxDesigner:"UX Designer",productDesigner:"Product Designer",designStrategist:"Design Strategist",interactionDesigner:"Interaction Designer"}}},projects:{title:"Projetos",description:"Casos reais de UX/Product Design com foco em estratégia, impacto e resultados mensuráveis para negócios e usuários.",overview:"Visão Geral",discovery:"Descoberta",solution:"Solução",iteration:"Iteração",outcomes:"Resultados",insights:"Insights",seeMore:"Ver detalhes",seeLess:"Ocultar detalhes",projectImage:"Imagem do projeto",badges:{usability:"Usabilidade",informationArchitecture:"Arquitetura da Informação",userTesting:"Testes de Usuário",uxResearch:"UX Research",journeyMapping:"Mapa de Jornada",stakeholderManagement:"Stakeholder Management",productStrategy:"Product Strategy",seo:"SEO",productValidation:"Validação de Produto",visualDesign:"Design Visual",communication:"Comunicação",engagement:"Engajamento"},fgvLaw:{title:"FGV LAW",category:"Navegação e Usabilidade",overview:"Reestruturação da área de cursos jurídicos da Direito GV com foco em usabilidade e organização da informação para melhorar a experiência dos usuários.",discovery:"Identifiquei que os usuários enfrentavam dificuldade para localizar e comparar cursos na plataforma da Direito GV.",solution:"Projetei um novo painel com sistema de abas e filtros temáticos específicos para o contexto jurídico.",iteration:"Após testes com usuários, simplificamos a terminologia dos filtros e ajustamos a hierarquia de informações.",outcomes:["Melhora de 25% na visibilidade dos cursos e 35% mais interações com páginas específicas em 3 meses","Aumento de 18% na taxa de conversão de acessos em inscrições, passando de aproximadamente 8% para 10%","Redução de 30% no tempo médio de navegação, de cerca de 4 minutos para 3 minutos até a escolha do curso"],insights:"A estrutura de navegação precisa guiar, não apenas mostrar. Clareza e agrupamento relevante influenciam diretamente a percepção de valor de um curso."},direitoGV:{title:"Pesquisa Direito FGV",category:"Mapas, Fluxos e Pesquisa",overview:"Reorganização da área de pesquisa para melhorar visibilidade dos projetos acadêmicos e facilitar acesso a pesquisadores.",discovery:"A área de pesquisa estava fragmentada e pouco acessível. Pesquisadores tinham dificuldade para divulgar seus trabalhos e usuários externos não conseguiam encontrar informações relevantes sobre projetos em andamento.",solution:"Desenvolvi uma nova arquitetura de informação com categorização por áreas temáticas, perfis de pesquisadores e linha do tempo de projetos. Criei também um sistema de busca avançada.",iteration:"Realizamos testes com alunos, professores e pesquisadores. A navegação foi ajustada com base em feedback sobre nomenclatura e ordem de prioridades. Validei cada alteração com os stakeholders envolvidos.",outcomes:["Redução de 65% no tempo de navegação para encontrar projetos específicos, de aproximadamente 6 minutos para 2 minutos","Aumento de 85% nas visitas às páginas de pesquisadores, passando de cerca de 150 para mais de 280 acessos mensais","Crescimento de 40% na consulta de publicações acadêmicas e 25% mais solicitações de parcerias em 5 meses"],insights:"Áreas institucionais ganham relevância quando são navegáveis, atualizadas e refletidas de forma estratégica na arquitetura da informação."},taliparts:{title:"Taliparts",category:"UX Estratégico + B2B",overview:"Estruturação e validação digital da Taliparts para publicação de peças automotivas no Mercado Livre com foco em aprendizado rápido.",discovery:"Conduzi benchmark detalhado com concorrentes do setor automotivo. Entrevistei mecânicos e lojistas, modelei personas e apliquei a Matriz CSD para identificar certezas, suposições e dúvidas no catálogo físico.",solution:"Criei uma estratégia de validação com SEO para Mercado Livre, padronização visual de anúncios, categorização centrada no vocabulário do comprador e histórico de buscas. Também organizei KPIs e defini plano de priorização de produtos.",iteration:"Testei produtos por blocos temáticos, monitorando cliques, perguntas e taxa de conversão. Refinei descrições, títulos e até a seleção de itens com base em performance real.",outcomes:["Crescimento de 45% nas vendas dos produtos priorizados, gerando aproximadamente R$ 6.500 em receita adicional em 4 meses","Redução de 40% nas dúvidas dos compradores, diminuindo de cerca de 20 para 12 perguntas por produto publicado","Criação de processo que aumentou a eficiência de publicação em 50%, permitindo análise de mais de 80 produtos em 2 meses"],insights:"Validar digitalmente com baixo custo é possível — e necessário. A lógica de produto precisa considerar contexto físico, vocabulário técnico e diferenciais percebidos pelo cliente."},tvInstitucional:{title:"FGV TV Institucional",category:"Engajamento e Comunicação Visual",overview:"Sistema visual para TVs no hall da FGV para comunicar eventos e atualizações institucionais de forma atrativa e dinâmica.",discovery:"Alunos ignoravam murais físicos e e-mails institucionais. Identifiquei que a linguagem dos canais era desatualizada e pouco integrada com a rotina visual dos espaços.",solution:"Implementei um painel digital com curadoria de conteúdo semanal, foco em ritmo visual e clareza imediata das mensagens. A plataforma foi pensada para ser automatizada, com flexibilidade de atualização remota.",iteration:"Testamos tipos de animações, tempo de exibição e contraste. Ajustamos o calendário visual e otimizamos o layout com base em feedback de alunos e coordenação.",outcomes:["Aumento de 35% na visibilidade de eventos institucionais, melhorando o conhecimento dos alunos sobre atividades do campus","Crescimento de 20% na participação em eventos, com maior engajamento da comunidade acadêmica","Melhora de 40% na retenção de informações institucionais comparado aos métodos anteriores de comunicação"],insights:"Ambientes físicos também são interfaces. Quando bem projetados, informam, engajam e conectam — sem precisar de login."}},backlog:{title:"Ciclo de Backlogs Estratégicos",description:"Demonstração prática de como transformo desafios de negócio em soluções de UX mensuráveis. Cada caso apresenta metodologia aplicada, resultados alcançados e insights estratégicos que geram impacto real para stakeholders e usuários.",solution:"Solução",result:"Resultado",note:"Nota",noItems:"Nenhum item nesta página.",previous:"Anterior",next:"Próxima",items:[{challenge:"Usuários abandonavam formulários longos sem completar o cadastro",solution:"Implementei formulário em etapas com barra de progresso e validação em tempo real",result:"Aumento de 40% na taxa de conclusão de cadastros em 2 semanas",note:"Dividir tarefas complexas em etapas menores reduz a ansiedade e melhora a experiência."},{challenge:"Baixa adesão a recursos premium devido à falta de clareza sobre benefícios",solution:"Criei onboarding interativo com demonstrações práticas dos recursos premium",result:"Crescimento de 60% nas conversões para planos pagos em 1 mês",note:"Mostrar valor através de experiência prática é mais eficaz que apenas listar funcionalidades."},{challenge:"Usuários não encontravam facilmente o suporte quando precisavam de ajuda",solution:"Redesenhei sistema de ajuda contextual com chatbot inteligente e FAQ dinâmico",result:"Redução de 50% nos tickets de suporte e aumento de 35% na satisfação",note:"Ajuda contextual no momento certo previne frustrações e melhora a autonomia do usuário."},{challenge:"Interface complexa causava confusão em usuários iniciantes",solution:"Desenvolvi modo simplificado com tutorial progressivo e tooltips adaptativos",result:"Diminuição de 45% na taxa de abandono de novos usuários",note:"Adaptar a complexidade ao nível de experiência do usuário melhora significativamente a adoção."},{challenge:"Processo de checkout tinha alta taxa de abandono no último passo",solution:"Simplifiquei fluxo removendo campos desnecessários e adicionando opções de pagamento express",result:"Aumento de 30% na conclusão de compras e redução de 25% no tempo de checkout",note:"Cada campo extra no checkout é uma barreira potencial. Simplicidade gera conversão."},{challenge:"Usuários não percebiam atualizações importantes do produto",solution:"Implementei sistema de notificações in-app com design não intrusivo e personalização",result:"Melhoria de 55% no engajamento com novas funcionalidades",note:"Comunicação eficaz sobre mudanças mantém usuários informados sem interromper o fluxo."},{challenge:"Dificuldade para encontrar conteúdo relevante em base de conhecimento extensa",solution:"Criei sistema de busca inteligente com filtros contextuais e sugestões automáticas",result:"Aumento de 70% na utilização da base de conhecimento e redução de 40% em consultas repetitivas",note:"Busca eficiente transforma informação abundante em conhecimento acessível."},{challenge:"Baixo engajamento em funcionalidades colaborativas da plataforma",solution:"Redesenhei interface de colaboração com indicadores visuais de atividade e gamificação sutil",result:"Crescimento de 80% na colaboração entre usuários em 6 semanas",note:"Tornar a colaboração visível e recompensadora incentiva naturalmente a participação."},{challenge:"Usuários perdiam progresso ao navegar entre seções do aplicativo",solution:"Implementei sistema de auto-save com indicadores visuais de status de salvamento",result:"Eliminação de 95% das reclamações sobre perda de dados",note:"Confiança na tecnologia cresce quando o usuário vê que seu trabalho está sempre protegido."},{challenge:"Interface não responsiva causava frustração em dispositivos móveis",solution:"Desenvolvi versão mobile-first com gestos intuitivos e navegação otimizada para toque",result:"Aumento de 120% no uso mobile e melhoria de 4.2 para 4.7 na avaliação da app store",note:"Design mobile-first garante experiência consistente independente do dispositivo usado."},{challenge:"Usuários com deficiência visual enfrentavam barreiras de acessibilidade",solution:"Implementei padrões WCAG 2.1 com navegação por teclado e compatibilidade com leitores de tela",result:"Aumento de 200% no uso por pessoas com deficiência e reconhecimento em prêmio de acessibilidade",note:"Acessibilidade não é apenas compliance — é design inclusivo que beneficia todos os usuários."},{challenge:"Textos técnicos confundiam usuários não especializados",solution:"Reescrevi microcopy com linguagem clara e adicionei explicações contextuais quando necessário",result:"Redução de 60% em dúvidas de usuários e melhoria na percepção de facilidade de uso",note:"Pequenas decisões no texto têm grande impacto na experiência de leitura e compreensão."}]},contact:{title:"Vamos conversar?",description:"Estou sempre aberto a novas oportunidades e colaborações. Entre em contato para discutir projetos, parcerias ou apenas trocar ideias sobre UX e design de produtos.",form:{name:"Nome",namePlaceholder:"Digite seu nome completo",email:"E-mail",emailPlaceholder:"Digite seu melhor e-mail",subject:"Assunto",subjectPlaceholder:"Sobre o que você gostaria de conversar?",message:"Mensagem",messagePlaceholder:"Conte-me sobre seu projeto, oportunidade ou como posso ajudar...",send:"Enviar mensagem",sending:"Enviando...",success:"✅ Mensagem enviada com sucesso! Eu retorno em breve.",error:"❌ Ops! Não conseguimos enviar sua mensagem. Tente novamente ou entre em contato diretamente.",nameRequired:"Nome é obrigatório",emailRequired:"E-mail é obrigatório",emailInvalid:"E-mail inválido",subjectRequired:"Assunto é obrigatório",messageRequired:"Mensagem é obrigatória",messageMinLength:"Mensagem deve ter pelo menos 10 caracteres"},info:{email:"<EMAIL>",location:"São Paulo, Brasil",availability:"Disponível para projetos freelance e oportunidades full-time"},social:{linkedin:"LinkedIn",whatsapp:"WhatsApp",email:"E-mail"}},accessibility:{title:"Acessibilidade",subtitle:"Personalize sua experiência de navegação",description:"Configure opções de acessibilidade para melhorar sua experiência.",instructions:"Use as opções abaixo para personalizar a interface.",close:"Fechar",open:"Abrir",menuLabel:"Menu de Acessibilidade",menuTooltip:"Configurações de acessibilidade (Shift + A)",fontSize:{label:"Tamanho da fonte",increase:"Aumentar",decrease:"Diminuir",reset:"Resetar",increaseLabel:"Aumentar tamanho da fonte",decreaseLabel:"Diminuir tamanho da fonte",resetLabel:"Resetar tamanho da fonte"},contrast:{label:"Alto contraste",enable:"Ativar alto contraste",disable:"Desativar alto contraste",enabled:"Alto contraste ativado",disabled:"Alto contraste desativado"},readingMode:{label:"Modo leitura",enable:"Ativar modo leitura",disable:"Desativar modo leitura",enabled:"Modo leitura ativado",disabled:"Modo leitura desativado"},screenReader:{label:"Leitor de tela",enable:"Ativar leitor de tela",disable:"Desativar leitor de tela",enabled:"Leitor de tela ativado",disabled:"Leitor de tela desativado"},reset:{label:"Resetar configurações",action:"Configurações de acessibilidade resetadas",success:"Configurações resetadas com sucesso"},features:{fontSize:"Tamanho da fonte",fontSizeIncrease:"Aumentar fonte",fontSizeDecrease:"Diminuir fonte",fontSizeReset:"Resetar fonte",contrast:"Alto contraste",contrastEnable:"Ativar contraste",contrastDisable:"Desativar contraste",readingMode:"Modo leitura",readingModeEnable:"Ativar leitura",readingModeDisable:"Desativar leitura"},skipLinks:{skipToContent:"Pular para conteúdo principal",skipToNavigation:"Pular para navegação"},status:{enabled:"Ativado",disabled:"Desativado",fontIncreased:"Fonte aumentada",fontDecreased:"Fonte diminuída",fontReset:"Fonte resetada",contrastEnabled:"Alto contraste ativado",contrastDisabled:"Alto contraste desativado",readingEnabled:"Modo leitura ativado",readingDisabled:"Modo leitura desativado"}},sound:{enabled:"Som ativado",disabled:"Som desativado",enabledDesc:"Efeitos sonoros ativados",disabledDesc:"Efeitos sonoros desativados",enable:"Ativar som",disable:"Desativar som",toggle:"Alternar som",changed:"Som alterado para",volume:"Volume",volumeControl:"Ajustar volume do som"},tooltips:{theme:{light:"Alternar para modo claro",dark:"Alternar para modo escuro",system:"Usar preferência do sistema"},language:{switch:"Trocar idioma",current:"Idioma atual",available:"Idiomas disponíveis"},navigation:{home:"Ir para início",profile:"Ir para perfil",projects:"Ver projetos",backlog:"Ver backlog estratégico",contact:"Entrar em contato"},social:{linkedin:"Perfil no LinkedIn",email:"Enviar e-mail",whatsapp:"Conversar no WhatsApp"},sound:{enable:"Ativar efeitos sonoros",disable:"Desativar efeitos sonoros"},actions:{expand:"Expandir detalhes",collapse:"Recolher detalhes",download:"Baixar arquivo",share:"Compartilhar",copy:"Copiar link",print:"Imprimir página",backToTop:"Voltar ao topo"}},toasts:{success:{title:"Sucesso!",messageSent:"Mensagem enviada com sucesso!",settingsSaved:"Configurações salvas",linkCopied:"Link copiado para área de transferência",themeChanged:"Tema alterado",languageChanged:"Idioma alterado"},error:{title:"Erro",messageNotSent:"Erro ao enviar mensagem",networkError:"Erro de conexão",genericError:"Algo deu errado",tryAgain:"Tente novamente"},info:{title:"Informação",loading:"Carregando...",processing:"Processando...",saving:"Salvando..."},warning:{title:"Aviso",unsavedChanges:"Você tem alterações não salvas",confirmAction:"Tem certeza que deseja continuar?"}},seo:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer especializado em estratégia, impacto e experiência do usuário. Veja meus projetos de design de produtos digitais e soluções UX.",keywords:"UX Designer, Product Designer, Design de Produto, Experiência do Usuário, UI/UX, Portfolio, São Paulo",author:"Tarcisio Bispo de Araujo",pages:{home:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer focado em estratégia, impacto e experiência do usuário. Especialista em design de produtos digitais, conversão e impacto de negócio."},projects:{title:"Projetos - Tarcisio Bispo | UX Designer",description:"Veja meus principais projetos de UX/Product Design: FGV LAW, Direito GV, Taliparts e FGV TV Institucional. Casos reais com resultados mensuráveis."},backlog:{title:"Backlog Estratégico - Tarcisio Bispo | UX Designer",description:"Demonstração prática de como transformo desafios de negócio em soluções UX mensuráveis. Metodologia aplicada, resultados alcançados e insights estratégicos."},contact:{title:"Contato - Tarcisio Bispo | UX Designer",description:"Entre em contato para discutir projetos, parcerias ou oportunidades. Disponível para projetos freelance e oportunidades full-time."}}},schema:{person:{name:"Tarcisio Bispo de Araujo",jobTitle:"UX/Product Designer",description:"UX/Product Designer especializado em estratégia, impacto e experiência do usuário",location:"São Paulo, Brasil",email:"<EMAIL>",skills:["UX Design","Product Design","Estratégia de Design","Pesquisa de Usuário","Prototipagem","Testes de Usabilidade"]},organization:{name:"Tarcisio Bispo Portfolio",description:"Portfolio profissional de UX/Product Design",location:"São Paulo, Brasil"}},alts:{profile:{photo:"Foto de perfil de Tarcisio Bispo",ixdfLogo:"Logo da Interaction Design Foundation",ixdfSeal:"Selo de certificação IxDF"},projects:{fgvLaw:"Screenshot do projeto FGV LAW - designers trabalhando em equipe no escritório",direitoGV:"Screenshot do projeto Pesquisa Direito FGV - mesa de madeira com livro em frente a estante",taliparts:"Screenshot do projeto Taliparts - e-commerce de peças automotivas",tvInstitucional:"Screenshot do projeto FGV TV Institucional - placa branca pendurada na lateral de um edifício"},icons:{menu:"Ícone de menu",close:"Ícone de fechar",expand:"Ícone de expandir",collapse:"Ícone de recolher",external:"Ícone de link externo",download:"Ícone de download",email:"Ícone de e-mail",phone:"Ícone de telefone",linkedin:"Ícone do LinkedIn",whatsapp:"Ícone do WhatsApp",github:"Ícone do GitHub",sun:"Ícone de sol",moon:"Ícone de lua",globe:"Ícone de globo",volume:"Ícone de volume",success:"Ícone de sucesso",error:"Ícone de erro",warning:"Ícone de aviso",info:"Ícone de informação"},decorative:{gradient:"Gradiente decorativo",pattern:"Padrão decorativo",divider:"Divisor visual",background:"Imagem de fundo decorativa"}},language:{changed:"Idioma alterado com sucesso",current:"Idioma atual",available:"Idiomas disponíveis",portuguese:"Português",english:"English",spanish:"Español",select:"Selecionar idioma"},theme:{toggle:"Alternar tema",changed:"Tema alterado com sucesso",light:"Modo claro ativado",dark:"Modo escuro ativado",system:"Usando preferência do sistema"},footer:{copyright:"© 2024 Tarcisio Bispo. Todos os direitos reservados.",title:"UX/Product Designer"},feedback:{title:"Feedback",subtitle:"Sua opinião importa",description:"Compartilhe sua experiência e sugestões",typeQuestion:"Que tipo de feedback você gostaria de compartilhar?",close:"Fechar",back:"Voltar",send:"Enviar feedback",sending:"Enviando...",includeEmail:"Incluir meu e-mail para resposta",privacyPolicy:"Política de Privacidade",problem:"Reportar Problema",idea:"Compartilhar Ideia",praise:"Dar Elogio",problemTitle:"Reportar Problema",ideaTitle:"Compartilhar Ideia",praiseTitle:"Dar Elogio",defaultTitle:"Enviar Feedback",problemInstruction:"Descreva o problema que você encontrou em detalhes",ideaInstruction:"Compartilhe sua ideia ou sugestão de melhoria",praiseInstruction:"Conte-nos o que você gostou",defaultInstruction:"Compartilhe seu feedback conosco",problemPlaceholder:"Descreva o problema que você encontrou...",ideaPlaceholder:"Compartilhe sua ideia ou sugestão...",praisePlaceholder:"Conte-nos o que você gostou...",defaultPlaceholder:"Compartilhe seu feedback...",validation:{messageRequired:"Mensagem é obrigatória",messageMinLength:"Mínimo 5 caracteres",emailInvalid:"E-mail inválido"},form:{type:"Tipo de feedback",message:"Sua mensagem",email:"Seu e-mail (opcional)",send:"Enviar feedback",sending:"Enviando...",success:"✅ Obrigado pelo seu feedback! Sua opinião é muito importante para nós.",error:"❌ Ops! Não conseguimos enviar seu feedback. Tente novamente ou entre em contato diretamente.",messageRequired:"A mensagem é obrigatória"},status:{success:"Obrigado pelo seu feedback!",error:"Erro ao enviar feedback. Tente novamente.",sending:"Enviando feedback..."},types:{bug:"Reportar bug",suggestion:"Sugestão",compliment:"Elogio",other:"Outro"}},cookies:{title:"Este site usa cookies",description:"Usamos cookies para melhorar sua experiência, analisar o tráfego do site e personalizar conteúdo.",learnMore:"Saiba mais",acceptAll:"Aceitar todos",rejectAll:"Rejeitar todos",managePreferences:"Gerenciar preferências",savePreferences:"Salvar preferências",required:"Obrigatório",preferences:{title:"Preferências de Cookies"},types:{necessary:{title:"Cookies Necessários",description:"Estes cookies são essenciais para o funcionamento do site e não podem ser desativados."},analytics:{title:"Cookies de Análise",description:"Nos ajudam a entender como os visitantes interagem com o site coletando informações de forma anônima.",providers:"Provedores"},marketing:{title:"Cookies de Marketing",description:"São usados para rastrear visitantes em sites para exibir anúncios relevantes e atraentes."}}},common:{close:"Fechar",open:"Abrir",save:"Salvar",cancel:"Cancelar",confirm:"Confirmar",yes:"Sim",no:"Não",loading:"Carregando...",error:"Erro",success:"Sucesso",warning:"Aviso",info:"Informação"}}},"en-US":{translation:{navigation:{profile:"Profile",projects:"Projects",backlog:"Backlog",contact:"Contact",home:"Home",goToProfile:"Go to Profile section",goToProjetos:"Go to Projects section",goToBacklog:"Go to Backlog section",goToContato:"Go to Contact section",menu:{open:"Open navigation menu",close:"Close navigation menu",toggle:"Toggle navigation menu"},settings:{title:"Settings",open:"Open settings",close:"Close settings",description:"Customize your experience",theme:{toggle:"Toggle theme",lightMode:"Light mode enabled",darkMode:"Dark mode enabled"},language:{select:"Select language"},sound:{toggle:"Toggle sound",enabled:"Sound enabled",disabled:"Sound disabled"},accessibility:{menu:"Accessibility menu",description:"Accessibility features"},feedback:{open:"Open feedback",description:"Share your experience and suggestions"}}},profile:{title:"UX/Product Designer focused on strategy, impact and experience",bio:"I'm a UX/Product Designer with strong expertise in designing digital products focused on user experience, conversion and business impact. With a background in Digital Marketing, SEO and AI, I integrate strategy, design and usability in continuous improvement and innovation processes.",exploreProjects:"Explore projects",letsChat:"Let's Chat",downloadCV:"Download CV",linkedin:"LinkedIn",name:"Tarcisio Bispo de Araujo",ixdf:"IxDF | Interaction Design Foundation",hero:{greeting:"Hello, I'm",roles:{uxDesigner:"UX Designer",productDesigner:"Product Designer",designStrategist:"Design Strategist",interactionDesigner:"Interaction Designer"}}},projects:{title:"Projects",description:"Real UX/Product Design cases focused on strategy, impact and measurable results for businesses and users.",overview:"Overview",discovery:"Discovery",solution:"Solution",iteration:"Iteration",outcomes:"Outcomes",insights:"Insights",seeMore:"See details",seeLess:"Hide details",projectImage:"Project image",badges:{usability:"Usability",informationArchitecture:"Information Architecture",userTesting:"User Testing",uxResearch:"UX Research",journeyMapping:"Journey Mapping",stakeholderManagement:"Stakeholder Management",productStrategy:"Product Strategy",seo:"SEO",productValidation:"Product Validation",visualDesign:"Visual Design",communication:"Communication",engagement:"Engagement"},fgvLaw:{title:"FGV LAW",category:"Navigation and Usability",overview:"Restructuring of the legal courses area at Direito GV focusing on usability and information organization to improve user experience.",discovery:"I identified that users faced difficulty locating and comparing courses on the Direito GV platform.",solution:"I designed a new panel with tab system and thematic filters specific to the legal context.",iteration:"After user testing, we simplified filter terminology and adjusted information hierarchy.",outcomes:["25% improvement in course visibility and 35% more interactions with specific pages in 3 months","18% increase in conversion rate from visits to enrollments, rising from approximately 8% to 10%","30% reduction in average navigation time, from about 4 minutes to 3 minutes until course selection"],insights:"Navigation structure needs to guide, not just show. Clarity and relevant grouping directly influence the perception of a course's value."},direitoGV:{title:"FGV Law Research",category:"Maps, Flows and Research",overview:"Reorganization of the research area to improve visibility of academic projects and facilitate access to researchers.",discovery:"The research area was fragmented and poorly accessible. Researchers had difficulty promoting their work and external users couldn't find relevant information about ongoing projects.",solution:"I developed a new information architecture with categorization by thematic areas, researcher profiles and project timeline. I also created an advanced search system.",iteration:"We conducted tests with students, professors and researchers. Navigation was adjusted based on feedback about nomenclature and priority order. I validated each change with involved stakeholders.",outcomes:["65% reduction in navigation time to find specific projects, from approximately 6 minutes to 2 minutes","85% increase in visits to researcher pages, rising from about 150 to over 280 monthly visits","40% growth in academic publication consultation and 25% more partnership requests in 5 months"],insights:"Institutional areas gain relevance when they are navigable, updated and strategically reflected in information architecture."},taliparts:{title:"Taliparts",category:"Strategic UX + B2B",overview:"Structuring and digital validation of Taliparts for publishing automotive parts on Mercado Livre with focus on rapid learning.",discovery:"I conducted detailed benchmark with automotive sector competitors. I interviewed mechanics and store owners, modeled personas and applied CSD Matrix to identify certainties, assumptions and doubts in the physical catalog.",solution:"I created a validation strategy with SEO for Mercado Livre, visual standardization of ads, categorization centered on buyer vocabulary and search history. I also organized KPIs and defined product prioritization plan.",iteration:"I tested products by thematic blocks, monitoring clicks, questions and conversion rate. I refined descriptions, titles and even item selection based on real performance.",outcomes:["45% growth in sales of prioritized products, generating approximately R$ 6,500 in additional revenue in 4 months","40% reduction in buyer doubts, decreasing from about 20 to 12 questions per published product","Creation of process that increased publication efficiency by 50%, allowing analysis of over 80 products in 2 months"],insights:"Digital validation at low cost is possible — and necessary. Product logic needs to consider physical context, technical vocabulary and differentials perceived by the customer."},tvInstitucional:{title:"FGV Institutional TV",category:"Engagement and Visual Communication",overview:"Visual system for TVs in FGV hall to communicate events and institutional updates in an attractive and dynamic way.",discovery:"Students ignored physical bulletin boards and institutional emails. I identified that the language of channels was outdated and poorly integrated with the visual routine of spaces.",solution:"I implemented a digital panel with weekly content curation, focus on visual rhythm and immediate clarity of messages. The platform was designed to be automated, with remote update flexibility.",iteration:"We tested types of animations, display time and contrast. We adjusted the visual calendar and optimized layout based on feedback from students and coordination.",outcomes:["35% increase in institutional event visibility, improving student knowledge about campus activities","20% growth in event participation, with greater engagement from the academic community","40% improvement in institutional information retention compared to previous communication methods"],insights:"Physical environments are also interfaces. When well designed, they inform, engage and connect — without needing login."}},backlog:{title:"Strategic Backlog Cycle",description:"Practical demonstration of how I transform business challenges into measurable UX solutions. Each case presents applied methodology, achieved results and strategic insights that generate real impact for stakeholders and users.",solution:"Solution",result:"Result",note:"Note",noItems:"No items on this page.",previous:"Previous",next:"Next",items:[{challenge:"Users abandoned long forms without completing registration",solution:"Implemented step-by-step form with progress bar and real-time validation",result:"40% increase in registration completion rate in 2 weeks",note:"Breaking complex tasks into smaller steps reduces anxiety and improves experience."},{challenge:"Low adoption of premium features due to lack of clarity about benefits",solution:"Created interactive onboarding with practical demonstrations of premium features",result:"60% growth in conversions to paid plans in 1 month",note:"Showing value through practical experience is more effective than just listing features."},{challenge:"Users couldn't easily find support when they needed help",solution:"Redesigned contextual help system with intelligent chatbot and dynamic FAQ",result:"50% reduction in support tickets and 35% increase in satisfaction",note:"Contextual help at the right moment prevents frustrations and improves user autonomy."},{challenge:"Complex interface caused confusion in beginner users",solution:"Developed simplified mode with progressive tutorial and adaptive tooltips",result:"45% decrease in new user abandonment rate",note:"Adapting complexity to user experience level significantly improves adoption."},{challenge:"Checkout process had high abandonment rate at the last step",solution:"Simplified flow by removing unnecessary fields and adding express payment options",result:"30% increase in purchase completion and 25% reduction in checkout time",note:"Each extra field in checkout is a potential barrier. Simplicity generates conversion."},{challenge:"Users didn't notice important product updates",solution:"Implemented in-app notification system with non-intrusive design and personalization",result:"55% improvement in engagement with new features",note:"Effective communication about changes keeps users informed without interrupting flow."},{challenge:"Difficulty finding relevant content in extensive knowledge base",solution:"Created intelligent search system with contextual filters and automatic suggestions",result:"70% increase in knowledge base usage and 40% reduction in repetitive queries",note:"Efficient search transforms abundant information into accessible knowledge."},{challenge:"Low engagement in platform's collaborative features",solution:"Redesigned collaboration interface with visual activity indicators and subtle gamification",result:"80% growth in user collaboration in 6 weeks",note:"Making collaboration visible and rewarding naturally encourages participation."},{challenge:"Users lost progress when navigating between app sections",solution:"Implemented auto-save system with visual indicators of save status",result:"Elimination of 95% of complaints about data loss",note:"Trust in technology grows when users see their work is always protected."},{challenge:"Non-responsive interface caused frustration on mobile devices",solution:"Developed mobile-first version with intuitive gestures and touch-optimized navigation",result:"120% increase in mobile usage and improvement from 4.2 to 4.7 in app store rating",note:"Mobile-first design ensures consistent experience regardless of device used."},{challenge:"Users with visual impairments faced accessibility barriers",solution:"Implemented WCAG 2.1 standards with keyboard navigation and screen reader compatibility",result:"200% increase in usage by people with disabilities and recognition in accessibility award",note:"Accessibility is not just compliance — it's inclusive design that benefits all users."},{challenge:"Technical texts confused non-specialized users",solution:"Rewrote microcopy with clear language and added contextual explanations when necessary",result:"60% reduction in user doubts and improvement in perceived ease of use",note:"Small decisions in text have great impact on reading experience and comprehension."}]},contact:{title:"Let's talk?",description:"I'm always open to new opportunities and collaborations. Get in touch to discuss projects, partnerships or just exchange ideas about UX and product design.",form:{name:"Name",namePlaceholder:"Enter your full name",email:"Email",emailPlaceholder:"Enter your best email",subject:"Subject",subjectPlaceholder:"What would you like to talk about?",message:"Message",messagePlaceholder:"Tell me about your project, opportunity or how I can help...",send:"Send message",sending:"Sending...",success:"✅ Message sent successfully! I'll get back to you soon.",error:"❌ Oops! We couldn't send your message. Try again or contact directly.",nameRequired:"Name is required",emailRequired:"Email is required",emailInvalid:"Invalid email",subjectRequired:"Subject is required",messageRequired:"Message is required",messageMinLength:"Message must be at least 10 characters"},info:{email:"<EMAIL>",location:"São Paulo, Brazil",availability:"Available for freelance projects and full-time opportunities"},social:{linkedin:"LinkedIn",whatsapp:"WhatsApp",email:"Email"}},accessibility:{title:"Accessibility",subtitle:"Customize your browsing experience",description:"Configure accessibility options to improve your experience.",instructions:"Use the options below to customize the interface.",close:"Close",open:"Open",menuLabel:"Accessibility Menu",menuTooltip:"Accessibility settings (Shift + A)",fontSize:{label:"Font size",increase:"Increase",decrease:"Decrease",reset:"Reset",increaseLabel:"Increase font size",decreaseLabel:"Decrease font size",resetLabel:"Reset font size"},contrast:{label:"High contrast",enable:"Enable high contrast",disable:"Disable high contrast",enabled:"High contrast enabled",disabled:"High contrast disabled"},readingMode:{label:"Reading mode",enable:"Enable reading mode",disable:"Disable reading mode",enabled:"Reading mode enabled",disabled:"Reading mode disabled"},screenReader:{label:"Screen reader",enable:"Enable screen reader",disable:"Disable screen reader",enabled:"Screen reader enabled",disabled:"Screen reader disabled"},reset:{label:"Reset settings",action:"Accessibility settings reset",success:"Settings reset successfully"},features:{fontSize:"Font size",fontSizeIncrease:"Increase font",fontSizeDecrease:"Decrease font",fontSizeReset:"Reset font",contrast:"High contrast",contrastEnable:"Enable contrast",contrastDisable:"Disable contrast",readingMode:"Reading mode",readingModeEnable:"Enable reading",readingModeDisable:"Disable reading"},skipLinks:{skipToContent:"Skip to main content",skipToNavigation:"Skip to navigation"},status:{enabled:"Enabled",disabled:"Disabled",fontIncreased:"Font increased",fontDecreased:"Font decreased",fontReset:"Font reset",contrastEnabled:"High contrast enabled",contrastDisabled:"High contrast disabled",readingEnabled:"Reading mode enabled",readingDisabled:"Reading mode disabled"}},sound:{enabled:"Sound enabled",disabled:"Sound disabled",enabledDesc:"Sound effects enabled",disabledDesc:"Sound effects disabled",enable:"Enable sound",disable:"Disable sound",toggle:"Toggle sound",changed:"Sound changed to",volume:"Volume",volumeControl:"Adjust sound volume"},tooltips:{theme:{light:"Switch to light mode",dark:"Switch to dark mode",system:"Use system preference"},language:{switch:"Switch language",current:"Current language",available:"Available languages"},navigation:{home:"Go to home",profile:"Go to profile",projects:"View projects",backlog:"View strategic backlog",contact:"Get in touch"},social:{linkedin:"LinkedIn profile",email:"Send email",whatsapp:"Chat on WhatsApp"},sound:{enable:"Enable sound effects",disable:"Disable sound effects"},actions:{expand:"Expand details",collapse:"Collapse details",download:"Download file",share:"Share",copy:"Copy link",print:"Print page",backToTop:"Back to top"}},toasts:{success:{title:"Success!",messageSent:"Message sent successfully!",settingsSaved:"Settings saved",linkCopied:"Link copied to clipboard",themeChanged:"Theme changed",languageChanged:"Language changed"},error:{title:"Error",messageNotSent:"Error sending message",networkError:"Connection error",genericError:"Something went wrong",tryAgain:"Try again"},info:{title:"Information",loading:"Loading...",processing:"Processing...",saving:"Saving..."},warning:{title:"Warning",unsavedChanges:"You have unsaved changes",confirmAction:"Are you sure you want to continue?"}},seo:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer specialized in strategy, impact and user experience. See my digital product design projects and UX solutions.",keywords:"UX Designer, Product Designer, Product Design, User Experience, UI/UX, Portfolio, São Paulo",author:"Tarcisio Bispo de Araujo",pages:{home:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer focused on strategy, impact and user experience. Expert in digital product design, conversion and business impact."},projects:{title:"Projects - Tarcisio Bispo | UX Designer",description:"See my main UX/Product Design projects: FGV LAW, Direito GV, Taliparts and FGV Institutional TV. Real cases with measurable results."},backlog:{title:"Strategic Backlog - Tarcisio Bispo | UX Designer",description:"Practical demonstration of how I transform business challenges into measurable UX solutions. Applied methodology, achieved results and strategic insights."},contact:{title:"Contact - Tarcisio Bispo | UX Designer",description:"Get in touch to discuss projects, partnerships or opportunities. Available for freelance projects and full-time opportunities."}}},schema:{person:{name:"Tarcisio Bispo de Araujo",jobTitle:"UX/Product Designer",description:"UX/Product Designer specialized in strategy, impact and user experience",location:"São Paulo, Brazil",email:"<EMAIL>",skills:["UX Design","Product Design","Design Strategy","User Research","Prototyping","Usability Testing"]},organization:{name:"Tarcisio Bispo Portfolio",description:"Professional UX/Product Design portfolio",location:"São Paulo, Brazil"}},alts:{profile:{photo:"Tarcisio Bispo profile photo",ixdfLogo:"Interaction Design Foundation logo",ixdfSeal:"IxDF certification seal"},projects:{fgvLaw:"FGV LAW project screenshot - designers working as a team in the office",direitoGV:"FGV Law Research project screenshot - wooden table with book in front of bookshelf",taliparts:"Taliparts project screenshot - automotive parts e-commerce",tvInstitucional:"FGV Institutional TV project screenshot - white sign hanging on the side of a building"},icons:{menu:"Menu icon",close:"Close icon",expand:"Expand icon",collapse:"Collapse icon",external:"External link icon",download:"Download icon",email:"Email icon",phone:"Phone icon",linkedin:"LinkedIn icon",whatsapp:"WhatsApp icon",github:"GitHub icon",sun:"Sun icon",moon:"Moon icon",globe:"Globe icon",volume:"Volume icon",success:"Success icon",error:"Error icon",warning:"Warning icon",info:"Information icon"},decorative:{gradient:"Decorative gradient",pattern:"Decorative pattern",divider:"Visual divider",background:"Decorative background image"}},language:{changed:"Language changed successfully",current:"Current language",available:"Available languages",portuguese:"Português",english:"English",spanish:"Español",select:"Select language"},theme:{toggle:"Toggle theme",changed:"Theme changed successfully",light:"Light mode enabled",dark:"Dark mode enabled",system:"Using system preference"},footer:{copyright:"© 2024 Tarcisio Bispo. All rights reserved.",title:"UX/Product Designer"},feedback:{title:"Feedback",subtitle:"Your opinion matters",description:"Share your experience and suggestions",typeQuestion:"What type of feedback would you like to share?",close:"Close",back:"Back",send:"Send feedback",sending:"Sending...",includeEmail:"Include my email for response",privacyPolicy:"Privacy Policy",problem:"Report Problem",idea:"Share Idea",praise:"Give Praise",problemTitle:"Report Problem",ideaTitle:"Share Idea",praiseTitle:"Give Praise",defaultTitle:"Send Feedback",problemInstruction:"Describe the problem you found in detail",ideaInstruction:"Share your idea or improvement suggestion",praiseInstruction:"Tell us what you liked",defaultInstruction:"Share your feedback with us",problemPlaceholder:"Describe the problem you found...",ideaPlaceholder:"Share your idea or suggestion...",praisePlaceholder:"Tell us what you liked...",defaultPlaceholder:"Share your feedback...",validation:{messageRequired:"Message is required",messageMinLength:"Minimum 5 characters",emailInvalid:"Invalid email"},form:{type:"Feedback type",message:"Your message",email:"Your email (optional)",send:"Send feedback",sending:"Sending...",success:"✅ Thank you for your feedback! Your opinion is very important to us.",error:"❌ Oops! We couldn't send your feedback. Try again or contact directly.",messageRequired:"Message is required"},status:{success:"Thank you for your feedback!",error:"Error sending feedback. Please try again.",sending:"Sending feedback..."},types:{bug:"Report bug",suggestion:"Suggestion",compliment:"Compliment",other:"Other"}},cookies:{title:"This site uses cookies",description:"We use cookies to improve your experience, analyze site traffic and personalize content.",learnMore:"Learn more",acceptAll:"Accept all",rejectAll:"Reject all",managePreferences:"Manage preferences",savePreferences:"Save preferences",required:"Required",preferences:{title:"Cookie Preferences"},types:{necessary:{title:"Necessary Cookies",description:"These cookies are essential for the website to function and cannot be disabled."},analytics:{title:"Analytics Cookies",description:"Help us understand how visitors interact with the site by collecting information anonymously.",providers:"Providers"},marketing:{title:"Marketing Cookies",description:"Used to track visitors across websites to display relevant and engaging ads."}}},common:{close:"Close",open:"Open",save:"Save",cancel:"Cancel",confirm:"Confirm",yes:"Yes",no:"No",loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Information"}}},"es-ES":{translation:{navigation:{profile:"Perfil",projects:"Proyectos",backlog:"Backlog",contact:"Contacto",home:"Inicio",goToProfile:"Ir a sección Perfil",goToProjetos:"Ir a sección Proyectos",goToBacklog:"Ir a sección Backlog",goToContato:"Ir a sección Contacto",menu:{open:"Abrir menú de navegación",close:"Cerrar menú de navegación",toggle:"Alternar menú de navegación"},settings:{title:"Configuraciones",open:"Abrir configuraciones",close:"Cerrar configuraciones",description:"Personaliza tu experiencia",theme:{toggle:"Alternar tema",lightMode:"Modo claro activado",darkMode:"Modo oscuro activado"},language:{select:"Seleccionar idioma"},sound:{toggle:"Alternar sonido",enabled:"Sonido activado",disabled:"Sonido desactivado"},accessibility:{menu:"Menú de accesibilidad",description:"Funciones de accesibilidad"},feedback:{open:"Abrir feedback",description:"Comparte tu experiencia y sugerencias"}}},profile:{title:"UX/Product Designer enfocado en estrategia, impacto y experiencia",bio:"Soy UX/Product Designer con fuerte experiencia en el diseño de productos digitales enfocados en experiencia del usuario, conversión e impacto empresarial. Con experiencia en Marketing Digital, SEO e IA, integro estrategia, diseño y usabilidad en procesos continuos de mejora e innovación.",exploreProjects:"Explorar proyectos",letsChat:"Conversemos",downloadCV:"Descargar CV",linkedin:"LinkedIn",name:"Tarcisio Bispo de Araujo",ixdf:"IxDF | Interaction Design Foundation",hero:{greeting:"Hola, soy",roles:{uxDesigner:"UX Designer",productDesigner:"Product Designer",designStrategist:"Design Strategist",interactionDesigner:"Interaction Designer"}}},projects:{title:"Proyectos",description:"Casos reales de UX/Product Design enfocados en estrategia, impacto y resultados medibles para empresas y usuarios.",overview:"Resumen",discovery:"Descubrimiento",solution:"Solución",iteration:"Iteración",outcomes:"Resultados",insights:"Insights",seeMore:"Ver detalles",seeLess:"Ocultar detalles",projectImage:"Imagen del proyecto",badges:{usability:"Usabilidad",informationArchitecture:"Arquitectura de la Información",userTesting:"Pruebas de Usuario",uxResearch:"UX Research",journeyMapping:"Mapeo de Jornada",stakeholderManagement:"Gestión de Stakeholders",productStrategy:"Estrategia de Producto",seo:"SEO",productValidation:"Validación de Producto",visualDesign:"Diseño Visual",communication:"Comunicación",engagement:"Engagement"},fgvLaw:{title:"FGV LAW",category:"Navegación y Usabilidad",overview:"Reestructuración del área de cursos jurídicos de Direito GV enfocada en usabilidad y organización de la información para mejorar la experiencia del usuario.",discovery:"Identifiqué que los usuarios tenían dificultades para localizar y comparar cursos en la plataforma de Direito GV.",solution:"Diseñé un nuevo panel con sistema de pestañas y filtros temáticos específicos para el contexto jurídico.",iteration:"Después de las pruebas con usuarios, simplificamos la terminología de los filtros y ajustamos la jerarquía de información.",outcomes:["Mejora del 25% en la visibilidad de cursos y 35% más interacciones con páginas específicas en 3 meses","Aumento del 18% en la tasa de conversión de visitas a inscripciones, pasando de aproximadamente 8% a 10%","Reducción del 30% en el tiempo promedio de navegación, de cerca de 4 minutos a 3 minutos hasta la selección del curso"],insights:"La estructura de navegación necesita guiar, no solo mostrar. La claridad y agrupación relevante influyen directamente en la percepción de valor de un curso."},direitoGV:{title:"Investigación Derecho FGV",category:"Mapas, Flujos e Investigación",overview:"Reorganización del área de investigación para mejorar la visibilidad de proyectos académicos y facilitar el acceso a investigadores.",discovery:"El área de investigación estaba fragmentada y era poco accesible. Los investigadores tenían dificultades para promocionar su trabajo y los usuarios externos no podían encontrar información relevante sobre proyectos en curso.",solution:"Desarrollé una nueva arquitectura de información con categorización por áreas temáticas, perfiles de investigadores y línea de tiempo de proyectos. También creé un sistema de búsqueda avanzada.",iteration:"Realizamos pruebas con estudiantes, profesores e investigadores. La navegación se ajustó basándose en comentarios sobre nomenclatura y orden de prioridades. Validé cada cambio con los stakeholders involucrados.",outcomes:["Reducción del 65% en el tiempo de navegación para encontrar proyectos específicos, de aproximadamente 6 minutos a 2 minutos","Aumento del 85% en visitas a páginas de investigadores, pasando de cerca de 150 a más de 280 visitas mensuales","Crecimiento del 40% en consulta de publicaciones académicas y 25% más solicitudes de asociaciones en 5 meses"],insights:"Las áreas institucionales ganan relevancia cuando son navegables, actualizadas y reflejadas estratégicamente en la arquitectura de información."},taliparts:{title:"Taliparts",category:"UX Estratégico + B2B",overview:"Estructuración y validación digital de Taliparts para publicación de autopartes en Mercado Libre con enfoque en aprendizaje rápido.",discovery:"Realicé benchmark detallado con competidores del sector automotriz. Entrevisté mecánicos y propietarios de tiendas, modelé personas y apliqué la Matriz CSD para identificar certezas, suposiciones y dudas en el catálogo físico.",solution:"Creé una estrategia de validación con SEO para Mercado Libre, estandarización visual de anuncios, categorización centrada en el vocabulario del comprador e historial de búsquedas. También organicé KPIs y definí plan de priorización de productos.",iteration:"Probé productos por bloques temáticos, monitoreando clics, preguntas y tasa de conversión. Refiné descripciones, títulos e incluso selección de artículos basándome en rendimiento real.",outcomes:["Crecimiento del 45% en ventas de productos priorizados, generando aproximadamente R$ 6,500 en ingresos adicionales en 4 meses","Reducción del 40% en dudas de compradores, disminuyendo de cerca de 20 a 12 preguntas por producto publicado","Creación de proceso que aumentó la eficiencia de publicación en 50%, permitiendo análisis de más de 80 productos en 2 meses"],insights:"La validación digital a bajo costo es posible — y necesaria. La lógica del producto necesita considerar contexto físico, vocabulario técnico y diferenciales percibidos por el cliente."},tvInstitucional:{title:"FGV TV Institucional",category:"Engagement y Comunicación Visual",overview:"Sistema visual para TVs en el hall de FGV para comunicar eventos y actualizaciones institucionales de manera atractiva y dinámica.",discovery:"Los estudiantes ignoraban carteleras físicas y emails institucionales. Identifiqué que el lenguaje de los canales estaba desactualizado y mal integrado con la rutina visual de los espacios.",solution:"Implementé un panel digital con curaduría de contenido semanal, enfoque en ritmo visual y claridad inmediata de mensajes. La plataforma fue diseñada para ser automatizada, con flexibilidad de actualización remota.",iteration:"Probamos tipos de animaciones, tiempo de visualización y contraste. Ajustamos el calendario visual y optimizamos el layout basándose en comentarios de estudiantes y coordinación.",outcomes:["Aumento del 35% en visibilidad de eventos institucionales, mejorando el conocimiento de estudiantes sobre actividades del campus","Crecimiento del 20% en participación en eventos, con mayor engagement de la comunidad académica","Mejora del 40% en retención de información institucional comparado con métodos anteriores de comunicación"],insights:"Los ambientes físicos también son interfaces. Cuando están bien diseñados, informan, involucran y conectan — sin necesidad de login."}},backlog:{title:"Ciclo de Backlog Estratégico",description:"Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Cada caso presenta metodología aplicada, resultados alcanzados e insights estratégicos que generan impacto real para stakeholders y usuarios.",solution:"Solución",result:"Resultado",note:"Nota",noItems:"No hay elementos en esta página.",previous:"Anterior",next:"Siguiente",items:[{challenge:"Los usuarios abandonaban formularios largos sin completar el registro",solution:"Implementé formulario paso a paso con barra de progreso y validación en tiempo real",result:"Aumento del 40% en la tasa de finalización de registros en 2 semanas",note:"Dividir tareas complejas en pasos más pequeños reduce la ansiedad y mejora la experiencia."},{challenge:"Baja adopción de funciones premium debido a falta de claridad sobre beneficios",solution:"Creé onboarding interactivo con demostraciones prácticas de funciones premium",result:"Crecimiento del 60% en conversiones a planes pagos en 1 mes",note:"Mostrar valor a través de experiencia práctica es más efectivo que solo listar funcionalidades."},{challenge:"Los usuarios no encontraban fácilmente soporte cuando necesitaban ayuda",solution:"Rediseñé sistema de ayuda contextual con chatbot inteligente y FAQ dinámico",result:"Reducción del 50% en tickets de soporte y aumento del 35% en satisfacción",note:"Ayuda contextual en el momento correcto previene frustraciones y mejora la autonomía del usuario."},{challenge:"Interfaz compleja causaba confusión en usuarios principiantes",solution:"Desarrollé modo simplificado con tutorial progresivo y tooltips adaptativos",result:"Disminución del 45% en tasa de abandono de nuevos usuarios",note:"Adaptar la complejidad al nivel de experiencia del usuario mejora significativamente la adopción."},{challenge:"Proceso de checkout tenía alta tasa de abandono en el último paso",solution:"Simplifiqué flujo eliminando campos innecesarios y agregando opciones de pago express",result:"Aumento del 30% en finalización de compras y reducción del 25% en tiempo de checkout",note:"Cada campo extra en checkout es una barrera potencial. La simplicidad genera conversión."},{challenge:"Los usuarios no percibían actualizaciones importantes del producto",solution:"Implementé sistema de notificaciones in-app con diseño no intrusivo y personalización",result:"Mejora del 55% en engagement con nuevas funcionalidades",note:"Comunicación efectiva sobre cambios mantiene a usuarios informados sin interrumpir el flujo."},{challenge:"Dificultad para encontrar contenido relevante en base de conocimiento extensa",solution:"Creé sistema de búsqueda inteligente con filtros contextuales y sugerencias automáticas",result:"Aumento del 70% en utilización de base de conocimiento y reducción del 40% en consultas repetitivas",note:"Búsqueda eficiente transforma información abundante en conocimiento accesible."},{challenge:"Bajo engagement en funcionalidades colaborativas de la plataforma",solution:"Rediseñé interfaz de colaboración con indicadores visuales de actividad y gamificación sutil",result:"Crecimiento del 80% en colaboración entre usuarios en 6 semanas",note:"Hacer la colaboración visible y gratificante incentiva naturalmente la participación."},{challenge:"Los usuarios perdían progreso al navegar entre secciones de la app",solution:"Implementé sistema de auto-guardado con indicadores visuales de estado de guardado",result:"Eliminación del 95% de quejas sobre pérdida de datos",note:"La confianza en la tecnología crece cuando los usuarios ven que su trabajo está siempre protegido."},{challenge:"Interfaz no responsiva causaba frustración en dispositivos móviles",solution:"Desarrollé versión mobile-first con gestos intuitivos y navegación optimizada para toque",result:"Aumento del 120% en uso móvil y mejora de 4.2 a 4.7 en calificación de app store",note:"Diseño mobile-first garantiza experiencia consistente independientemente del dispositivo usado."},{challenge:"Usuarios con discapacidad visual enfrentaban barreras de accesibilidad",solution:"Implementé estándares WCAG 2.1 con navegación por teclado y compatibilidad con lectores de pantalla",result:"Aumento del 200% en uso por personas con discapacidad y reconocimiento en premio de accesibilidad",note:"Accesibilidad no es solo cumplimiento — es diseño inclusivo que beneficia a todos los usuarios."},{challenge:"Textos técnicos confundían a usuarios no especializados",solution:"Reescribí microcopy con lenguaje claro y agregué explicaciones contextuales cuando era necesario",result:"Reducción del 60% en dudas de usuarios y mejora en percepción de facilidad de uso",note:"Pequeñas decisiones en el texto tienen gran impacto en la experiencia de lectura y comprensión."}]},contact:{title:"¿Conversamos?",description:"Siempre estoy abierto a nuevas oportunidades y colaboraciones. Ponte en contacto para discutir proyectos, asociaciones o simplemente intercambiar ideas sobre UX y diseño de productos.",form:{name:"Nombre",namePlaceholder:"Ingresa tu nombre completo",email:"Email",emailPlaceholder:"Ingresa tu mejor email",subject:"Asunto",subjectPlaceholder:"¿De qué te gustaría hablar?",message:"Mensaje",messagePlaceholder:"Cuéntame sobre tu proyecto, oportunidad o cómo puedo ayudar...",send:"Enviar mensaje",sending:"Enviando...",success:"✅ ¡Mensaje enviado con éxito! Te responderé pronto.",error:"❌ ¡Ups! No pudimos enviar tu mensaje. Intenta de nuevo o contacta directamente.",nameRequired:"El nombre es obligatorio",emailRequired:"El email es obligatorio",emailInvalid:"Email inválido",subjectRequired:"El asunto es obligatorio",messageRequired:"El mensaje es obligatorio",messageMinLength:"El mensaje debe tener al menos 10 caracteres"},info:{email:"<EMAIL>",location:"São Paulo, Brasil",availability:"Disponible para proyectos freelance y oportunidades full-time"},social:{linkedin:"LinkedIn",whatsapp:"WhatsApp",email:"Email"}},accessibility:{title:"Accesibilidad",subtitle:"Personaliza tu experiencia de navegación",description:"Configura opciones de accesibilidad para mejorar tu experiencia.",instructions:"Usa las opciones a continuación para personalizar la interfaz.",close:"Cerrar",open:"Abrir",menuLabel:"Menú de Accesibilidad",menuTooltip:"Configuraciones de accesibilidad (Shift + A)",fontSize:{label:"Tamaño de fuente",increase:"Aumentar",decrease:"Disminuir",reset:"Resetear",increaseLabel:"Aumentar tamaño de fuente",decreaseLabel:"Disminuir tamaño de fuente",resetLabel:"Resetear tamaño de fuente"},contrast:{label:"Alto contraste",enable:"Activar alto contraste",disable:"Desactivar alto contraste",enabled:"Alto contraste activado",disabled:"Alto contraste desactivado"},readingMode:{label:"Modo lectura",enable:"Activar modo lectura",disable:"Desactivar modo lectura",enabled:"Modo lectura activado",disabled:"Modo lectura desactivado"},screenReader:{label:"Lector de pantalla",enable:"Activar lector de pantalla",disable:"Desactivar lector de pantalla",enabled:"Lector de pantalla activado",disabled:"Lector de pantalla desactivado"},reset:{label:"Resetear configuraciones",action:"Configuraciones de accesibilidad reseteadas",success:"Configuraciones reseteadas con éxito"},features:{fontSize:"Tamaño de fuente",fontSizeIncrease:"Aumentar fuente",fontSizeDecrease:"Disminuir fuente",fontSizeReset:"Resetear fuente",contrast:"Alto contraste",contrastEnable:"Activar contraste",contrastDisable:"Desactivar contraste",readingMode:"Modo lectura",readingModeEnable:"Activar lectura",readingModeDisable:"Desactivar lectura"},skipLinks:{skipToContent:"Saltar al contenido principal",skipToNavigation:"Saltar a navegación"},status:{enabled:"Activado",disabled:"Desactivado",fontIncreased:"Fuente aumentada",fontDecreased:"Fuente disminuida",fontReset:"Fuente reseteada",contrastEnabled:"Alto contraste activado",contrastDisabled:"Alto contraste desactivado",readingEnabled:"Modo lectura activado",readingDisabled:"Modo lectura desactivado"}},sound:{enabled:"Sonido activado",disabled:"Sonido desactivado",enabledDesc:"Efectos de sonido activados",disabledDesc:"Efectos de sonido desactivados",enable:"Activar sonido",disable:"Desactivar sonido",toggle:"Alternar sonido",changed:"Sonido cambiado a",volume:"Volumen",volumeControl:"Ajustar volumen del sonido"},tooltips:{theme:{light:"Cambiar a modo claro",dark:"Cambiar a modo oscuro",system:"Usar preferencia del sistema"},language:{switch:"Cambiar idioma",current:"Idioma actual",available:"Idiomas disponibles"},navigation:{home:"Ir al inicio",profile:"Ir al perfil",projects:"Ver proyectos",backlog:"Ver backlog estratégico",contact:"Ponerse en contacto"},social:{linkedin:"Perfil de LinkedIn",email:"Enviar email",whatsapp:"Chatear en WhatsApp"},sound:{enable:"Activar efectos de sonido",disable:"Desactivar efectos de sonido"},actions:{expand:"Expandir detalles",collapse:"Contraer detalles",download:"Descargar archivo",share:"Compartir",copy:"Copiar enlace",print:"Imprimir página",backToTop:"Volver arriba"}},toasts:{success:{title:"¡Éxito!",messageSent:"¡Mensaje enviado con éxito!",settingsSaved:"Configuraciones guardadas",linkCopied:"Enlace copiado al portapapeles",themeChanged:"Tema cambiado",languageChanged:"Idioma cambiado"},error:{title:"Error",messageNotSent:"Error al enviar mensaje",networkError:"Error de conexión",genericError:"Algo salió mal",tryAgain:"Intenta de nuevo"},info:{title:"Información",loading:"Cargando...",processing:"Procesando...",saving:"Guardando..."},warning:{title:"Advertencia",unsavedChanges:"Tienes cambios no guardados",confirmAction:"¿Estás seguro de que quieres continuar?"}},seo:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer especializado en estrategia, impacto y experiencia del usuario. Ve mis proyectos de diseño de productos digitales y soluciones UX.",keywords:"UX Designer, Product Designer, Diseño de Producto, Experiencia del Usuario, UI/UX, Portfolio, São Paulo",author:"Tarcisio Bispo de Araujo",pages:{home:{title:"Tarcisio Bispo - UX/Product Designer | Portfolio",description:"UX/Product Designer enfocado en estrategia, impacto y experiencia del usuario. Experto en diseño de productos digitales, conversión e impacto empresarial."},projects:{title:"Proyectos - Tarcisio Bispo | UX Designer",description:"Ve mis principales proyectos de UX/Product Design: FGV LAW, Direito GV, Taliparts y FGV TV Institucional. Casos reales con resultados medibles."},backlog:{title:"Backlog Estratégico - Tarcisio Bispo | UX Designer",description:"Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Metodología aplicada, resultados alcanzados e insights estratégicos."},contact:{title:"Contacto - Tarcisio Bispo | UX Designer",description:"Ponte en contacto para discutir proyectos, asociaciones u oportunidades. Disponible para proyectos freelance y oportunidades full-time."}}},schema:{person:{name:"Tarcisio Bispo de Araujo",jobTitle:"UX/Product Designer",description:"UX/Product Designer especializado en estrategia, impacto y experiencia del usuario",location:"São Paulo, Brasil",email:"<EMAIL>",skills:["UX Design","Product Design","Estrategia de Diseño","Investigación de Usuario","Prototipado","Pruebas de Usabilidad"]},organization:{name:"Tarcisio Bispo Portfolio",description:"Portfolio profesional de UX/Product Design",location:"São Paulo, Brasil"}},alts:{profile:{photo:"Foto de perfil de Tarcisio Bispo",ixdfLogo:"Logo de Interaction Design Foundation",ixdfSeal:"Sello de certificación IxDF"},projects:{fgvLaw:"Captura de pantalla del proyecto FGV LAW - diseñadores trabajando en equipo en la oficina",direitoGV:"Captura de pantalla del proyecto Investigación Derecho FGV - mesa de madera con libro frente a estantería",taliparts:"Captura de pantalla del proyecto Taliparts - e-commerce de autopartes",tvInstitucional:"Captura de pantalla del proyecto FGV TV Institucional - cartel blanco colgado en el lateral de un edificio"},icons:{menu:"Ícono de menú",close:"Ícono de cerrar",expand:"Ícono de expandir",collapse:"Ícono de contraer",external:"Ícono de enlace externo",download:"Ícono de descarga",email:"Ícono de email",phone:"Ícono de teléfono",linkedin:"Ícono de LinkedIn",whatsapp:"Ícono de WhatsApp",github:"Ícono de GitHub",sun:"Ícono de sol",moon:"Ícono de luna",globe:"Ícono de globo",volume:"Ícono de volumen",success:"Ícono de éxito",error:"Ícono de error",warning:"Ícono de advertencia",info:"Ícono de información"},decorative:{gradient:"Gradiente decorativo",pattern:"Patrón decorativo",divider:"Divisor visual",background:"Imagen de fondo decorativa"}},language:{changed:"Idioma cambiado con éxito",current:"Idioma actual",available:"Idiomas disponibles",portuguese:"Português",english:"English",spanish:"Español",select:"Seleccionar idioma"},theme:{toggle:"Alternar tema",changed:"Tema cambiado con éxito",light:"Modo claro activado",dark:"Modo oscuro activado",system:"Usando preferencia del sistema"},footer:{copyright:"© 2024 Tarcisio Bispo. Todos los derechos reservados.",title:"UX/Product Designer"},feedback:{title:"Feedback",subtitle:"Tu opinión importa",description:"Comparte tu experiencia y sugerencias",typeQuestion:"¿Qué tipo de feedback te gustaría compartir?",close:"Cerrar",back:"Volver",send:"Enviar feedback",sending:"Enviando...",includeEmail:"Incluir mi email para respuesta",privacyPolicy:"Política de Privacidad",problem:"Reportar Problema",idea:"Compartir Idea",praise:"Dar Elogio",problemTitle:"Reportar Problema",ideaTitle:"Compartir Idea",praiseTitle:"Dar Elogio",defaultTitle:"Enviar Feedback",problemInstruction:"Describe el problema que encontraste en detalle",ideaInstruction:"Comparte tu idea o sugerencia de mejora",praiseInstruction:"Cuéntanos qué te gustó",defaultInstruction:"Comparte tu feedback con nosotros",problemPlaceholder:"Describe el problema que encontraste...",ideaPlaceholder:"Comparte tu idea o sugerencia...",praisePlaceholder:"Cuéntanos qué te gustó...",defaultPlaceholder:"Comparte tu feedback...",validation:{messageRequired:"El mensaje es obligatorio",messageMinLength:"Mínimo 5 caracteres",emailInvalid:"Email inválido"},form:{type:"Tipo de feedback",message:"Tu mensaje",email:"Tu email (opcional)",send:"Enviar feedback",sending:"Enviando...",success:"✅ ¡Gracias por tu feedback! Tu opinión es muy importante para nosotros.",error:"❌ ¡Ups! No pudimos enviar tu feedback. Intenta de nuevo o contacta directamente.",messageRequired:"El mensaje es obligatorio"},status:{success:"¡Gracias por tu feedback!",error:"Error al enviar feedback. Inténtalo de nuevo.",sending:"Enviando feedback..."},types:{bug:"Reportar bug",suggestion:"Sugerencia",compliment:"Elogio",other:"Otro"}},cookies:{title:"Este sitio usa cookies",description:"Usamos cookies para mejorar tu experiencia, analizar el tráfico del sitio y personalizar contenido.",learnMore:"Saber más",acceptAll:"Aceptar todas",rejectAll:"Rechazar todas",managePreferences:"Gestionar preferencias",savePreferences:"Guardar preferencias",required:"Obligatorio",preferences:{title:"Preferencias de Cookies"},types:{necessary:{title:"Cookies Necesarias",description:"Estas cookies son esenciales para el funcionamiento del sitio web y no se pueden desactivar."},analytics:{title:"Cookies de Análisis",description:"Nos ayudan a entender cómo los visitantes interactúan con el sitio recopilando información de forma anónima.",providers:"Proveedores"},marketing:{title:"Cookies de Marketing",description:"Se utilizan para rastrear visitantes en sitios web para mostrar anuncios relevantes y atractivos."}}},common:{close:"Cerrar",open:"Abrir",save:"Guardar",cancel:"Cancelar",confirm:"Confirmar",yes:"Sí",no:"No",loading:"Cargando...",error:"Error",success:"Éxito",warning:"Advertencia",info:"Información"}}}};le.use(ce).use(de).init({resources:Vo,lng:"pt-BR",fallbackLng:"pt-BR",debug:!1,react:{useSuspense:!1},interpolation:{escapeValue:!1},defaultNS:"translation",detection:{order:["localStorage","navigator"],caches:["localStorage"]}}).then((()=>{})).catch((e=>{console.error("❌ i18n initialization failed:",e)}));const Fo=({children:e})=>{const[t,a]=l.useState(!1),{t:o}=ue();return l.useEffect((()=>{const e=()=>{le.isInitialized&&le.hasResourceBundle("pt-BR","translation")?a(!0):setTimeout(e,100)};e();const t=()=>{a(!0)};return le.on("languageChanged",t),le.on("initialized",(()=>{e()})),()=>{le.off("languageChanged",t),le.off("initialized",e)}}),[o]),t?r.jsx(r.Fragment,{children:e}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-white dark:bg-gray-900",children:r.jsxs("div",{className:"text-center",children:[r.jsx(Bo,{}),r.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Carregando traduções..."})]})})},$o=ft("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-error text-error-foreground shadow-sm hover:bg-error/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:border-primary/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md",ghost:"hover:bg-accent hover:text-accent-foreground hover:shadow-sm",link:"text-primary underline-offset-4 hover:underline hover:text-primary/80",gradient:"bg-gradient-to-r from-primary to-secondary text-white shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]",success:"bg-success text-success-foreground shadow-sm hover:bg-success/90",warning:"bg-warning text-warning-foreground shadow-sm hover:bg-warning/90"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-11 rounded-lg px-8",xl:"h-12 rounded-xl px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8","icon-lg":"h-12 w-12"}},defaultVariants:{variant:"default",size:"default"}});l.forwardRef((({className:e,variant:t,size:a,asChild:o=!1,loading:i=!1,leftIcon:n,rightIcon:s,children:l,disabled:c,...d},u)=>{const p=o?L:"button",m=c||i;return r.jsxs(p,{className:la($o({variant:t,size:a,className:e})),ref:u,disabled:m,"aria-disabled":m,...d,children:[i&&r.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"}),!i&&n&&n,l,!i&&s&&s]})})).displayName="Button";const qo=new class{constructor(){o(this,"sounds",new Map),o(this,"config",{volume:.3,enabled:!0,preload:!0}),o(this,"initialized",!1),this.loadConfig(),this.initializeAudioContext()}loadConfig(){try{const e=localStorage.getItem("portfolio-sound-config");e&&(this.config={...this.config,...JSON.parse(e)})}catch(e){console.warn("Failed to load sound config:",e)}}saveConfig(){try{localStorage.setItem("portfolio-sound-config",JSON.stringify(this.config))}catch(e){console.warn("Failed to save sound config:",e)}}initializeAudioContext(){if(this.initialized)return;const e=()=>{this.initialized=!0,this.preloadSounds(),document.removeEventListener("click",e),document.removeEventListener("keydown",e),document.removeEventListener("touchstart",e)};document.addEventListener("click",e,{once:!0}),document.addEventListener("keydown",e,{once:!0}),document.addEventListener("touchstart",e,{once:!0})}createSoundDataUrl(e){const t=(new(window.AudioContext||window.webkitAudioContext)).sampleRate;switch(e){case"success":return this.generateTone([523.25,659.25,783.99],.3,t);case"error":return this.generateTone([220,261.63],.2,t);case"hover":default:return this.generateTone([440],.1,t);case"click":return this.generateTone([800],.05,t);case"toggle":return this.generateTone([523.25,659.25],.15,t)}}generateTone(e,t,a){const o=Math.floor(a*t),r=new ArrayBuffer(44+2*o),i=new DataView(r),n=(e,t)=>{for(let a=0;a<t.length;a++)i.setUint8(e+a,t.charCodeAt(a))};n(0,"RIFF"),i.setUint32(4,36+2*o,!0),n(8,"WAVE"),n(12,"fmt "),i.setUint32(16,16,!0),i.setUint16(20,1,!0),i.setUint16(22,1,!0),i.setUint32(24,a,!0),i.setUint32(28,2*a,!0),i.setUint16(32,2,!0),i.setUint16(34,16,!0),n(36,"data"),i.setUint32(40,2*o,!0);for(let l=0;l<o;l++){let t=0;const o=l/a;e.forEach((e=>{t+=Math.sin(2*Math.PI*e*o)})),t/=e.length;const r=Math.exp(3*-o);t*=r;const n=Math.max(-32768,Math.min(32767,32767*t));i.setInt16(44+2*l,n,!0)}const s=new Blob([r],{type:"audio/wav"});return URL.createObjectURL(s)}preloadSounds(){if(!this.config.preload||!this.config.enabled)return;["success","error","hover","click","toggle"].forEach((e=>{try{const t=new Audio;t.src=this.createSoundDataUrl(e),t.volume=this.config.volume,t.preload="auto",t.addEventListener("error",(()=>{console.warn(`Failed to load sound: ${e}`)})),this.sounds.set(e,t)}catch(t){console.warn(`Failed to create sound: ${e}`,t)}}))}play(e,t={}){if(this.config.enabled&&this.initialized)try{let a=this.sounds.get(e);a||(a=new Audio,a.src=this.createSoundDataUrl(e),this.sounds.set(e,a));const o=a.cloneNode();o.volume=(t.volume??this.config.volume)*this.config.volume;const r=o.play();r&&r.catch((e=>{"NotAllowedError"!==e.name&&console.warn("Sound play failed:",e)}))}catch(a){console.warn("Sound play error:",a)}}updateConfig(e){this.config={...this.config,...e},this.saveConfig(),void 0!==e.volume&&this.sounds.forEach((e=>{e.volume=this.config.volume}))}getConfig(){return{...this.config}}setEnabled(e){this.updateConfig({enabled:e})}setVolume(e){const t=Math.max(0,Math.min(1,e));this.updateConfig({volume:t})}isSupported(){return"undefined"!=typeof Audio&&"undefined"!=typeof AudioContext||void 0!==window.webkitAudioContext}},Go=()=>qo.getConfig(),Xo=(e={})=>{const[t,a]=l.useState(Go()),[o,r]=l.useState({});l.useEffect((()=>{const e=()=>{a(Go())};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}}),[]);const i=l.useCallback(((t,a={})=>{const i=Date.now(),n=e.throttle||100;if(o[t]&&i-o[t]<n)return;r((e=>({...e,[t]:i})));const s=a.volume??e.volume;((e,t)=>{qo.play(e,t)})(t,s?{volume:s}:void 0)}),[e.volume,e.throttle,o]),n=l.useCallback((e=>{(e=>{qo.setEnabled(e)})(e),a(Go())}),[]),s=l.useCallback((e=>{(e=>{qo.setVolume(e)})(e),a(Go())}),[]);return{play:i,isEnabled:t.enabled,volume:t.volume,isSupported:qo.isSupported(),setEnabled:n,setVolume:s,config:t}},Ho=()=>{const e=Xo({throttle:200});return{playSubmitSuccess:l.useCallback((()=>e.play("success")),[e]),playSubmitError:l.useCallback((()=>e.play("error")),[e]),playFieldFocus:l.useCallback((()=>e.play("hover",{volume:.05})),[e]),playFieldValid:l.useCallback((()=>e.play("click",{volume:.1})),[e]),...e}},Wo=()=>{const e=Xo({throttle:150});return{playPageTransition:l.useCallback((()=>e.play("toggle")),[e]),playMenuOpen:l.useCallback((()=>e.play("click")),[e]),playMenuClose:l.useCallback((()=>e.play("click",{volume:.1})),[e]),playButtonHover:l.useCallback((()=>e.play("hover",{volume:.08})),[e]),playButtonClick:l.useCallback((()=>e.play("click",{volume:.15})),[e]),...e}},Ko=()=>{const e=Xo({throttle:100});return{playCardHover:l.useCallback((()=>e.play("hover",{volume:.06})),[e]),playCardClick:l.useCallback((()=>e.play("click",{volume:.12})),[e]),playExpand:l.useCallback((()=>e.play("toggle",{volume:.1})),[e]),playCollapse:l.useCallback((()=>e.play("toggle",{volume:.08})),[e]),...e}},Yo=({children:e,onClick:t,href:a,variant:o="primary",size:n="md",icon:s,iconPosition:l="right",disabled:c=!1,loading:d=!1,className:u="",ariaLabel:p,target:m,rel:g,enableSound:f=!0})=>{const{playButtonHover:h,playButtonClick:b}=Wo(),v=`cta-button variant-${o} size-${n} ${u}`.trim(),y=()=>{f&&b(),null==t||t()},x=()=>{f&&h()},w={initial:{scale:1},hover:{scale:1.05,y:-2},tap:{scale:.98,y:0}},k=r.jsxs(i.div,{className:"flex items-center justify-center gap-2 relative z-10",variants:{initial:{y:0},hover:{y:-1}},children:[s&&"left"===l&&r.jsx(s,{className:"w-5 h-5","aria-hidden":"true"}),r.jsx("span",{children:d?r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Carregando..."})]}):e}),s&&"right"===l&&!d&&r.jsx(s,{className:"w-5 h-5","aria-hidden":"true"})]});return a?r.jsx(i.a,{href:a,target:m,rel:g,className:v,"aria-label":p,onClick:y,onMouseEnter:x,style:{textDecoration:"none",pointerEvents:c?"none":"auto",opacity:c?.5:1},variants:w,initial:"initial",whileHover:"hover",whileTap:"tap",children:k}):r.jsx(i.button,{onClick:c?void 0:y,onMouseEnter:x,className:v,disabled:c||d,"aria-label":p,variants:w,initial:"initial",whileHover:"hover",whileTap:"tap",children:k})},Qo=({isOpen:e,onClick:t,className:a=""})=>{const{t:o}=ue(),{playButtonHover:n,playButtonClick:s}=Wo();return r.jsx(i.button,{onClick:()=>{s(),t()},onMouseEnter:()=>{n()},className:`\n        relative flex items-center justify-center\n        w-11 h-11 rounded-lg\n        border border-[var(--color-border)]\n        bg-[var(--color-surface)]\n        shadow hover:shadow-lg\n        transition-all duration-200\n        focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]\n        hover:scale-105 active:scale-95\n        ${a}\n      `,"aria-label":o(e?"navigation.menu.close":"navigation.menu.open"),"aria-expanded":e,"aria-controls":"mobile-navigation-menu",whileHover:{scale:1.05},whileTap:{scale:.95},children:r.jsxs("div",{className:"relative w-5 h-5 flex flex-col justify-center items-center",children:[r.jsx(i.span,{className:"absolute w-5 h-0.5 bg-[var(--color-text)] rounded-full",animate:{rotate:e?45:0,y:e?0:-6},transition:{duration:.2,ease:"easeInOut"}}),r.jsx(i.span,{className:"absolute w-5 h-0.5 bg-[var(--color-text)] rounded-full",animate:{opacity:e?0:1,x:e?-10:0},transition:{duration:.2,ease:"easeInOut"}}),r.jsx(i.span,{className:"absolute w-5 h-0.5 bg-[var(--color-text)] rounded-full",animate:{rotate:e?-45:0,y:e?0:6},transition:{duration:.2,ease:"easeInOut"}})]})})},Jo=({isOpen:e,onClick:t,className:a=""})=>{const{t:o}=ue(),{playButtonHover:n,playButtonClick:s}=Wo();return r.jsx(i.button,{onClick:()=>{s(),t()},onMouseEnter:()=>{n()},className:`\n        relative flex items-center justify-center\n        w-11 h-11 rounded-lg\n        border border-[var(--color-border)]\n        bg-[var(--color-surface)]\n        shadow hover:shadow-lg\n        transition-all duration-200\n        focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)]\n        hover:scale-105 active:scale-95\n        ${a}\n      `,"aria-label":o(e?"navigation.settings.close":"navigation.settings.open"),"aria-expanded":e,"aria-controls":"mobile-config-menu",whileHover:{scale:1.05},whileTap:{scale:.95},children:r.jsx(i.div,{animate:{rotate:e?180:0},transition:{duration:.3,ease:"easeInOut"},children:r.jsx(U,{className:"w-5 h-5 text-[var(--color-text)]","aria-hidden":"true"})})})},Zo=()=>{const{theme:e,setTheme:t}=va(),{playButtonHover:a,playButtonClick:o}=Wo(),{t:n}=ue(),s="dark"===e;return r.jsxs(i.button,{onClick:()=>{o(),t(s?"light":"dark")},onMouseEnter:()=>{a()},className:" relative flex items-center justify-center w-11 h-11 rounded-lg border border-[var(--color-border)] bg-[var(--color-surface)] shadow hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] hover:scale-105 active:scale-95 ","aria-label":n(s?"tooltips.theme.light":"tooltips.theme.dark"),whileHover:{scale:1.05},whileTap:{scale:.95},children:[r.jsxs(i.div,{className:"relative w-5 h-5",animate:{rotate:s?180:0},transition:{duration:.3,ease:"easeInOut"},children:[r.jsx(i.div,{className:"absolute inset-0 flex items-center justify-center",animate:{opacity:s?0:1,scale:s?.5:1},transition:{duration:.2},children:r.jsx(_,{className:"w-5 h-5 text-[var(--color-text)]"})}),r.jsx(i.div,{className:"absolute inset-0 flex items-center justify-center",animate:{opacity:s?1:0,scale:s?1:.5},transition:{duration:.2},children:r.jsx(B,{className:"w-5 h-5 text-[var(--color-text)]"})})]}),r.jsx(i.div,{className:"absolute inset-0 rounded-lg bg-[var(--color-primary)] opacity-0",whileTap:{opacity:[0,.1,0],scale:[1,1.2,1]},transition:{duration:.3}})]})},er=()=>{const{isEnabled:e,setEnabled:t,playToggle:a}=(()=>{const e=Xo({throttle:50});return{playSuccess:l.useCallback((()=>e.play("success")),[e]),playError:l.useCallback((()=>e.play("error")),[e]),playHover:l.useCallback((()=>e.play("hover",{volume:.1})),[e]),playClick:l.useCallback((()=>e.play("click",{volume:.2})),[e]),playToggle:l.useCallback((()=>e.play("toggle")),[e]),...e}})(),{t:o}=ue();return r.jsxs(i.button,{onClick:()=>{a(),t(!e)},className:" relative flex items-center justify-center w-11 h-11 rounded-lg border border-[var(--color-border)] bg-[var(--color-surface)] shadow hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] hover:scale-105 active:scale-95 ","aria-label":o(e?"tooltips.sound.disable":"tooltips.sound.enable"),whileHover:{scale:1.05},whileTap:{scale:.95},children:[r.jsxs(i.div,{className:"relative w-5 h-5",animate:{scale:e?1:.9},transition:{duration:.2,ease:"easeInOut"},children:[r.jsx(i.div,{className:"absolute inset-0 flex items-center justify-center",animate:{opacity:e?1:0,scale:e?1:.5},transition:{duration:.2},children:r.jsx(V,{className:"w-5 h-5 text-[var(--color-text)]"})}),r.jsx(i.div,{className:"absolute inset-0 flex items-center justify-center",animate:{opacity:e?0:1,scale:e?.5:1},transition:{duration:.2},children:r.jsx(F,{className:"w-5 h-5 text-[var(--color-text)]"})})]}),e&&r.jsx(i.div,{className:"absolute inset-0 rounded-lg border-2 border-[var(--color-primary)]",animate:{scale:[1,1.1,1],opacity:[.3,.6,.3]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}}),r.jsx(i.div,{className:"absolute inset-0 rounded-lg bg-[var(--color-primary)] opacity-0",whileTap:{opacity:[0,.1,0],scale:[1,1.2,1]},transition:{duration:.3}})]})},tr=[{code:"pt-BR",label:"Português",nativeName:"Português",flag:"🇧🇷"},{code:"en-US",label:"English",nativeName:"English",flag:"🇺🇸"},{code:"es-ES",label:"Español",nativeName:"Español",flag:"🇪🇸"}],ar=()=>{const{i18n:e,t:t}=ue(),{playButtonHover:a,playButtonClick:o}=Wo(),[s,c]=l.useState(!1),d=()=>{a()};return r.jsxs("div",{className:"relative",children:[r.jsxs(i.button,{onClick:()=>{o(),c(!s)},onMouseEnter:d,className:" relative flex items-center justify-center gap-1 w-11 h-11 rounded-lg border border-[var(--color-border)] bg-[var(--color-surface)] shadow hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] hover:scale-105 active:scale-95 ","aria-label":t("tooltips.language.switch"),"aria-expanded":s,whileHover:{scale:1.05},whileTap:{scale:.95},children:[r.jsx($,{className:"w-4 h-4 text-[var(--color-text)]"}),r.jsx(i.div,{animate:{rotate:s?180:0},transition:{duration:.2},children:r.jsx(q,{className:"w-3 h-3 text-[var(--color-text)]"})})]}),r.jsx(n,{children:s&&r.jsx(i.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2,ease:"easeOut"},className:" absolute top-full right-0 mt-2 z-50 min-w-[160px] rounded-lg border border-[var(--color-border)] bg-[var(--color-surface)] shadow-lg backdrop-blur-sm ",children:r.jsx("div",{className:"p-1",children:tr.map((t=>r.jsxs(i.button,{onClick:()=>{return a=t.code,o(),e.changeLanguage(a),void c(!1);var a},onMouseEnter:d,className:`\n                    w-full flex items-center gap-3 px-3 py-2 rounded-md\n                    text-sm text-left transition-colors duration-150\n                    hover:bg-[var(--color-accent)]\n                    focus:outline-none focus:bg-[var(--color-accent)]\n                    ${e.language===t.code?"bg-[var(--color-accent)] text-[var(--color-primary)]":"text-[var(--color-text)]"}\n                  `,whileHover:{x:2},whileTap:{scale:.98},children:[r.jsx("span",{className:"text-base",children:t.flag}),r.jsx("span",{className:"font-medium",children:t.nativeName}),e.language===t.code&&r.jsx(i.div,{className:"ml-auto w-2 h-2 rounded-full bg-[var(--color-primary)]",initial:{scale:0},animate:{scale:1},transition:{duration:.2}})]},t.code)))})})}),s&&r.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>c(!1)})]})},or=()=>{const{playButtonHover:e,playButtonClick:t}=Wo(),{t:a}=ue(),[o,s]=l.useState(!1),[c,d]=l.useState(!1),[u,p]=l.useState(!1),[m,g]=l.useState(!1),[f,h]=l.useState(!1),b=()=>{e()},v=[{id:"contrast",label:"Alto Contraste",icon:X,i18nKey:"alto-contraste",action:()=>d((e=>!e))},{id:"text-size",label:"Aumentar Fonte",icon:H,i18nKey:"aumentar-fonte",action:()=>p((e=>!e))},{id:"voice-over",label:"Ler Conteúdo",icon:W,i18nKey:"ler-conteudo",action:()=>g((e=>!e))},{id:"dyslexia",label:"Modo Dislexia",icon:H,i18nKey:"modo-dislexia",action:()=>h((e=>!e))}];return r.jsxs("div",{className:"relative",children:[r.jsx(i.button,{onClick:()=>{t(),s(!o)},onMouseEnter:b,className:" relative flex items-center justify-center w-11 h-11 rounded-lg border border-[var(--color-border)] bg-[var(--color-surface)] shadow hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] hover:scale-105 active:scale-95 ","aria-label":a("accessibility.menuTooltip"),"aria-expanded":o,whileHover:{scale:1.05},whileTap:{scale:.95},children:r.jsx(G,{className:"w-5 h-5 text-[var(--color-text)]"})}),r.jsx(n,{children:o&&r.jsx(i.div,{initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2,ease:"easeOut"},className:" absolute top-full right-0 mt-2 z-50 min-w-[200px] rounded-lg border border-[var(--color-border)] bg-[var(--color-surface)] shadow-lg backdrop-blur-sm ",children:r.jsxs("div",{className:"p-2",children:[r.jsxs("div",{className:"px-3 py-2 border-b border-[var(--color-border)]",children:[r.jsx("h3",{className:"text-sm font-semibold text-[var(--color-text)]",children:a("accessibility.title")}),r.jsx("p",{className:"text-xs text-[var(--color-muted)] mt-1",children:a("accessibility.subtitle")})]}),r.jsx("div",{className:"mt-2 space-y-1",children:v.map((e=>{const a=e.icon,o="contrast"===e.id&&c||"text-size"===e.id&&u||"voice-over"===e.id&&m||"dyslexia"===e.id&&f;return r.jsxs(i.button,{onClick:()=>(e=>{t(),e.action()})(e),onMouseEnter:b,className:`\n                        w-full flex items-center gap-3 px-3 py-2 rounded-md\n                        text-sm text-left transition-colors duration-150\n                        hover:bg-[var(--color-accent)]\n                        focus:outline-none focus:bg-[var(--color-accent)]\n                        ${o?"bg-[var(--color-accent)] text-[var(--color-primary)]":"text-[var(--color-text)]"}\n                      `,whileHover:{x:2},whileTap:{scale:.98},children:[r.jsx(a,{className:"w-4 h-4"}),r.jsx("span",{className:"font-medium",children:e.label}),o&&r.jsx(i.div,{className:"ml-auto w-2 h-2 rounded-full bg-[var(--color-primary)]",initial:{scale:0},animate:{scale:1},transition:{duration:.2}})]},e.id)}))})]})})}),o&&r.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>s(!1)})]})},rr={initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.95},transition:{duration:.2,ease:[.16,1,.3,1]}},ir={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl",full:"max-w-[90vw]"},nr={default:"border-gray-200 dark:border-gray-700",feedback:"border-blue-200 dark:border-blue-800",confirm:"border-yellow-200 dark:border-yellow-800",alert:"border-orange-200 dark:border-orange-800",success:"border-green-200 dark:border-green-800",error:"border-red-200 dark:border-red-800",warning:"border-yellow-200 dark:border-yellow-800"},sr=({open:e,onClose:t,title:a,description:o,children:s,variant:c="default",size:d="md",className:u="",showCloseButton:p=!0,closeOnOverlayClick:m=!0,closeOnEscape:g=!0,initialFocus:f,ariaLabel:h,ariaDescribedBy:b,...v})=>{const{t:y}=ue(),{playButtonClick:x}=Wo();l.useEffect((()=>{if(!g)return;const a=a=>{"Escape"===a.key&&e&&t()};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)}),[e,t,g]);const w=()=>{x(),t()};return r.jsx(n,{children:e&&r.jsxs(z,{open:e,onClose:w,initialFocus:f,className:"fixed z-50 inset-0 overflow-y-auto","aria-label":h,"aria-describedby":b,children:[r.jsx(i.div,{className:"fixed inset-0 bg-black/30 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},onClick:()=>{m&&w()},"aria-hidden":"true"}),r.jsx("div",{className:"flex items-center justify-center min-h-screen px-4 py-6",children:r.jsxs(i.div,{className:la("relative w-full rounded-2xl shadow-xl z-10","bg-white dark:bg-gray-800","border",nr[c],ir[d],u),...rr,children:[p&&r.jsx("button",{onClick:w,className:la("absolute top-4 right-4 p-2 rounded-full","hover:bg-gray-100 dark:hover:bg-gray-700","focus:outline-none focus:ring-2 focus:ring-blue-500","text-gray-500 dark:text-gray-400","transition-colors duration-200"),"aria-label":y("common.close"),children:r.jsx(O,{className:"w-5 h-5"})}),r.jsxs("div",{className:"p-6",children:[(a||o)&&r.jsxs("div",{className:"mb-6",children:[a&&r.jsx(z.Title,{className:la("text-lg font-semibold mb-2","text-gray-900 dark:text-gray-100"),children:a}),o&&r.jsx(z.Description,{className:la("text-sm","text-gray-600 dark:text-gray-300"),children:o})]}),s]})]})})]})})},lr={_origin:"https://api.emailjs.com"},cr=(e,t,a)=>{if(!e)throw"The user ID is required. Visit https://dashboard.emailjs.com/admin/integration";if(!t)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!a)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates";return!0};class dr{constructor(e){this.status=e.status,this.text=e.responseText}}const ur=(e,t,a={})=>new Promise(((o,r)=>{const i=new XMLHttpRequest;i.addEventListener("load",(({target:e})=>{const t=new dr(e);200===t.status||"OK"===t.text?o(t):r(t)})),i.addEventListener("error",(({target:e})=>{r(new dr(e))})),i.open("POST",lr._origin+e,!0),Object.keys(a).forEach((e=>{i.setRequestHeader(e,a[e])})),i.send(t)})),pr={init:(e,t="https://api.emailjs.com")=>{lr._userID=e,lr._origin=t},send:(e,t,a,o)=>{const r=o||lr._userID;cr(r,e,t);const i={lib_version:"3.2.0",user_id:r,service_id:e,template_id:t,template_params:a};return ur("/api/v1.0/email/send",JSON.stringify(i),{"Content-type":"application/json"})},sendForm:(e,t,a,o)=>{const r=o||lr._userID,i=(e=>{let t;if(t="string"==typeof e?document.querySelector(e):e,!t||"FORM"!==t.nodeName)throw"The 3rd parameter is expected to be the HTML form element or the style selector of form";return t})(a);cr(r,e,t);const n=new FormData(i);return n.append("lib_version","3.2.0"),n.append("service_id",e),n.append("template_id",t),n.append("user_id",r),ur("/api/v1.0/email/send-form",n)}},mr={"common.close":"Fechar","common.open":"Abrir","common.save":"Salvar","common.cancel":"Cancelar","common.confirm":"Confirmar","common.yes":"Sim","common.no":"Não","common.loading":"Carregando...","common.error":"Erro","common.success":"Sucesso","common.warning":"Aviso","common.info":"Informação","feedback.title":"Feedback","feedback.close":"Fechar","feedback.back":"Voltar","feedback.send":"Enviar","feedback.sending":"Enviando...","feedback.problem":"Reportar Problema","feedback.idea":"Compartilhar Ideia","feedback.praise":"Dar Elogio","feedback.defaultTitle":"Enviar Feedback","feedback.defaultPlaceholder":"Compartilhe seu feedback...","feedback.validation.messageRequired":"Mensagem é obrigatória","feedback.validation.messageMinLength":"Mínimo 5 caracteres","feedback.status.success":"Obrigado pelo seu feedback!","feedback.status.error":"Erro ao enviar feedback. Tente novamente.","profile.name":"Tarcisio Bispo de Araujo","profile.bio":"UX/Product Designer especializado em estratégia, impacto e experiência do usuário.","profile.letsChat":"Vamos Conversar","profile.hero.greeting":"Olá, eu sou","profile.hero.roles.uxDesigner":"UX Designer","profile.hero.roles.productDesigner":"Product Designer","profile.hero.roles.designStrategist":"Design Strategist","profile.hero.roles.interactionDesigner":"Interaction Designer","navigation.home":"Início","navigation.about":"Sobre","navigation.projects":"Projetos","navigation.contact":"Contato","navigation.backlog":"Backlog","theme.toggle":"Alternar Tema","theme.light":"Modo Claro","theme.dark":"Modo Escuro","theme.changed":"Tema alterado","sound.enabled":"Som Ativado","sound.disabled":"Som Desativado","sound.toggle":"Alternar Som","language.changed":"Idioma alterado","tooltips.theme.light":"Mudar para modo claro","tooltips.theme.dark":"Mudar para modo escuro","tooltips.language.switch":"Alterar idioma","tooltips.sound.enable":"Ativar som","tooltips.sound.disable":"Desativar som"},gr=e=>{const{component:t,namespace:a,fallbacks:o={},enableLogging:r=!1}=e,{t:i,hasTranslation:n,getTranslationInfo:s,...c}=((e={})=>{const{fallbackLanguage:t="pt-BR",enableLogging:a=!1,customFallbacks:o={}}=e,{t:r,i18n:i}=ue(),n=l.useMemo((()=>({...mr,...o})),[o]),s=(e,o,s)=>{try{const c=r(e,o);if(c&&c!==e&&"string"==typeof c)return c;if(s)return a&&console.warn(`Translation missing for "${e}", using custom fallback: "${s}"`),s;if(n[e])return a&&console.warn(`Translation missing for "${e}", using default fallback: "${n[e]}"`),n[e];if(i.language!==t)try{const r=i.getFixedT(t)(e,o);if(r&&r!==e)return a&&console.warn(`Translation missing for "${e}" in ${i.language}, using ${t} fallback`),r}catch(l){a&&console.warn(`Fallback language ${t} also failed for "${e}"`)}const d=(e.split(".").pop()||e).replace(/([A-Z])/g," $1").replace(/^./,(e=>e.toUpperCase()));return a&&console.error(`No translation found for "${e}", using formatted key: "${d}"`),d}catch(l){return a&&console.error(`Translation error for "${e}":`,l),s||n[e]||e}},c=e=>{try{const t=r(e);return t&&t!==e&&"string"==typeof t}catch{return!1}};return{t:s,tOriginal:r,tArray:(e,t=[])=>{try{const a=r(e,{returnObjects:!0});return Array.isArray(a)?a:t}catch{return t}},tObject:(e,t={})=>{try{const a=r(e,{returnObjects:!0});return"object"!=typeof a||null===a||Array.isArray(a)?t:a}catch{return t}},hasTranslation:c,getTranslationInfo:e=>{const t=s(e),a=c(e),o=!a;return{key:e,translation:t,exists:a,isFallback:o,fallbackType:o?n[e]?"default":"formatted":"none",language:i.language}},i18n:i,currentLanguage:i.language,isReady:i.isInitialized,fallbacks:n}})({enableLogging:r,customFallbacks:o}),d=e=>s(a?`${a}.${e}`:`${t}.${e}`);return{t:(e,o,r)=>i(a?`${a}.${e}`:`${t}.${e}`,o,r),tGlobal:i,hasTranslation:e=>n(a?`${a}.${e}`:`${t}.${e}`),getTranslationInfo:d,validateTranslations:e=>{const t=e.map((e=>({key:e,...d(e)}))),a=t.filter((e=>!e.exists)),o=(t.length-a.length)/t.length*100;return{isValid:0===a.length,coverage:Math.round(100*o)/100,missing:a.map((e=>e.key)),results:t}},component:t,namespace:a,...c}},fr={"feedback.title":"Feedback","feedback.close":"Fechar","feedback.back":"Voltar","feedback.send":"Enviar","feedback.sending":"Enviando...","feedback.problem":"Reportar Problema","feedback.idea":"Compartilhar Ideia","feedback.praise":"Dar Elogio","feedback.defaultTitle":"Enviar Feedback","feedback.defaultPlaceholder":"Compartilhe seu feedback...","feedback.validation.messageRequired":"Mensagem é obrigatória","feedback.validation.messageMinLength":"Mínimo 5 caracteres","feedback.status.success":"Obrigado pelo seu feedback!","feedback.status.error":"Erro ao enviar feedback. Tente novamente."},hr=[{type:"problem",icon:Y},{type:"idea",icon:Q},{type:"praise",icon:K}],br=({open:e,onClose:t,section:a="default",onSubmitSuccess:o,onSubmitError:n,...s})=>{const[c,d]=l.useState(1),[u,p]=l.useState(null),[m,g]=l.useState(""),[f,h]=l.useState(""),[b,v]=l.useState(!1),[y,x]=l.useState(!1),[w,k]=l.useState(!1),[E,C]=l.useState("idle"),[j,T]=l.useState(!1),S=l.useRef(null),{t:A}=ue();((e=!1)=>{const t=gr({component:"feedback",fallbacks:fr,enableLogging:e})})();const{playSubmitSuccess:I,playSubmitError:N,playFieldFocus:P}=Ho(),{playButtonClick:D}=Wo(),R=m.trim().length>=5,M=!f||/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(f),L=R&&(!b||M)&&!y,z=()=>{(1===c||""===m.trim()&&""===f.trim())&&(d(1),p(null),g(""),h(""),v(!1),x(!1),k(!1),C("idle"),T(!1)),t()};return w?r.jsx(sr,{open:e,onClose:z,variant:"success",size:"sm",showCloseButton:!1,...s,children:r.jsxs("div",{className:"flex flex-col items-center py-8 text-center",children:[r.jsx(K,{className:"w-12 h-12 text-green-600 dark:text-green-400 mb-4"}),r.jsx("p",{className:"text-lg font-semibold text-green-700 dark:text-green-300 mb-6",children:A("feedback.form.success")}),r.jsx(Yo,{onClick:z,variant:"primary",size:"md",children:A("feedback.close")})]})}):1===c?r.jsx(sr,{open:e,onClose:z,title:A("feedback.typeQuestion"),variant:"feedback",size:"sm",initialFocus:S,...s,children:r.jsx("div",{className:"flex flex-col gap-3",children:hr.map(((e,t)=>r.jsx(Yo,{ref:0===t?S:void 0,variant:u===e.type?"primary":"ghost",size:"md",icon:e.icon,iconPosition:"left",onClick:()=>{p(e.type),d(2),D()},className:"justify-center py-3",children:A(`feedback.${e.type}`)},e.type)))})}):r.jsx(sr,{open:e,onClose:z,title:A(`feedback.${u}Title`)||A("feedback.defaultTitle"),description:A(`feedback.${u}Instruction`)||A("feedback.defaultInstruction"),variant:"feedback",size:"md",...s,children:r.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),L&&u){x(!0),C("idle");try{await pr.send("service_4z3a60b","template_nc73bg4",{from_name:`Feedback Portfolio - ${u}`,from_email:f||"<EMAIL>",message:`Tipo: ${u}\nSeção: ${a}\n\nMensagem:\n${m}`,to_email:"<EMAIL>",subject:`Feedback do Portfolio - ${u} (${a})`,reply_to:f||"<EMAIL>"},"eRzZy4gTZ2NXGjFKz"),C("success"),k(!0),I(),null==o||o()}catch(t){console.error("Erro ao enviar feedback:",t),C("error"),N(),null==n||n(t)}finally{x(!1)}}},className:"space-y-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("textarea",{className:la("w-full rounded-lg border-2 p-3 min-h-[90px] text-base","transition-all focus:outline-none focus:ring-2 focus:ring-blue-500","resize-none pr-10","bg-white dark:bg-gray-700","text-gray-900 dark:text-gray-100","placeholder:text-gray-500 dark:placeholder:text-gray-400",j&&!R?"border-red-500 focus:ring-red-500":j&&R?"border-green-500 focus:ring-green-500":"border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500"),placeholder:A(`feedback.${u}Placeholder`,{defaultValue:A("feedback.defaultPlaceholder")}),value:m,onChange:e=>g(e.target.value),onBlur:()=>T(!0),onFocus:P,required:!0,autoFocus:!0,maxLength:1e3,"aria-invalid":j&&!R,"aria-describedby":"feedback-message-help"}),j&&!R&&r.jsx(O,{className:"absolute right-3 top-3 w-5 h-5 text-red-500","aria-hidden":"true"}),j&&R&&r.jsx(J,{className:"absolute right-3 top-3 w-5 h-5 text-green-500","aria-hidden":"true"})]}),j&&!R?r.jsxs("div",{id:"feedback-message-help",className:"text-xs text-red-500 flex items-center gap-1",role:"alert",children:[r.jsx(Z,{className:"w-3 h-3","aria-hidden":"true"}),A("feedback.validation.messageRequired")]}):r.jsx("div",{id:"feedback-message-help",className:"text-xs text-gray-500 dark:text-gray-400",children:A("feedback.validation.messageMinLength")}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("input",{id:"show-email",type:"checkbox",checked:b,onChange:e=>v(e.target.checked),className:"accent-blue-700"}),r.jsx("label",{htmlFor:"show-email",className:"text-sm text-gray-700 dark:text-gray-300 cursor-pointer select-none",children:A("feedback.includeEmail")})]}),b&&r.jsxs("div",{className:"relative",children:[r.jsx("input",{className:la("rounded-lg border-2 p-2 text-base transition-all","focus:outline-none focus:ring-2 focus:ring-blue-500","bg-white dark:bg-gray-700","text-gray-900 dark:text-gray-100","placeholder:text-gray-500 dark:placeholder:text-gray-400","w-full pr-10",f.length>0?M?"border-green-500":"border-red-500":"border-gray-300 dark:border-gray-600"),type:"email",placeholder:"E-mail",value:f,onChange:e=>h(e.target.value),"aria-invalid":f.length>0&&!M}),f.length>0&&!M&&r.jsx(O,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-red-500","aria-hidden":"true"}),f.length>0&&M&&r.jsx(J,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500","aria-hidden":"true"})]}),r.jsx("a",{href:"/portfolio/privacy-policy",target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-700 dark:text-blue-400 underline",children:A("feedback.privacyPolicy")}),r.jsxs("div",{className:"flex gap-2 pt-2",children:[r.jsx(Yo,{type:"submit",variant:"primary",size:"md",icon:ee,iconPosition:"left",disabled:!L,loading:y,className:"flex-1",children:A(y?"feedback.sending":"feedback.send")}),r.jsx(Yo,{type:"button",onClick:()=>{d(1),D()},variant:"ghost",size:"md",icon:te,iconPosition:"left",className:"flex-1",children:A("feedback.back")})]}),"success"===E&&r.jsxs(i.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mt-4 flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-lg text-green-700 dark:text-green-400",role:"status","aria-live":"polite",children:[r.jsx(J,{className:"w-4 h-4 flex-shrink-0","aria-hidden":"true"}),r.jsx("span",{className:"text-sm font-medium",children:A("feedback.status.success")})]}),"error"===E&&r.jsxs(i.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"mt-4 flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400",role:"alert","aria-live":"assertive",children:[r.jsx(Z,{className:"w-4 h-4 flex-shrink-0","aria-hidden":"true"}),r.jsx("span",{className:"text-sm font-medium",children:A("feedback.status.error")})]})]})})};const vr={GTM_ID:"GTM-M2NFRBD9",EVENTS:{PAGE_VIEW:"page_view",NAVIGATION:"navigation",BUTTON_CLICK:"button_click",EXTERNAL_LINK:"external_link",PROJECT_VIEW:"project_view",CONTACT_FORM:"contact_form_submit",CV_DOWNLOAD:"cv_download",SOCIAL_CLICK:"social_media_click",ACCESSIBILITY_TOGGLE:"accessibility_toggle",THEME_CHANGE:"theme_change",LANGUAGE_CHANGE:"language_change",PERFORMANCE_METRIC:"performance_metric",ERROR:"error"},CATEGORIES:{ENGAGEMENT:"engagement",NAVIGATION:"navigation",PORTFOLIO:"portfolio",ACCESSIBILITY:"accessibility",PERFORMANCE:"performance",ERROR:"error"}},yr=(e,t,a,o,r)=>({event:e,event_category:t,event_label:a,value:o,custom_parameters:r}),xr=e=>yr(vr.EVENTS.NAVIGATION,vr.CATEGORIES.NAVIGATION,e),wr=e=>yr(vr.EVENTS.PROJECT_VIEW,vr.CATEGORIES.PORTFOLIO,e),kr=e=>yr(vr.EVENTS.CONTACT_FORM,vr.CATEGORIES.ENGAGEMENT,e),Er=()=>yr(vr.EVENTS.CV_DOWNLOAD,vr.CATEGORIES.ENGAGEMENT,"curriculum_vitae"),Cr=e=>yr(vr.EVENTS.SOCIAL_CLICK,vr.CATEGORIES.ENGAGEMENT,e),jr=(e,t)=>yr(vr.EVENTS.ACCESSIBILITY_TOGGLE,vr.CATEGORIES.ACCESSIBILITY,e,t?1:0),Tr=e=>yr(vr.EVENTS.THEME_CHANGE,vr.CATEGORIES.ACCESSIBILITY,e),Sr=e=>yr(vr.EVENTS.LANGUAGE_CHANGE,vr.CATEGORIES.ACCESSIBILITY,e),Ar=e=>{if("string"!=typeof e||!e)return!1;const t=e.toLowerCase().trim();return["tarcisiobispo.github.io","localhost","127.0.0.1","::1","images.unsplash.com","unsplash.com","plus.unsplash.com","production.wootric.com","web.delighted.com","e.logrocket.com","api.logrocket.com"].includes(t)},Ir=e=>"string"!=typeof e?"":e.split("<").join("&lt;").split(">").join("&gt;").split('"').join("&quot;").split("'").join("&#x27;").split("&").join("&amp;").trim(),Nr=e=>{if("string"!=typeof e||!e)return!1;const t=e.toLowerCase().trim();return["production.wootric.com","web.delighted.com","e.logrocket.com","api.logrocket.com","www.google-analytics.com","analytics.google.com","clarity.microsoft.com"].includes(t)},Pr={validateHostname:Ar,validateUrl:e=>{if("string"!=typeof e||!e)return!1;try{const t=new URL(e);return("https:"===t.protocol||"http:"===t.protocol)&&Ar(t.hostname)}catch{return!1}},validateEmail:e=>{if("string"!=typeof e||!e)return!1;if(!e.includes("@")||e.length<5)return!1;try{const t=document.createElement("input");return t.type="email",t.value=e,t.validity.valid&&t.value===e}catch{return!1}},sanitizeString:Ir,validatePath:e=>{if("string"!=typeof e||!e)return!1;const t=["../","..\\","<script","javascript:","data:","vbscript:"],a=e.toLowerCase();for(const o of t)if(a.includes(o))return!1;return!0},extractDomain:e=>{if("string"!=typeof e||!e)return null;try{return new URL(e).hostname}catch{return null}},parseQueryParams:e=>{if("string"!=typeof e||!e)return{};const t={};try{const a=new URLSearchParams(e);for(const[e,o]of a.entries()){const a=Ir(e),r=Ir(o);a&&(t[a]=r)}}catch{}return t},validateContentType:e=>{if("string"!=typeof e||!e)return!1;const t=e.toLowerCase().split(";")[0].trim();return["text/html","text/plain","application/json","application/javascript","text/css","image/jpeg","image/png","image/gif","image/webp","image/svg+xml"].includes(t)},validateFileExtension:e=>{if("string"!=typeof e||!e)return!1;const t=e.lastIndexOf(".");if(-1===t)return!1;const a=e.slice(t).toLowerCase();return[".jpg",".jpeg",".png",".gif",".webp",".svg",".pdf",".txt",".json",".css",".js",".html"].includes(a)},validateAnalyticsDomain:Nr,validateAnalyticsUrl:e=>{if("string"!=typeof e||!e)return!1;try{const t=new URL(e);return"https:"===t.protocol&&Nr(t.hostname)}catch{return!1}},matchesAnalyticsPattern:(e,t)=>{if("string"!=typeof e||!e)return!1;try{const a=new URL(e);switch(t){case"wootric":return"production.wootric.com"===a.hostname&&a.pathname.startsWith("/responses");case"delighted":return"web.delighted.com"===a.hostname&&a.pathname.includes("/e/")&&a.pathname.includes("/c");case"logrocket":return"e.logrocket.com"===a.hostname||"api.logrocket.com"===a.hostname;default:return!1}}catch{return!1}}},Dr=()=>{let e;try{e=p()}catch{e={pathname:"/"}}return{trackPageView:l.useCallback((()=>{"undefined"!=typeof window&&(window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:vr.EVENTS.PAGE_VIEW,page_path:e.pathname,page_title:document.title,page_location:window.location.href,timestamp:(new Date).toISOString()}))}),[e.pathname])}},Rr=({isOpen:e,onClose:t,activeSection:a,onNavigate:o,items:i})=>{const{t:n}=ue();return e?r.jsxs("div",{className:"fixed inset-0 z-50 md:hidden",children:[r.jsx("div",{className:"fixed inset-0 bg-black/50",onClick:t}),r.jsx("div",{className:"fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-800 shadow-xl",children:r.jsxs("div",{className:"p-6",children:[r.jsxs("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold",children:"Menu"}),r.jsx("button",{onClick:t,className:"p-2",children:"✕"})]}),r.jsx("nav",{className:"space-y-4",children:i.map((e=>r.jsxs("a",{href:e.href,onClick:a=>{a.preventDefault(),o(e.sectionId),t()},className:"flex items-center gap-3 p-3 rounded-lg transition-colors "+(a===e.sectionId?"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400":"hover:bg-gray-100 dark:hover:bg-gray-700"),children:[e.icon,r.jsx("span",{children:n(e.label)})]},e.id)))})]})})]}):null},Mr=({isOpen:e,onClose:t,onFeedbackOpen:a,onAccessibilityOpen:o})=>e?r.jsx("div",{className:"fixed inset-0 z-50 bg-black/50 md:hidden",onClick:t,children:r.jsxs("div",{className:"fixed top-16 left-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 min-w-[200px]",children:[r.jsx("h3",{className:"font-semibold mb-3",children:"Configurações"}),r.jsxs("div",{className:"space-y-2",children:[r.jsx("button",{onClick:a,className:"w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded",children:"Feedback"}),r.jsx("button",{onClick:o,className:"w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded",children:"Acessibilidade"})]})]})}):null,Lr=[{href:"#perfil",icon:ae,sectionId:"perfil",i18nKey:"navigation.profile"},{href:"#projetos",icon:oe,sectionId:"projetos",i18nKey:"navigation.projects"},{href:"#backlog",icon:re,sectionId:"backlog",i18nKey:"navigation.backlog"},{href:"#contato",icon:Y,sectionId:"contato",i18nKey:"navigation.contact"}];function zr(){const[e,t]=l.useState(!1),[a,o]=l.useState("perfil"),[i,n]=l.useState(!1),[s,c]=l.useState(!1),[d,u]=l.useState(!1),{t:m}=ue(),{trackNavigation:g}=(()=>{let e;try{e=p()}catch{e={pathname:"/"}}const t=l.useCallback((t=>{"undefined"!=typeof window&&(window.dataLayer=window.dataLayer||[],window.dataLayer.push({...t,page_location:window.location.href,page_path:e.pathname,timestamp:(new Date).toISOString()}))}),[e.pathname]);return{trackNavigation:l.useCallback((e=>{t(xr(e))}),[t]),trackProjectView:l.useCallback((e=>{t(wr(e))}),[t]),trackContactForm:l.useCallback(((e="contact")=>{t(kr(e))}),[t]),trackCVDownload:l.useCallback((()=>{t(Er())}),[t]),trackSocialClick:l.useCallback((e=>{t(Cr(e))}),[t]),trackAccessibilityToggle:l.useCallback(((e,a)=>{t(jr(e,a))}),[t]),trackThemeChange:l.useCallback((e=>{t(Tr(e))}),[t]),trackLanguageChange:l.useCallback((e=>{t(Sr(e))}),[t]),trackButtonClick:l.useCallback(((e,a)=>{t({event:vr.EVENTS.BUTTON_CLICK,event_category:vr.CATEGORIES.ENGAGEMENT,event_label:e,custom_parameters:{section:a||"unknown",button_type:"cta"}})}),[t]),trackExternalLink:l.useCallback(((e,a)=>{if(!Pr.validateUrl(e))return void console.warn("URL inválida para tracking:",e);const o=Pr.extractDomain(e)||"unknown";t({event:vr.EVENTS.EXTERNAL_LINK,event_category:vr.CATEGORIES.ENGAGEMENT,event_label:a||e,custom_parameters:{destination_url:e,link_domain:o}})}),[t]),trackError:l.useCallback(((a,o)=>{t({event:vr.EVENTS.ERROR,event_category:vr.CATEGORIES.ERROR,event_label:a,custom_parameters:{error_type:o||"javascript_error",page_path:e.pathname}})}),[t,e.pathname]),trackPerformance:l.useCallback(((a,o,r)=>{t({event:vr.EVENTS.PERFORMANCE_METRIC,event_category:vr.CATEGORIES.PERFORMANCE,event_label:a,value:Math.round(o),custom_parameters:{metric_unit:r||"ms",page_path:e.pathname}})}),[t,e.pathname]),sendEvent:t}})(),{playButtonHover:f,playButtonClick:h,playPageTransition:b}=Wo(),v=function(){const[e,t]=l.useState(void 0);return l.useEffect((()=>{const e=window.matchMedia("(max-width: 767px)"),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)}),[]),!!e}();l.useEffect((()=>{const e=e=>{"Escape"===e.key&&(s&&c(!1),d&&u(!1))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[s,d]),l.useEffect((()=>{v||(s&&c(!1),d&&u(!1))}),[v,s,d]),l.useEffect((()=>{const e=()=>{t(window.scrollY>10);let e="perfil";for(const t of Lr){const a=document.getElementById(t.sectionId);if(a){const o=a.getBoundingClientRect();if(o.top<=80&&o.bottom>80){e=t.sectionId;break}}}o(e)};return window.addEventListener("scroll",e,{passive:!0}),e(),()=>window.removeEventListener("scroll",e)}),[]);const y=l.useCallback(((e,t)=>{e.preventDefault();const a=document.getElementById(t);a&&(window.scrollTo({top:a.offsetTop-70,behavior:"smooth"}),o(t),g(t))}),[g]),x=l.useCallback((()=>{c((e=>!e))}),[]),w=l.useCallback((()=>{c(!1)}),[]),k=l.useCallback((e=>{const t=document.getElementById(e);t&&(window.scrollTo({top:t.offsetTop-70,behavior:"smooth"}),o(e))}),[]),E=l.useCallback((()=>{u((e=>!e)),s&&c(!1)}),[s]),C=l.useCallback((()=>{u(!1)}),[]),j=l.useCallback((()=>{u(!1)}),[]);return r.jsxs("header",{className:`fixed top-0 left-0 w-full z-50 transition-all duration-300 border-b border-[var(--color-border)]\n        ${e?"bg-[var(--color-surface)] shadow-md":"bg-[var(--color-surface)]/80 backdrop-blur"}\n      `,role:"banner",children:[r.jsxs("nav",{id:"navigation",className:"max-w-7xl mx-auto flex items-center justify-between px-6 py-2","aria-label":"Menu principal",children:[r.jsx("a",{href:"#perfil",className:"hidden md:flex items-center gap-2 text-2xl font-bold tracking-tight text-[var(--color-primary-dark)]","aria-label":m("navigation.home"),children:r.jsx("span",{className:"rounded bg-[#1d4ed8] text-white px-2 py-1 text-lg font-black shadow-sm",children:"TBA"})}),r.jsx("div",{className:"md:hidden",children:r.jsx(Jo,{isOpen:d,onClick:E})}),r.jsx("ul",{className:"hidden md:flex gap-8 items-end",children:Lr.map((e=>{const t=e.icon,o=a===e.sectionId;return r.jsx("li",{className:"group relative flex flex-col items-center justify-end",children:r.jsxs("a",{href:e.href,onClick:t=>{y(t,e.sectionId),b()},onMouseEnter:()=>f(),className:`relative flex flex-col items-center px-3 py-2 outline-none transition-all duration-300 ease-out\n                    ${o?"text-[var(--color-primary)]":"text-[var(--color-text)] hover:text-[var(--color-primary)]"}\n                    focus-visible:text-[var(--color-primary)]`,"aria-current":o?"page":void 0,children:[r.jsx(t,{className:`mb-1 w-5 h-5 transition-all duration-300 ease-out\n                      ${o?"text-[var(--color-primary)]":"group-hover:text-[var(--color-primary)] group-hover:transform group-hover:translate-x-0.5 group-hover:-translate-y-0.5"}\n                    `,"aria-hidden":"true"}),r.jsx("span",{className:"text-sm font-medium transition-colors duration-300 ease-out "+(o?"text-[var(--color-primary)]":"text-gray-900 dark:text-gray-100 group-hover:text-[var(--color-primary)]"),children:m(e.i18nKey)}),o&&r.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-0.5 bg-[var(--color-primary)]"}),!o&&r.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-[var(--color-primary)] group-hover:w-full transition-all duration-300 ease-out"})]})},e.href)}))}),r.jsxs("div",{className:"flex gap-2 sm:gap-3 items-center",children:[r.jsx("div",{className:"md:hidden",children:r.jsx(Qo,{isOpen:s,onClick:x})}),r.jsxs("div",{className:"hidden md:flex gap-2 sm:gap-3 items-center",children:[r.jsx(ar,{}),r.jsx(Zo,{}),r.jsx(er,{}),r.jsx(or,{}),r.jsxs("button",{onClick:()=>{n(!0),h()},onMouseEnter:()=>f(),className:"group relative transition-all duration-300 flex items-center justify-center rounded-full border border-[var(--color-border)] bg-[var(--color-surface)] shadow hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] w-10 h-10 text-[var(--color-primary)] hover:scale-110 overflow-hidden",style:{color:"var(--color-primary)"},"aria-label":m("feedback.openFeedback"),title:m("feedback.openFeedback"),children:[r.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-green-400/10 via-blue-400/10 to-purple-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),r.jsx("div",{className:"absolute top-0 right-1 w-1 h-1 bg-green-400 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-400 group-hover:animate-bounce"}),r.jsx("div",{className:"absolute top-1 left-0 w-0.5 h-0.5 bg-blue-400 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-600 group-hover:animate-pulse"}),r.jsx("div",{className:"absolute bottom-0 left-1 w-0.5 h-0.5 bg-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 group-hover:animate-ping"}),r.jsx(ie,{className:"w-5 h-5 transition-all duration-300 relative z-10 group-hover:text-green-500 group-hover:drop-shadow-[0_0_6px_rgba(34,197,94,0.6)] group-hover:rotate-12 group-hover:translate-y-[-2px] group-hover:translate-x-[1px]","aria-hidden":"true"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400/30 scale-0 group-hover:scale-110 transition-transform duration-400 opacity-0 group-hover:opacity-100 group-hover:animate-pulse"})]})]})]})]}),r.jsx(Rr,{isOpen:s,onClose:w,activeSection:a,onNavigate:k,items:[{id:"perfil",sectionId:"perfil",label:"navigation.profile",href:"#perfil",icon:r.jsx(ae,{className:"w-5 h-5"}),disabled:!1},{id:"projetos",sectionId:"projetos",label:"navigation.projects",href:"#projetos",icon:r.jsx(oe,{className:"w-5 h-5"}),disabled:!1},{id:"backlog",sectionId:"backlog",label:"navigation.backlog",href:"#backlog",icon:r.jsx(re,{className:"w-5 h-5"}),disabled:!1},{id:"contato",sectionId:"contato",label:"navigation.contact",href:"#contato",icon:r.jsx(Y,{className:"w-5 h-5"}),disabled:!1}],config:{position:"side",animation:"slide",backdrop:!0,swipeToClose:!0,autoClose:!0}}),r.jsx(Mr,{isOpen:d,onClose:C,onFeedbackOpen:()=>n(!0),onAccessibilityOpen:j}),r.jsx(br,{open:i,onClose:()=>n(!1),section:a})]})}const Or=l.lazy((()=>he((()=>import("./Index-C7GLQPZa.js")),__vite__mapDeps([0,1,2,3,4,5,6,7])))),Ur=l.lazy((()=>he((()=>import("./NotFound-BjHS4ktM.js")),__vite__mapDeps([8,1,2,3,5,6,7,4])))),_r=l.lazy((()=>he((()=>import("./PrivacyPolicy-BjMS_s1l.js")),__vite__mapDeps([9,1,2,3,5])))),Br=l.lazy((()=>he((()=>import("./AnalyticsProvider-Da6roXPw.js")),__vite__mapDeps([10,1,2,3,11,6,5,7,4])))),Vr=l.lazy((()=>he((()=>import("./BackToTop-Dir6kW45.js")),__vite__mapDeps([12,1,2,3,5])))),Fr=l.lazy((()=>he((()=>import("./FluidGradientBackground-fz9tBY22.js")),__vite__mapDeps([13,1,2,3])).then((e=>({default:e.default}))))),$r=l.lazy((()=>he((()=>import("./LazyScripts-Chw9cytQ.js")),__vite__mapDeps([14,2,3])))),qr=l.lazy((()=>he((()=>import("./CookieConsent-ek5hpc7h.js")),__vite__mapDeps([15,1,2,3,4,5])))),Gr=new ne,Xr=()=>{const{t:e}=ue();return r.jsx(Do,{children:r.jsx(se,{client:Gr,children:r.jsx(Fo,{children:r.jsx(Ba,{children:r.jsx(l.Suspense,{fallback:r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(Bo,{})}),children:r.jsxs(Br,{children:[r.jsx("a",{href:"#main-content",className:"skip-link",children:e("accessibility.features.skipToContent")}),r.jsx("a",{href:"#navigation",className:"skip-link",children:e("accessibility.features.skipToNavigation")}),r.jsx(ha,{}),r.jsx(_a,{}),r.jsx(l.Suspense,{fallback:null,children:r.jsx(Fr,{})}),!1,!1,!1,r.jsx(l.Suspense,{fallback:null,children:r.jsx(Vr,{})}),r.jsx(l.Suspense,{fallback:null,children:r.jsx($r,{delay:2e3})}),r.jsx(l.Suspense,{fallback:null,children:r.jsx(qr,{})}),!1,r.jsx(zr,{}),r.jsx(m,{basename:"/portfolio",future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:r.jsx(l.Suspense,{fallback:r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(Bo,{})}),children:r.jsxs(g,{children:[r.jsx(f,{path:"/",element:r.jsx(Or,{})}),r.jsx(f,{path:"/privacy-policy",element:r.jsx(_r,{})}),r.jsx(f,{path:"*",element:r.jsx(Ur,{})})]})})})]})})})})})})},Hr=()=>{(()=>{const e="/portfolio/";[{href:`${e}images/tarcisio_bispo.webp`,as:"image",type:"image/webp",fetchpriority:"high"},{href:`${e}images/tarcisio_bispo.png`,as:"image",type:"image/png",fetchpriority:"high"}].forEach((e=>{const t=document.createElement("link");t.rel="preload",t.href=e.href,t.as=e.as,e.type&&(t.type=e.type),e.fetchpriority&&t.setAttribute("fetchpriority",e.fetchpriority),e.crossorigin&&""!==e.crossorigin&&t.setAttribute("crossorigin",e.crossorigin),t.onerror=()=>{console.warn(`Failed to preload resource: ${e.href}`)},document.head.appendChild(t)}))})(),document.querySelectorAll('link[href*="fonts.googleapis.com"]').forEach((e=>{const t=e.getAttribute("href");if(t&&!t.includes("display=swap")){const a=new URL(t);a.searchParams.set("display","swap"),e.setAttribute("href",a.toString())}})),(async()=>{if("serviceWorker"in navigator)try{const e="/portfolio/sw.js";return await navigator.serviceWorker.register(e)}catch(e){return console.warn("Service Worker registration failed:",e),null}})();const e=(()=>{if("IntersectionObserver"in window){const e=new IntersectionObserver((t=>{t.forEach((t=>{if(t.isIntersecting){const a=t.target;a.dataset.src&&(a.src=a.dataset.src,a.removeAttribute("data-src")),a.dataset.srcset&&(a.srcset=a.dataset.srcset,a.removeAttribute("data-srcset")),a.classList.remove("lazy-loading"),a.classList.add("lazy-loaded"),e.unobserve(a)}}))}),{rootMargin:"50px 0px",threshold:.01});return e}return null})();if(e){document.querySelectorAll("img[data-src]").forEach((t=>e.observe(t)))}{const e=[];e.length>0&&(e=>{"requestIdleCallback"in window?requestIdleCallback((()=>{e.forEach((e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}))})):setTimeout((()=>{e.forEach((e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}))}),2e3)})(e)}},Wr=e=>new Promise(((t,a)=>{const o=new Image;o.onload=()=>t(),o.onerror=()=>a(new Error(`Failed to load image: ${e}`)),o.src=e})),Kr=e=>new Promise((t=>setTimeout(t,e))),Yr=()=>{(()=>{const e="/portfolio/";[`${e}images/tarcisio_bispo.webp`,`${e}images/tarcisio_bispo.png`,`${e}images/ixdf-symbol-dark.png`,`${e}images/ixdf-symbol-white.png`].forEach((e=>{const t=document.createElement("link");t.rel="preload",t.as="image",t.href=e,t.onerror=()=>{console.warn(`Failed to preload image: ${e}`)},document.head.appendChild(t)}))})();const e=(t=t=>{t.forEach((t=>{if(t.isIntersecting){const a=t.target,o=a.dataset.src;o&&(async e=>{const{src:t,fallback:a,retryAttempts:o=3,retryDelay:r=1e3}=e;for(let n=0;n<o;n++)try{return await Wr(t),t}catch(i){console.warn(`Image load attempt ${n+1} failed for: ${t}`),n<o-1&&await Kr(r)}if(a)try{return await Wr(a),a}catch(i){console.warn(`Fallback image also failed: ${a}`)}return e.placeholder||""})({src:o}).then((e=>{a.src=e,a.removeAttribute("data-src"),a.classList.remove("lazy-loading"),a.classList.add("lazy-loaded")})).catch((e=>{console.warn("Failed to load lazy image:",e),a.classList.add("lazy-error")})),null==e||e.unobserve(a)}}))},"IntersectionObserver"in window?new IntersectionObserver(t,{rootMargin:"50px 0px",threshold:.01}):null);var t;if(e){document.querySelectorAll("img[data-src]").forEach((t=>e.observe(t)))}};class Qr{static parseJSON(e){try{return JSON.parse(e)}catch(t){return console.error("CSPSecurity: Invalid JSON string:",t),null}}static interpolateTemplate(e,t){return e.replace(/\{\{(\w+)\}\}/g,((e,a)=>void 0!==t[a]?String(t[a]):e))}static getNestedProperty(e,t){return t.split(".").reduce(((e,t)=>e&&void 0!==e[t]?e[t]:void 0),e)}static setNestedProperty(e,t,a){const o=t.split("."),r=o.pop();if(!r)return;o.reduce(((e,t)=>(e[t]&&"object"==typeof e[t]||(e[t]={}),e[t])),e)[r]=a}static executeSafeOperation(e,t){const a={add:(e,t)=>e+t,subtract:(e,t)=>e-t,multiply:(e,t)=>e*t,divide:(e,t)=>0!==t?e/t:0,concat:(e,t)=>e+t,uppercase:e=>e.toUpperCase(),lowercase:e=>e.toLowerCase(),length:e=>e.length,reverse:e=>[...e].reverse(),sort:e=>[...e].sort()};return a[e]?a[e]:(console.warn("CSPSecurity: Unsafe operation blocked:",e),null)}static secureSetTimeout(e,t){return"function"!=typeof e?(console.error("CSPSecurity: setTimeout callback must be a function"),0):window.setTimeout(e,t)}static secureSetInterval(e,t){return"function"!=typeof e?(console.error("CSPSecurity: setInterval callback must be a function"),0):window.setInterval(e,t)}static async secureImport(e){if(!this.isValidModulePath(e))throw new Error("CSPSecurity: Invalid module path");try{return await import(e)}catch(t){throw console.error("CSPSecurity: Failed to import module:",e,t),t}}static isValidModulePath(e){return[/^\.\//,/^\.\.\//,/^@?[a-zA-Z0-9_-]+\/[a-zA-Z0-9_/-]+$/,/^[a-zA-Z0-9_-]+$/].some((t=>t.test(e)))}static sanitizeHTML(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}static buildSecureURL(e,t){try{const a=new URL(e);return Object.entries(t).forEach((([e,t])=>{a.searchParams.set(e,t)})),a.toString()}catch(a){return console.error("CSPSecurity: Invalid URL construction:",a),e}}static installCSPInterceptors(){if("undefined"!=typeof window){window.eval=function(e){throw console.error("CSPSecurity: eval() usage blocked for security. Use secure alternatives."),new Error("eval() is not allowed due to Content Security Policy")},window.Function=function(...e){throw console.error("CSPSecurity: Function constructor blocked for security."),new Error("Function constructor is not allowed due to Content Security Policy")};const e=window.setTimeout,t=window.setInterval;window.setTimeout=function(t,a,...o){if("string"==typeof t)throw console.error("CSPSecurity: setTimeout with string blocked. Use function instead."),new Error("setTimeout with string is not allowed due to Content Security Policy");return e.call(this,t,a,...o)},window.setInterval=function(e,a,...o){if("string"==typeof e)throw console.error("CSPSecurity: setInterval with string blocked. Use function instead."),new Error("setInterval with string is not allowed due to Content Security Policy");return t.call(this,e,a,...o)}}}static generateNonce(){const e=new Uint8Array(16);return crypto.getRandomValues(e),btoa(String.fromCharCode(...e))}static validateCSPCompliance(){const e=[];if("undefined"!=typeof window){window.eval.toString().includes("eval() usage blocked")||e.push("eval() interceptor not installed");window.Function.toString().includes("Function constructor blocked")||e.push("Function constructor interceptor not installed")}return!(e.length>0)||(console.warn("CSPSecurity: CSP violations detected:",e),!1)}}"undefined"!=typeof window&&(window.React=s);const Jr=document.getElementById("root");if(!Jr)throw new Error("Root element not found");const Zr=ge.createRoot(Jr);"undefined"!=typeof window&&(Hr(),Yr(),setTimeout((()=>{"undefined"!=typeof window&&(Qr.installCSPInterceptors(),setTimeout((()=>{Qr.validateCSPCompliance()}),100))}),1e3)),Zr.render(r.jsx(l.StrictMode,{children:r.jsx(ya,{defaultTheme:"system",storageKey:"portfolio-theme",children:r.jsx(Xr,{})})}));export{vr as A,Yo as C,_o as H,Bo as L,Pr as S,he as _,Ko as a,la as b,je as c,Wo as d,Ho as e,pr as f,Dr as u};
//# sourceMappingURL=index-C6NRgFfu.js.map

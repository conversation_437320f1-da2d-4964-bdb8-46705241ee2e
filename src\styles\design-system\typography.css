/**
 * 🎯 SISTEMA TIPOGRÁFICO
 * 
 * Tipografia baseada nos tokens
 * Hierarquia visual clara
 * Acessibilidade otimizada
 */

/* ===== FONT FAMILIES ===== */
:root {
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, monospace;
  --font-family-display: 'Inter', var(--font-family);
}

/* ===== FONT SIZES SEMÂNTICOS ===== */
:root {
  --font-size-xs: var(--text-xs);
  --font-size-sm: var(--text-sm);
  --font-size-base: var(--text-base);
  --font-size-lg: var(--text-lg);
  --font-size-xl: var(--text-xl);
  --font-size-2xl: var(--text-2xl);
  --font-size-3xl: var(--text-3xl);
  --font-size-4xl: var(--text-4xl);
  --font-size-5xl: var(--text-5xl);
  --font-size-6xl: var(--text-6xl);
}

/* ===== FONT WEIGHTS SEMÂNTICOS ===== */
:root {
  --font-weight-normal: var(--font-normal);
  --font-weight-medium: var(--font-medium);
  --font-weight-semibold: var(--font-semibold);
  --font-weight-bold: var(--font-bold);
}

/* ===== LINE HEIGHTS SEMÂNTICOS ===== */
:root {
  --line-height-tight: var(--leading-tight);
  --line-height-base: var(--leading-normal);
  --line-height-relaxed: var(--leading-relaxed);
}

/* ===== ESTILOS BASE ===== */
body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text);
  font-weight: var(--font-weight-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== HEADINGS ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-display);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  margin: 0;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* ===== PARAGRAPHS ===== */
p {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  color: var(--color-text);
  margin: 0;
}

.lead {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-muted);
}

.small {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

/* ===== LINKS ===== */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-200) var(--ease-out);
}

a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-focus-ring);
  outline-offset: 2px;
}

/* ===== LISTS ===== */
ul, ol {
  margin: 0;
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
}

/* ===== CODE ===== */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background: var(--color-surface-hover);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
  color: var(--color-text);
}

pre {
  font-family: var(--font-family-mono);
  background: var(--color-surface);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* ===== BLOCKQUOTES ===== */
blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: var(--spacing-4);
  margin: var(--spacing-4) 0;
  font-style: italic;
  color: var(--color-text-muted);
}

/* ===== UTILITY CLASSES ===== */

/* Font Sizes */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }
.text-5xl { font-size: var(--font-size-5xl) !important; }
.text-6xl { font-size: var(--font-size-6xl) !important; }

/* Font Weights */
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* Line Heights */
.leading-tight { line-height: var(--line-height-tight) !important; }
.leading-normal { line-height: var(--line-height-base) !important; }
.leading-relaxed { line-height: var(--line-height-relaxed) !important; }

/* Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Text Transform */
.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }
.normal-case { text-transform: none !important; }

/* Text Decoration */
.underline { text-decoration: underline !important; }
.line-through { text-decoration: line-through !important; }
.no-underline { text-decoration: none !important; }

/* Font Style */
.italic { font-style: italic !important; }
.not-italic { font-style: normal !important; }

/* Font Variant */
.font-mono { font-family: var(--font-family-mono) !important; }
.font-display { font-family: var(--font-family-display) !important; }

/* Text Overflow */
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-ellipsis { text-overflow: ellipsis !important; }
.text-clip { text-overflow: clip !important; }

/* White Space */
.whitespace-normal { white-space: normal !important; }
.whitespace-nowrap { white-space: nowrap !important; }
.whitespace-pre { white-space: pre !important; }
.whitespace-pre-line { white-space: pre-line !important; }
.whitespace-pre-wrap { white-space: pre-wrap !important; }

/* Word Break */
.break-normal { overflow-wrap: normal; word-break: normal !important; }
.break-words { overflow-wrap: break-word !important; }
.break-all { word-break: break-all !important; }

/* ===== RESPONSIVE TYPOGRAPHY ===== */
@media (max-width: 768px) {
  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
  h4 { font-size: var(--font-size-lg); }
  
  .lead { font-size: var(--font-size-base); }
}

/* ===== ACESSIBILIDADE ===== */
@media (prefers-contrast: high) {
  body {
    font-weight: var(--font-weight-medium);
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
  }
  
  a {
    text-decoration: underline;
  }
}

@media (prefers-reduced-motion: reduce) {
  a {
    transition: none;
  }
}

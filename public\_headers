# Cache headers for GitHub Pages
# Assets with hash in filename - cache for 1 year
/js/*
  Cache-Control: public, max-age=31536000, immutable

/css/*
  Cache-Control: public, max-age=31536000, immutable

/images/*
  Cache-Control: public, max-age=31536000, immutable

/fonts/*
  Cache-Control: public, max-age=31536000, immutable

# HTML files - cache for 1 hour
/*.html
  Cache-Control: public, max-age=3600

# Root HTML
/
  Cache-Control: public, max-age=3600

# Service worker - no cache
/sw.js
  Cache-Control: no-cache

# Manifest
/manifest.json
  Cache-Control: public, max-age=86400

# Security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

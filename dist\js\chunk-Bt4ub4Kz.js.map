{"version": 3, "file": "chunk-Bt4ub4Kz.js", "sources": ["../../node_modules/@radix-ui/primitive/dist/index.mjs", "../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../node_modules/@radix-ui/react-context/dist/index.mjs", "../../node_modules/@radix-ui/react-slot/dist/index.mjs", "../../node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../node_modules/@radix-ui/react-portal/dist/index.mjs", "../../node_modules/@radix-ui/react-presence/dist/index.mjs", "../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "../../node_modules/clsx/dist/clsx.mjs", "../../node_modules/@radix-ui/react-id/dist/index.mjs", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../node_modules/@radix-ui/react-arrow/dist/index.mjs", "../../node_modules/@radix-ui/react-popper/dist/index.mjs", "../../node_modules/@radix-ui/react-use-size/dist/index.mjs", "../../node_modules/@radix-ui/react-tooltip/dist/index.mjs"], "sourcesContent": ["// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport {\n  Presence,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VISUALLY_HIDDEN_STYLES,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "// src/arrow.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Arrow\";\nvar Arrow = React.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ jsx(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\nexport {\n  Arrow,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/popper.tsx\nimport * as React from \"react\";\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size\n} from \"@floating-ui/react-dom\";\nimport * as ArrowPrimitive from \"@radix-ui/react-arrow\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SIDE_OPTIONS = [\"top\", \"right\", \"bottom\", \"left\"];\nvar ALIGN_OPTIONS = [\"start\", \"center\", \"end\"];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState(null);\n  return /* @__PURE__ */ jsx(PopperProvider, { scope: __scopePopper, anchor, onAnchorChange: setAnchor, children });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    React.useEffect(() => {\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ jsx(Primitive.div, { ...anchorProps, ref: composedRefs });\n  }\n);\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = \"bottom\",\n      sideOffset = 0,\n      align = \"center\",\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = \"partial\",\n      hideWhenDetached = false,\n      updatePositionStrategy = \"optimized\",\n      onPlaced,\n      ...contentProps\n    } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [arrow, setArrow] = React.useState(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: \"fixed\",\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === \"always\"\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions && shift({\n          mainAxis: true,\n          crossAxis: false,\n          limiter: sticky === \"partial\" ? limitShift() : void 0,\n          ...detectOverflowOptions\n        }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n          }\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: \"referenceHidden\", ...detectOverflowOptions })\n      ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = React.useState();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n          // keep off the page when measuring\n          minWidth: \"max-content\",\n          zIndex: contentZIndex,\n          [\"--radix-popper-transform-origin\"]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y\n          ].join(\" \"),\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...middlewareData.hide?.referenceHidden && {\n            visibility: \"hidden\",\n            pointerEvents: \"none\"\n          }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ jsx(\n          PopperContentProvider,\n          {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                  ...contentProps.style,\n                  // if the PopperContent hasn't been placed yet (not all measurements done)\n                  // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                  animation: !isPositioned ? \"none\" : void 0\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nvar PopperArrow = React.forwardRef(function PopperArrow2(props, forwardedRef) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ jsx(\n      \"span\",\n      {\n        ref: contentContext.onArrowChange,\n        style: {\n          position: \"absolute\",\n          left: contentContext.arrowX,\n          top: contentContext.arrowY,\n          [baseSide]: 0,\n          transformOrigin: {\n            top: \"\",\n            right: \"0 0\",\n            bottom: \"center 0\",\n            left: \"100% 0\"\n          }[contentContext.placedSide],\n          transform: {\n            top: \"translateY(100%)\",\n            right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n            bottom: `rotate(180deg)`,\n            left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n          }[contentContext.placedSide],\n          visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ jsx(\n          ArrowPrimitive.Root,\n          {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n              ...arrowProps.style,\n              // ensures the element can be measured correctly (mostly for if SVG)\n              display: \"block\"\n            }\n          }\n        )\n      }\n    )\n  );\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n  return value !== null;\n}\nvar transformOrigin = (options) => ({\n  name: \"transformOrigin\",\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: \"0%\", center: \"50%\", end: \"100%\" }[placedAlign];\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n    let x = \"\";\n    let y = \"\";\n    if (placedSide === \"bottom\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === \"top\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === \"right\") {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === \"left\") {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  }\n});\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\nexport {\n  ALIGN_OPTIONS,\n  Anchor,\n  Arrow,\n  Content,\n  Popper,\n  PopperAnchor,\n  PopperArrow,\n  PopperContent,\n  Root2 as Root,\n  SIDE_OPTIONS,\n  createPopperScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/tooltip.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createSlottable } from \"@radix-ui/react-slot\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport * as VisuallyHiddenPrimitive from \"@radix-ui/react-visually-hidden\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar [createTooltipContext, createTooltipScope] = createContextScope(\"Tooltip\", [\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children\n  } = props;\n  const isOpenDelayedRef = React.useRef(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n  return /* @__PURE__ */ jsx(\n    TooltipProviderContextProvider,\n    {\n      scope: __scopeTooltip,\n      isOpenDelayedRef,\n      delayDuration,\n      onOpen: React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        isOpenDelayedRef.current = false;\n      }, []),\n      onClose: React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => isOpenDelayedRef.current = true,\n          skipDelayDuration\n        );\n      }, [skipDelayDuration]),\n      isPointerInTransitRef,\n      onPointerInTransitChange: React.useCallback((inTransit) => {\n        isPointerInTransitRef.current = inTransit;\n      }, []),\n      disableHoverableContent,\n      children\n    }\n  );\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: (open2) => {\n      if (open2) {\n        providerContext.onOpen();\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open2);\n    },\n    caller: TOOLTIP_NAME\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n  }, [open]);\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    setOpen(false);\n  }, [setOpen]);\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n      openTimerRef.current = 0;\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n  React.useEffect(() => {\n    return () => {\n      if (openTimerRef.current) {\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n      }\n    };\n  }, []);\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    TooltipContextProvider,\n    {\n      scope: __scopeTooltip,\n      contentId,\n      open,\n      stateAttribute,\n      trigger,\n      onTriggerChange: setTrigger,\n      onTriggerEnter: React.useCallback(() => {\n        if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n        else handleOpen();\n      }, [providerContext.isOpenDelayedRef, handleDelayedOpen, handleOpen]),\n      onTriggerLeave: React.useCallback(() => {\n        if (disableHoverableContent) {\n          handleClose();\n        } else {\n          window.clearTimeout(openTimerRef.current);\n          openTimerRef.current = 0;\n        }\n      }, [handleClose, disableHoverableContent]),\n      onOpen: handleOpen,\n      onClose: handleClose,\n      disableHoverableContent,\n      children\n    }\n  ) });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => isPointerDownRef.current = false, []);\n    React.useEffect(() => {\n      return () => document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [handlePointerUp]);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        \"aria-describedby\": context.open ? context.contentId : void 0,\n        \"data-state\": context.stateAttribute,\n        ...triggerProps,\n        ref: composedRefs,\n        onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n          if (event.pointerType === \"touch\") return;\n          if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n            context.onTriggerEnter();\n            hasPointerMoveOpenedRef.current = true;\n          }\n        }),\n        onPointerLeave: composeEventHandlers(props.onPointerLeave, () => {\n          context.onTriggerLeave();\n          hasPointerMoveOpenedRef.current = false;\n        }),\n        onPointerDown: composeEventHandlers(props.onPointerDown, () => {\n          if (context.open) {\n            context.onClose();\n          }\n          isPointerDownRef.current = true;\n          document.addEventListener(\"pointerup\", handlePointerUp, { once: true });\n        }),\n        onFocus: composeEventHandlers(props.onFocus, () => {\n          if (!isPointerDownRef.current) context.onOpen();\n        }),\n        onBlur: composeEventHandlers(props.onBlur, context.onClose),\n        onClick: composeEventHandlers(props.onClick, context.onClose)\n      }\n    ) });\n  }\n);\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar TooltipPortal = (props) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeTooltip, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.disableHoverableContent ? /* @__PURE__ */ jsx(TooltipContentImpl, { side, ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(TooltipContentHoverable, { side, ...contentProps, ref: forwardedRef }) });\n  }\n);\nvar TooltipContentHoverable = React.forwardRef((props, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState(null);\n  const { trigger, onClose } = context;\n  const content = ref.current;\n  const { onPointerInTransitChange } = providerContext;\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n  const handleCreateGraceArea = React.useCallback(\n    (event, hoverTarget) => {\n      const currentTarget = event.currentTarget;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event) => handleCreateGraceArea(event, trigger);\n      trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n      content.addEventListener(\"pointerleave\", handleContentLeave);\n      return () => {\n        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n        content.removeEventListener(\"pointerleave\", handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event) => {\n        const target = event.target;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n      return () => document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n  return /* @__PURE__ */ jsx(TooltipContentImpl, { ...props, ref: composedRefs });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, { isInside: false });\nvar Slottable = createSlottable(\"TooltipContent\");\nvar TooltipContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      \"aria-label\": ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event) => {\n          const target = event.target;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener(\"scroll\", handleScroll, { capture: true });\n        return () => window.removeEventListener(\"scroll\", handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n    return /* @__PURE__ */ jsx(\n      DismissableLayer,\n      {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event) => event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ jsxs(\n          PopperPrimitive.Content,\n          {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n              ...contentProps.style,\n              // re-namespace exposed content custom properties\n              ...{\n                \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n              }\n            },\n            children: [\n              /* @__PURE__ */ jsx(Slottable, { children }),\n              /* @__PURE__ */ jsx(VisuallyHiddenContentContextProvider, { scope: __scopeTooltip, isInside: true, children: /* @__PURE__ */ jsx(VisuallyHiddenPrimitive.Root, { id: context.contentId, role: \"tooltip\", children: ariaLabel || children }) })\n            ]\n          }\n        )\n      }\n    );\n  }\n);\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return \"left\";\n    case right:\n      return \"right\";\n    case top:\n      return \"top\";\n    case bottom:\n      return \"bottom\";\n    default:\n      throw new Error(\"unreachable\");\n  }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n  const paddedExitPoints = [];\n  switch (exitSide) {\n    case \"top\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case \"bottom\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case \"left\":\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case \"right\":\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom }\n  ];\n}\nfunction isPointInPolygon(point, polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i];\n    const jj = polygon[j];\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n    const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) inside = !inside;\n  }\n  return inside;\n}\nfunction getHull(points) {\n  const newPoints = points.slice();\n  newPoints.sort((a, b) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return 1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return 1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n  if (points.length <= 1) return points.slice();\n  const upperHull = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i];\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1];\n      const r = upperHull[upperHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n  const lowerHull = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i];\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1];\n      const r = lowerHull[lowerHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n  if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  Portal,\n  Provider,\n  Root3 as Root,\n  Tooltip,\n  TooltipArrow,\n  TooltipContent,\n  TooltipPortal,\n  TooltipProvider,\n  TooltipTrigger,\n  Trigger,\n  createTooltipScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "event", "defaultPrevented", "setRef", "ref", "value", "current", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "map", "cleanup", "i", "length", "useComposedRefs", "React.useCallback", "useCallback", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createScope", "scopeContexts", "defaultContext", "React.createContext", "scope", "contexts", "React.useMemo", "useMemo", "rootComponentName", "BaseContext", "createContext", "index", "Provider", "props", "children", "context", "Context", "_a", "Object", "values", "jsx", "displayName", "consumerName", "React.useContext", "useContext", "Error", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "createScope2", "useScope", "overrideScopes", "nextScopes", "reduce", "nextScopes2", "createSlot", "ownerName", "SlotClone", "Slot2", "React.forwardRef", "forwardedRef", "slotProps", "childrenA<PERSON>y", "React.Children", "toArray", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "Children", "count", "only", "React.isValidElement", "isValidElement", "React.cloneElement", "Slot", "createSlotClone", "childrenRef", "element", "getter", "getOwnPropertyDescriptor", "get", "<PERSON><PERSON><PERSON><PERSON>", "isReactWarning", "_b", "getElementRef", "props2", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "test", "args", "result", "filter", "Boolean", "join", "mergeProps", "type", "React.Fragment", "cloneElement", "SLOTTABLE_IDENTIFIER", "Symbol", "createSlottable", "Slottable2", "Fragment2", "__radixId", "Primitive", "primitive", "Node", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "window", "for", "dispatchDiscreteCustomEvent", "target", "ReactDOM.flushSync", "dispatchEvent", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "React.useRef", "useRef", "React.useEffect", "originalBodyPointerEvents", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "DismissableLayerContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "setNode", "React.useState", "ownerDocument", "globalThis", "document", "force", "useState", "composedRefs", "node2", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "handlePointerDownOutside", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "handleAndDispatchPointerDownOutsideEvent2", "handleAndDispatchCustomEvent", "eventDetail", "discrete", "originalEvent", "pointerType", "removeEventListener", "addEventListener", "once", "timerId", "setTimeout", "clearTimeout", "onPointerDownCapture", "usePointerDownOutside", "isPointerDownOnBranch", "some", "branch", "contains", "focusOutside", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "onFocusCapture", "onBlurCapture", "useFocusOutside", "onEscapeKeyDownProp", "handleKeyDown", "key", "capture", "useEscapeKeydown", "preventDefault", "body", "style", "pointerEvents", "add", "dispatchUpdate", "delete", "handleUpdate", "jsxRuntimeExports", "div", "DismissableLayerBranch", "CustomEvent", "name", "handler", "detail", "bubbles", "cancelable", "Root", "Branch", "useLayoutEffect2", "React.useLayoutEffect", "Portal", "container", "containerProp", "portalProps", "mounted", "setMounted", "useLayoutEffect", "ReactDOM", "createPortal", "Presence", "present", "presence", "React2.useState", "stylesRef", "React2.useRef", "prevPresentRef", "prevAnimationNameRef", "initialState", "state", "send", "machine", "React.useReducer", "useReducer", "useStateMachine", "UNMOUNT", "ANIMATION_OUT", "unmountSuspended", "MOUNT", "ANIMATION_END", "unmounted", "React2.useEffect", "currentAnimationName", "getAnimationName", "styles", "wasPresent", "prevAnimationName", "display", "timeoutId", "ownerWindow", "defaultView", "handleAnimationEnd", "isCurrentAnimation", "includes", "animationName", "currentFillMode", "animationFillMode", "handleAnimationStart", "isPresent", "React2.useCallback", "getComputedStyle", "usePresence", "React2.Children", "React2.cloneElement", "useInsertionEffect", "React", "trim", "toString", "useControllableState", "prop", "defaultProp", "onChange", "caller", "uncontrolledProp", "setUncontrolledProp", "onChangeRef", "setValue", "prevValueRef", "call", "useUncontrolledState", "isControlled", "isControlledRef", "wasControlled", "to", "console", "warn", "nextValue", "value2", "isFunction", "VISUALLY_HIDDEN_STYLES", "freeze", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "VisuallyHidden", "span", "r", "e", "t", "f", "n", "isArray", "o", "clsx", "arguments", "useReactId", "useId", "deterministicId", "id", "setId", "reactId", "String", "sides", "min", "Math", "max", "round", "floor", "createCoords", "v", "x", "y", "oppositeSideMap", "left", "right", "bottom", "top", "oppositeAlignmentMap", "start", "end", "clamp", "evaluate", "param", "getSide", "placement", "split", "getAlignment", "getOppositeAxis", "axis", "getAxisLength", "getSideAxis", "getAlignmentAxis", "getOppositeAlignmentPlacement", "replace", "alignment", "getOppositePlacement", "side", "getPaddingObject", "expandPaddingObject", "rectToClientRect", "rect", "computeCoordsFromPlacement", "_ref", "rtl", "reference", "floating", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "isVertical", "commonX", "commonY", "commonAlign", "coords", "async", "detectOverflow", "options", "_await$platform$isEle", "platform", "rects", "elements", "strategy", "boundary", "rootBoundary", "elementContext", "altBoundary", "paddingObject", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "getSideOffsets", "isAnySideFullyClipped", "hasW<PERSON>ow", "getNodeName", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "documentElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "overflowX", "overflowY", "isTableElement", "isTop<PERSON><PERSON>er", "selector", "matches", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "containerType", "<PERSON><PERSON>ilter", "<PERSON><PERSON><PERSON><PERSON>", "contain", "CSS", "supports", "isLastTraversableNode", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "getParentNode", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "concat", "visualViewport", "parent", "getPrototypeOf", "getCssDimensions", "parseFloat", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "dom<PERSON>lement", "getBoundingClientRect", "Number", "isFinite", "noOffsets", "getVisualOffsets", "offsetLeft", "offsetTop", "includeScale", "isFixedStrategy", "clientRect", "scale", "visualOffsets", "isFixed", "floatingOffsetParent", "shouldAddVisualOffsets", "offsetWin", "currentWin", "currentIFrame", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "getWindowScrollBarX", "leftScroll", "getHTMLOffset", "scroll", "ignoreScrollbarX", "htmlRect", "getClientRectFromClippingAncestor", "clippingAncestor", "html", "clientWidth", "clientHeight", "visualViewportBased", "getViewportRect", "scrollWidth", "scrollHeight", "direction", "getDocumentRect", "getInnerBoundingClientRect", "hasFixedPositionAncestor", "stopNode", "getRectRelativeToOffsetParent", "isOffsetParentAnElement", "offsets", "setLeftRTLScrollbarOffset", "offsetRect", "htmlOffset", "isStaticPositioned", "getTrueOffsetParent", "polyfill", "rawOffsetParent", "svgOffsetParent", "currentNode", "getContainingBlock", "topLayer", "clippingAncestors", "cache", "cachedResult", "el", "currentContainingBlockComputedStyle", "elementIsFixed", "computedStyle", "currentNodeIsContaining", "ancestor", "set", "getClippingElementAncestors", "this", "_c", "firstClippingAncestor", "clippingRect", "accRect", "getElementRects", "data", "getOffsetParentFn", "getDimensionsFn", "getDimensions", "floatingDimensions", "getClientRects", "isRTL", "rectsAreEqual", "a", "b", "autoUpdate", "update", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "IntersectionObserver", "animationFrame", "referenceEl", "ancestors", "for<PERSON>ach", "passive", "cleanupIo", "onMove", "io", "root", "_io", "disconnect", "refresh", "skip", "threshold", "elementRectForRootMargin", "rootMargin", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "_e", "observe", "<PERSON><PERSON><PERSON>", "frameId", "reobserveFrame", "resizeObserver", "firstEntry", "unobserve", "cancelAnimationFrame", "requestAnimationFrame", "_resizeObserver", "prevRefRect", "frameLoop", "nextRefRect", "_resizeObserver2", "offset", "fn", "_middlewareData$offse", "_middlewareData$arrow", "middlewareData", "diffCoords", "mainAxisMulti", "crossAxisMulti", "rawValue", "mainAxis", "crossAxis", "convertValueToCoords", "arrow", "alignmentOffset", "shift", "checkMainAxis", "checkCrossAxis", "limiter", "detectOverflowOptions", "mainAxisCoord", "crossAxisCoord", "maxSide", "limitedCoords", "enabled", "flip", "_middlewareData$flip", "initialPlacement", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "flipAlignment", "initialSideAxis", "isBasePlacement", "oppositePlacement", "getExpandedPlacements", "hasFallbackAxisSideDirection", "push", "isStart", "lr", "rl", "tb", "bt", "getSideList", "getOppositeAxisPlacements", "placements", "overflows", "overflowsData", "mainAlignmentSide", "getAlignmentSides", "every", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "nextPlacement", "_overflowsData$", "ignoreCrossAxisOverflow", "hasInitialMainAxisOverflow", "reset", "resetPlacement", "d", "sort", "_overflowsData$filter2", "currentSideAxis", "acc", "_state$middlewareData", "_state$middlewareData2", "apply", "isYAxis", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions", "hide", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "arrowDimensions", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "shouldAddOffset", "centerOffset", "limitShift", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "computePosition", "Map", "mergedOptions", "platformWithCache", "config", "middleware", "validMiddleware", "statefulPlacement", "resetCount", "nextX", "nextY", "computePosition$1", "useEffect", "deepEqual", "keys", "hasOwnProperty", "$$typeof", "getDPR", "devicePixelRatio", "roundByDPR", "dpr", "useLatestRef", "arrow$1", "arrow$2", "deps", "shift$1", "limitShift$1", "flip$1", "size$1", "hide$1", "Arrow", "arrowProps", "svg", "viewBox", "preserveAspectRatio", "points", "POPPER_NAME", "createPopperContext", "createPopperScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "ANCHOR_NAME", "PopperA<PERSON><PERSON>", "__scope<PERSON>opper", "virtualRef", "anchorProps", "onAnchorChange", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sideOffset", "align", "alignOffset", "arrowPadding", "avoidCollisions", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "updatePositionStrategy", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "setArrow", "arrowSize", "setSize", "entry", "borderSizeEntry", "borderSize", "box", "useSize", "arrow<PERSON>idth", "arrowHeight", "desiredPlacement", "hasExplicitBoundaries", "isNotNull", "floatingStyles", "isPositioned", "externalReference", "externalFloating", "transform", "whileElementsMounted", "open", "setData", "latestMiddleware", "setLatestMiddleware", "_reference", "_setReference", "_floating", "_setFloating", "setReference", "referenceRef", "setFloating", "floatingRef", "floatingEl", "dataRef", "hasWhileElementsMounted", "whileElementsMountedRef", "platformRef", "openRef", "then", "fullData", "isMountedRef", "initialStyles", "useFloating", "anchor", "offset$1", "anchorWidth", "anchorHeight", "contentStyle", "setProperty", "floatingUIarrow", "transform<PERSON><PERSON>in", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "arrowX", "arrowY", "cannotCenterArrow", "contentZIndex", "setContentZIndex", "zIndex", "min<PERSON><PERSON><PERSON>", "_d", "_f", "visibility", "dir", "onArrowChange", "shouldHideArrow", "animation", "ARROW_NAME", "OPPOSITE_SIDE", "PopperArrow", "contentContext", "baseSide", "ArrowPrimitive.Root", "isArrowHidden", "noArrowAlign", "arrowXCenter", "arrowYCenter", "<PERSON><PERSON>", "Content", "createTooltipContext", "createTooltipScope", "usePopperScope", "PROVIDER_NAME", "DEFAULT_DELAY_DURATION", "TOOLTIP_OPEN", "TooltipProviderContextProvider", "useTooltipProviderContext", "TooltipProvider", "__scopeTooltip", "delayDuration", "skipDelayDuration", "disableHover<PERSON><PERSON><PERSON>nt", "isOpenDelayedRef", "isPointerInTransitRef", "skip<PERSON>elayTimerRef", "skip<PERSON><PERSON><PERSON>T<PERSON>r", "onOpen", "onClose", "onPointerInTransitChange", "inTransit", "TOOLTIP_NAME", "TooltipContextProvider", "useTooltipContext", "TRIGGER_NAME", "triggerProps", "providerContext", "popperScope", "onTriggerChange", "isPointerDownRef", "hasPointerMoveOpenedRef", "handlePointerUp", "PopperPrimitive.Anchor", "button", "contentId", "stateAttribute", "onPointerMove", "onTriggerEnter", "onPointerLeave", "onTriggerLeave", "onPointerDown", "onFocus", "onBlur", "onClick", "PortalProvider", "usePortalContext", "forceMount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "portalContext", "TooltipContentImpl", "TooltipContentHoverable", "pointerGraceArea", "setPointerGraceArea", "trigger", "handleRemoveGraceArea", "handleCreateGraceArea", "hoverTarget", "currentTarget", "exitPoint", "clientX", "clientY", "paddedExitPoints", "exitSide", "getPaddedExitPoints", "point", "abs", "getExitSideFromRect", "grace<PERSON><PERSON>", "newPoints", "upperHull", "p", "q", "pop", "lowerHull", "getHullPresorted", "getHull", "getPointsFromRect", "handleTriggerLeave", "handleContentLeave", "handleTrackPointerGrace", "pointerPosition", "hasEnteredTarget", "isPointerOutsideGraceArea", "polygon", "inside", "j", "ii", "jj", "xi", "yi", "xj", "yj", "isPointInPolygon", "VisuallyHiddenContentContextProvider", "useVisuallyHiddenContentContext", "isInside", "Slottable", "aria<PERSON><PERSON><PERSON>", "handleScroll", "jsxs", "PopperPrimitive.Content", "VisuallyHiddenPrimitive.Root", "role", "PopperPrimitive.Arrow", "Content2"], "mappings": "qGACA,SAASA,EAAqBC,EAAsBC,GAAiBC,yBAAEA,GAA2B,GAAS,IAClG,OAAA,SAAqBC,GAE1B,GADuB,MAAAH,GAAAA,EAAAG,IACU,IAA7BD,IAAuCC,EAAMC,iBAC/C,OAAyB,MAAlBH,OAAkB,EAAAA,EAAAE,EAE5B,CACH,CCNA,SAASE,EAAOC,EAAKC,GACf,GAAe,mBAARD,EACT,OAAOA,EAAIC,GACFD,UACTA,EAAIE,QAAUD,EAElB,CACA,SAASE,KAAeC,GACtB,OAAQC,IACN,IAAIC,GAAa,EACjB,MAAMC,EAAWH,EAAKI,KAAKR,IACnB,MAAAS,EAAUV,EAAOC,EAAKK,GAIrB,OAHFC,GAAgC,mBAAXG,IACXH,GAAA,GAERG,CAAA,IAET,GAAIH,EACF,MAAO,KACL,IAAA,IAASI,EAAI,EAAGA,EAAIH,EAASI,OAAQD,IAAK,CAClC,MAAAD,EAAUF,EAASG,GACH,mBAAXD,EACAA,IAEFV,EAAAK,EAAKM,GAAI,KAE5B,EAEA,CAEA,CACA,SAASE,KAAmBR,GAC1B,OAAOS,EAAiBC,YAACX,KAAeC,GAAOA,EACjD,CChBA,SAASW,EAAmBC,EAAWC,EAAyB,IAC9D,IAAIC,EAAkB,GAqBtB,MAAMC,EAAc,KAClB,MAAMC,EAAgBF,EAAgBV,KAAKa,GAClCC,EAAAA,cAAoBD,KAEtB,OAAA,SAAkBE,GACjB,MAAAC,SAAWD,WAAQP,KAAcI,EACvC,OAAOK,EAAaC,SAClB,KAAO,CAAE,CAAC,UAAUV,KAAc,IAAKO,EAAOP,CAACA,GAAYQ,MAC3D,CAACD,EAAOC,GAEX,CAAA,EAGH,OADAL,EAAYH,UAAYA,EACjB,CAjCE,SAAeW,EAAmBN,GACnC,MAAAO,EAAcN,EAAmBO,cAACR,GAClCS,EAAQZ,EAAgBP,OACZO,EAAA,IAAIA,EAAiBG,GACjCU,MAAAA,EAAYC,UAChB,MAAMT,MAAEA,EAAAU,SAAOA,KAAaC,GAAYF,EAClCG,GAAU,OAAAC,EAAA,MAAAb,OAAA,EAAAA,EAAQP,SAAR,EAAAoB,EAAqBN,KAAUF,EACzC3B,EAAQwB,EAAAA,SAAc,IAAMS,GAASG,OAAOC,OAAOJ,IACzD,SAAuBK,IAAIJ,EAAQJ,SAAU,CAAE9B,QAAOgC,YAAU,EAU3D,OARPF,EAASS,YAAcb,EAAoB,WAQpC,CAACI,EAPC,SAAYU,EAAclB,SACjC,MAAMY,GAAU,OAAAC,EAAA,MAAAb,OAAA,EAAAA,EAAQP,SAAR,EAAAoB,EAAqBN,KAAUF,EACzCM,EAAUQ,EAAgBC,WAACR,GACjC,GAAID,EAAgB,OAAAA,EAChB,QAAmB,IAAnBb,EAAkC,OAAAA,EACtC,MAAM,IAAIuB,MAAM,KAAKH,6BAAwCd,MACnE,EAEA,EAc0BkB,EAAqB1B,KAAgBF,GAC/D,CACA,SAAS4B,KAAwBC,GACzB,MAAAC,EAAYD,EAAO,GACrB,GAAkB,IAAlBA,EAAOnC,OAAqB,OAAAoC,EAChC,MAAM5B,EAAc,KAClB,MAAM6B,EAAaF,EAAOtC,KAAKyC,IAAkB,CAC/CC,SAAUD,IACVjC,UAAWiC,EAAajC,cAEnB,OAAA,SAA2BmC,GAC1B,MAAAC,EAAaJ,EAAWK,QAAO,CAACC,GAAeJ,WAAUlC,gBAGtD,IAAKsC,KAFOJ,EAASC,GACI,UAAUnC,QAEzC,IACH,OAAOS,WAAc,KAAO,CAAE,CAAC,UAAUsB,EAAU/B,aAAcoC,KAAe,CAACA,GAClF,CAAA,EAGI,OADPjC,EAAYH,UAAY+B,EAAU/B,UAC3BG,CACT,CCtEA,SAASoC,EAAWC,GACZ,MAAAC,IAA4CD,GAC5CE,EAAQC,EAAAA,YAAiB,CAAC3B,EAAO4B,KACrC,MAAM3B,SAAEA,KAAa4B,GAAc7B,EAC7B8B,EAAgBC,EAAAA,SAAeC,QAAQ/B,GACvCgC,EAAYH,EAAcI,KAAKC,GACrC,GAAIF,EAAW,CACP,MAAAG,EAAaH,EAAUjC,MAAMC,SAC7BoC,EAAcP,EAActD,KAAK8D,GACjCA,IAAUL,EACRF,EAAcQ,SAACC,MAAMJ,GAAc,EAAUL,EAAcQ,SAACE,KAAK,MAC9DC,EAAAA,eAAqBN,GAAcA,EAAWpC,MAAMC,SAAW,KAE/DqC,IAGY/B,OAAAA,EAAAA,IAAIkB,EAAW,IAAKI,EAAW7D,IAAK4D,EAAc3B,SAAUyC,EAAoBC,eAACP,GAAcQ,EAAAA,aAAmBR,OAAY,EAAQC,GAAe,MAClL,CAC2B9B,OAAAA,EAAAA,IAAIkB,EAAW,IAAKI,EAAW7D,IAAK4D,EAAc3B,YAAU,IAG9E,OADDyB,EAAAlB,YAAc,GAAGgB,SAChBE,CACT,CACG,IAACmB,IAAkC,QAEtC,SAASC,EAAgBtB,GACvB,MAAMC,EAAYE,EAAAA,YAAiB,CAAC3B,EAAO4B,KACzC,MAAM3B,SAAEA,KAAa4B,GAAc7B,EAC/B0C,GAAAA,EAAAA,eAAqBzC,GAAW,CAC5B,MAAA8C,EAkDZ,SAAuBC,WACrB,IAAIC,EAAS,OAAA7C,EAAOC,OAAA6C,yBAAyBF,EAAQhD,MAAO,aAAQ,EAAAI,EAAA+C,IAChEC,EAAUH,GAAU,mBAAoBA,GAAUA,EAAOI,eAC7D,GAAID,EACF,OAAOJ,EAAQhF,IAIjB,GAFAiF,EAAS,OAAAK,EAAOjD,OAAA6C,yBAAyBF,EAAS,aAAQ,EAAAM,EAAAH,IAChDC,EAAAH,GAAU,mBAAoBA,GAAUA,EAAOI,eACrDD,EACF,OAAOJ,EAAQhD,MAAMhC,IAEhB,OAAAgF,EAAQhD,MAAMhC,KAAOgF,EAAQhF,GACtC,CA9D0BuF,CAActD,GAC5BuD,EAyBZ,SAAoB3B,EAAW4B,GACvB,MAAAC,EAAgB,IAAKD,GAC3B,IAAA,MAAWE,KAAYF,EAAY,CAC3B,MAAAG,EAAgB/B,EAAU8B,GAC1BE,EAAiBJ,EAAWE,GAChB,WAAWG,KAAKH,GAE5BC,GAAiBC,EACLH,EAAAC,GAAY,IAAII,KACtB,MAAAC,EAASH,KAAkBE,GAE1B,OADPH,KAAiBG,GACVC,CAAA,EAEAJ,IACTF,EAAcC,GAAYC,GAEN,UAAbD,EACTD,EAAcC,GAAY,IAAKC,KAAkBC,GAC3B,cAAbF,IACKD,EAAAC,GAAY,CAACC,EAAeC,GAAgBI,OAAOC,SAASC,KAAK,KAErF,CACE,MAAO,IAAKtC,KAAc6B,EAC5B,CAhDqBU,CAAWvC,EAAW5B,EAASD,OAIvC4C,OAHH3C,EAASoE,OAASC,aACpBd,EAAOxF,IAAM4D,EAAezD,EAAYyD,EAAcmB,GAAeA,GAEhEH,EAAkB2B,aAACtE,EAAUuD,EAC1C,CACWzB,OAAAA,EAAcQ,SAACC,MAAMvC,GAAY,EAAI8B,WAAeU,KAAK,MAAQ,IAAA,IAGnE,OADGhB,EAAAjB,YAAc,GAAGgB,cACpBC,CACT,CACA,IAAI+C,EAAuBC,OAAO,mBAElC,SAASC,EAAgBlD,GACvB,MAAMmD,EAAa,EAAG1E,oBACO2E,EAAAA,SAAW,CAAE3E,aAInC,OAFI0E,EAAAnE,YAAc,GAAGgB,cAC5BmD,EAAWE,UAAYL,EAChBG,CACT,CAEA,SAASxC,EAAYG,GACnB,OAAOI,EAAoBC,eAACL,IAAgC,mBAAfA,EAAM+B,MAAuB,cAAe/B,EAAM+B,MAAQ/B,EAAM+B,KAAKQ,YAAcL,CAClI,CCtDA,IAmBIM,EAnBQ,CACV,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,MAEoBzD,QAAO,CAAC0D,EAAW1G,KACvC,MAAMwE,EAAOtB,EAAW,aAAalD,KAC/B2G,EAAOrD,EAAAA,YAAiB,CAAC3B,EAAO4B,KACpC,MAAMqD,QAAEA,KAAYC,GAAmBlF,EACjCmF,EAAOF,EAAUpC,EAAOxE,EAIPkC,MAHD,oBAAX6E,SACTA,OAAOX,OAAOY,IAAI,cAAe,GAEZ9E,EAAAA,IAAI4E,EAAM,IAAKD,EAAgBlH,IAAK4D,GAAc,IAG3E,OADAoD,EAAKxE,YAAc,aAAanC,IACzB,IAAK0G,EAAW1G,CAACA,GAAO2G,EAAM,GACpC,CAAE,GACL,SAASM,EAA4BC,EAAQ1H,GACvC0H,GAAQC,EAAAA,WAAmB,IAAMD,EAAOE,cAAc5H,IAC5D,CCrCA,SAAS6H,EAAeC,GAChB,MAAAC,EAAcC,EAAYC,OAACH,GAIjC,OAHAI,EAAAA,WAAgB,KACdH,EAAY1H,QAAUyH,CAAA,IAEjBlG,EAAaC,SAAC,IAAM,IAAIqE,WAAqB,OAAZ,OAAY3D,EAAAwF,EAAA1H,4BAAa6F,EAAA,GAAO,GAC1E,CCEA,IAIIiC,EAHAC,EAAiB,0BACjBC,EAAuB,sCACvBC,EAAgB,gCAEhBC,EAA0B9G,EAAAA,cAAoB,CAChD+G,WAA4BC,IAC5BC,2CAA4DD,IAC5DE,aAA8BF,MAE5BG,EAAmB9E,EAAM+E,YAC3B,CAAC1G,EAAO4B,KACA,MAAA+E,4BACJA,GAA8B,EAAAC,gBAC9BA,EAAAC,qBACAA,EAAAC,eACAA,EAAAC,kBACAA,EAAAC,UACAA,KACGC,GACDjH,EACEE,EAAUQ,EAAMC,WAAWyF,IAC1B/H,EAAM6I,GAAWC,EAAAA,SAAe,MACjCC,GAAsB,MAAN/I,OAAM,EAAAA,EAAA+I,iBAA6B,MAAZC,gBAAY,EAAAA,WAAAC,YAChDC,GAASJ,EAAMK,SAAS,IAC3BC,EAAe7I,EAAgBgD,GAAe8F,GAAUR,EAAQQ,KAChErB,EAASsB,MAAMC,KAAK1H,EAAQmG,SAC3BwB,GAAgD,IAAI3H,EAAQqG,wCAAwCuB,OAAQ,GAC7GC,EAAoD1B,EAAO2B,QAAQH,GACnE/H,EAAQzB,EAAOgI,EAAO2B,QAAQ3J,IAAQ,EACtC4J,EAA8B/H,EAAQqG,uCAAuC2B,KAAO,EACpFC,EAAyBrI,GAASiI,EAClCK,EA4FV,SAA+BvB,EAAsBO,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WACzE,MAAAe,EAA2B3C,EAAemB,GAC1CyB,EAA8BzC,EAAMC,QAAO,GAC3CyC,EAAiB1C,EAAAA,QAAa,SAoC7B,OAlCPE,EAAAA,WAAgB,KACR,MAAAyC,EAAqB3K,IACzB,GAAIA,EAAM0H,SAAW+C,EAA4BpK,QAAS,CACxD,IAAIuK,EAA4C,WAC9CC,EACExC,EACAmC,EACAM,EACA,CAAEC,UAAU,GAEhB,EAEM,MAAAD,EAAc,CAAEE,cAAehL,GACX,UAAtBA,EAAMiL,aACM1B,EAAA2B,oBAAoB,QAASR,EAAerK,SAC1DqK,EAAerK,QAAUuK,EACzBrB,EAAc4B,iBAAiB,QAAST,EAAerK,QAAS,CAAE+K,MAAM,KAE9BR,GAC5C,MAEcrB,EAAA2B,oBAAoB,QAASR,EAAerK,SAE5DoK,EAA4BpK,SAAU,CAAA,EAElCgL,EAAU9D,OAAO+D,YAAW,KAClB/B,EAAA4B,iBAAiB,cAAeR,EAAiB,GAC9D,GACH,MAAO,KACLpD,OAAOgE,aAAaF,GACN9B,EAAA2B,oBAAoB,cAAeP,GACnCpB,EAAA2B,oBAAoB,QAASR,EAAerK,QAAO,CACnE,GACC,CAACkJ,EAAeiB,IACZ,CAELgB,qBAAsB,IAAMf,EAA4BpK,SAAU,EAEtE,CAvI+BoL,EAAuBzL,IAChD,MAAM0H,EAAS1H,EAAM0H,OACfgE,EAAwB,IAAIrJ,EAAQsG,UAAUgD,MAAMC,GAAWA,EAAOC,SAASnE,KAChF4C,IAA0BoB,IACR,MAAA1C,GAAAA,EAAAhJ,GACH,MAAAkJ,GAAAA,EAAAlJ,GACfA,EAAMC,kBAA8B,MAAAkJ,GAAAA,IAAA,GACxCI,GACGuC,EAgIV,SAAyB7C,EAAgBM,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WAC7D,MAAAsC,EAAqBlE,EAAeoB,GACpC+C,EAA4BhE,EAAMC,QAAO,GAaxC,OAZPC,EAAAA,WAAgB,KACR,MAAA+D,EAAejM,IACnB,GAAIA,EAAM0H,SAAWsE,EAA0B3L,QAAS,CAEzBwK,EAAAvC,EAAeyD,EADxB,CAAEf,cAAehL,GACwC,CAC3E+K,UAAU,GACX,GAIL,OADcxB,EAAA4B,iBAAiB,UAAWc,GACnC,IAAM1C,EAAc2B,oBAAoB,UAAWe,EAAW,GACpE,CAAC1C,EAAewC,IACZ,CACLG,eAAgB,IAAMF,EAA0B3L,SAAU,EAC1D8L,cAAe,IAAMH,EAA0B3L,SAAU,EAE7D,CAnJyB+L,EAAiBpM,IACpC,MAAM0H,EAAS1H,EAAM0H,OACG,IAAIrF,EAAQsG,UAAUgD,MAAMC,GAAWA,EAAOC,SAASnE,OAE9D,MAAAuB,GAAAA,EAAAjJ,GACG,MAAAkJ,GAAAA,EAAAlJ,GACfA,EAAMC,kBAA8B,MAAAkJ,GAAAA,IAAA,GACxCI,GAwCoB,OC9F3B,SAA0B8C,EAAqB9C,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WACnE,MAAAV,EAAkBlB,EAAewE,GACvCnE,EAAAA,WAAgB,KACR,MAAAoE,EAAiBtM,IACH,WAAdA,EAAMuM,KACRxD,EAAgB/I,EAAK,EAIlB,OADPuJ,EAAc4B,iBAAiB,UAAWmB,EAAe,CAAEE,SAAS,IAC7D,IAAMjD,EAAc2B,oBAAoB,UAAWoB,EAAe,CAAEE,SAAS,GAAM,GACzF,CAACzD,EAAiBQ,GACvB,CD4CIkD,EAAkBzM,IACOiC,IAAUI,EAAQmG,OAAO6B,KAAO,IAErC,MAAAtB,GAAAA,EAAA/I,IACbA,EAAMC,kBAAoBkJ,IAC7BnJ,EAAM0M,iBACIvD,KAAA,GAEXI,GACHrB,EAAAA,WAAgB,KACd,GAAK1H,EAUL,OATIsI,IAC0D,IAAxDzG,EAAQqG,uCAAuC2B,OACrBlC,EAAAoB,EAAcoD,KAAKC,MAAMC,cACvCtD,EAAAoD,KAAKC,MAAMC,cAAgB,QAEnCxK,EAAAqG,uCAAuCoE,IAAItM,IAE7C6B,EAAAmG,OAAOsE,IAAItM,GACJuM,IACR,KACDjE,GAAuF,IAAxDzG,EAAQqG,uCAAuC2B,OAClEd,EAAAoD,KAAKC,MAAMC,cAAgB1E,EAAA,CAE7C,GACC,CAAC3H,EAAM+I,EAAeT,EAA6BzG,IACtD6F,EAAAA,WAAgB,IACP,KACA1H,IACG6B,EAAAmG,OAAOwE,OAAOxM,GACd6B,EAAAqG,uCAAuCsE,OAAOxM,GACvCuM,IAAA,GAEhB,CAACvM,EAAM6B,IACV6F,EAAAA,WAAgB,KACd,MAAM+E,EAAe,IAAMvD,EAAM,IAEjC,OADSD,SAAA0B,iBAAiB/C,EAAgB6E,GACnC,IAAMxD,SAASyB,oBAAoB9C,EAAgB6E,EAAY,GACrE,IACoBC,EAAAxK,IACrBuE,EAAUkG,IACV,IACK/D,EACHjJ,IAAKyJ,EACLgD,MAAO,CACLC,cAAezC,EAA8BE,EAAyB,OAAS,YAAS,KACrFnI,EAAMyK,OAEXV,eAAgBtM,EAAqBuC,EAAM+J,eAAgBJ,EAAaI,gBACxEC,cAAevM,EAAqBuC,EAAMgK,cAAeL,EAAaK,eACtEX,qBAAsB5L,EACpBuC,EAAMqJ,qBACNjB,EAAmBiB,uBAGzB,IAGJ5C,EAAiBjG,YA1GY,mBA2G7B,IACIyK,EAAyBtJ,EAAiB+E,YAAA,CAAC1G,EAAO4B,KAC9C,MAAA1B,EAAUQ,EAAMC,WAAWyF,GAC3BpI,EAAM6H,EAAMC,OAAO,MACnB2B,EAAe7I,EAAgBgD,EAAc5D,GAU5BuC,OATvBwF,EAAAA,WAAgB,KACd,MAAM1H,EAAOL,EAAIE,QACjB,GAAIG,EAEF,OADQ6B,EAAAsG,SAASmE,IAAItM,GACd,KACG6B,EAAAsG,SAASqE,OAAOxM,EAAI,CAC9B,GAED,CAAC6B,EAAQsG,WACWjG,EAAAA,IAAIuE,EAAUkG,IAAK,IAAKhL,EAAOhC,IAAKyJ,GAAc,IAmE3E,SAASmD,IACD,MAAA/M,EAAQ,IAAIqN,YAAYjF,GAC9BqB,SAAS7B,cAAc5H,EACzB,CACA,SAAS6K,EAA6ByC,EAAMC,EAASC,GAAQzC,SAAEA,IACvD,MAAArD,EAAS8F,EAAOxC,cAActD,OAC9B1H,EAAQ,IAAIqN,YAAYC,EAAM,CAAEG,SAAS,EAAOC,YAAY,EAAMF,WACpED,KAAgBpC,iBAAiBmC,EAAMC,EAAS,CAAEnC,MAAM,IACxDL,EACFtD,EAA4BC,EAAQ1H,GAEpC0H,EAAOE,cAAc5H,EAEzB,CA9EAoN,EAAuBzK,YAhBL,yBA+FlB,IAAIgL,EAAO/E,EACPgF,EAASR,EEnNTS,GAAmB,MAAArE,gBAAA,EAAAA,WAAYC,UAAWqE,EAAAA,gBAAwB,OCOlEC,EAASjK,EAAiB+E,YAAA,CAAC1G,EAAO4B,WACpC,MAAQiK,UAAWC,KAAkBC,GAAgB/L,GAC9CgM,EAASC,GAAc9E,EAAAA,UAAe,GAC7C+E,GAAgB,IAAMD,GAAW,IAAO,IACxC,MAAMJ,EAAYC,GAAiBE,IAAW,OAAA5L,EAAA,MAAAiH,gBAAA,EAAAA,WAAYC,eAAU,EAAAlH,EAAAoK,MACpE,OAAOqB,EAAYM,EAASC,aAA6B7L,EAAAA,IAAIuE,EAAUkG,IAAK,IAAKe,EAAa/N,IAAK4D,IAAiBiK,GAAa,IAAA,IAEnID,EAAOpL,YARW,SCSf,IAAC6L,EAAYrM,IACR,MAAAsM,QAAEA,EAASrM,SAAAA,GAAaD,EACxBuM,EAOR,SAAqBD,GACnB,MAAOjO,EAAM6I,GAAWsF,aAClBC,EAAYC,EAAa5G,OAAC,MAC1B6G,EAAiBD,EAAa5G,OAACwG,GAC/BM,EAAuBF,EAAa5G,OAAC,QACrC+G,EAAeP,EAAU,UAAY,aACpCQ,EAAOC,GAvBhB,SAAyBF,EAAcG,GACrC,OAAOC,EAAgBC,YAAC,CAACJ,EAAOjP,IACZmP,EAAQF,GAAOjP,IACbiP,GACnBD,EACL,CAkBwBM,CAAgBN,EAAc,CAClDb,QAAS,CACPoB,QAAS,YACTC,cAAe,oBAEjBC,iBAAkB,CAChBC,MAAO,UACPC,cAAe,aAEjBC,UAAW,CACTF,MAAO,aAmEJ,OAhEPG,EAAAA,WAAiB,KACT,MAAAC,EAAuBC,EAAiBnB,EAAUvO,SACnC0O,EAAA1O,QAAoB,YAAV4O,EAAsBa,EAAuB,MAAA,GAC3E,CAACb,IACJZ,GAAgB,KACd,MAAM2B,EAASpB,EAAUvO,QACnB4P,EAAanB,EAAezO,QAElC,GAD0B4P,IAAexB,EAClB,CACrB,MAAMyB,EAAoBnB,EAAqB1O,QACzCyP,EAAuBC,EAAiBC,GAC9C,GAAIvB,EACFS,EAAK,cACI,GAAyB,SAAzBY,GAAuD,UAApB,MAAAE,OAAA,EAAAA,EAAQG,SACpDjB,EAAK,eACA,CAGHA,EADEe,GADgBC,IAAsBJ,EAEnC,gBAEA,UAEf,CACMhB,EAAezO,QAAUoO,CAC/B,IACK,CAACA,EAASS,IACbb,GAAgB,KACd,GAAI7N,EAAM,CACJ,IAAA4P,EACE,MAAAC,EAAc7P,EAAK+I,cAAc+G,aAAe/I,OAChDgJ,EAAsBvQ,IACpB,MACAwQ,EADuBT,EAAiBnB,EAAUvO,SACRoQ,SAASzQ,EAAM0Q,eAC3D,GAAA1Q,EAAM0H,SAAWlH,GAAQgQ,IAC3BtB,EAAK,kBACAJ,EAAezO,SAAS,CACrB,MAAAsQ,EAAkBnQ,EAAKoM,MAAMgE,kBACnCpQ,EAAKoM,MAAMgE,kBAAoB,WACnBR,EAAAC,EAAY/E,YAAW,KACI,aAAjC9K,EAAKoM,MAAMgE,oBACbpQ,EAAKoM,MAAMgE,kBAAoBD,EAC/C,GAEA,CACA,EAEYE,EAAwB7Q,IACxBA,EAAM0H,SAAWlH,IACEuO,EAAA1O,QAAU0P,EAAiBnB,EAAUvO,SACpE,EAKM,OAHKG,EAAA2K,iBAAiB,iBAAkB0F,GACnCrQ,EAAA2K,iBAAiB,kBAAmBoF,GACpC/P,EAAA2K,iBAAiB,eAAgBoF,GAC/B,KACLF,EAAY9E,aAAa6E,GACpB5P,EAAA0K,oBAAoB,iBAAkB2F,GACtCrQ,EAAA0K,oBAAoB,kBAAmBqF,GACvC/P,EAAA0K,oBAAoB,eAAgBqF,EAAkB,CAEnE,CACMrB,EAAK,gBACX,GACK,CAAC1O,EAAM0O,IACH,CACL4B,UAAW,CAAC,UAAW,oBAAoBL,SAASxB,GACpD9O,IAAK4Q,EAAAA,aAAoBlH,IACvB+E,EAAUvO,QAAUwJ,EAAQmH,iBAAiBnH,GAAS,KACtDR,EAAQQ,EAAK,GACZ,IAEP,CAjGmBoH,CAAYxC,GACvBhK,EAA4B,mBAAbrC,EAA0BA,EAAS,CAAEqM,QAASC,EAASoC,YAAeI,WAAgBtM,KAAKxC,GAC1GjC,EAAMY,EAAgB2N,EAASvO,IAmGvC,SAAuBgF,WACrB,IAAIC,EAAS,OAAA7C,EAAOC,OAAA6C,yBAAyBF,EAAQhD,MAAO,aAAQ,EAAAI,EAAA+C,IAChEC,EAAUH,GAAU,mBAAoBA,GAAUA,EAAOI,eAC7D,GAAID,EACF,OAAOJ,EAAQhF,IAIjB,GAFAiF,EAAS,OAAAK,EAAOjD,OAAA6C,yBAAyBF,EAAS,aAAQ,EAAAM,EAAAH,IAChDC,EAAAH,GAAU,mBAAoBA,GAAUA,EAAOI,eACrDD,EACF,OAAOJ,EAAQhD,MAAMhC,IAEhB,OAAAgF,EAAQhD,MAAMhC,KAAOgF,EAAQhF,GACtC,CA/G4CuF,CAAcjB,IAEjD,MADgC,mBAAbrC,GACLsM,EAASoC,UAAYK,EAAAA,aAAoB1M,EAAO,CAAEtE,QAAS,IAAA,EA8FlF,SAAS4P,EAAiBC,GACxB,aAAOA,WAAQU,gBAAiB,MAClC,CA9FAlC,EAAS7L,YAAc,WCtBvB,IAAIyO,EAAqBC,EAAM,uBAAuBC,OAAOC,aAAelD,EAC5E,SAASmD,GAAqBC,KAC5BA,EAAAC,YACAA,EAAAC,SACAA,EAAW,OACVC,OACDA,IAEA,MAAOC,EAAkBC,EAAqBC,GAmChD,UAA8BL,YAC5BA,EAAAC,SACAA,IAEA,MAAOvR,EAAO4R,GAAY1I,EAAAA,SAAeoI,GACnCO,EAAejK,EAAYC,OAAC7H,GAC5B2R,EAAc/J,EAAYC,OAAC0J,GAU1B,OATPP,GAAmB,KACjBW,EAAY1R,QAAUsR,CAAA,GACrB,CAACA,IACJzJ,EAAAA,WAAgB,WACV+J,EAAa5R,UAAYD,IAC3B,OAAAmC,EAAAwP,EAAY1R,UAAUkC,EAAA2P,KAAAH,EAAA3R,GACtB6R,EAAa5R,QAAUD,EAC7B,GACK,CAACA,EAAO6R,IACJ,CAAC7R,EAAO4R,EAAUD,EAC3B,CApD+DI,CAAqB,CAChFT,cACAC,aAEIS,OAAwB,IAATX,EACfrR,EAAQgS,EAAeX,EAAOI,EAC1B,CACR,MAAMQ,EAAkBrK,EAAAA,YAAsB,IAATyJ,GACrCvJ,EAAAA,WAAgB,KACd,MAAMoK,EAAgBD,EAAgBhS,QACtC,GAAIiS,IAAkBF,EAAc,CAC5B,MAAArI,EAAOuI,EAAgB,aAAe,eACtCC,EAAKH,EAAe,aAAe,eACjCI,QAAAC,KACN,GAAGb,sBAA2B7H,QAAWwI,8KAEnD,CACMF,EAAgBhS,QAAU+R,CAAA,GACzB,CAACA,EAAcR,GACtB,CACE,MAAMI,EAAWhR,EAAiBC,aAC/ByR,UACC,GAAIN,EAAc,CAChB,MAAMO,EA8Bd,SAAoBvS,GAClB,MAAwB,mBAAVA,CAChB,CAhCuBwS,CAAWF,GAAaA,EAAUjB,GAAQiB,EACrDC,IAAWlB,IACb,OAAAlP,EAAAwP,EAAY1R,UAAUkC,EAAA2P,KAAAH,EAAAY,GAEhC,MACQb,EAAoBY,EAC5B,GAEI,CAACN,EAAcX,EAAMK,EAAqBC,IAErC,MAAA,CAAC3R,EAAO4R,EACjB,CCzCA,IAAIa,EAAyBrQ,OAAOsQ,OAAO,CAEzCC,SAAU,WACVC,OAAQ,EACRC,MAAO,EACPC,OAAQ,EACRC,QAAS,EACTC,QAAQ,EACRC,SAAU,SACVC,KAAM,mBACNC,WAAY,SACZC,SAAU,WAGRC,EAAiB3P,EAAgB+E,YACnC,CAAC1G,EAAO4B,IACoBmJ,EAAAxK,IACxBuE,EAAUyM,KACV,IACKvR,EACHhC,IAAK4D,EACL6I,MAAO,IAAKiG,KAA2B1Q,EAAMyK,WAKrD6G,EAAe9Q,YAbJ,iBAcX,IAAIgL,EAAO8F,EC/BX,SAASE,EAAEC,GAAO,IAAAC,EAAEC,EAAEC,EAAE,GAAG,GAAG,iBAAiBH,GAAG,iBAAiBA,EAAKG,GAAAH,OAAA,GAAU,iBAAiBA,KAAK9J,MAAMkK,QAAQJ,GAAG,CAAC,IAAIK,EAAEL,EAAE9S,OAAO,IAAI+S,EAAE,EAAEA,EAAEI,EAAEJ,MAAMA,KAAKC,EAAEH,EAAEC,EAAEC,OAAOE,IAAIA,GAAG,KAAKA,GAAGD,EAAE,MAAU,IAAAA,KAAKF,EAAEA,EAAEE,KAAKC,IAAIA,GAAG,KAAKA,GAAGD,GAAU,OAAAC,CAAC,CAAQ,SAASG,IAAe,IAAA,IAAAN,EAAEC,EAAEC,EAAE,EAAEC,EAAE,GAAGE,EAAEE,UAAUrT,OAAOgT,EAAEG,EAAEH,KAAKF,EAAEO,UAAUL,MAAMD,EAAEF,EAAEC,MAAMG,IAAIA,GAAG,KAAKA,GAAGF,GAAU,OAAAE,CAAC,CCG/W,IAAIK,EAAa/C,EAAM,UAAUC,OAAOC,mBAAsB,GAC1D5M,EAAQ,EACZ,SAAS0P,EAAMC,GACb,MAAOC,EAAIC,GAASlL,EAAcK,SAACyK,KAIR,OAH3B/F,GAAgB,KACQmG,GAAOC,GAAYA,GAAWC,OAAO/P,MAAQ,GAClE,CAAC2P,IACuBC,EAAK,SAASA,IAAO,EAClD,CCNA,MAAMI,EAAQ,CAAC,MAAO,QAAS,SAAU,QAGnCC,EAAMC,KAAKD,IACXE,EAAMD,KAAKC,IACXC,EAAQF,KAAKE,MACbC,EAAQH,KAAKG,MACbC,EAAqBC,IAAA,CACzBC,EAAGD,EACHE,EAAGF,IAECG,EAAkB,CACtBC,KAAM,QACNC,MAAO,OACPC,OAAQ,MACRC,IAAK,UAEDC,EAAuB,CAC3BC,MAAO,MACPC,IAAK,SAEP,SAASC,EAAMF,EAAOvV,EAAOwV,GAC3B,OAAOd,EAAIa,EAAOf,EAAIxU,EAAOwV,GAC/B,CACA,SAASE,GAAS1V,EAAO2V,GACvB,MAAwB,mBAAV3V,EAAuBA,EAAM2V,GAAS3V,CACtD,CACA,SAAS4V,GAAQC,GACf,OAAOA,EAAUC,MAAM,KAAK,EAC9B,CACA,SAASC,GAAaF,GACpB,OAAOA,EAAUC,MAAM,KAAK,EAC9B,CACA,SAASE,GAAgBC,GAChB,MAAS,MAATA,EAAe,IAAM,GAC9B,CACA,SAASC,GAAcD,GACd,MAAS,MAATA,EAAe,SAAW,OACnC,CACA,SAASE,GAAYN,GACZ,MAAA,CAAC,MAAO,UAAUxF,SAASuF,GAAQC,IAAc,IAAM,GAChE,CACA,SAASO,GAAiBP,GACjB,OAAAG,GAAgBG,GAAYN,GACrC,CAkBA,SAASQ,GAA8BR,GACrC,OAAOA,EAAUS,QAAQ,cAA2BC,GAAAjB,EAAqBiB,IAC3E,CA6BA,SAASC,GAAqBX,GAC5B,OAAOA,EAAUS,QAAQ,0BAAkCG,GAAAxB,EAAgBwB,IAC7E,CAUA,SAASC,GAAiB3D,GACxB,MAA0B,iBAAZA,EAVhB,SAA6BA,GACpB,MAAA,CACLsC,IAAK,EACLF,MAAO,EACPC,OAAQ,EACRF,KAAM,KACHnC,EAEP,CAEuC4D,CAAoB5D,GAAW,CAClEsC,IAAKtC,EACLoC,MAAOpC,EACPqC,OAAQrC,EACRmC,KAAMnC,EAEV,CACA,SAAS6D,GAAiBC,GAClB,MAAA9B,EACJA,EAAAC,EACAA,EAAAnC,MACAA,EAAAC,OACAA,GACE+D,EACG,MAAA,CACLhE,QACAC,SACAuC,IAAKL,EACLE,KAAMH,EACNI,MAAOJ,EAAIlC,EACXuC,OAAQJ,EAAIlC,EACZiC,IACAC,IAEJ,CCpIA,SAAS8B,GAA2BC,EAAMlB,EAAWmB,GAC/C,IAAAC,UACFA,EAAAC,SACAA,GACEH,EACE,MAAAI,EAAWhB,GAAYN,GACvBuB,EAAgBhB,GAAiBP,GACjCwB,EAAcnB,GAAckB,GAC5BX,EAAOb,GAAQC,GACfyB,EAA0B,MAAbH,EACbI,EAAUN,EAAUlC,EAAIkC,EAAUpE,MAAQ,EAAIqE,EAASrE,MAAQ,EAC/D2E,EAAUP,EAAUjC,EAAIiC,EAAUnE,OAAS,EAAIoE,EAASpE,OAAS,EACjE2E,EAAcR,EAAUI,GAAe,EAAIH,EAASG,GAAe,EACrE,IAAAK,EACJ,OAAQjB,GACN,IAAK,MACMiB,EAAA,CACP3C,EAAGwC,EACHvC,EAAGiC,EAAUjC,EAAIkC,EAASpE,QAE5B,MACF,IAAK,SACM4E,EAAA,CACP3C,EAAGwC,EACHvC,EAAGiC,EAAUjC,EAAIiC,EAAUnE,QAE7B,MACF,IAAK,QACM4E,EAAA,CACP3C,EAAGkC,EAAUlC,EAAIkC,EAAUpE,MAC3BmC,EAAGwC,GAEL,MACF,IAAK,OACME,EAAA,CACP3C,EAAGkC,EAAUlC,EAAImC,EAASrE,MAC1BmC,EAAGwC,GAEL,MACF,QACWE,EAAA,CACP3C,EAAGkC,EAAUlC,EACbC,EAAGiC,EAAUjC,GAGX,OAAAe,GAAaF,IACnB,IAAK,QACH6B,EAAON,IAAkBK,GAAeT,GAAOM,GAAkB,EAAA,GACjE,MACF,IAAK,MACHI,EAAON,IAAkBK,GAAeT,GAAOM,GAAkB,EAAA,GAG9D,OAAAI,CACT,CAqGAC,eAAeC,GAAe/I,EAAOgJ,GAC/B,IAAAC,OACY,IAAZD,IACFA,EAAU,CAAE,GAER,MAAA9C,EACJA,EAAAC,EACAA,EACA+C,SAAAA,EAAAA,MACAC,EAAAC,SACAA,EAAAC,SACAA,GACErJ,GACEsJ,SACJA,EAAW,oBAAAC,aACXA,EAAe,WAAAC,eACfA,EAAiB,WAAAC,YACjBA,GAAc,EAAAvF,QACdA,EAAU,GACR2C,GAASmC,EAAShJ,GAChB0J,EAAgB7B,GAAiB3D,GAEjChO,EAAUkT,EAASK,EADa,aAAnBD,EAAgC,YAAc,WACbA,GAC9CG,EAAqB5B,SAAuBmB,EAASU,gBAAgB,CACzE1T,QAAiH,OAAtG+S,QAAqD,MAAtBC,EAASW,eAAoB,EAASX,EAASW,UAAU3T,MAAqB+S,EAAgC/S,EAAUA,EAAQ4T,sBAAyD,MAA/BZ,EAASa,wBAA6B,EAASb,EAASa,mBAAmBX,EAASf,WACxRiB,WACAC,eACAF,cAEIrB,EAA0B,aAAnBwB,EAAgC,CAC3CtD,IACAC,IACAnC,MAAOmF,EAAMd,SAASrE,MACtBC,OAAQkF,EAAMd,SAASpE,QACrBkF,EAAMf,UACJ4B,QAAkD,MAA5Bd,EAASe,qBAA0B,EAASf,EAASe,gBAAgBb,EAASf,WACpG6B,QAA4C,MAAtBhB,EAASW,eAAoB,EAASX,EAASW,UAAUG,WAA+C,MAArBd,EAASiB,cAAmB,EAASjB,EAASiB,SAASH,KAGlK,CACF9D,EAAG,EACHC,EAAG,GAECiE,EAAoBrC,GAAiBmB,EAASmB,4DAA8DnB,EAASmB,sDAAsD,CAC/KjB,WACApB,OACAgC,eACAX,aACGrB,GACE,MAAA,CACLxB,KAAMmD,EAAmBnD,IAAM4D,EAAkB5D,IAAMkD,EAAclD,KAAO0D,EAAY/D,EACxFI,QAAS6D,EAAkB7D,OAASoD,EAAmBpD,OAASmD,EAAcnD,QAAU2D,EAAY/D,EACpGE,MAAOsD,EAAmBtD,KAAO+D,EAAkB/D,KAAOqD,EAAcrD,MAAQ6D,EAAYhE,EAC5FI,OAAQ8D,EAAkB9D,MAAQqD,EAAmBrD,MAAQoD,EAAcpD,OAAS4D,EAAYhE,EAEpG,CA8TA,SAASoE,GAAelG,EAAU4D,GACzB,MAAA,CACLxB,IAAKpC,EAASoC,IAAMwB,EAAK/D,OACzBqC,MAAOlC,EAASkC,MAAQ0B,EAAKhE,MAC7BuC,OAAQnC,EAASmC,OAASyB,EAAK/D,OAC/BoC,KAAMjC,EAASiC,KAAO2B,EAAKhE,MAE/B,CACA,SAASuG,GAAsBnG,GAC7B,OAAOsB,EAAMhJ,MAAKkL,GAAQxD,EAASwD,IAAS,GAC9C,CC7hBA,SAAS4C,KACP,MAAyB,oBAAXlS,MAChB,CACA,SAASmS,GAAYlZ,GACf,OAAAmZ,GAAOnZ,IACDA,EAAKoZ,UAAY,IAAIC,cAKxB,WACT,CACA,SAASC,GAAUtZ,GACb,IAAAuZ,EACI,OAAQ,MAARvZ,GAA8D,OAA7CuZ,EAAsBvZ,EAAK+I,oBAAyB,EAASwQ,EAAoBzJ,cAAgB/I,MAC5H,CACA,SAASyR,GAAmBxY,GACtB,IAAA2W,EACJ,OAA0F,OAAlFA,GAAQwC,GAAOnZ,GAAQA,EAAK+I,cAAgB/I,EAAKiJ,WAAalC,OAAOkC,eAAoB,EAAS0N,EAAK6C,eACjH,CACA,SAASL,GAAOvZ,GACV,QAACqZ,OAGErZ,aAAiB+G,MAAQ/G,aAAiB0Z,GAAU1Z,GAAO+G,KACpE,CACA,SAAS2R,GAAU1Y,GACb,QAACqZ,OAGErZ,aAAiB6Z,SAAW7Z,aAAiB0Z,GAAU1Z,GAAO6Z,QACvE,CACA,SAASC,GAAc9Z,GACjB,QAACqZ,OAGErZ,aAAiB+Z,aAAe/Z,aAAiB0Z,GAAU1Z,GAAO+Z,YAC3E,CACA,SAASC,GAAaha,GACpB,SAAKqZ,MAAqC,oBAAfY,cAGpBja,aAAiBia,YAAcja,aAAiB0Z,GAAU1Z,GAAOia,WAC1E,CACA,SAASC,GAAkBnV,GACnB,MAAAkO,SACJA,EAAAkH,UACAA,EAAAC,UACAA,EAAArK,QACAA,GACEa,GAAiB7L,GACrB,MAAO,kCAAkCc,KAAKoN,EAAWmH,EAAYD,KAAe,CAAC,SAAU,YAAY9J,SAASN,EACtH,CACA,SAASsK,GAAetV,GACf,MAAA,CAAC,QAAS,KAAM,MAAMsL,SAASiJ,GAAYvU,GACpD,CACA,SAASuV,GAAWvV,GAClB,MAAO,CAAC,gBAAiB,UAAUwG,MAAiBgP,IAC9C,IACK,OAAAxV,EAAQyV,QAAQD,EACxB,OAAQ/G,GACA,OAAA,CACb,IAEA,CACA,SAASiH,GAAkBC,GACzB,MAAMC,EAASC,KACTC,EAAMnC,GAAUgC,GAAgB9J,GAAiB8J,GAAgBA,EAIvE,MAAO,CAAC,YAAa,YAAa,QAAS,SAAU,eAAenP,MAAKvL,KAAS6a,EAAI7a,IAAwB,SAAf6a,EAAI7a,QAA+B6a,EAAIC,eAAsC,WAAtBD,EAAIC,gBAAwCH,KAAWE,EAAIE,gBAAwC,SAAvBF,EAAIE,iBAAuCJ,KAAWE,EAAI7U,QAAwB,SAAf6U,EAAI7U,QAA8B,CAAC,YAAa,YAAa,QAAS,SAAU,cAAe,UAAUuF,MAAKvL,IAAU6a,EAAIG,YAAc,IAAI3K,SAASrQ,MAAW,CAAC,QAAS,SAAU,SAAU,WAAWuL,UAAesP,EAAII,SAAW,IAAI5K,SAASrQ,IAC7hB,CAaA,SAAS4a,KACP,QAAmB,oBAARM,MAAwBA,IAAIC,WAChCD,IAAIC,SAAS,0BAA2B,OACjD,CACA,SAASC,GAAsBhb,GACtB,MAAA,CAAC,OAAQ,OAAQ,aAAaiQ,SAASiJ,GAAYlZ,GAC5D,CACA,SAASwQ,GAAiB7L,GACxB,OAAO2U,GAAU3U,GAAS6L,iBAAiB7L,EAC7C,CACA,SAASsW,GAActW,GACjB,OAAA2T,GAAU3T,GACL,CACLuW,WAAYvW,EAAQuW,WACpBC,UAAWxW,EAAQwW,WAGhB,CACLD,WAAYvW,EAAQyW,QACpBD,UAAWxW,EAAQ0W,QAEvB,CACA,SAASC,GAActb,GACjB,GAAsB,SAAtBkZ,GAAYlZ,GACP,OAAAA,EAEH,MAAA2F,EAEN3F,EAAKub,cAELvb,EAAKwb,YAEL5B,GAAa5Z,IAASA,EAAKyb,MAE3BjD,GAAmBxY,GACnB,OAAO4Z,GAAajU,GAAUA,EAAO8V,KAAO9V,CAC9C,CACA,SAAS+V,GAA2B1b,GAC5B,MAAAwb,EAAaF,GAActb,GAC7B,OAAAgb,GAAsBQ,GACjBxb,EAAK+I,cAAgB/I,EAAK+I,cAAcoD,KAAOnM,EAAKmM,KAEzDuN,GAAc8B,IAAe1B,GAAkB0B,GAC1CA,EAEFE,GAA2BF,EACpC,CACA,SAASG,GAAqB3b,EAAM4b,EAAMC,GACpC,IAAAC,OACS,IAATF,IACFA,EAAO,SAEe,IAApBC,IACgBA,GAAA,GAEd,MAAAE,EAAqBL,GAA2B1b,GAChDgc,EAASD,KAAuE,OAA9CD,EAAuB9b,EAAK+I,oBAAyB,EAAS+S,EAAqB3P,MACrH8P,EAAM3C,GAAUyC,GACtB,GAAIC,EAAQ,CACJ,MAAAE,EAAeC,GAAgBF,GACrC,OAAOL,EAAKQ,OAAOH,EAAKA,EAAII,gBAAkB,GAAIvC,GAAkBiC,GAAsBA,EAAqB,GAAIG,GAAgBL,EAAkBF,GAAqBO,GAAgB,GAC9L,CACS,OAAAN,EAAKQ,OAAOL,EAAoBJ,GAAqBI,EAAoB,GAAIF,GACtF,CACA,SAASM,GAAgBF,GAChB,OAAAA,EAAIK,QAAUta,OAAOua,eAAeN,EAAIK,QAAUL,EAAIC,aAAe,IAC9E,CClJA,SAASM,GAAiB7X,GAClB,MAAA8V,EAAMjK,GAAiB7L,GAG7B,IAAI8N,EAAQgK,WAAWhC,EAAIhI,QAAU,EACjCC,EAAS+J,WAAWhC,EAAI/H,SAAW,EACjC,MAAAgK,EAAYhD,GAAc/U,GAC1BgY,EAAcD,EAAY/X,EAAQgY,YAAclK,EAChDmK,EAAeF,EAAY/X,EAAQiY,aAAelK,EAClDmK,EAAiBtI,EAAM9B,KAAWkK,GAAepI,EAAM7B,KAAYkK,EAKlE,OAJHC,IACMpK,EAAAkK,EACCjK,EAAAkK,GAEJ,CACLnK,QACAC,SACAoK,EAAGD,EAEP,CAEA,SAASE,GAAcpY,GACrB,OAAQ2T,GAAU3T,GAAoCA,EAAzBA,EAAQ4T,cACvC,CAEA,SAASK,GAASjU,GACV,MAAAqY,EAAaD,GAAcpY,GAC7B,IAAC+U,GAAcsD,GACjB,OAAOvI,EAAa,GAEhB,MAAAgC,EAAOuG,EAAWC,yBAClBxK,MACJA,EAAAC,OACAA,EAAAoK,EACAA,GACEN,GAAiBQ,GACrB,IAAIrI,GAAKmI,EAAIvI,EAAMkC,EAAKhE,OAASgE,EAAKhE,OAASA,EAC3CmC,GAAKkI,EAAIvI,EAAMkC,EAAK/D,QAAU+D,EAAK/D,QAAUA,EAU1C,OANFiC,GAAMuI,OAAOC,SAASxI,KACrBA,EAAA,GAEDC,GAAMsI,OAAOC,SAASvI,KACrBA,EAAA,GAEC,CACLD,IACAC,IAEJ,CAEA,MAAMwI,KAAsC,GAC5C,SAASC,GAAiB1Y,GAClB,MAAAsX,EAAM3C,GAAU3U,GACtB,OAAK6V,MAAeyB,EAAII,eAGjB,CACL1H,EAAGsH,EAAII,eAAeiB,WACtB1I,EAAGqH,EAAII,eAAekB,WAJfH,EAMX,CAWA,SAASH,GAAsBtY,EAAS6Y,EAAcC,EAAiBhF,QAChD,IAAjB+E,IACaA,GAAA,QAEO,IAApBC,IACgBA,GAAA,GAEd,MAAAC,EAAa/Y,EAAQsY,wBACrBD,EAAaD,GAAcpY,GAC7B,IAAAgZ,EAAQlJ,EAAa,GACrB+I,IACE/E,EACEH,GAAUG,KACZkF,EAAQ/E,GAASH,IAGnBkF,EAAQ/E,GAASjU,IAGf,MAAAiZ,EA7BR,SAAgCjZ,EAASkZ,EAASC,GAIhD,YAHgB,IAAZD,IACQA,GAAA,MAEPC,GAAwBD,GAAWC,IAAyBxE,GAAU3U,KAGpEkZ,CACT,CAqBwBE,CAAuBf,EAAYS,EAAiBhF,GAAgB4E,GAAiBL,GAAcvI,EAAa,GACtI,IAAIE,GAAK+I,EAAW5I,KAAO8I,EAAcjJ,GAAKgJ,EAAMhJ,EAChDC,GAAK8I,EAAWzI,IAAM2I,EAAchJ,GAAK+I,EAAM/I,EAC/CnC,EAAQiL,EAAWjL,MAAQkL,EAAMhJ,EACjCjC,EAASgL,EAAWhL,OAASiL,EAAM/I,EACvC,GAAIoI,EAAY,CACR,MAAAf,EAAM3C,GAAU0D,GAChBgB,EAAYvF,GAAgBH,GAAUG,GAAgBa,GAAUb,GAAgBA,EACtF,IAAIwF,EAAahC,EACbiC,EAAgB/B,GAAgB8B,GAC7B,KAAAC,GAAiBzF,GAAgBuF,IAAcC,GAAY,CAC1D,MAAAE,EAAcvF,GAASsF,GACvBE,EAAaF,EAAcjB,wBAC3BxC,EAAMjK,GAAiB0N,GACvBpJ,EAAOsJ,EAAWtJ,MAAQoJ,EAAcG,WAAa5B,WAAWhC,EAAI6D,cAAgBH,EAAYxJ,EAChGM,EAAMmJ,EAAWnJ,KAAOiJ,EAAcK,UAAY9B,WAAWhC,EAAI+D,aAAeL,EAAYvJ,EAClGD,GAAKwJ,EAAYxJ,EACjBC,GAAKuJ,EAAYvJ,EACjBnC,GAAS0L,EAAYxJ,EACrBjC,GAAUyL,EAAYvJ,EACjBD,GAAAG,EACAF,GAAAK,EACLgJ,EAAa3E,GAAU4E,GACvBA,EAAgB/B,GAAgB8B,EACtC,CACA,CACE,OAAOzH,GAAiB,CACtB/D,QACAC,SACAiC,IACAC,KAEJ,CAIA,SAAS6J,GAAoB9Z,EAAS8R,GAC9B,MAAAiI,EAAazD,GAActW,GAASuW,WAC1C,OAAKzE,EAGEA,EAAK3B,KAAO4J,EAFVzB,GAAsBzE,GAAmB7T,IAAUmQ,KAAO4J,CAGrE,CAEA,SAASC,GAAcnF,EAAiBoF,EAAQC,QACrB,IAArBA,IACiBA,GAAA,GAEf,MAAAC,EAAWtF,EAAgByD,wBAK1B,MAAA,CACLtI,EALQmK,EAAShK,KAAO8J,EAAO1D,YAAc2D,EAAmB,EAElEJ,GAAoBjF,EAAiBsF,IAInClK,EAHQkK,EAAS7J,IAAM2J,EAAOzD,UAKlC,CA6GA,SAAS4D,GAAkCpa,EAASqa,EAAkBlH,GAChE,IAAArB,EACJ,GAAyB,aAArBuI,EACKvI,EA7CX,SAAyB9R,EAASmT,GAC1B,MAAAmE,EAAM3C,GAAU3U,GAChBsa,EAAOzG,GAAmB7T,GAC1B0X,EAAiBJ,EAAII,eAC3B,IAAI5J,EAAQwM,EAAKC,YACbxM,EAASuM,EAAKE,aACdxK,EAAI,EACJC,EAAI,EACR,GAAIyH,EAAgB,CAClB5J,EAAQ4J,EAAe5J,MACvBC,EAAS2J,EAAe3J,OACxB,MAAM0M,EAAsB5E,OACvB4E,GAAuBA,GAAoC,UAAbtH,KACjDnD,EAAI0H,EAAeiB,WACnB1I,EAAIyH,EAAekB,UAEzB,CACS,MAAA,CACL9K,QACAC,SACAiC,IACAC,IAEJ,CAsBWyK,CAAgB1a,EAASmT,QACpC,GAAkC,aAArBkH,EACFvI,EAlEX,SAAyB9R,GACjB,MAAAsa,EAAOzG,GAAmB7T,GAC1Bia,EAAS3D,GAActW,GACvBwH,EAAOxH,EAAQoE,cAAcoD,KAC7BsG,EAAQ6B,EAAI2K,EAAKK,YAAaL,EAAKC,YAAa/S,EAAKmT,YAAanT,EAAK+S,aACvExM,EAAS4B,EAAI2K,EAAKM,aAAcN,EAAKE,aAAchT,EAAKoT,aAAcpT,EAAKgT,cACjF,IAAIxK,GAAKiK,EAAO1D,WAAauD,GAAoB9Z,GAC3C,MAAAiQ,GAAKgK,EAAOzD,UAIX,MAHkC,QAArC3K,GAAiBrE,GAAMqT,YACzB7K,GAAKL,EAAI2K,EAAKC,YAAa/S,EAAK+S,aAAezM,GAE1C,CACLA,QACAC,SACAiC,IACAC,IAEJ,CAiDW6K,CAAgBjH,GAAmB7T,SAC9C,GAAa2T,GAAU0G,GACZvI,EAvBX,SAAoC9R,EAASmT,GAC3C,MAAM4F,EAAaT,GAAsBtY,GAAS,EAAmB,UAAbmT,GAClD7C,EAAMyI,EAAWzI,IAAMtQ,EAAQ4Z,UAC/BzJ,EAAO4I,EAAW5I,KAAOnQ,EAAQ0Z,WACjCV,EAAQjE,GAAc/U,GAAWiU,GAASjU,GAAW8P,EAAa,GAKjE,MAAA,CACLhC,MALY9N,EAAQua,YAAcvB,EAAMhJ,EAMxCjC,OALa/N,EAAQwa,aAAexB,EAAM/I,EAM1CD,EALQG,EAAO6I,EAAMhJ,EAMrBC,EALQK,EAAM0I,EAAM/I,EAOxB,CAQW8K,CAA2BV,EAAkBlH,OAC/C,CACC,MAAA8F,EAAgBP,GAAiB1Y,GAChC8R,EAAA,CACL9B,EAAGqK,EAAiBrK,EAAIiJ,EAAcjJ,EACtCC,EAAGoK,EAAiBpK,EAAIgJ,EAAchJ,EACtCnC,MAAOuM,EAAiBvM,MACxBC,OAAQsM,EAAiBtM,OAE/B,CACE,OAAO8D,GAAiBC,EAC1B,CACA,SAASkJ,GAAyBhb,EAASib,GACnC,MAAApE,EAAaF,GAAc3W,GAC7B,QAAA6W,IAAeoE,IAAatH,GAAUkD,IAAeR,GAAsBQ,MAG9B,UAA1ChL,GAAiBgL,GAAYjJ,UAAwBoN,GAAyBnE,EAAYoE,GACnG,CA2EA,SAASC,GAA8Blb,EAAS8T,EAAcX,GACtD,MAAAgI,EAA0BpG,GAAcjB,GACxCe,EAAkBhB,GAAmBC,GACrCoF,EAAuB,UAAb/F,EACVrB,EAAOwG,GAAsBtY,GAAS,EAAMkZ,EAASpF,GAC3D,IAAImG,EAAS,CACX1D,WAAY,EACZC,UAAW,GAEP,MAAA4E,EAAUtL,EAAa,GAI7B,SAASuL,IACCD,EAAApL,EAAI8J,GAAoBjF,EACpC,CACE,GAAIsG,IAA4BA,IAA4BjC,EAI1D,IAHkC,SAA9B3E,GAAYT,IAA4BqB,GAAkBN,MAC5DoF,EAAS3D,GAAcxC,IAErBqH,EAAyB,CAC3B,MAAMG,EAAahD,GAAsBxE,GAAc,EAAMoF,EAASpF,GAC9DsH,EAAApL,EAAIsL,EAAWtL,EAAI8D,EAAa4F,WAChC0B,EAAAnL,EAAIqL,EAAWrL,EAAI6D,EAAa8F,SACzC,MAAU/E,GACkBwG,IAG3BnC,IAAYiC,GAA2BtG,GACdwG,IAEvB,MAAAE,GAAa1G,GAAoBsG,GAA4BjC,EAAmDpJ,EAAa,GAAtDkK,GAAcnF,EAAiBoF,GAGrG,MAAA,CACLjK,EAHQ8B,EAAK3B,KAAO8J,EAAO1D,WAAa6E,EAAQpL,EAAIuL,EAAWvL,EAI/DC,EAHQ6B,EAAKxB,IAAM2J,EAAOzD,UAAY4E,EAAQnL,EAAIsL,EAAWtL,EAI7DnC,MAAOgE,EAAKhE,MACZC,OAAQ+D,EAAK/D,OAEjB,CAEA,SAASyN,GAAmBxb,GACnB6L,MAAuC,WAAvCA,GAAiB7L,GAAS4N,QACnC,CAEA,SAAS6N,GAAoBzb,EAAS0b,GAChC,IAAC3G,GAAc/U,IAAmD,UAAvC6L,GAAiB7L,GAAS4N,SAChD,OAAA,KAET,GAAI8N,EACF,OAAOA,EAAS1b,GAElB,IAAI2b,EAAkB3b,EAAQ8T,aASvB,OAHHD,GAAmB7T,KAAa2b,IAClCA,EAAkBA,EAAgBvX,cAAcoD,MAE3CmU,CACT,CAIA,SAAS5H,GAAgB/T,EAAS0b,GAC1B,MAAApE,EAAM3C,GAAU3U,GAClB,GAAAuV,GAAWvV,GACN,OAAAsX,EAEL,IAACvC,GAAc/U,GAAU,CACvB,IAAA4b,EAAkBjF,GAAc3W,GACpC,KAAO4b,IAAoBvF,GAAsBuF,IAAkB,CACjE,GAAIjI,GAAUiI,KAAqBJ,GAAmBI,GAC7C,OAAAA,EAETA,EAAkBjF,GAAciF,EACtC,CACW,OAAAtE,CACX,CACM,IAAAxD,EAAe2H,GAAoBzb,EAAS0b,GAChD,KAAO5H,GAAgBwB,GAAexB,IAAiB0H,GAAmB1H,IACzDA,EAAA2H,GAAoB3H,EAAc4H,GAE/C,OAAA5H,GAAgBuC,GAAsBvC,IAAiB0H,GAAmB1H,KAAkB4B,GAAkB5B,GACzGwD,EAEFxD,GD5XT,SAA4B9T,GACtB,IAAA6b,EAAclF,GAAc3W,GAChC,KAAO+U,GAAc8G,KAAiBxF,GAAsBwF,IAAc,CACpE,GAAAnG,GAAkBmG,GACb,OAAAA,EACb,GAAetG,GAAWsG,GACb,OAAA,KAETA,EAAclF,GAAckF,EAChC,CACS,OAAA,IACT,CCiXyBC,CAAmB9b,IAAYsX,CACxD,CAqBA,MAAMtE,GAAW,CACfmB,sDA/TF,SAA+DnC,GACzD,IAAAkB,SACFA,EAAApB,KACAA,EAAAgC,aACAA,EAAAX,SACAA,GACEnB,EACJ,MAAMkH,EAAuB,UAAb/F,EACV0B,EAAkBhB,GAAmBC,GACrCiI,IAAW7I,GAAWqC,GAAWrC,EAASf,UAC5C,GAAA2B,IAAiBe,GAAmBkH,GAAY7C,EAC3C,OAAApH,EAET,IAAImI,EAAS,CACX1D,WAAY,EACZC,UAAW,GAETwC,EAAQlJ,EAAa,GACnB,MAAAsL,EAAUtL,EAAa,GACvBqL,EAA0BpG,GAAcjB,GAC9C,IAAIqH,IAA4BA,IAA4BjC,MACxB,SAA9B3E,GAAYT,IAA4BqB,GAAkBN,MAC5DoF,EAAS3D,GAAcxC,IAErBiB,GAAcjB,IAAe,CACzB,MAAAwH,EAAahD,GAAsBxE,GACzCkF,EAAQ/E,GAASH,GACTsH,EAAApL,EAAIsL,EAAWtL,EAAI8D,EAAa4F,WAChC0B,EAAAnL,EAAIqL,EAAWrL,EAAI6D,EAAa8F,SAC9C,CAEE,MAAM2B,GAAa1G,GAAoBsG,GAA4BjC,EAAyDpJ,EAAa,GAA5DkK,GAAcnF,EAAiBoF,GAAQ,GAC7G,MAAA,CACLnM,MAAOgE,EAAKhE,MAAQkL,EAAMhJ,EAC1BjC,OAAQ+D,EAAK/D,OAASiL,EAAM/I,EAC5BD,EAAG8B,EAAK9B,EAAIgJ,EAAMhJ,EAAIiK,EAAO1D,WAAayC,EAAMhJ,EAAIoL,EAAQpL,EAAIuL,EAAWvL,EAC3EC,EAAG6B,EAAK7B,EAAI+I,EAAM/I,EAAIgK,EAAOzD,UAAYwC,EAAM/I,EAAImL,EAAQnL,EAAIsL,EAAWtL,EAE9E,EA0RE4D,sBACAH,gBAvJF,SAAyB1B,GACnB,IAAAhS,QACFA,EAAAoT,SACAA,EAAAC,aACAA,EAAAF,SACAA,GACEnB,EACJ,MACMgK,EAAoB,IADoB,sBAAb5I,EAAmCmC,GAAWvV,GAAW,GAxC5F,SAAqCA,EAASic,GACtC,MAAAC,EAAeD,EAAM9b,IAAIH,GAC/B,GAAIkc,EACK,OAAAA,EAET,IAAIlb,EAASgW,GAAqBhX,EAAS,IAAI,GAAOiB,QAAOkb,GAAMxI,GAAUwI,IAA2B,SAApB5H,GAAY4H,KAC5FC,EAAsC,KAC1C,MAAMC,EAAwD,UAAvCxQ,GAAiB7L,GAAS4N,SACjD,IAAIiO,EAAcQ,EAAiB1F,GAAc3W,GAAWA,EAG5D,KAAO2T,GAAUkI,KAAiBxF,GAAsBwF,IAAc,CAC9D,MAAAS,EAAgBzQ,GAAiBgQ,GACjCU,EAA0B7G,GAAkBmG,GAC7CU,GAAsD,UAA3BD,EAAc1O,WACNwO,EAAA,OAEVC,GAAkBE,IAA4BH,GAAuCG,GAAsD,WAA3BD,EAAc1O,UAA2BwO,GAAuC,CAAC,WAAY,SAAS9Q,SAAS8Q,EAAoCxO,WAAauH,GAAkB0G,KAAiBU,GAA2BvB,GAAyBhb,EAAS6b,IAG5Y7a,EAASA,EAAOC,QAAmBub,GAAAA,IAAaX,IAGVO,EAAAE,EAExCT,EAAclF,GAAckF,EAChC,CAES,OADDI,EAAAQ,IAAIzc,EAASgB,GACZA,CACT,CAWiG0b,CAA4B1c,EAAS2c,KAAKC,IAAM,GAAGnF,OAAOrE,GACjGC,GAClDwJ,EAAwBb,EAAkB,GAC1Cc,EAAed,EAAkB3d,QAAO,CAAC0e,EAAS1C,KACtD,MAAMvI,EAAOsI,GAAkCpa,EAASqa,EAAkBlH,GAKnE,OAJP4J,EAAQzM,IAAMX,EAAImC,EAAKxB,IAAKyM,EAAQzM,KACpCyM,EAAQ3M,MAAQX,EAAIqC,EAAK1B,MAAO2M,EAAQ3M,OACxC2M,EAAQ1M,OAASZ,EAAIqC,EAAKzB,OAAQ0M,EAAQ1M,QAC1C0M,EAAQ5M,KAAOR,EAAImC,EAAK3B,KAAM4M,EAAQ5M,MAC/B4M,CAAA,GACN3C,GAAkCpa,EAAS6c,EAAuB1J,IAC9D,MAAA,CACLrF,MAAOgP,EAAa1M,MAAQ0M,EAAa3M,KACzCpC,OAAQ+O,EAAazM,OAASyM,EAAaxM,IAC3CN,EAAG8M,EAAa3M,KAChBF,EAAG6M,EAAaxM,IAEpB,EAgIEyD,mBACAiJ,gBAxBsBpK,eAAgBqK,GAChC,MAAAC,EAAoBP,KAAK5I,iBAAmBA,GAC5CoJ,EAAkBR,KAAKS,cACvBC,QAA2BF,EAAgBF,EAAK9K,UAC/C,MAAA,CACLD,UAAWgJ,GAA8B+B,EAAK/K,gBAAiBgL,EAAkBD,EAAK9K,UAAW8K,EAAK9J,UACtGhB,SAAU,CACRnC,EAAG,EACHC,EAAG,EACHnC,MAAOuP,EAAmBvP,MAC1BC,OAAQsP,EAAmBtP,QAGjC,EAYEuP,eA5RF,SAAwBtd,GACtB,OAAO2E,MAAMC,KAAK5E,EAAQsd,iBAC5B,EA2REF,cAjIF,SAAuBpd,GACf,MAAA8N,MACJA,EAAAC,OACAA,GACE8J,GAAiB7X,GACd,MAAA,CACL8N,QACAC,SAEJ,EAyHEkG,YACAN,aACA4J,MAdF,SAAevd,GACN6L,MAAwC,QAAxCA,GAAiB7L,GAAS6a,SACnC,GAeA,SAAS2C,GAAcC,EAAGC,GACxB,OAAOD,EAAEzN,IAAM0N,EAAE1N,GAAKyN,EAAExN,IAAMyN,EAAEzN,GAAKwN,EAAE3P,QAAU4P,EAAE5P,OAAS2P,EAAE1P,SAAW2P,EAAE3P,MAC7E,CAkGA,SAAS4P,GAAWzL,EAAWC,EAAUyL,EAAQ9K,QAC/B,IAAZA,IACFA,EAAU,CAAE,GAER,MAAA+K,eACJA,GAAiB,EAAAC,eACjBA,GAAiB,EAAAC,cACjBA,EAA0C,mBAAnBC,eAAmBC,YAC1CA,EAA8C,mBAAzBC,qBAAyBC,eAC9CA,GAAiB,GACfrL,EACEsL,EAAchG,GAAclG,GAC5BmM,EAAYR,GAAkBC,EAAiB,IAAKM,EAAcpH,GAAqBoH,GAAe,MAAQpH,GAAqB7E,IAAa,GACtJkM,EAAUC,SAAoB9B,IACVqB,GAAArB,EAASxW,iBAAiB,SAAU4X,EAAQ,CAC5DW,SAAS,IAEOT,GAAAtB,EAASxW,iBAAiB,SAAU4X,EAAM,IAE9D,MAAMY,EAAYJ,GAAeH,EAlHnC,SAAqBje,EAASye,GAC5B,IACIxT,EADAyT,EAAK,KAEH,MAAAC,EAAO9K,GAAmB7T,GAChC,SAASvE,IACH,IAAAmjB,EACJxY,aAAa6E,GACC,OAAb2T,EAAMF,IAAeE,EAAIC,aACrBH,EAAA,IACT,CA2ES,OA1EE,SAAAI,EAAQC,EAAMC,QACR,IAATD,IACKA,GAAA,QAES,IAAdC,IACUA,EAAA,GAELvjB,IACH,MAAAwjB,EAA2Bjf,EAAQsY,yBACnCnI,KACJA,EAAAG,IACAA,EAAAxC,MACAA,EAAAC,OACAA,GACEkR,EAIA,GAHCF,GACKN,KAEL3Q,IAAUC,EACb,OAEI,MAKA+E,EAAU,CACdoM,YANerP,EAAMS,GAIQ,OAHZT,EAAM8O,EAAKpE,aAAepK,EAAOrC,IAGC,OAFjC+B,EAAM8O,EAAKnE,cAAgBlK,EAAMvC,IAEuB,OAD1D8B,EAAMM,GACyE,KAG/F6O,UAAWrP,EAAI,EAAGF,EAAI,EAAGuP,KAAe,GAE1C,IAAIG,GAAgB,EACpB,SAASC,EAAcC,GACf,MAAAC,EAAQD,EAAQ,GAAGE,kBACzB,GAAID,IAAUN,EAAW,CACvB,IAAKG,EACH,OAAOL,IAEJQ,EAOHR,GAAQ,EAAOQ,GAJfrU,EAAY9E,YAAW,KACrB2Y,GAAQ,EAAO,KAAI,GAClB,IAIb,CACoB,IAAVQ,GAAgB9B,GAAcyB,EAA0Bjf,EAAQsY,0BAQzDwG,IAEKK,GAAA,CACtB,CAIQ,IACGT,EAAA,IAAIR,qBAAqBkB,EAAe,IACxCtM,EAEH6L,KAAMA,EAAKva,eAEd,OAAQob,GACFd,EAAA,IAAIR,qBAAqBkB,EAAetM,EACnD,CACI4L,EAAGe,QAAQzf,EACf,CACE8e,EAAQ,GACDrjB,CACT,CA6BiDikB,CAAYtB,EAAaR,GAAU,KAClF,IAsBI+B,EAtBAC,GAAiB,EACjBC,EAAiB,KACjB9B,IACe8B,EAAA,IAAI7B,gBAAuBhM,IACtC,IAAC8N,GAAc9N,EACf8N,GAAcA,EAAWvd,SAAW6b,GAAeyB,IAGrDA,EAAeE,UAAU5N,GACzB6N,qBAAqBJ,GACrBA,EAAiBK,uBAAsB,KACjC,IAAAC,EACkC,OAArCA,EAAkBL,IAA2BK,EAAgBT,QAAQtN,EAAQ,KAG1EyL,GAAA,IAENQ,IAAgBD,GAClB0B,EAAeJ,QAAQrB,GAEzByB,EAAeJ,QAAQtN,IAGzB,IAAIgO,EAAchC,EAAiB7F,GAAsBpG,GAAa,KAatE,OAZIiM,GAGJ,SAASiC,IACD,MAAAC,EAAc/H,GAAsBpG,GACtCiO,IAAgB3C,GAAc2C,EAAaE,IACrCzC,IAEIuC,EAAAE,EACdV,EAAUM,sBAAsBG,EACpC,CATeA,GAULxC,IACD,KACD,IAAA0C,EACJjC,EAAUC,SAAoB9B,IACVqB,GAAArB,EAASzW,oBAAoB,SAAU6X,GACvCE,GAAAtB,EAASzW,oBAAoB,SAAU6X,EAAM,IAEpD,MAAbY,GAAqBA,IACkB,OAAtC8B,EAAmBT,IAA2BS,EAAiBzB,aAC/CgB,EAAA,KACb1B,GACF6B,qBAAqBL,EAC3B,CAEA,CAmBA,MAAMY,GFyGS,SAAUzN,GAIhB,YAHS,IAAZA,IACQA,EAAA,GAEL,CACL3K,KAAM,SACN2K,UACA,QAAM0N,CAAG1W,GACP,IAAI2W,EAAuBC,EACrB,MAAA1Q,EACJA,EAAAC,EACAA,EAAAa,UACAA,EAAA6P,eACAA,GACE7W,EACE8W,QA9DZhO,eAAoC9I,EAAOgJ,GACnC,MAAAhC,UACJA,EACAkC,SAAAA,EAAAA,SACAE,GACEpJ,EACEmI,QAA+B,MAAlBe,EAASuK,WAAgB,EAASvK,EAASuK,MAAMrK,EAASf,WACvET,EAAOb,GAAQC,GACfU,EAAYR,GAAaF,GACzByB,EAAwC,MAA3BnB,GAAYN,GACzB+P,EAAgB,CAAC,OAAQ,OAAOvV,SAASoG,IAAa,EAAA,EACtDoP,EAAiB7O,GAAOM,GAAkB,EAAA,EAC1CwO,EAAWpQ,GAASmC,EAAShJ,GAG/B,IAAAkX,SACFA,EAAAC,UACAA,EAAA5O,cACAA,GACsB,iBAAb0O,EAAwB,CACjCC,SAAUD,EACVE,UAAW,EACX5O,cAAe,MACb,CACF2O,SAAUD,EAASC,UAAY,EAC/BC,UAAWF,EAASE,WAAa,EACjC5O,cAAe0O,EAAS1O,eAK1B,OAHIb,GAAsC,iBAAlBa,IACV4O,EAAc,QAAdzP,GAA2C,EAArBa,EAAqBA,GAElDE,EAAa,CAClBvC,EAAGiR,EAAYH,EACf7Q,EAAG+Q,EAAWH,GACZ,CACF7Q,EAAGgR,EAAWH,EACd5Q,EAAGgR,EAAYH,EAEnB,CAwB+BI,CAAqBpX,EAAOgJ,GAIrD,OAAIhC,KAAkE,OAAlD2P,EAAwBE,EAAeJ,aAAkB,EAASE,EAAsB3P,YAAgE,OAAjD4P,EAAwBC,EAAeQ,QAAkBT,EAAsBU,gBACjM,CAAE,EAEJ,CACLpR,EAAGA,EAAI4Q,EAAW5Q,EAClBC,EAAGA,EAAI2Q,EAAW3Q,EAClBgN,KAAM,IACD2D,EACH9P,aAGV,EAEA,EE1HMuQ,GFiIQ,SAAUvO,GAIf,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,QACN2K,UACA,QAAM0N,CAAG1W,GACD,MAAAkG,EACJA,EAAAC,EACAA,EAAAa,UACAA,GACEhH,GAEFkX,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,EAAAC,QAC5BA,EAAU,CACRhB,GAAYxO,IACN,IACFhC,EAAAA,EACAC,EAAAA,GACE+B,EACG,MAAA,CACLhC,EAAAA,EACAC,EAAAA,EACD,MAGFwR,GACD9Q,GAASmC,EAAShJ,GAChB6I,EAAS,CACb3C,IACAC,KAEI/B,QAAiB2E,GAAe/I,EAAO2X,GACvCR,EAAY7P,GAAYP,GAAQC,IAChCkQ,EAAW/P,GAAgBgQ,GAC7B,IAAAS,EAAgB/O,EAAOqO,GACvBW,EAAiBhP,EAAOsO,GAC5B,GAAIK,EAAe,CACX,MACAM,EAAuB,MAAbZ,EAAmB,SAAW,QAG9BU,EAAAhR,EAFJgR,EAAgBxT,EAFC,MAAb8S,EAAmB,MAAQ,QAIhBU,EADfA,EAAgBxT,EAAS0T,GAE7C,CACM,GAAIL,EAAgB,CACZ,MACAK,EAAwB,MAAdX,EAAoB,SAAW,QAG9BU,EAAAjR,EAFLiR,EAAiBzT,EAFC,MAAd+S,EAAoB,MAAQ,QAIhBU,EADhBA,EAAiBzT,EAAS0T,GAE9C,CACY,MAAAC,EAAgBL,EAAQhB,GAAG,IAC5B1W,EACHkX,CAACA,GAAWU,EACZT,CAACA,GAAYU,IAER,MAAA,IACFE,EACH5E,KAAM,CACJjN,EAAG6R,EAAc7R,EAAIA,EACrBC,EAAG4R,EAAc5R,EAAIA,EACrB6R,QAAS,CACPd,CAACA,GAAWM,EACZL,CAACA,GAAYM,IAIzB,EAEA,EEhMMQ,GFrSO,SAAUjP,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,OACN2K,UACA,QAAM0N,CAAG1W,GACP,IAAI4W,EAAuBsB,EACrB,MAAAlR,UACJA,EAAA6P,eACAA,EAAA1N,MACAA,EAAAgP,iBACAA,EACAjP,SAAAA,EAAAA,SACAE,GACEpJ,GAEFkX,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,EAC5BW,mBAAoBC,EAAAC,iBACpBA,EAAmB,UAAAC,0BACnBA,EAA4B,OAAAC,cAC5BA,GAAgB,KACbb,GACD9Q,GAASmC,EAAShJ,GAMtB,GAAsD,OAAjD4W,EAAwBC,EAAeQ,QAAkBT,EAAsBU,gBAClF,MAAO,CAAE,EAEL,MAAA1P,EAAOb,GAAQC,GACfyR,EAAkBnR,GAAY6Q,GAC9BO,EAAkB3R,GAAQoR,KAAsBA,EAChDhQ,QAA+B,MAAlBe,EAASuK,WAAgB,EAASvK,EAASuK,MAAMrK,EAASf,WACvE+P,EAAqBC,IAAgCK,IAAoBF,EAAgB,CAAC7Q,GAAqBwQ,ID7X3H,SAA+BnR,GACvB,MAAA2R,EAAoBhR,GAAqBX,GAC/C,MAAO,CAACQ,GAA8BR,GAAY2R,EAAmBnR,GAA8BmR,GACrG,CC0XgJC,CAAsBT,IAC1JU,EAA6D,SAA9BN,GAChCF,GAA+BQ,GAClCT,EAAmBU,QDxW3B,SAAmC9R,EAAWwR,EAAezH,EAAW5I,GAChE,MAAAT,EAAYR,GAAaF,GAC/B,IAAImG,EAnBN,SAAqBvF,EAAMmR,EAAS5Q,GAC5B,MAAA6Q,EAAK,CAAC,OAAQ,SACdC,EAAK,CAAC,QAAS,QACfC,EAAK,CAAC,MAAO,UACbC,EAAK,CAAC,SAAU,OACtB,OAAQvR,GACN,IAAK,MACL,IAAK,SACC,OAAAO,EAAY4Q,EAAUE,EAAKD,EACxBD,EAAUC,EAAKC,EACxB,IAAK,OACL,IAAK,QACH,OAAOF,EAAUG,EAAKC,EACxB,QACE,MAAO,GAEb,CAGaC,CAAYrS,GAAQC,GAA0B,UAAd+J,EAAuB5I,GAO3D,OANHT,IACFyF,EAAOA,EAAKzb,KAAYkW,GAAAA,EAAO,IAAMF,IACjC8Q,IACFrL,EAAOA,EAAKQ,OAAOR,EAAKzb,IAAI8V,OAGzB2F,CACT,CC8VmCkM,CAA0BlB,EAAkBK,EAAeD,EAA2BpQ,IAEnH,MAAMmR,EAAa,CAACnB,KAAqBC,GACnChU,QAAiB2E,GAAe/I,EAAO2X,GACvC4B,EAAY,GACd,IAAAC,GAAiE,OAA/CtB,EAAuBrB,EAAeoB,WAAgB,EAASC,EAAqBqB,YAAc,GAIxH,GAHI/B,GACQ+B,EAAAT,KAAK1U,EAASwD,IAEtB6P,EAAgB,CAClB,MAAM/R,EDvZd,SAA2BsB,EAAWmC,EAAOhB,QAC/B,IAARA,IACIA,GAAA,GAEF,MAAAT,EAAYR,GAAaF,GACzBuB,EAAgBhB,GAAiBP,GACjCnV,EAASwV,GAAckB,GACzB,IAAAkR,EAAsC,MAAlBlR,EAAwBb,KAAeS,EAAM,MAAQ,SAAW,QAAU,OAAuB,UAAdT,EAAwB,SAAW,MAI9I,OAHIyB,EAAMf,UAAUvW,GAAUsX,EAAMd,SAASxW,KAC3C4nB,EAAoB9R,GAAqB8R,IAEpC,CAACA,EAAmB9R,GAAqB8R,GAClD,CC2YsBC,CAAkB1S,EAAWmC,EAAOhB,GACxCoR,EAAAT,KAAK1U,EAASsB,EAAM,IAAKtB,EAASsB,EAAM,IAC1D,CAOM,GANgB8T,EAAA,IAAIA,EAAe,CACjCxS,YACAuS,eAIGA,EAAUI,OAAM/R,GAAQA,GAAQ,IAAI,CACvC,IAAIgS,EAAuBC,EACrB,MAAAC,IAA+D,OAAhDF,EAAwB/C,EAAeoB,WAAgB,EAAS2B,EAAsB5mB,QAAU,GAAK,EACpH+mB,EAAgBT,EAAWQ,GACjC,GAAIC,EAAe,CACb,IAAAC,EACJ,MAAMC,EAA6C,cAAnBxC,GAAiCgB,IAAoBnR,GAAYyS,GAC3FG,GAAsE,OAAvCF,EAAkBR,EAAc,SAAc,EAASQ,EAAgBT,UAAU,IAAM,EACxH,IAACU,GAA2BC,EAEvB,MAAA,CACL/G,KAAM,CACJngB,MAAO8mB,EACPP,UAAWC,GAEbW,MAAO,CACLnT,UAAW+S,GAI3B,CAIQ,IAAIK,EAAgJ,OAA9HP,EAAwBL,EAAcriB,QAAYkjB,GAAAA,EAAEd,UAAU,IAAM,IAAGe,MAAK,CAAC3G,EAAGC,IAAMD,EAAE4F,UAAU,GAAK3F,EAAE2F,UAAU,KAAI,SAAc,EAASM,EAAsB7S,UAG1L,IAAKoT,EACH,OAAQ9B,GACN,IAAK,UACH,CACM,IAAAiC,EACJ,MAAMvT,EASmJ,OATtIuT,EAAyBf,EAAcriB,QAAYkjB,IACpE,GAAIxB,EAA8B,CAC1B,MAAA2B,EAAkBlT,GAAY+S,EAAErT,WACtC,OAAOwT,IAAoB/B,GAGP,MAApB+B,CACpB,CACyB,OAAA,CAAA,IACN9oB,QAAS,CAAC2oB,EAAErT,UAAWqT,EAAEd,UAAUpiB,QAAOiN,GAAYA,EAAW,IAAG7P,QAAO,CAACkmB,EAAKrW,IAAaqW,EAAMrW,GAAU,MAAKkW,MAAK,CAAC3G,EAAGC,IAAMD,EAAE,GAAKC,EAAE,KAAI,SAAc,EAAS2G,EAAuB,GAC5LvT,IACeA,EAAAA,GAEnB,KAChB,CACY,IAAK,mBACcoT,EAAAjC,EAIvB,GAAInR,IAAcoT,EACT,MAAA,CACLD,MAAO,CACLnT,UAAWoT,GAIzB,CACM,MAAO,CAAE,CACf,EAEA,EEkLMhf,GFoQO,SAAU4N,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,OACN2K,UACA,QAAM0N,CAAG1W,GACP,IAAI0a,EAAuBC,EACrB,MAAA3T,UACJA,EAAAmC,MACAA,EACAD,SAAAA,EAAAA,SACAE,GACEpJ,GACE4a,MACJA,EAAQ,UACLjD,GACD9Q,GAASmC,EAAShJ,GAChBoE,QAAiB2E,GAAe/I,EAAO2X,GACvC/P,EAAOb,GAAQC,GACfU,EAAYR,GAAaF,GACzB6T,EAAqC,MAA3BvT,GAAYN,IACtBhD,MACJA,EAAAC,OACAA,GACEkF,EAAMd,SACN,IAAAyS,EACAC,EACS,QAATnT,GAA2B,WAATA,GACPkT,EAAAlT,EACbmT,EAAYrT,WAAyC,MAAlBwB,EAASuK,WAAgB,EAASvK,EAASuK,MAAMrK,EAASf,WAAc,QAAU,OAAS,OAAS,UAE3H0S,EAAAnT,EACCkT,EAAc,QAAdpT,EAAsB,MAAQ,UAE7C,MAAMsT,EAAwB/W,EAASG,EAASoC,IAAMpC,EAASmC,OACzD0U,EAAuBjX,EAAQI,EAASiC,KAAOjC,EAASkC,MACxD4U,EAA0BvV,EAAI1B,EAASG,EAAS0W,GAAaE,GAC7DG,EAAyBxV,EAAI3B,EAAQI,EAAS2W,GAAYE,GAC1DG,GAAWpb,EAAM6W,eAAeU,MACtC,IAAI8D,EAAkBH,EAClBI,EAAiBH,EAOjB,GANwD,OAAvDT,EAAwB1a,EAAM6W,eAAeU,QAAkBmD,EAAsB1C,QAAQ9R,IAC/EoV,EAAAL,GAE0C,OAAxDN,EAAyB3a,EAAM6W,eAAeU,QAAkBoD,EAAuB3C,QAAQ7R,IAChFkV,EAAAL,GAEhBI,IAAY1T,EAAW,CACzB,MAAM6T,EAAO1V,EAAIzB,EAASiC,KAAM,GAC1BmV,EAAO3V,EAAIzB,EAASkC,MAAO,GAC3BmV,EAAO5V,EAAIzB,EAASoC,IAAK,GACzBkV,EAAO7V,EAAIzB,EAASmC,OAAQ,GAC9BsU,EACFS,EAAiBtX,EAAQ,GAAc,IAATuX,GAAuB,IAATC,EAAaD,EAAOC,EAAO3V,EAAIzB,EAASiC,KAAMjC,EAASkC,QAEnG+U,EAAkBpX,EAAS,GAAc,IAATwX,GAAuB,IAATC,EAAaD,EAAOC,EAAO7V,EAAIzB,EAASoC,IAAKpC,EAASmC,QAE9G,OACYqU,EAAM,IACP5a,EACHsb,iBACAD,oBAEF,MAAMM,QAAuBzS,EAASoK,cAAclK,EAASf,UAC7D,OAAIrE,IAAU2X,EAAe3X,OAASC,IAAW0X,EAAe1X,OACvD,CACLkW,MAAO,CACLhR,OAAO,IAIN,CAAE,CACf,EAEA,EEzUMyS,GFvKO,SAAU5S,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL3K,KAAM,OACN2K,UACA,QAAM0N,CAAG1W,GACD,MAAAmJ,MACJA,GACEnJ,GACEqJ,SACJA,EAAW,qBACRsO,GACD9Q,GAASmC,EAAShJ,GACtB,OAAQqJ,GACN,IAAK,kBACH,CACQ,MAIAiI,EAAUhH,SAJOvB,GAAe/I,EAAO,IACxC2X,EACHnO,eAAgB,cAEuBL,EAAMf,WACxC,MAAA,CACL+K,KAAM,CACJ0I,uBAAwBvK,EACxBwK,gBAAiBvR,GAAsB+G,IAGvD,CACQ,IAAK,UACH,CACQ,MAIAA,EAAUhH,SAJOvB,GAAe/I,EAAO,IACxC2X,EACHlO,aAAa,IAE0BN,EAAMd,UACxC,MAAA,CACL8K,KAAM,CACJ4I,eAAgBzK,EAChB0K,QAASzR,GAAsB+G,IAG/C,CACQ,QAEI,MAAO,CAAE,EAGrB,EAEA,EE2HM+F,GFrfoBrO,IAAA,CACxB3K,KAAM,QACN2K,UACA,QAAM0N,CAAG1W,GACD,MAAAkG,EACJA,EAAAC,EACAA,EAAAa,UACAA,EAAAmC,MACAA,EACAD,SAAAA,EAAAA,SACAE,EAAAyN,eACAA,GACE7W,GAEE9J,QACJA,EAAAgO,QACAA,EAAU,GACR2C,GAASmC,EAAShJ,IAAU,CAAE,EAClC,GAAe,MAAX9J,EACF,MAAO,CAAE,EAEL,MAAAwT,EAAgB7B,GAAiB3D,GACjC2E,EAAS,CACb3C,IACAC,KAEIiB,EAAOG,GAAiBP,GACxBnV,EAASwV,GAAcD,GACvB6U,QAAwB/S,EAASoK,cAAcpd,GAC/C2kB,EAAmB,MAATzT,EACV8U,EAAUrB,EAAU,MAAQ,OAC5BsB,EAAUtB,EAAU,SAAW,QAC/BuB,EAAavB,EAAU,eAAiB,cACxCwB,EAAUlT,EAAMf,UAAUvW,GAAUsX,EAAMf,UAAUhB,GAAQyB,EAAOzB,GAAQ+B,EAAMd,SAASxW,GAC1FyqB,EAAYzT,EAAOzB,GAAQ+B,EAAMf,UAAUhB,GAC3CmV,QAAuD,MAA5BrT,EAASe,qBAA0B,EAASf,EAASe,gBAAgB/T,IACtG,IAAIsmB,EAAaD,EAAoBA,EAAkBH,GAAc,EAGhEI,SAA6C,MAAtBtT,EAASW,eAAoB,EAASX,EAASW,UAAU0S,MACnFC,EAAapT,EAASf,SAAS+T,IAAejT,EAAMd,SAASxW,IAEzD,MAAA4qB,EAAoBJ,EAAU,EAAIC,EAAY,EAI9CI,EAAyBF,EAAa,EAAIP,EAAgBpqB,GAAU,EAAI,EACxE8qB,EAAahX,EAAI+D,EAAcwS,GAAUQ,GACzCE,EAAajX,EAAI+D,EAAcyS,GAAUO,GAIzCG,EAAQF,EACR9W,EAAM2W,EAAaP,EAAgBpqB,GAAU+qB,EAC7CE,EAASN,EAAa,EAAIP,EAAgBpqB,GAAU,EAAI4qB,EACxDhG,EAAS7P,EAAMiW,EAAOC,EAAQjX,GAM9BkX,GAAmBlG,EAAeQ,OAAoC,MAA3BnQ,GAAaF,IAAsB8V,IAAWrG,GAAUtN,EAAMf,UAAUvW,GAAU,GAAKirB,EAASD,EAAQF,EAAaC,GAAcX,EAAgBpqB,GAAU,EAAI,EAC5MylB,EAAkByF,EAAkBD,EAASD,EAAQC,EAASD,EAAQC,EAASjX,EAAM,EACpF,MAAA,CACLuB,CAACA,GAAOyB,EAAOzB,GAAQkQ,EACvBnE,KAAM,CACJ/L,CAACA,GAAOqP,EACRuG,aAAcF,EAASrG,EAASa,KAC5ByF,GAAmB,CACrBzF,oBAGJ6C,MAAO4C,EAEb,IEubME,GFkKa,SAAUjU,GAIpB,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACLA,UACA,EAAA0N,CAAG1W,GACK,MAAAkG,EACJA,EAAAC,EACAA,EAAAa,UACAA,EAAAmC,MACAA,EAAA0N,eACAA,GACE7W,GAEFyW,OAAAA,EAAS,EACTS,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,GAC1B5Q,GAASmC,EAAShJ,GAChB6I,EAAS,CACb3C,IACAC,KAEIgR,EAAY7P,GAAYN,GACxBkQ,EAAW/P,GAAgBgQ,GAC7B,IAAAS,EAAgB/O,EAAOqO,GACvBW,EAAiBhP,EAAOsO,GACtB,MAAA+F,EAAYrW,GAAS4P,EAAQzW,GAC7Bmd,EAAsC,iBAAdD,EAAyB,CACrDhG,SAAUgG,EACV/F,UAAW,GACT,CACFD,SAAU,EACVC,UAAW,KACR+F,GAEL,GAAI1F,EAAe,CACX,MAAA4F,EAAmB,MAAblG,EAAmB,SAAW,QACpCmG,EAAWlU,EAAMf,UAAU8O,GAAY/N,EAAMd,SAAS+U,GAAOD,EAAejG,SAC5EoG,EAAWnU,EAAMf,UAAU8O,GAAY/N,EAAMf,UAAUgV,GAAOD,EAAejG,SAC/EU,EAAgByF,EACFzF,EAAAyF,EACPzF,EAAgB0F,IACT1F,EAAA0F,EAE1B,CACM,GAAI7F,EAAgB,CAClB,IAAId,EAAuB4G,EACrB,MAAAH,EAAmB,MAAblG,EAAmB,QAAU,SACnCsG,EAAe,CAAC,MAAO,QAAQhc,SAASuF,GAAQC,IAChDqW,EAAWlU,EAAMf,UAAU+O,GAAahO,EAAMd,SAAS+U,IAAQI,IAAmE,OAAlD7G,EAAwBE,EAAeJ,aAAkB,EAASE,EAAsBQ,KAAmB,IAAMqG,EAAe,EAAIL,EAAehG,WACnOmG,EAAWnU,EAAMf,UAAU+O,GAAahO,EAAMf,UAAUgV,IAAQI,EAAe,GAAyD,OAAnDD,EAAyB1G,EAAeJ,aAAkB,EAAS8G,EAAuBpG,KAAe,IAAMqG,EAAeL,EAAehG,UAAY,GAChPU,EAAiBwF,EACFxF,EAAAwF,EACRxF,EAAiByF,IACTzF,EAAAyF,EAE3B,CACa,MAAA,CACLpG,CAACA,GAAWU,EACZT,CAACA,GAAYU,EAErB,EAEA,EE5NM4F,GAAkB,CAACrV,EAAWC,EAAUW,KAItC,MAAAmJ,MAAYuL,IACZC,EAAgB,CACpBzU,eACGF,GAEC4U,EAAoB,IACrBD,EAAczU,SACjB4J,GAAIX,GAEC,MF9qBerJ,OAAOV,EAAWC,EAAUwV,KAC5C,MAAA7W,UACJA,EAAY,SAAAqC,SACZA,EAAW,WAAAyU,WACXA,EAAa,GACb5U,SAAAA,GACE2U,EACEE,EAAkBD,EAAW3mB,OAAOC,SACpC+Q,QAA+B,MAAlBe,EAASuK,WAAgB,EAASvK,EAASuK,MAAMpL,IAChE,IAAAc,QAAcD,EAASgK,gBAAgB,CACzC9K,YACAC,WACAgB,cAEEnD,EACFA,EAAAC,EACAA,GACE8B,GAA2BkB,EAAOnC,EAAWmB,GAC7C6V,EAAoBhX,EACpB6P,EAAiB,CAAE,EACnBoH,EAAa,EACjB,IAAA,IAASrsB,EAAI,EAAGA,EAAImsB,EAAgBlsB,OAAQD,IAAK,CACzC,MAAAyM,KACJA,EAAAqY,GACAA,GACEqH,EAAgBnsB,IAElBsU,EAAGgY,EACH/X,EAAGgY,EAAAhL,KACHA,EAAAgH,MACAA,SACQzD,EAAG,CACXxQ,IACAC,IACAgS,iBAAkBnR,EAClBA,UAAWgX,EACX3U,WACAwN,iBACA1N,QACAD,SAAAA,EACAE,SAAU,CACRhB,YACAC,cAGAnC,EAAS,MAATgY,EAAgBA,EAAQhY,EACxBC,EAAS,MAATgY,EAAgBA,EAAQhY,EACX0Q,EAAA,IACZA,EACHxY,CAACA,GAAO,IACHwY,EAAexY,MACf8U,IAGHgH,GAAS8D,GAAc,KACzBA,IACqB,iBAAV9D,IACLA,EAAMnT,YACRgX,EAAoB7D,EAAMnT,WAExBmT,EAAMhR,QACRA,GAAwB,IAAhBgR,EAAMhR,YAAuBD,EAASgK,gBAAgB,CAC5D9K,YACAC,WACAgB,aACG8Q,EAAMhR,SAGXjD,IACAC,KACE8B,GAA2BkB,EAAO6U,EAAmB7V,KAEvDvW,GAAA,EAEV,CACS,MAAA,CACLsU,IACAC,IACAa,UAAWgX,EACX3U,WACAwN,iBACD,EE6lBMuH,CAAkBhW,EAAWC,EAAU,IACzCsV,EACHzU,SAAU0U,GACX,EC7uBH,IAAI5qB,GAA4B,oBAAbwH,SAA2B4E,EAAeA,gBAAGif,EAASA,UAIzE,SAASC,GAAU3K,EAAGC,GACpB,GAAID,IAAMC,EACD,OAAA,EAEL,UAAOD,UAAaC,EACf,OAAA,EAEL,GAAa,mBAAND,GAAoBA,EAAErR,aAAesR,EAAEtR,WACzC,OAAA,EAEL,IAAAzQ,EACAD,EACA2sB,EACJ,GAAI5K,GAAKC,GAAkB,iBAAND,EAAgB,CAC/B,GAAA9Y,MAAMkK,QAAQ4O,GAAI,CAEhB,GADJ9hB,EAAS8hB,EAAE9hB,OACPA,IAAW+hB,EAAE/hB,OAAe,OAAA,EAC3B,IAAAD,EAAIC,EAAgB,IAARD,KACX,IAAC0sB,GAAU3K,EAAE/hB,GAAIgiB,EAAEhiB,IACd,OAAA,EAGJ,OAAA,CACb,CAGI,GAFO2sB,EAAAhrB,OAAOgrB,KAAK5K,GACnB9hB,EAAS0sB,EAAK1sB,OACVA,IAAW0B,OAAOgrB,KAAK3K,GAAG/hB,OACrB,OAAA,EAEJ,IAAAD,EAAIC,EAAgB,IAARD,KACX,IAAC,CAAE,EAAC4sB,eAAevb,KAAK2Q,EAAG2K,EAAK3sB,IAC3B,OAAA,EAGN,IAAAA,EAAIC,EAAgB,IAARD,KAAY,CACrB,MAAA0L,EAAMihB,EAAK3sB,GACb,IAAQ,WAAR0L,IAAoBqW,EAAE8K,YAGrBH,GAAU3K,EAAErW,GAAMsW,EAAEtW,IAChB,OAAA,CAEf,CACW,OAAA,CACX,CACS,OAAAqW,GAAMA,GAAKC,GAAMA,CAC1B,CAEA,SAAS8K,GAAOxoB,GACV,GAAkB,oBAAXoC,OACF,OAAA,EAGT,OADYpC,EAAQoE,cAAc+G,aAAe/I,QACtCqmB,kBAAoB,CACjC,CAEA,SAASC,GAAW1oB,EAAS/E,GACrB,MAAA0tB,EAAMH,GAAOxoB,GACnB,OAAO0P,KAAKE,MAAM3U,EAAQ0tB,GAAOA,CACnC,CAEA,SAASC,GAAa3tB,GACd,MAAAD,EAAM6H,EAAYC,OAAC7H,GAIlB,OAHP6B,IAAM,KACJ9B,EAAIE,QAAUD,CAAA,IAETD,CACT,CAoKA,MAAM6tB,GAAqB/V,IAIlB,CACL3K,KAAM,QACN2K,UACA,EAAA0N,CAAG1W,GACK,MAAA9J,QACJA,EAAAgO,QACAA,GACqB,mBAAZ8E,EAAyBA,EAAQhJ,GAASgJ,EACjD,OAAA9S,IAXO/E,EAWU+E,EAVhB,CAAE,EAACsoB,eAAevb,KAAK9R,EAAO,YAWV,MAAnB+E,EAAQ9E,QACH4tB,GAAQ,CACb9oB,QAASA,EAAQ9E,QACjB8S,YACCwS,GAAG1W,GAED,CAAE,EAEP9J,EACK8oB,GAAQ,CACb9oB,UACAgO,YACCwS,GAAG1W,GAED,CAAE,EA1Bb,IAAe7O,CA2BjB,IAqBMomB,GAAQ,CAACvO,EAASiW,KAAU,IAC7BC,GAAQlW,GACXA,QAAS,CAACA,EAASiW,KAMfhC,GAAa,CAACjU,EAASiW,KAAU,IAClCE,GAAanW,GAChBA,QAAS,CAACA,EAASiW,KASfhH,GAAO,CAACjP,EAASiW,KAAU,IAC5BG,GAAOpW,GACVA,QAAS,CAACA,EAASiW,KASf7jB,GAAO,CAAC4N,EAASiW,KAAU,IAC5BI,GAAOrW,GACVA,QAAS,CAACA,EAASiW,KAmBfrD,GAAO,CAAC5S,EAASiW,KAAU,IAC5BK,GAAOtW,GACVA,QAAS,CAACA,EAASiW,KAmBf5H,GAAQ,CAACrO,EAASiW,KAAU,IAC7BF,GAAQ/V,GACXA,QAAS,CAACA,EAASiW,KCxWrB,IACIM,GAAQ1qB,EAAgB+E,YAAC,CAAC1G,EAAO4B,KAC7B,MAAA3B,SAAEA,QAAU6Q,EAAQ,GAAAC,OAAIA,EAAS,KAAMub,GAAetsB,EAC5D,OAA0B+K,EAAAxK,IACxBuE,EAAUynB,IACV,IACKD,EACHtuB,IAAK4D,EACLkP,QACAC,SACAyb,QAAS,YACTC,oBAAqB,OACrBxsB,SAAUD,EAAMiF,QAAUhF,EAA2BM,EAAGA,IAAC,UAAW,CAAEmsB,OAAQ,oBAEjF,IAEHL,GAAM7rB,YAhBK,QAiBX,IAAIgL,GAAO6gB,GCIX,IAAIM,GAAc,UACbC,GAAqBC,IAAqB9tB,EAAmB4tB,KAC7DG,GAAgBC,IAAoBH,GAAoBD,IAOzDK,GAAc,eACdC,GAAetrB,EAAgB+E,YACjC,CAAC1G,EAAO4B,KACN,MAAMsrB,cAAEA,EAAAC,WAAeA,KAAeC,GAAgBptB,EAChDE,EAAU6sB,GAAiBC,GAAaE,GACxClvB,EAAM6H,EAAYC,OAAC,MACnB2B,EAAe7I,EAAgBgD,EAAc5D,GAI5C,OAHP+H,EAAAA,WAAgB,KACd7F,EAAQmtB,gBAAe,MAAAF,OAAA,EAAAA,EAAYjvB,UAAWF,EAAIE,QAAO,IAEpDivB,EAAa,KAAuB5sB,EAAAA,IAAIuE,EAAUkG,IAAK,IAAKoiB,EAAapvB,IAAKyJ,GAAc,IAGvGwlB,GAAazsB,YAAcwsB,GAC3B,IAAIM,GAAe,iBACdC,GAAuBC,IAAqBZ,GAAoBU,IACjEG,GAAgB9rB,EAAgB+E,YAClC,CAAC1G,EAAO4B,qBACA,MAAAsrB,cACJA,EAAAxY,KACAA,EAAO,SAAAgZ,WACPA,EAAa,EAAAC,MACbA,EAAQ,SAAAC,YACRA,EAAc,EAAAC,aACdA,EAAe,EAAAC,gBACfA,GAAkB,EAAAC,kBAClBA,EAAoB,GACpBC,iBAAkBC,EAAuB,EAAAC,OACzCA,EAAS,UAAAC,iBACTA,GAAmB,EAAAC,uBACnBA,EAAyB,YAAAC,SACzBA,KACGC,GACDtuB,EACEE,EAAU6sB,GAAiBO,GAAcJ,IACxCqB,EAASC,GAAcrnB,EAAAA,SAAe,MACvCM,EAAe7I,EAAgBgD,GAAevD,GAASmwB,EAAWnwB,MACjE8lB,EAAOsK,GAAYtnB,EAAAA,SAAe,MACnCunB,ECrEV,SAAiB1rB,GACf,MAAOkF,EAAMymB,GAAWxnB,EAAAA,cAAe,GA+BhCe,OA9BPgE,GAAgB,KACd,GAAIlJ,EAAS,CACX2rB,EAAQ,CAAE7d,MAAO9N,EAAQgY,YAAajK,OAAQ/N,EAAQiY,eACtD,MAAM4H,EAAiB,IAAI7B,gBAAgBqB,IACzC,IAAK1a,MAAMkK,QAAQwQ,GACjB,OAEE,IAACA,EAAQ1jB,OACX,OAEI,MAAAiwB,EAAQvM,EAAQ,GAClB,IAAAvR,EACAC,EACJ,GAAI,kBAAmB6d,EAAO,CACtB,MAAAC,EAAkBD,EAAqB,cACvCE,EAAannB,MAAMkK,QAAQgd,GAAmBA,EAAgB,GAAKA,EACzE/d,EAAQge,EAAuB,WAC/B/d,EAAS+d,EAAsB,SACzC,MACUhe,EAAQ9N,EAAQgY,YAChBjK,EAAS/N,EAAQiY,aAEX0T,EAAA,CAAE7d,QAAOC,UAAQ,IAGpB,OADP8R,EAAeJ,QAAQzf,EAAS,CAAE+rB,IAAK,eAChC,IAAMlM,EAAeE,UAAU/f,EAC5C,CACM2rB,OAAQ,EACd,GACK,CAAC3rB,IACGkF,CACT,CDoCsB8mB,CAAQ7K,GACpB8K,SAAaP,WAAW5d,QAAS,EACjCoe,SAAcR,WAAW3d,SAAU,EACnCoe,EAAmBza,GAAkB,WAAViZ,EAAqB,IAAMA,EAAQ,IAC9DK,EAAmD,iBAAzBC,EAAoCA,EAAuB,CAAE3a,IAAK,EAAGF,MAAO,EAAGC,OAAQ,EAAGF,KAAM,KAAM8a,GAChI7X,EAAWzO,MAAMkK,QAAQkc,GAAqBA,EAAoB,CAACA,GACnEqB,EAAwBhZ,EAASzX,OAAS,EAC1C8lB,EAAwB,CAC5BzT,QAASgd,EACT5X,SAAUA,EAASnS,OAAOorB,IAE1B9Y,YAAa6Y,IAEThxB,KAAEA,EAAMkxB,eAAAA,EAAAxb,UAAgBA,eAAWyb,EAAc5L,eAAAA,GFF3D,SAAqB7N,QACH,IAAZA,IACFA,EAAU,CAAE,GAER,MAAAhC,UACJA,EAAY,SAAAqC,SACZA,EAAW,WAAAyU,WACXA,EAAa,GACb5U,SAAAA,EACAE,UACEhB,UAAWsa,EACXra,SAAUsa,GACR,CAAE,EAAAC,UACNA,GAAY,EAAAC,qBACZA,EAAAC,KACAA,GACE9Z,GACGmK,EAAM4P,GAAW1oB,WAAe,CACrC6L,EAAG,EACHC,EAAG,EACHkD,WACArC,YACA6P,eAAgB,CAAE,EAClB4L,cAAc,KAETO,EAAkBC,GAAuB5oB,EAAAA,SAAeyjB,GAC1DQ,GAAU0E,EAAkBlF,IAC/BmF,EAAoBnF,GAEtB,MAAOoF,EAAYC,GAAiB9oB,EAAAA,SAAe,OAC5C+oB,EAAWC,GAAgBhpB,EAAAA,SAAe,MAC3CipB,EAAevxB,EAAiBC,aAAST,IACzCA,IAASgyB,EAAanyB,UACxBmyB,EAAanyB,QAAUG,EACvB4xB,EAAc5xB,GACpB,GACK,IACGiyB,EAAczxB,EAAiBC,aAAST,IACxCA,IAASkyB,EAAYryB,UACvBqyB,EAAYryB,QAAUG,EACtB8xB,EAAa9xB,GACnB,GACK,IACG+iB,EAAcoO,GAAqBQ,EACnCQ,EAAaf,GAAoBS,EACjCG,EAAexqB,EAAYC,OAAC,MAC5ByqB,EAAc1qB,EAAYC,OAAC,MAC3B2qB,EAAU5qB,EAAYC,OAACma,GACvByQ,EAAkD,MAAxBf,EAC1BgB,EAA0B/E,GAAa+D,GACvCiB,EAAchF,GAAa5V,GAC3B6a,EAAUjF,GAAagE,GACvBhP,EAAS/hB,EAAAA,aAAkB,KAC/B,IAAKwxB,EAAanyB,UAAYqyB,EAAYryB,QACxC,OAEF,MAAMysB,EAAS,CACb7W,YACAqC,WACAyU,WAAYkF,GAEVc,EAAY1yB,UACdysB,EAAO3U,SAAW4a,EAAY1yB,SAEhBqsB,GAAA8F,EAAanyB,QAASqyB,EAAYryB,QAASysB,GAAQmG,MAAK7Q,IACtE,MAAM8Q,EAAW,IACZ9Q,EAKHsP,cAAkC,IAApBsB,EAAQ3yB,SAEpB8yB,EAAa9yB,UAAYktB,GAAUqF,EAAQvyB,QAAS6yB,KACtDN,EAAQvyB,QAAU6yB,EAClBvrB,EAAAA,WAAmB,KACjBqqB,EAAQkB,EAAQ,IAE1B,GACK,GACA,CAACjB,EAAkBhc,EAAWqC,EAAUya,EAAaC,IACxD/wB,IAAM,MACS,IAAT8vB,GAAkBa,EAAQvyB,QAAQqxB,eACpCkB,EAAQvyB,QAAQqxB,cAAe,EAC/BM,GAAQ5P,IAAS,IACZA,EACHsP,cAAc,MAEtB,GACK,CAACK,IACE,MAAAoB,EAAenrB,EAAYC,QAAC,GAClChG,IAAM,KACJkxB,EAAa9yB,SAAU,EAChB,KACL8yB,EAAa9yB,SAAU,CAAA,IAExB,IACH4B,IAAM,KAGJ,GAFIshB,MAA0BljB,QAAUkjB,GACpCoP,MAAwBtyB,QAAUsyB,GAClCpP,GAAeoP,EAAY,CAC7B,GAAIG,EAAwBzyB,QAC1B,OAAOyyB,EAAwBzyB,QAAQkjB,EAAaoP,EAAY5P,GAE1DA,GACd,IACK,CAACQ,EAAaoP,EAAY5P,EAAQ+P,EAAyBD,IACxD,MAAAtyB,EAAOqB,EAAAA,SAAc,KAAO,CAChCyV,UAAWmb,EACXlb,SAAUob,EACVH,eACAE,iBACE,CAACF,EAAcE,IACbpa,EAAWzW,EAAAA,SAAc,KAAO,CACpCyV,UAAWkM,EACXjM,SAAUqb,KACR,CAACpP,EAAaoP,IACZlB,EAAiB7vB,EAAAA,SAAc,KACnC,MAAMwxB,EAAgB,CACpBrgB,SAAUuF,EACVhD,KAAM,EACNG,IAAK,GAEH,IAAC4C,EAASf,SACL,OAAA8b,EAET,MAAMje,EAAI0Y,GAAWxV,EAASf,SAAU8K,EAAKjN,GACvCC,EAAIyY,GAAWxV,EAASf,SAAU8K,EAAKhN,GAC7C,OAAIyc,EACK,IACFuB,EACHvB,UAAW,aAAe1c,EAAI,OAASC,EAAI,SACvCuY,GAAOtV,EAASf,WAAa,KAAO,CACtC8D,WAAY,cAIX,CACLrI,SAAUuF,EACVhD,KAAMH,EACNM,IAAKL,EACN,GACA,CAACkD,EAAUuZ,EAAWxZ,EAASf,SAAU8K,EAAKjN,EAAGiN,EAAKhN,IAClDxT,OAAAA,EAAaC,SAAC,KAAO,IACvBugB,EACHW,SACAxiB,OACA8X,WACAoZ,oBACE,CAACrP,EAAMW,EAAQxiB,EAAM8X,EAAUoZ,GACrC,CEpJ8E4B,CAAY,CAEpF/a,SAAU,QACVrC,UAAWqb,EACXQ,qBAAsB,IAAI5rB,IACR4c,MAAc5c,EAAM,CAClCod,eAA2C,WAA3BiN,IAIpBlY,SAAU,CACRhB,UAAWhV,EAAQixB,QAErBvG,WAAY,EFsLF9U,EErLD,CAAEkO,SAAU0J,EAAawB,EAAa7Z,cAAeuY,GFqLjC,IAC9BwD,GAAStb,GACZA,QAAS,CAACA,EAASiW,KEtLb+B,GAAmBzJ,GAAM,CACvBL,UAAU,EACVC,WAAW,EACXO,QAAoB,YAAX0J,EAAuBnE,UAAe,KAC5CtF,IAELqJ,GAAmB/I,GAAK,IAAKN,IAC7Bvc,GAAK,IACAuc,EACHiD,MAAO,EAAGxR,WAAUD,QAAOmS,iBAAgBD,sBACzC,MAAQrX,MAAOugB,EAAatgB,OAAQugB,GAAiBrb,EAAMf,UACrDqc,EAAerb,EAASf,SAAS1K,MACvC8mB,EAAaC,YAAY,iCAAkC,GAAGpJ,OAC9DmJ,EAAaC,YAAY,kCAAmC,GAAGrJ,OAC/DoJ,EAAaC,YAAY,8BAA+B,GAAGH,OAC3DE,EAAaC,YAAY,+BAAgC,GAAGF,MAAgB,IAGhFnN,GAASsN,GAAgB,CAAEzuB,QAASmhB,EAAOnT,QAAS6c,IACpD6D,GAAgB,CAAEzC,aAAYC,gBAC9Bf,GAAoBzF,GAAK,CAAEvS,SAAU,qBAAsBsO,OFgKpD,IAAC3O,EAASiW,EE7JrB,MAAO4F,EAAYC,GAAeC,GAA6B/d,GACzDge,EAAepsB,EAAe2oB,GACpCniB,GAAgB,KACVqjB,IACF,MAAAuC,GAAAA,IACR,GACO,CAACvC,EAAcuC,IACZ,MAAAC,EAAS,OAAA3xB,EAAeujB,EAAAQ,YAAO,EAAA/jB,EAAA4S,EAC/Bgf,GAAS,OAAA1uB,EAAeqgB,EAAAQ,YAAO,EAAA7gB,EAAA2P,EAC/Bgf,GAA2D,KAAvC,OAAArS,EAAA+D,EAAeQ,YAAf,EAAAvE,EAAsBkK,eACzCoI,GAAeC,IAAoBhrB,aAI1C,OAHA+E,GAAgB,KACVqiB,GAA0B4D,GAAA/sB,OAAOyJ,iBAAiB0f,GAAS6D,OAAM,GACpE,CAAC7D,IACsBxjB,EAAAxK,IACxB,MACA,CACEvC,IAAKI,EAAKkyB,YACV,oCAAqC,GACrC7lB,MAAO,IACF6kB,EACHI,UAAWH,EAAeD,EAAeI,UAAY,sBAErD2C,SAAU,cACVD,OAAQF,GACR,kCAAqC,CACnC,OAAAI,EAAA3O,EAAe+N,sBAAiB,EAAAY,EAAAtf,EAChC,OAAAwP,EAAAmB,EAAe+N,sBAAiB,EAAAlP,EAAAvP,GAChC9O,KAAK,SAIJ,OAAAouB,EAAA5O,EAAe+E,WAAf,EAAA6J,EAAqB3J,kBAAmB,CACzC4J,WAAY,SACZ9nB,cAAe,SAGnB+nB,IAAKzyB,EAAMyyB,IACXxyB,SAA6B8K,EAAAxK,IAC3BgtB,GACA,CACEhuB,MAAO2tB,EACPyE,aACAe,cAAejE,EACfsD,SACAC,UACAW,gBAAiBV,GACjBhyB,SAA6B8K,EAAAxK,IAC3BuE,EAAUkG,IACV,CACE,YAAa2mB,EACb,aAAcC,KACXtD,EACHtwB,IAAKyJ,EACLgD,MAAO,IACF6jB,EAAa7jB,MAGhBmoB,UAAYrD,OAAwB,EAAT,aAOxC,IAGL9B,GAAcjtB,YAAc8sB,GAC5B,IAAIuF,GAAa,cACbC,GAAgB,CAClBxf,IAAK,SACLF,MAAO,OACPC,OAAQ,MACRF,KAAM,SAEJ4f,GAAcpxB,EAAAA,YAAiB,SAAsB3B,EAAO4B,GAC9D,MAAMsrB,cAAEA,KAAkBZ,GAAetsB,EACnCgzB,EAAiBxF,GAAkBqF,GAAY3F,GAC/C+F,EAAWH,GAAcE,EAAerB,YAC9C,OAIqB5mB,EAAAxK,IACjB,OACA,CACEvC,IAAKg1B,EAAeN,cACpBjoB,MAAO,CACLmG,SAAU,WACVuC,KAAM6f,EAAejB,OACrBze,IAAK0f,EAAehB,OACpBiB,CAACA,GAAW,EACZvB,gBAAiB,CACfpe,IAAK,GACLF,MAAO,MACPC,OAAQ,WACRF,KAAM,UACN6f,EAAerB,YACjBjC,UAAW,CACTpc,IAAK,mBACLF,MAAO,iDACPC,OAAQ,iBACRF,KAAM,kDACN6f,EAAerB,YACjBa,WAAYQ,EAAeL,gBAAkB,cAAW,GAE1D1yB,SAA6B8K,EAAAxK,IAC3B2yB,GACA,IACK5G,EACHtuB,IAAK4D,EACL6I,MAAO,IACF6hB,EAAW7hB,MAEduD,QAAS,YAOvB,IAEA,SAASqhB,GAAUpxB,GACjB,OAAiB,OAAVA,CACT,CAHA80B,GAAYvyB,YAAcqyB,GAI1B,IAAInB,GAAmB5b,IAAa,CAClC3K,KAAM,kBACN2K,UACA,EAAA0N,CAAGvD,aACD,MAAMnM,UAAEA,EAAAmC,MAAWA,EAAO0N,eAAAA,GAAmB1D,EAEvCkT,EAD2D,KAAvC,OAAA/yB,EAAAujB,EAAeQ,YAAf,EAAA/jB,EAAsB0pB,cAE1CmF,EAAakE,EAAgB,EAAIrd,EAAQmZ,WACzCC,EAAciE,EAAgB,EAAIrd,EAAQoZ,aACzCyC,EAAYC,GAAeC,GAA6B/d,GACzDsf,EAAe,CAAE5f,MAAO,KAAMoW,OAAQ,MAAOnW,IAAK,QAASme,GAC3DyB,IAAgB,OAAA/vB,EAAeqgB,EAAAQ,YAAO,EAAA7gB,EAAA0P,IAAK,GAAKic,EAAa,EAC7DqE,IAAgB,OAAA1T,EAAe+D,EAAAQ,YAAO,EAAAvE,EAAA3M,IAAK,GAAKic,EAAc,EACpE,IAAIlc,EAAI,GACJC,EAAI,GAcR,MAbmB,WAAf0e,GACE3e,EAAAmgB,EAAgBC,EAAe,GAAGC,MAClCpgB,GAAIic,EAAJ,MACoB,QAAfyC,GACL3e,EAAAmgB,EAAgBC,EAAe,GAAGC,MACtCpgB,EAAI,GAAGgD,EAAMd,SAASpE,OAASme,OACP,UAAfyC,GACL3e,GAAIkc,EAAJ,KACAjc,EAAAkgB,EAAgBC,EAAe,GAAGE,OACd,SAAf3B,IACT3e,EAAI,GAAGiD,EAAMd,SAASrE,MAAQoe,MAC1Bjc,EAAAkgB,EAAgBC,EAAe,GAAGE,OAEjC,CAAErT,KAAM,CAAEjN,IAAGC,KACxB,IAEA,SAAS4e,GAA6B/d,GACpC,MAAOY,EAAMiZ,EAAQ,UAAY7Z,EAAUC,MAAM,KAC1C,MAAA,CAACW,EAAMiZ,EAChB,CAEA,IAAI4F,GAAStG,GACTuG,GAAU/F,GACVpB,GAAQ0G,IE9QPU,GAAsBC,IAAsB30B,EAAmB,UAAW,CAC7E8tB,KAEE8G,GAAiB9G,KACjB+G,GAAgB,kBAChBC,GAAyB,IACzBC,GAAe,gBACdC,GAAgCC,IAA6BP,GAAqBG,IACnFK,GAAmBj0B,IACf,MAAAk0B,eACJA,EAAAC,cACAA,EAAgBN,GAAAO,kBAChBA,EAAoB,IAAAC,wBACpBA,GAA0B,EAAAp0B,SAC1BA,GACED,EACEs0B,EAAmBzuB,EAAYC,QAAC,GAChCyuB,EAAwB1uB,EAAYC,QAAC,GACrC0uB,EAAoB3uB,EAAYC,OAAC,GAKvC,OAJAC,EAAAA,WAAgB,KACd,MAAM0uB,EAAiBD,EAAkBt2B,QAClC,MAAA,IAAMkH,OAAOgE,aAAaqrB,EAAc,GAC9C,IACuB1pB,EAAAxK,IACxBwzB,GACA,CACEx0B,MAAO20B,EACPI,mBACAH,gBACAO,OAAQ71B,EAAiBC,aAAC,KACjBsG,OAAAgE,aAAaorB,EAAkBt2B,SACtCo2B,EAAiBp2B,SAAU,CAAA,GAC1B,IACHy2B,QAAS91B,EAAiBC,aAAC,KAClBsG,OAAAgE,aAAaorB,EAAkBt2B,SACtCs2B,EAAkBt2B,QAAUkH,OAAO+D,YACjC,IAAMmrB,EAAiBp2B,SAAU,GACjCk2B,EACD,GACA,CAACA,IACJG,wBACAK,yBAA0B/1B,EAAAA,aAAmBg2B,IAC3CN,EAAsBr2B,QAAU22B,CAAA,GAC/B,IACHR,0BACAp0B,YAEH,EAEHg0B,GAAgBzzB,YAAcozB,GAC9B,IAAIkB,GAAe,WACdC,GAAwBC,IAAqBvB,GAAqBqB,IA4FnEG,GAAe,iBACEtzB,EAAgB+E,YACnC,CAAC1G,EAAO4B,KACN,MAAMsyB,eAAEA,KAAmBgB,GAAiBl1B,EACtCE,EAAU80B,GAAkBC,GAAcf,GAC1CiB,EAAkBnB,GAA0BiB,GAAcf,GAC1DkB,EAAczB,GAAeO,GAE7BzsB,EAAe7I,EAAgBgD,EADzBiE,EAAYC,OAAC,MAC+B5F,EAAQm1B,iBAC1DC,EAAmBzvB,EAAYC,QAAC,GAChCyvB,EAA0B1vB,EAAYC,QAAC,GACvC0vB,EAAkB32B,EAAAA,aAAkB,IAAMy2B,EAAiBp3B,SAAU,GAAO,IAI3DqC,OAHvBwF,EAAAA,WAAgB,IACP,IAAMuB,SAASyB,oBAAoB,YAAaysB,IACtD,CAACA,IACmBj1B,EAAAA,IAAIk1B,GAAwB,CAAExwB,SAAS,KAASmwB,EAAan1B,SAA6B8K,EAAAxK,IAC/GuE,EAAU4wB,OACV,CACE,mBAAoBx1B,EAAQ0vB,KAAO1vB,EAAQy1B,eAAY,EACvD,aAAcz1B,EAAQ01B,kBACnBV,EACHl3B,IAAKyJ,EACLouB,cAAep4B,EAAqBuC,EAAM61B,eAAgBh4B,IAC9B,UAAtBA,EAAMiL,cACLysB,EAAwBr3B,SAAYi3B,EAAgBZ,sBAAsBr2B,UAC7EgC,EAAQ41B,iBACRP,EAAwBr3B,SAAU,GAC9C,IAEQ63B,eAAgBt4B,EAAqBuC,EAAM+1B,gBAAgB,KACzD71B,EAAQ81B,iBACRT,EAAwBr3B,SAAU,CAAA,IAEpC+3B,cAAex4B,EAAqBuC,EAAMi2B,eAAe,KACnD/1B,EAAQ0vB,MACV1vB,EAAQy0B,UAEVW,EAAiBp3B,SAAU,EAC3BoJ,SAAS0B,iBAAiB,YAAawsB,EAAiB,CAAEvsB,MAAM,GAAM,IAExEitB,QAASz4B,EAAqBuC,EAAMk2B,SAAS,KACtCZ,EAAiBp3B,SAASgC,EAAQw0B,QAAQ,IAEjDyB,OAAQ14B,EAAqBuC,EAAMm2B,OAAQj2B,EAAQy0B,SACnDyB,QAAS34B,EAAqBuC,EAAMo2B,QAASl2B,EAAQy0B,YAEtD,IAGQn0B,YAAcy0B,GAC7B,IACKoB,GAAgBC,IAAoB7C,GADvB,gBACyD,CACzE8C,gBAAY,IAQVjJ,GAAe,iBACfkJ,GAAiB70B,EAAgB+E,YACnC,CAAC1G,EAAO4B,KACN,MAAM60B,EAAgBH,GAAiBhJ,GAActtB,EAAMk0B,iBACrDqC,WAAEA,EAAaE,EAAcF,WAAA7hB,KAAYA,EAAO,SAAU4Z,GAAiBtuB,EAC3EE,EAAU80B,GAAkB1H,GAActtB,EAAMk0B,gBACtD,OAA0BnpB,EAAAxK,IAAC8L,EAAU,CAAEC,QAASiqB,GAAcr2B,EAAQ0vB,KAAM3vB,SAAUC,EAAQm0B,wBAA6CtpB,EAAAxK,IAACm2B,GAAoB,CAAEhiB,UAAS4Z,EAActwB,IAAK4D,MAAqCrB,IAACo2B,GAAyB,CAAEjiB,UAAS4Z,EAActwB,IAAK4D,KAAiB,IAG5S+0B,GAA0Bh1B,EAAgB+E,YAAC,CAAC1G,EAAO4B,KACrD,MAAM1B,EAAU80B,GAAkB1H,GAActtB,EAAMk0B,gBAChDiB,EAAkBnB,GAA0B1G,GAActtB,EAAMk0B,gBAChEl2B,EAAM6H,EAAYC,OAAC,MACnB2B,EAAe7I,EAAgBgD,EAAc5D,IAC5C44B,EAAkBC,GAAuB1vB,EAAAA,SAAe,OACzD2vB,QAAEA,EAASnC,QAAAA,GAAYz0B,EACvBquB,EAAUvwB,EAAIE,SACd02B,yBAAEA,GAA6BO,EAC/B4B,EAAwBl4B,EAAAA,aAAkB,KAC9Cg4B,EAAoB,MACpBjC,GAAyB,EAAK,GAC7B,CAACA,IACEoC,EAAwBn4B,EAAiBC,aAC7C,CAACjB,EAAOo5B,KACN,MAAMC,EAAgBr5B,EAAMq5B,cACtBC,EAAY,CAAEnkB,EAAGnV,EAAMu5B,QAASnkB,EAAGpV,EAAMw5B,SAEzCC,EA6IZ,SAA6BH,EAAWI,EAAUvmB,EAAU,GAC1D,MAAMsmB,EAAmB,GACzB,OAAQC,GACN,IAAK,MACcD,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAE/C,MACF,IAAK,SACcsmB,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAE/C,MACF,IAAK,OACcsmB,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAE/C,MACF,IAAK,QACcsmB,EAAA1R,KACf,CAAE5S,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,GAC7C,CAAEgC,EAAGmkB,EAAUnkB,EAAIhC,EAASiC,EAAGkkB,EAAUlkB,EAAIjC,IAI5C,OAAAsmB,CACT,CA1K+BE,CAAoBL,EA2HnD,SAA6BM,EAAO3iB,GAClC,MAAMxB,EAAMZ,KAAKglB,IAAI5iB,EAAKxB,IAAMmkB,EAAMxkB,GAChCI,EAASX,KAAKglB,IAAI5iB,EAAKzB,OAASokB,EAAMxkB,GACtCG,EAAQV,KAAKglB,IAAI5iB,EAAK1B,MAAQqkB,EAAMzkB,GACpCG,EAAOT,KAAKglB,IAAI5iB,EAAK3B,KAAOskB,EAAMzkB,GACxC,OAAQN,KAAKD,IAAIa,EAAKD,EAAQD,EAAOD,IACnC,KAAKA,EACI,MAAA,OACT,KAAKC,EACI,MAAA,QACT,KAAKE,EACI,MAAA,MACT,KAAKD,EACI,MAAA,SACT,QACQ,MAAA,IAAIzS,MAAM,eAEtB,CA7IuB+2B,CAAoBR,EAAWD,EAAc5b,0BAGxDsc,EAiMZ,SAAiBlL,GACT,MAAAmL,EAAYnL,EAAO5kB,QAQzB,OAPU+vB,EAAAzQ,MAAK,CAAC3G,EAAGC,IACbD,EAAEzN,EAAI0N,EAAE1N,GAAU,EACbyN,EAAEzN,EAAI0N,EAAE1N,EAAU,EAClByN,EAAExN,EAAIyN,EAAEzN,GAAU,EAClBwN,EAAExN,EAAIyN,EAAEzN,EAAU,EACf,IAIhB,SAA0ByZ,GACxB,GAAIA,EAAO/tB,QAAU,EAAG,OAAO+tB,EAAO5kB,QACtC,MAAMgwB,EAAY,GAClB,IAAA,IAASp5B,EAAI,EAAGA,EAAIguB,EAAO/tB,OAAQD,IAAK,CAChC,MAAAq5B,EAAIrL,EAAOhuB,GACV,KAAAo5B,EAAUn5B,QAAU,GAAG,CAC5B,MAAMq5B,EAAIF,EAAUA,EAAUn5B,OAAS,GACjC6S,EAAIsmB,EAAUA,EAAUn5B,OAAS,GACvC,MAAKq5B,EAAEhlB,EAAIxB,EAAEwB,IAAM+kB,EAAE9kB,EAAIzB,EAAEyB,KAAO+kB,EAAE/kB,EAAIzB,EAAEyB,IAAM8kB,EAAE/kB,EAAIxB,EAAEwB,IACnD,QADiEilB,KAE5E,CACIH,EAAUlS,KAAKmS,EACnB,CACED,EAAUG,MACV,MAAMC,EAAY,GAClB,IAAA,IAASx5B,EAAIguB,EAAO/tB,OAAS,EAAGD,GAAK,EAAGA,IAAK,CACrC,MAAAq5B,EAAIrL,EAAOhuB,GACV,KAAAw5B,EAAUv5B,QAAU,GAAG,CAC5B,MAAMq5B,EAAIE,EAAUA,EAAUv5B,OAAS,GACjC6S,EAAI0mB,EAAUA,EAAUv5B,OAAS,GACvC,MAAKq5B,EAAEhlB,EAAIxB,EAAEwB,IAAM+kB,EAAE9kB,EAAIzB,EAAEyB,KAAO+kB,EAAE/kB,EAAIzB,EAAEyB,IAAM8kB,EAAE/kB,EAAIxB,EAAEwB,IACnD,QADiEilB,KAE5E,CACIC,EAAUtS,KAAKmS,EACnB,CAEM,OADJG,EAAUD,MACe,IAArBH,EAAUn5B,QAAqC,IAArBu5B,EAAUv5B,QAAgBm5B,EAAU,GAAG9kB,IAAMklB,EAAU,GAAGllB,GAAK8kB,EAAU,GAAG7kB,IAAMilB,EAAU,GAAGjlB,EACpH6kB,EAEAA,EAAUrd,OAAOyd,EAE5B,CAjCSC,CAAiBN,EAC1B,CA3MwBO,CAAQ,IAAId,KAyKpC,SAA2BxiB,GACzB,MAAMxB,IAAEA,EAAAF,MAAKA,EAAOC,OAAAA,EAAAF,KAAQA,GAAS2B,EAC9B,MAAA,CACL,CAAE9B,EAAGG,EAAMF,EAAGK,GACd,CAAEN,EAAGI,EAAOH,EAAGK,GACf,CAAEN,EAAGI,EAAOH,EAAGI,GACf,CAAEL,EAAGG,EAAMF,EAAGI,GAElB,CAlLgCglB,CAAkBpB,EAAY3b,2BAExDub,EAAoBe,GACpBhD,GAAyB,EAAI,GAE/B,CAACA,IAmCoBr0B,OAjCvBwF,EAAAA,WAAgB,IACP,IAAMgxB,KACZ,CAACA,IACJhxB,EAAAA,WAAgB,KACd,GAAI+wB,GAAWvI,EAAS,CACtB,MAAM+J,EAAsBz6B,GAAUm5B,EAAsBn5B,EAAO0wB,GAC7DgK,EAAsB16B,GAAUm5B,EAAsBn5B,EAAOi5B,GAGnE,OAFQA,EAAA9tB,iBAAiB,eAAgBsvB,GACjC/J,EAAAvlB,iBAAiB,eAAgBuvB,GAClC,KACGzB,EAAA/tB,oBAAoB,eAAgBuvB,GACpC/J,EAAAxlB,oBAAoB,eAAgBwvB,EAAkB,CAEtE,IACK,CAACzB,EAASvI,EAASyI,EAAuBD,IAC7ChxB,EAAAA,WAAgB,KACd,GAAI6wB,EAAkB,CACd,MAAA4B,EAA2B36B,IAC/B,MAAM0H,EAAS1H,EAAM0H,OACfkzB,EAAkB,CAAEzlB,EAAGnV,EAAMu5B,QAASnkB,EAAGpV,EAAMw5B,SAC/CqB,GAAmB,MAAA5B,OAAA,EAAAA,EAASptB,SAASnE,YAAWgpB,WAAS7kB,SAASnE,IAClEozB,GAuJd,SAA0BlB,EAAOmB,GACzB,MAAA5lB,EAAEA,EAAGC,EAAAA,GAAMwkB,EACjB,IAAIoB,GAAS,EACJ,IAAA,IAAAn6B,EAAI,EAAGo6B,EAAIF,EAAQj6B,OAAS,EAAGD,EAAIk6B,EAAQj6B,OAAQm6B,EAAIp6B,IAAK,CAC7D,MAAAq6B,EAAKH,EAAQl6B,GACbs6B,EAAKJ,EAAQE,GACbG,EAAKF,EAAG/lB,EACRkmB,EAAKH,EAAG9lB,EACRkmB,EAAKH,EAAGhmB,EACRomB,EAAKJ,EAAG/lB,EACIimB,EAAKjmB,GAAMmmB,EAAKnmB,GAAKD,GAAKmmB,EAAKF,IAAOhmB,EAAIimB,IAAOE,EAAKF,GAAMD,OACrDJ,EAC7B,CACS,OAAAA,CACT,CArK2CQ,CAAiBZ,EAAiB7B,GACjE8B,EACqB3B,IACd4B,IACc5B,IACdpC,IACnB,EAGM,OADSrtB,SAAA0B,iBAAiB,cAAewvB,GAClC,IAAMlxB,SAASyB,oBAAoB,cAAeyvB,EAC/D,IACK,CAAC1B,EAASvI,EAASqI,EAAkBjC,EAASoC,IAC1Bx2B,EAAAA,IAAIm2B,GAAoB,IAAK12B,EAAOhC,IAAKyJ,GAAc,KAE3E6xB,GAAsCC,IAAmC9F,GAAqBqB,GAAc,CAAE0E,UAAU,IACzHC,KAA4B,kBAC5B/C,GAAqB/0B,EAAgB+E,YACvC,CAAC1G,EAAO4B,KACA,MAAAsyB,eACJA,EAAAj0B,SACAA,EACA,aAAcy5B,EAAA9yB,gBACdA,EAAAC,qBACAA,KACGynB,GACDtuB,EACEE,EAAU80B,GAAkB1H,GAAc4G,GAC1CkB,EAAczB,GAAeO,IAC7BS,QAAEA,GAAYz0B,EAepB,OAdA6F,EAAAA,WAAgB,KACLuB,SAAA0B,iBAAiB8qB,GAAca,GACjC,IAAMrtB,SAASyB,oBAAoB+qB,GAAca,KACvD,CAACA,IACJ5uB,EAAAA,WAAgB,KACd,GAAI7F,EAAQ42B,QAAS,CACb,MAAA6C,EAAgB97B,IACpB,MAAM0H,EAAS1H,EAAM0H,QACT,MAARA,OAAQ,EAAAA,EAAAmE,SAASxJ,EAAQ42B,WAAmBnC,GAAA,EAG3C,OADPvvB,OAAO4D,iBAAiB,SAAU2wB,EAAc,CAAEtvB,SAAS,IACpD,IAAMjF,OAAO2D,oBAAoB,SAAU4wB,EAAc,CAAEtvB,SAAS,GACnF,IACO,CAACnK,EAAQ42B,QAASnC,IACK5pB,EAAAxK,IACxBkG,EACA,CACExB,SAAS,EACT0B,6BAA6B,EAC7BC,kBACAC,uBACAC,eAAiBjJ,GAAUA,EAAM0M,iBACjCvD,UAAW2tB,EACX10B,SAA8B8K,EAAA6uB,KAC5BC,GACA,CACE,aAAc35B,EAAQ01B,kBACnBR,KACA9G,EACHtwB,IAAK4D,EACL6I,MAAO,IACF6jB,EAAa7jB,MAGd,2CAA4C,uCAC5C,0CAA2C,sCAC3C,2CAA4C,uCAC5C,gCAAiC,mCACjC,iCAAkC,qCAGtCxK,SAAU,OACYw5B,GAAW,CAAEx5B,aACjBM,MAAI+4B,GAAsC,CAAE/5B,MAAO20B,EAAgBsF,UAAU,EAAMv5B,eAA8B65B,EAA8B,CAAE1nB,GAAIlS,EAAQy1B,UAAWoE,KAAM,UAAW95B,SAAUy5B,GAAaz5B,UAKzO,IAGLu2B,GAAeh2B,YAAc8sB,GAC7B,IAAIuF,GAAa,eACElxB,EAAgB+E,YACjC,CAAC1G,EAAO4B,KACN,MAAMsyB,eAAEA,KAAmB5H,GAAetsB,EACpCo1B,EAAczB,GAAeO,GAKnC,OAJqCqF,GACnC1G,GACAqB,GAEkCsF,SAAW,KAAuBj5B,EAAGA,IAACy5B,GAAuB,IAAK5E,KAAgB9I,EAAYtuB,IAAK4D,GAAc,IAG5IpB,YAAcqyB,GAoHxB,IAAC9yB,GAAWk0B,GAIXgG,GAAWzD", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}
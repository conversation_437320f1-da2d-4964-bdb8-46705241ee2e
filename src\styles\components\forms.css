/**
 * 🎯 COMPONENTES DE FORMULÁRIO
 * 
 * Estilos para inputs, selects, textareas
 * Estados de validação
 */

/* ===== FORM BASE ===== */
.form {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-input {
  padding: var(--space-sm) var(--space-md);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius-md);
  background: var(--color-surface);
  color: var(--color-text);
  font-size: var(--font-size-base);
  transition: var(--transition);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-focus-ring);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* ===== VALIDATION STATES ===== */
.form-input.error {
  border-color: var(--color-error);
}

.form-input.success {
  border-color: var(--color-success);
}

.form-error {
  font-size: var(--font-size-sm);
  color: var(--color-error);
}

.form-success {
  font-size: var(--font-size-sm);
  color: var(--color-success);
}

export default {
  title: "Ciclo de Backlogs Estratégicos",
  description: "Demonstração prática de como transformo desafios de negócio em soluções de UX mensuráveis. Cada caso apresenta metodologia aplicada, resultados alcançados e insights estratégicos que geram impacto real para stakeholders e usuários.",
  solution: "Solução",
  result: "Resultado",
  note: "Nota",
  noItems: "Nenhum item nesta página.",
  previous: "Anterior",
  next: "Próxima",
  items: [
    {
      challenge: "Usuários abandonavam formulários longos sem completar o cadastro",
      solution: "Implementei formulário em etapas com barra de progresso e validação em tempo real",
      result: "Aumento de 40% na taxa de conclusão de cadastros em 2 semanas",
      note: "Dividir tarefas complexas em etapas menores reduz a ansiedade e melhora a experiência."
    },
    {
      challenge: "Baixa adesão a recursos premium devido à falta de clareza sobre benefícios",
      solution: "Criei onboarding interativo com demonstrações práticas dos recursos premium",
      result: "Crescimento de 60% nas conversões para planos pagos em 1 mês",
      note: "Mostrar valor através de experiência prática é mais eficaz que apenas listar funcionalidades."
    },
    {
      challenge: "Usuários não encontravam facilmente o suporte quando precisavam de ajuda",
      solution: "Redesenhei sistema de ajuda contextual com chatbot inteligente e FAQ dinâmico",
      result: "Redução de 50% nos tickets de suporte e aumento de 35% na satisfação",
      note: "Ajuda contextual no momento certo previne frustrações e melhora a autonomia do usuário."
    },
    {
      challenge: "Interface complexa causava confusão em usuários iniciantes",
      solution: "Desenvolvi modo simplificado com tutorial progressivo e tooltips adaptativos",
      result: "Diminuição de 45% na taxa de abandono de novos usuários",
      note: "Adaptar a complexidade ao nível de experiência do usuário melhora significativamente a adoção."
    },
    {
      challenge: "Processo de checkout tinha alta taxa de abandono no último passo",
      solution: "Simplifiquei fluxo removendo campos desnecessários e adicionando opções de pagamento express",
      result: "Aumento de 30% na conclusão de compras e redução de 25% no tempo de checkout",
      note: "Cada campo extra no checkout é uma barreira potencial. Simplicidade gera conversão."
    },
    {
      challenge: "Usuários não percebiam atualizações importantes do produto",
      solution: "Implementei sistema de notificações in-app com design não intrusivo e personalização",
      result: "Melhoria de 55% no engajamento com novas funcionalidades",
      note: "Comunicação eficaz sobre mudanças mantém usuários informados sem interromper o fluxo."
    },
    {
      challenge: "Dificuldade para encontrar conteúdo relevante em base de conhecimento extensa",
      solution: "Criei sistema de busca inteligente com filtros contextuais e sugestões automáticas",
      result: "Aumento de 70% na utilização da base de conhecimento e redução de 40% em consultas repetitivas",
      note: "Busca eficiente transforma informação abundante em conhecimento acessível."
    },
    {
      challenge: "Baixo engajamento em funcionalidades colaborativas da plataforma",
      solution: "Redesenhei interface de colaboração com indicadores visuais de atividade e gamificação sutil",
      result: "Crescimento de 80% na colaboração entre usuários em 6 semanas",
      note: "Tornar a colaboração visível e recompensadora incentiva naturalmente a participação."
    },
    {
      challenge: "Usuários perdiam progresso ao navegar entre seções do aplicativo",
      solution: "Implementei sistema de auto-save com indicadores visuais de status de salvamento",
      result: "Eliminação de 95% das reclamações sobre perda de dados",
      note: "Confiança na tecnologia cresce quando o usuário vê que seu trabalho está sempre protegido."
    },
    {
      challenge: "Interface não responsiva causava frustração em dispositivos móveis",
      solution: "Desenvolvi versão mobile-first com gestos intuitivos e navegação otimizada para toque",
      result: "Aumento de 120% no uso mobile e melhoria de 4.2 para 4.7 na avaliação da app store",
      note: "Design mobile-first garante experiência consistente independente do dispositivo usado."
    },
    {
      challenge: "Usuários com deficiência visual enfrentavam barreiras de acessibilidade",
      solution: "Implementei padrões WCAG 2.1 com navegação por teclado e compatibilidade com leitores de tela",
      result: "Aumento de 200% no uso por pessoas com deficiência e reconhecimento em prêmio de acessibilidade",
      note: "Acessibilidade não é apenas compliance — é design inclusivo que beneficia todos os usuários."
    },
    {
      challenge: "Textos técnicos confundiam usuários não especializados",
      solution: "Reescrevi microcopy com linguagem clara e adicionei explicações contextuais quando necessário",
      result: "Redução de 60% em dúvidas de usuários e melhoria na percepção de facilidade de uso",
      note: "Pequenas decisões no texto têm grande impacto na experiência de leitura e compreensão."
    }
  ]
};

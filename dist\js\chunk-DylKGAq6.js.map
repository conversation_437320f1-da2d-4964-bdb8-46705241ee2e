{"version": 3, "file": "chunk-DylKGAq6.js", "sources": ["../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../node_modules/@tanstack/query-core/build/modern/query.js", "../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../node_modules/@tanstack/query-core/build/modern/queryClient.js", "../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"], "sourcesContent": ["// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\nexport {\n  Subscribable\n};\n//# sourceMappingURL=subscribable.js.map", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport {\n  addToEnd,\n  addToStart,\n  ensureQueryFn,\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  isPlainArray,\n  isPlainObject,\n  isServer,\n  isValidTimeout,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceData,\n  replaceEqualDeep,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  shouldThrowError,\n  skipToken,\n  sleep,\n  timeUntilStale\n};\n//# sourceMappingURL=utils.js.map", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport {\n  FocusManager,\n  focusManager\n};\n//# sourceMappingURL=focusManager.js.map", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport {\n  OnlineManager,\n  onlineManager\n};\n//# sourceMappingURL=onlineManager.js.map", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport {\n  CancelledError,\n  canFetch,\n  createRetryer,\n  isCancelledError\n};\n//# sourceMappingURL=retryer.js.map", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  })?.catch(noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\nexport {\n  pendingThenable,\n  tryResolveSync\n};\n//# sourceMappingURL=thenable.js.map", "// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport {\n  createNotifyManager,\n  defaultScheduler,\n  notifyManager\n};\n//# sourceMappingURL=notifyManager.js.map", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport {\n  Removable\n};\n//# sourceMappingURL=removable.js.map", "// src/query.ts\nimport {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  skipToken,\n  timeUntilStale\n} from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const queryFnContext = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(\n      context,\n      this\n    );\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport {\n  Query,\n  fetchState\n};\n//# sourceMappingURL=query.js.map", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport {\n  Mutation,\n  getDefaultState\n};\n//# sourceMappingURL=mutation.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const queryFnContext = {\n            client: context.client,\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? \"backward\" : \"forward\",\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const page = await queryFn(\n            queryFnContext\n          );\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map", "\"use client\";\n\n// src/QueryClientProvider.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar QueryClientContext = React.createContext(\n  void 0\n);\nvar useQueryClient = (queryClient) => {\n  const client = React.useContext(QueryClientContext);\n  if (queryClient) {\n    return queryClient;\n  }\n  if (!client) {\n    throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n  }\n  return client;\n};\nvar QueryClientProvider = ({\n  client,\n  children\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n  return /* @__PURE__ */ jsx(QueryClientContext.Provider, { value: client, children });\n};\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient\n};\n//# sourceMappingURL=QueryClientProvider.js.map"], "names": ["Subscribable", "constructor", "this", "listeners", "Set", "subscribe", "bind", "listener", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "globalThis", "noop", "resolveStaleTime", "staleTime", "query", "matchQuery", "filters", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "status", "<PERSON><PERSON><PERSON>", "hash<PERSON><PERSON>", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "every", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aItems", "aSize", "length", "bItems", "bSize", "copy", "equalItems", "i", "includes", "value", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "prot", "prototype", "hasOwnProperty", "getPrototypeOf", "toString", "call", "replaceData", "prevData", "data", "structuralSharing", "addToEnd", "items", "item", "max", "newItems", "slice", "addToStart", "skipToken", "Symbol", "ensureQueryFn", "fetchOptions", "queryFn", "initialPromise", "Promise", "reject", "Error", "focusManager", "_a", "super", "__privateAdd", "_focused", "_cleanup", "_setup", "__privateSet", "onFocus", "addEventListener", "removeEventListener", "__privateGet", "setEventListener", "setup", "focused", "setFocused", "isFocused", "for<PERSON>ach", "document", "visibilityState", "WeakMap", "onlineManager", "_b", "_online", "onOnline", "onlineListener", "offlineListener", "setOnline", "online", "isOnline", "defaultRetryDelay", "failureCount", "Math", "min", "canFetch", "networkMode", "CancelledError", "revert", "silent", "isCancelledError", "createRetryer", "config", "continueFn", "isRetryCancelled", "isResolved", "thenable", "resolve", "_resolve", "_reject", "finalize", "assign", "catch", "reason", "pendingThenable", "canContinue", "canRun", "canStart", "onSuccess", "onError", "pause", "continueResolve", "onPause", "then", "onContinue", "run", "promiseOrValue", "fn", "error", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "timeout", "onFail", "setTimeout", "promise", "cancel", "cancelOptions", "abort", "continue", "cancelRetry", "continueRetry", "start", "defaultScheduler", "cb", "notify<PERSON><PERSON>ger", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "scheduleFn", "schedule", "push", "batch", "originalQueue", "flush", "batchCalls", "args", "setNotifyFunction", "setBatchNotifyFunction", "setScheduler", "createNotifyManager", "Removable", "_c", "_gcTimeout", "destroy", "clearGcTimeout", "scheduleGc", "gcTime", "Infinity", "optionalRemove", "updateGcTime", "newGcTime", "clearTimeout", "Query", "_d", "_Query_instances", "_initialState", "_revertState", "_cache", "_client", "_retryer", "_defaultOptions", "_abortSignalConsumed", "defaultOptions", "setOptions", "observers", "client", "get<PERSON><PERSON><PERSON><PERSON>ache", "initialData", "hasData", "initialDataUpdatedAt", "dataUpdateCount", "dataUpdatedAt", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "fetchFailureReason", "fetchMeta", "isInvalidated", "getDefaultState", "meta", "remove", "setData", "newData", "__privateMethod", "updatedAt", "manual", "setState", "setStateOptions", "reset", "some", "observer", "resolveEnabled", "enabled", "isDisabled", "getObserversCount", "getCurrentResult", "isStaleByTime", "timeUntilStale", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "filter", "invalidate", "dispatch_fn", "fetch", "abortController", "AbortController", "addSignalProperty", "object", "defineProperty", "enumerable", "get", "signal", "context", "fetchFn", "queryFnContext", "persister", "behavior", "onFetch", "onSettled", "WeakSet", "action", "reducer", "onQueryUpdate", "Query<PERSON>ache", "_e", "_queries", "Map", "build", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "set", "queryInMap", "clear", "getAll", "values", "defaultedFilters", "findAll", "queries", "event", "Mutation", "_f", "_Mutation_instances", "_observers", "_mutationCache", "mutationId", "mutationCache", "failureReason", "isPaused", "variables", "submittedAt", "execute", "mutationFn", "restored", "onMutate", "_h", "_g", "_j", "_i", "_l", "_k", "_n", "_m", "_p", "_o", "_r", "_q", "_t", "_s", "runNext", "onMutationUpdate", "MutationCache", "_mutations", "_scopes", "_mutationId", "__privateWrapper", "defaultMutationOptions", "scope", "scopeFor", "scopedMutations", "index", "indexOf", "splice", "mutationsWithSameScope", "firstPendingMutation", "m", "foundMutation", "from", "resumePausedMutations", "pausedMutations", "all", "map", "id", "infiniteQueryBehavior", "pages", "direction", "fetchMore", "oldPages", "oldPageParams", "pageParams", "currentPage", "async", "cancelled", "fetchPage", "param", "previous", "pageParam", "aborted", "page", "maxPages", "addTo", "oldData", "getPreviousPageParam", "getNextPageParam", "remainingPages", "initialPageParam", "lastIndex", "QueryClient", "_queryCache", "_queryDefaults", "_mutationDefaults", "_mountCount", "_unsubscribeFocus", "_unsubscribeOnline", "queryCache", "mount", "unmount", "isFetching", "isMutating", "getQueryData", "ensureQueryData", "defaultedOptions", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "revalidateIfStale", "prefetch<PERSON><PERSON>y", "getQueriesData", "setQueryData", "updater", "input", "functionalUpdate", "setQueriesData", "getQueryState", "removeQueries", "resetQueries", "refetchQueries", "cancelQueries", "defaultedCancelOptions", "promises", "invalidateQueries", "refetchType", "throwOnError", "fetchInfiniteQuery", "prefetchInfiniteQuery", "ensureInfiniteQueryData", "getMutationCache", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaults", "query<PERSON><PERSON><PERSON>", "setMutationDefaults", "getMutationDefaults", "_defaulted", "refetchOnReconnect", "suspense", "mutations", "QueryClientContext", "React.createContext", "createContext", "QueryClientProvider", "children", "React.useEffect", "jsx", "Provider"], "mappings": "klBACA,IAAIA,EAAe,MACjB,WAAAC,GACOC,KAAAC,cAAgCC,IACrCF,KAAKG,UAAYH,KAAKG,UAAUC,KAAKJ,KACzC,CACE,SAAAG,CAAUE,GAGR,OAFKL,KAAAC,UAAUK,IAAID,GACnBL,KAAKO,cACE,KACAP,KAAAC,UAAUO,OAAOH,GACtBL,KAAKS,eAAe,CAE1B,CACE,YAAAC,GACS,OAAAV,KAAKC,UAAUU,KAAO,CACjC,CACE,WAAAJ,GACF,CACE,aAAAE,GACF,GCnBIG,EAA6B,oBAAXC,QAA0B,SAAUC,WAC1D,SAASC,IACT,CAUA,SAASC,EAAiBC,EAAWC,GACnC,MAA4B,mBAAdD,EAA2BA,EAAUC,GAASD,CAC9D,CAIA,SAASE,GAAWC,EAASF,GACrB,MAAAG,KACJA,EAAO,MAAAC,MACPA,EAAAC,YACAA,EAAAC,UACAA,EAAAC,SACAA,EAAAC,MACAA,GACEN,EACJ,GAAIK,EACF,GAAIH,GACF,GAAIJ,EAAMS,YAAcC,GAAsBH,EAAUP,EAAMW,SACrD,OAAA,WAECC,GAAgBZ,EAAMO,SAAUA,GACnC,OAAA,EAGX,GAAa,QAATJ,EAAgB,CACZ,MAAAU,EAAWb,EAAMa,WACnB,GAAS,WAATV,IAAsBU,EACjB,OAAA,EAEL,GAAS,aAATV,GAAuBU,EAClB,OAAA,CACT,CAEF,OAAqB,kBAAVL,GAAuBR,EAAMc,YAAcN,OAGlDH,GAAeA,IAAgBL,EAAMe,MAAMV,gBAG3CC,IAAcA,EAAUN,IAI9B,CACA,SAASgB,GAAcd,EAASe,GAC9B,MAAMb,MAAEA,EAAAc,OAAOA,EAAQZ,UAAAA,EAAAa,YAAWA,GAAgBjB,EAClD,GAAIiB,EAAa,CACX,IAACF,EAASN,QAAQQ,YACb,OAAA,EAET,GAAIf,GACF,GAAIgB,GAAQH,EAASN,QAAQQ,eAAiBC,GAAQD,GAC7C,OAAA,WAECP,GAAgBK,EAASN,QAAQQ,YAAaA,GACjD,OAAA,CACT,CAEF,QAAID,GAAUD,EAASF,MAAMG,SAAWA,MAGpCZ,IAAcA,EAAUW,GAI9B,CACA,SAASP,GAAsBH,EAAUI,GAEvC,cADeA,WAASU,iBAAkBD,IAC5Bb,EAChB,CACA,SAASa,GAAQb,GACf,OAAOe,KAAKC,UACVhB,GACA,CAACiB,EAAGC,IAAQC,GAAcD,GAAOE,OAAOC,KAAKH,GAAKI,OAAOC,QAAO,CAACC,EAAQC,KAChED,EAAAC,GAAOP,EAAIO,GACXD,IACN,CAAE,GAAIN,GAEb,CACA,SAASb,GAAgBqB,EAAGC,GAC1B,OAAID,IAAMC,UAGCD,UAAaC,OAGpBD,IAAKC,GAAkB,iBAAND,GAA+B,iBAANC,IACrCP,OAAOC,KAAKM,GAAGC,OAAOH,GAAQpB,GAAgBqB,EAAED,GAAME,EAAEF,MAGnE,CACA,SAASI,GAAiBH,EAAGC,GAC3B,GAAID,IAAMC,EACD,OAAAD,EAET,MAAMI,EAAQC,GAAaL,IAAMK,GAAaJ,GAC9C,GAAIG,GAASX,GAAcO,IAAMP,GAAcQ,GAAI,CACjD,MAAMK,EAASF,EAAQJ,EAAIN,OAAOC,KAAKK,GACjCO,EAAQD,EAAOE,OACfC,EAASL,EAAQH,EAAIP,OAAOC,KAAKM,GACjCS,EAAQD,EAAOD,OACfG,EAAOP,EAAQ,GAAK,CAAC,EAC3B,IAAIQ,EAAa,EACjB,IAAA,IAASC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,MAAMd,EAAMK,EAAQS,EAAIJ,EAAOI,KACzBT,GAASE,EAAOQ,SAASf,IAAQK,SAAqB,IAAXJ,EAAED,SAA8B,IAAXE,EAAEF,IACtEY,EAAKZ,QAAO,EACZa,MAEKD,EAAAZ,GAAOI,GAAiBH,EAAED,GAAME,EAAEF,IACnCY,EAAKZ,KAASC,EAAED,SAAmB,IAAXC,EAAED,IAC5Ba,IAEJ,CAEF,OAAOL,IAAUG,GAASE,IAAeL,EAAQP,EAAIW,CAAA,CAEhD,OAAAV,CACT,CAYA,SAASI,GAAaU,GACb,OAAAC,MAAMC,QAAQF,IAAUA,EAAMP,SAAWd,OAAOC,KAAKoB,GAAOP,MACrE,CACA,SAASf,GAAcyB,GACjB,IAACC,GAAmBD,GACf,OAAA,EAET,MAAME,EAAOF,EAAEtE,YACf,QAAa,IAATwE,EACK,OAAA,EAET,MAAMC,EAAOD,EAAKE,UACd,QAACH,GAAmBE,OAGnBA,EAAKE,eAAe,kBAGrB7B,OAAO8B,eAAeN,KAAOxB,OAAO4B,UAI1C,CACA,SAASH,GAAmBD,GAC1B,MAA6C,oBAAtCxB,OAAO4B,UAAUG,SAASC,KAAKR,EACxC,CAMA,SAASS,GAAYC,EAAUC,EAAMnD,GAC/B,MAAqC,mBAA9BA,EAAQoD,kBACVpD,EAAQoD,kBAAkBF,EAAUC,IACJ,IAA9BnD,EAAQoD,kBAWV3B,GAAiByB,EAAUC,GAE7BA,CACT,CAIA,SAASE,GAASC,EAAOC,EAAMC,EAAM,GACnC,MAAMC,EAAW,IAAIH,EAAOC,GAC5B,OAAOC,GAAOC,EAAS3B,OAAS0B,EAAMC,EAASC,MAAM,GAAKD,CAC5D,CACA,SAASE,GAAWL,EAAOC,EAAMC,EAAM,GACrC,MAAMC,EAAW,CAACF,KAASD,GACpB,OAAAE,GAAOC,EAAS3B,OAAS0B,EAAMC,EAASC,MAAM,MAASD,CAChE,CACA,IAAIG,GAAYC,SAChB,SAASC,GAAc9D,EAAS+D,GAQ9B,OAAK/D,EAAQgE,UAAW,MAAAD,OAAA,EAAAA,EAAcE,gBAC7B,IAAMF,EAAaE,eAEvBjE,EAAQgE,SAAWhE,EAAQgE,UAAYJ,GAGrC5D,EAAQgE,QAFN,IAAME,QAAQC,OAAO,IAAIC,MAAM,qBAAqBpE,EAAQF,cAGvE,CCxNA,IA2DIuE,GAAe,IA3DAC,gBAAcrG,EAI/B,WAAAC,GACQqG,QAJRC,EAAArG,KAAAsG,GACAD,EAAArG,KAAAuG,GACAF,EAAArG,KAAAwG,GAGOC,EAAAzG,KAAAwG,GAAUE,IACT,IAAC9F,GAAYC,OAAO8F,iBAAkB,CAClC,MAAAtG,EAAW,IAAMqG,IAEvB,OADO7F,OAAA8F,iBAAiB,mBAAoBtG,GAAU,GAC/C,KACEQ,OAAA+F,oBAAoB,mBAAoBvG,EAAQ,CACzD,CAEF,GACF,CAEF,WAAAE,GACOsG,OAAKN,IACHvG,KAAA8G,iBAAiBD,OAAKL,GAC7B,CAEF,aAAA/F,SACOT,KAAKU,iBACR,OAAAyF,EAAAU,EAAA7G,KAAKuG,KAALJ,EAAAtB,KAAA7E,MACAyG,EAAAzG,KAAKuG,OAAW,GAClB,CAEF,gBAAAO,CAAiBC,SACfN,EAAAzG,KAAKwG,EAASO,GACd,OAAAZ,EAAAU,EAAA7G,KAAKuG,KAALJ,EAAAtB,KAAA7E,MACKyG,EAAAzG,KAAAuG,EAAWQ,GAAOC,IACE,kBAAZA,EACThH,KAAKiH,WAAWD,GAEhBhH,KAAK0G,SAAQ,IAEhB,CAEH,UAAAO,CAAWD,GACOH,OAAKP,KAAaU,IAEhCP,EAAAzG,KAAKsG,EAAWU,GAChBhH,KAAK0G,UACP,CAEF,OAAAA,GACQ,MAAAQ,EAAYlH,KAAKkH,YAClBlH,KAAAC,UAAUkH,SAAS9G,IACtBA,EAAS6G,EAAS,GACnB,CAEH,SAAAA,SACM,MAAyB,kBAAlBL,EAAK7G,KAAAsG,GACPO,EAAK7G,KAAAsG,GAEkC,YAAzC,OAAAH,EAAArF,WAAWsG,eAAX,EAAAjB,EAAqBkB,gBAAoB,GAvDlDf,EAAA,IAAAgB,QACAf,cACAC,EAHiB,IAAAc,QAAAnB,GCiDfoB,GAAgB,IAjDAC,gBAAc1H,EAIhC,WAAAC,GACSqG,QAJCC,EAAArG,KAAAyH,GAAA,GACVlB,EAAAA,KAAAA,GACAC,EAAAA,KAAAA,GAGOA,EAAAA,KAAAA,GAAUkB,IACT,IAAC9G,GAAYC,OAAO8F,iBAAkB,CAClC,MAAAgB,EAAiB,IAAMD,GAAS,GAChCE,EAAkB,IAAMF,GAAS,GAGvC,OAFO7G,OAAA8F,iBAAiB,SAAUgB,GAAgB,GAC3C9G,OAAA8F,iBAAiB,UAAWiB,GAAiB,GAC7C,KACE/G,OAAA+F,oBAAoB,SAAUe,GAC9B9G,OAAA+F,oBAAoB,UAAWgB,EAAe,CAE/D,CACM,GAEN,CACE,WAAArH,GACOsG,OAAKN,IACHvG,KAAA8G,iBAAiBD,OAAKL,GAEjC,CACE,aAAA/F,SACOT,KAAKU,iBACR,OAAAyF,EAAAU,EAAA7G,KAAKuG,KAALJ,EAAAtB,KAAA7E,MACAyG,EAAAzG,KAAKuG,OAAW,GAEtB,CACE,gBAAAO,CAAiBC,SACfN,EAAAzG,KAAKwG,EAASO,GACd,OAAAZ,EAAAU,EAAA7G,KAAKuG,KAALJ,EAAAtB,KAAA7E,MACAyG,EAAAzG,KAAKuG,EAAWQ,EAAM/G,KAAK6H,UAAUzH,KAAKJ,OAC9C,CACE,SAAA6H,CAAUC,GACQjB,OAAKY,KAAYK,IAE/BrB,EAAAzG,KAAKyH,EAAUK,GACV9H,KAAAC,UAAUkH,SAAS9G,IACtBA,EAASyH,EAAM,IAGvB,CACE,QAAAC,GACE,OAAOlB,EAAK7G,KAAAyH,EAChB,GA9CEA,EACAlB,IAAAA,QAAAA,EAAA,IACAC,QAAAA,EAAA,IAHkBc,QAAAE,GCEpB,SAASQ,GAAkBC,GACzB,OAAOC,KAAKC,IAAI,IAAM,GAAKF,EAAc,IAC3C,CACA,SAASG,GAASC,GAChB,MAAqC,YAA7BA,GAAe,WAAyBd,GAAcQ,UAChE,CACA,IAAIO,GAAiB,cAAcrC,MACjC,WAAAlG,CAAY8B,GACVuE,MAAM,kBACNpG,KAAKuI,OAAkB,MAAT1G,OAAS,EAAAA,EAAA0G,OACvBvI,KAAKwI,OAAkB,MAAT3G,OAAS,EAAAA,EAAA2G,MAC3B,GAEA,SAASC,GAAiBvE,GACxB,OAAOA,aAAiBoE,EAC1B,CACA,SAASI,GAAcC,GACrB,IAGIC,EAHAC,GAAmB,EACnBZ,EAAe,EACfa,GAAa,EAEjB,MAAMC,ECxBR,WACM,IAAAC,EACAhD,EACJ,MAAM+C,EAAW,IAAIhD,SAAQ,CAACkD,EAAUC,KAC5BF,EAAAC,EACDjD,EAAAkD,CAAA,IAKX,SAASC,EAASnE,GACTnC,OAAAuG,OAAOL,EAAU/D,UACjB+D,EAASC,eACTD,EAAS/C,MACpB,CAeS,OAtBP+C,EAAS3G,OAAS,UAClB2G,EAASM,OAAM,SAONN,EAAAC,QAAW9E,IACTiF,EAAA,CACP/G,OAAQ,YACR8B,UAEF8E,EAAQ9E,EAAK,EAEN6E,EAAA/C,OAAUsD,IACRH,EAAA,CACP/G,OAAQ,WACRkH,WAEFtD,EAAOsD,EAAM,EAERP,CACT,CDNmBQ,GAaXC,EAAc,IAAMtD,GAAagB,cAAuC,WAAvByB,EAAON,aAA4Bd,GAAcQ,aAAeY,EAAOc,SACxHC,EAAW,IAAMtB,GAASO,EAAON,cAAgBM,EAAOc,SACxDT,EAAW9E,UACV4E,IACUA,GAAA,EACb,OAAA3C,EAAAwC,EAAOgB,YAAPxD,EAAAtB,KAAmB8D,EAAAzE,GACnB,MAAA0E,GAAAA,IACAG,EAASC,QAAQ9E,GACvB,EAEQ8B,EAAU9B,UACT4E,IACUA,GAAA,EACb,OAAA3C,EAAAwC,EAAOiB,UAAPzD,EAAAtB,KAAiB8D,EAAAzE,GACjB,MAAA0E,GAAAA,IACAG,EAAS/C,OAAO9B,GACtB,EAEQ2F,EAAQ,IACL,IAAI9D,SAAS+D,UAClBlB,EAAc1E,KACR4E,GAAcU,MAChBM,EAAgB5F,EAC1B,EAEM,OAAAiC,EAAAwC,EAAOoB,UAAP5D,EAAAtB,KAAA8D,EAAA,IACCqB,MAAK,WACOpB,OAAA,EACRE,GACH,OAAA3C,EAAAwC,EAAOsB,aAAP9D,EAAAtB,KAAA8D,EACR,IAGQuB,EAAM,KACV,GAAIpB,EACF,OAEE,IAAAqB,EACJ,MAAMrE,EAAkC,IAAjBmC,EAAqBU,EAAO7C,oBAAiB,EAChE,IACeqE,EAAArE,GAAkB6C,EAAOyB,IAC3C,OAAQC,GACUF,EAAApE,QAAQC,OAAOqE,EACtC,CACYtE,QAAAiD,QAAQmB,GAAgBH,KAAKhB,GAASK,OAAOgB,UACnD,GAAIvB,EACF,OAEF,MAAMwB,EAAQ3B,EAAO2B,QAAU1J,EAAW,EAAI,GACxC2J,EAAa5B,EAAO4B,YAAcvC,GAClCwC,EAA8B,mBAAfD,EAA4BA,EAAWtC,EAAcoC,GAASE,EAC7EE,GAAwB,IAAVH,GAAmC,iBAAVA,GAAsBrC,EAAeqC,GAA0B,mBAAVA,GAAwBA,EAAMrC,EAAcoC,GH+EpJ,IAAeK,GG9EL7B,GAAqB4B,GAIzBxC,IACA,OAAA9B,EAAAwC,EAAOgC,SAAPxE,EAAAtB,OAAgBoD,EAAcoC,IHyErBK,EGxEHF,EHyEH,IAAIzE,SAASiD,IAClB4B,WAAW5B,EAAS0B,EAAO,KG1EZV,MAAK,IACTR,SAAgB,EAASK,MAC/BG,MAAK,KACFnB,EACF7C,EAAOqE,GAEFH,GACf,KAZQlE,EAAOqE,EAaR,GACF,EAEI,MAAA,CACLQ,QAAS9B,EACT+B,OAnFcC,UACTjC,IACI9C,EAAA,IAAIsC,GAAeyC,IAC1B,OAAA5E,EAAAwC,EAAOqC,QAAP7E,EAAAtB,KAAA8D,GACN,EAgFIsC,SAAU,KACR,MAAArC,GAAAA,IACOG,GAETmC,YAlFkB,KACCrC,GAAA,CAAA,EAkFnBsC,cAhFoB,KACDtC,GAAA,CAAA,EAgFnBa,WACA0B,MAAO,KACD1B,IACGQ,IAEEL,IAACG,KAAKE,GAERnB,GAGb,CE9HA,IAAIsC,GAAoBC,GAAOV,WAAWU,EAAI,GA6E9C,IAAIC,GA5EJ,WACE,IAAIC,EAAQ,GACRC,EAAe,EACfC,EAAYC,IACJA,GAAA,EAERC,EAAiBD,IACTA,GAAA,EAERE,EAAaR,GACX,MAAAS,EAAYH,IACZF,EACFD,EAAMO,KAAKJ,GAEXE,GAAW,KACTH,EAASC,EAAQ,GAEzB,EAeS,MAAA,CACLK,MAAQL,IACF,IAAA1I,EACJwI,IACI,IACFxI,EAAS0I,GACjB,CAAgB,QACRF,IACKA,GArBG,MACZ,MAAMQ,EAAgBT,EACtBA,EAAQ,GACJS,EAActI,QAChBkI,GAAW,KACTD,GAAc,KACEK,EAAA9E,SAASwE,IACrBD,EAASC,EAAQ,GAClB,GACF,GAET,EAWiBO,EAEjB,CACa,OAAAjJ,CAAA,EAKTkJ,WAAaR,GACJ,IAAIS,KACTN,GAAS,KACPH,KAAYS,EAAI,GACjB,EAGLN,WAKAO,kBAAoBjC,IACPsB,EAAAtB,CAAA,EAMbkC,uBAAyBlC,IACPwB,EAAAxB,CAAA,EAElBmC,aAAenC,IACAyB,EAAAzB,CAAA,EAGnB,CACoBoC,GC5EhBC,IAAYC,EAAM,MAAN,WAAA3M,GACdsG,EAAArG,KAAA2M,EAAA,CACA,OAAAC,GACE5M,KAAK6M,gBACT,CACE,UAAAC,GNAF,IAAwB5I,EMCpBlE,KAAK6M,iBNAiB,iBADF3I,EMEDlE,KAAK+M,SNDU7I,GAAS,GAAKA,IAAU8I,KMEnDvG,EAAAzG,KAAA2M,EAAa/B,YAAW,KAC3B5K,KAAKiN,gBAAgB,GACpBjN,KAAK+M,QAEd,CACE,YAAAG,CAAaC,GACXnN,KAAK+M,OAAS7E,KAAK7C,IACjBrF,KAAK+M,QAAU,EACfI,IAAcvM,EAAWoM,IAAW,KAE1C,CACE,cAAAH,GACMhG,OAAK8F,KACPS,aAAavG,OAAK8F,IAClBlG,EAAAzG,KAAK2M,OAAa,GAExB,GAvBEA,EADc,IAAArF,QAAAoF,GCUZW,IAAQC,gBAAcb,GAQxB,WAAA1M,CAAY4I,GACJvC,QATEC,EAAArG,KAAAuN,GACVlH,EAAArG,KAAAwN,GACAnH,EAAArG,KAAAyN,GACApH,EAAArG,KAAA0N,GACArH,EAAArG,KAAA2N,GACAtH,EAAArG,KAAA4N,GACAvH,EAAArG,KAAA6N,GACAxH,EAAArG,KAAA8N,GAGErH,EAAAzG,KAAK8N,GAAuB,GAC5BrH,EAAAzG,KAAK6N,EAAkBlF,EAAOoF,gBACzB/N,KAAAgO,WAAWrF,EAAO9G,SACvB7B,KAAKiO,UAAY,GACjBxH,EAAAzG,KAAK2N,EAAUhF,EAAOuF,QACjBzH,EAAAzG,KAAA0N,EAAS7G,EAAK7G,KAAA2N,GAAQQ,iBAC3BnO,KAAKyB,SAAWkH,EAAOlH,SACvBzB,KAAK2B,UAAYgH,EAAOhH,UACnB8E,EAAAzG,KAAAwN,EA6UT,SAAyB3L,GACjB,MAAAmD,EAAsC,mBAAxBnD,EAAQuM,YAA6BvM,EAAQuM,cAAgBvM,EAAQuM,YACnFC,OAAmB,IAATrJ,EACVsJ,EAAuBD,EAAkD,mBAAjCxM,EAAQyM,qBAAsCzM,EAAQyM,uBAAyBzM,EAAQyM,qBAAuB,EACrJ,MAAA,CACLtJ,OACAuJ,gBAAiB,EACjBC,cAAeH,EAAUC,GAAwBG,KAAKC,MAAQ,EAC9DrE,MAAO,KACPsE,iBAAkB,EAClBC,eAAgB,EAChBC,kBAAmB,EACnBC,mBAAoB,KACpBC,UAAW,KACXC,eAAe,EACf5M,OAAQiM,EAAU,UAAY,UAC9B9M,YAAa,OAEjB,CA/VyB0N,CAAgBjP,KAAK6B,UACrC7B,KAAAiC,MAAQ0G,EAAO1G,OAAS4E,EAAK7G,KAAAwN,GAClCxN,KAAK8M,YAAW,CAElB,QAAIoC,GACF,OAAOlP,KAAK6B,QAAQqN,IAAA,CAEtB,WAAIrE,SACF,OAAO,OAAA1E,EAAAU,EAAA7G,KAAK4N,SAAL,EAAAzH,EAAe0E,OAAA,CAExB,UAAAmD,CAAWnM,GACT7B,KAAK6B,QAAU,IAAKgF,EAAK7G,KAAA6N,MAAoBhM,GACxC7B,KAAAkN,aAAalN,KAAK6B,QAAQkL,OAAM,CAEvC,cAAAE,GACOjN,KAAKiO,UAAUtK,QAAqC,SAA3B3D,KAAKiC,MAAMV,aAClCsF,EAAA7G,KAAA0N,GAAOyB,OAAOnP,KACrB,CAEF,OAAAoP,CAAQC,EAASxN,GACf,MAAMmD,EAAOF,GAAY9E,KAAKiC,MAAM+C,KAAMqK,EAASrP,KAAK6B,SAOjD,OANPyN,EAAAtP,KAAKuN,KAAL1I,KAAe7E,KAAA,CACbgF,OACA3D,KAAM,UACNmN,cAAwB,MAAT3M,OAAS,EAAAA,EAAA0N,UACxBC,OAAiB,MAAT3N,OAAS,EAAAA,EAAA2N,SAEZxK,CAAA,CAET,QAAAyK,CAASxN,EAAOyN,GACdJ,EAAAtP,KAAKuN,KAAL1I,KAAe7E,KAAA,CAAEqB,KAAM,WAAYY,QAAOyN,mBAAiB,CAE7D,MAAA5E,CAAOjJ,WACC,MAAAgJ,EAAU,OAAA1E,EAAAU,EAAK7G,KAAA4N,WAALzH,EAAe0E,QAExB,OADF,OAAArD,EAAAX,EAAA7G,KAAA4N,KAAApG,EAAUsD,OAAOjJ,GACfgJ,EAAUA,EAAQb,KAAKjJ,GAAMsI,MAAMtI,GAAQgF,QAAQiD,SAAQ,CAEpE,OAAA4D,GACExG,MAAMwG,UACN5M,KAAK8K,OAAO,CAAEtC,QAAQ,GAAM,CAE9B,KAAAmH,GACE3P,KAAK4M,UACA5M,KAAAyP,SAAS5I,OAAK2G,GAAa,CAElC,QAAAzL,GACE,OAAO/B,KAAKiO,UAAU2B,MACnBC,IAAaC,OAAmD,KP7D/CC,EO6DWF,EAAShO,QAAQkO,QP7DnB7O,EO6D4BlB,KP5DjC,mBAAZ+P,EAAyBA,EAAQ7O,GAAS6O,GAD1D,IAAwBA,EAAS7O,CO8D7B,GAAA,CAEF,UAAA8O,GACM,OAAAhQ,KAAKiQ,oBAAsB,GACrBjQ,KAAK+B,WAER/B,KAAK6B,QAAQgE,UAAYJ,IAAazF,KAAKiC,MAAMsM,gBAAkBvO,KAAKiC,MAAM0M,mBAAqB,CAAA,CAE5G,OAAA3M,GACM,QAAAhC,KAAKiC,MAAM+M,gBAGXhP,KAAKiQ,oBAAsB,EACtBjQ,KAAKiO,UAAU2B,MACnBC,GAAaA,EAASK,mBAAmBlO,eAGnB,IAApBhC,KAAKiC,MAAM+C,KAAS,CAE7B,aAAAmL,CAAclP,EAAY,GACxB,OAAOjB,KAAKiC,MAAM+M,oBAAqC,IAApBhP,KAAKiC,MAAM+C,OPxFlD,SAAwBuK,EAAWtO,GAC1B,OAAAiH,KAAK7C,IAAIkK,GAAatO,GAAa,GAAKwN,KAAKC,MAAO,EAC7D,COsFsE0B,CAAepQ,KAAKiC,MAAMuM,cAAevN,EAAS,CAEtH,OAAAyF,SACQ,MAAAmJ,EAAW7P,KAAKiO,UAAUoC,MAAMC,GAAMA,EAAEC,6BACpC,MAAAV,GAAAA,EAAAW,QAAQ,CAAEC,eAAe,IACnC,OAAAtK,EAAAU,EAAA7G,KAAK4N,KAALzH,EAAe8E,UAAS,CAE1B,QAAAvD,SACQ,MAAAmI,EAAW7P,KAAKiO,UAAUoC,MAAMC,GAAMA,EAAEI,2BACpC,MAAAb,GAAAA,EAAAW,QAAQ,CAAEC,eAAe,IACnC,OAAAtK,EAAAU,EAAA7G,KAAK4N,KAALzH,EAAe8E,UAAS,CAE1B,WAAA0F,CAAYd,GACL7P,KAAKiO,UAAUhK,SAAS4L,KACtB7P,KAAAiO,UAAUlC,KAAK8D,GACpB7P,KAAK6M,iBACAhG,EAAA7G,KAAA0N,GAAOkD,OAAO,CAAEvP,KAAM,gBAAiBH,MAAOlB,KAAM6P,aAC3D,CAEF,cAAAgB,CAAehB,GACT7P,KAAKiO,UAAUhK,SAAS4L,KAC1B7P,KAAKiO,UAAYjO,KAAKiO,UAAU6C,QAAQR,GAAMA,IAAMT,IAC/C7P,KAAKiO,UAAUtK,SACdkD,OAAK+G,KACH/G,OAAKiH,GACPjH,EAAA7G,KAAK4N,GAAS9C,OAAO,CAAEvC,QAAQ,IAE/B1B,EAAA7G,KAAK4N,GAAS1C,eAGlBlL,KAAK8M,cAEFjG,EAAA7G,KAAA0N,GAAOkD,OAAO,CAAEvP,KAAM,kBAAmBH,MAAOlB,KAAM6P,aAC7D,CAEF,iBAAAI,GACE,OAAOjQ,KAAKiO,UAAUtK,MAAA,CAExB,UAAAoN,GACO/Q,KAAKiC,MAAM+M,eACdM,EAAAtP,KAAKuN,EAALyD,GAAAnM,KAAA7E,KAAe,CAAEqB,KAAM,cACzB,CAEF,KAAA4P,CAAMpP,EAAS+D,aACT,GAA2B,SAA3B5F,KAAKiC,MAAMV,YACb,QAAwB,IAApBvB,KAAKiC,MAAM+C,aAAmBY,WAAc6K,eAC9CzQ,KAAK8K,OAAO,CAAEtC,QAAQ,SAAM,GACnB3B,OAAK+G,GAEd,OADA/G,EAAA7G,KAAK4N,GAASzC,gBACPtE,OAAK+G,GAAS/C,QAMrB,GAHAhJ,GACF7B,KAAKgO,WAAWnM,IAEb7B,KAAK6B,QAAQgE,QAAS,CACnB,MAAAgK,EAAW7P,KAAKiO,UAAUoC,MAAMC,GAAMA,EAAEzO,QAAQgE,UAClDgK,GACG7P,KAAAgO,WAAW6B,EAAShO,QAC3B,CASI,MAAAqP,EAAkB,IAAIC,gBACtBC,EAAqBC,IAClBxO,OAAAyO,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACH/K,EAAAzG,KAAK8N,GAAuB,GACrBoD,EAAgBO,SAE1B,EAoBGC,EAAU,CACd9L,eACA/D,QAAS7B,KAAK6B,QACdJ,SAAUzB,KAAKyB,SACfyM,OAAQrH,EAAK7G,KAAA2N,GACb1L,MAAOjC,KAAKiC,MACZ0P,QAxBc,KACd,MAAM9L,EAAUF,GAAc3F,KAAK6B,QAAS+D,GACtCgM,EAAiB,CACrB1D,OAAQrH,EAAK7G,KAAA2N,GACblM,SAAUzB,KAAKyB,SACfyN,KAAMlP,KAAKkP,MAIT,OAFJkC,EAAkBQ,GAClBnL,EAAAzG,KAAK8N,GAAuB,GACxB9N,KAAK6B,QAAQgQ,UACR7R,KAAK6B,QAAQgQ,UAClBhM,EACA+L,EACA5R,MAGG6F,EAAQ+L,EAAc,GAU/BR,EAAkBM,GAClB,OAAAvL,EAAAnG,KAAK6B,QAAQiQ,WAAb3L,EAAuB4L,QACrBL,EACA1R,MAEFyG,EAAAzG,KAAKyN,EAAezN,KAAKiC,OACM,SAA3BjC,KAAKiC,MAAMV,aAA0BvB,KAAKiC,MAAM8M,aAAc,OAAAvH,EAAAkK,EAAQ9L,mBAAR4B,EAAAA,EAAsB0H,OACjFI,EAAAtP,KAAAuN,EAAAyD,GAAAnM,KAAU7E,KAAA,CAAEqB,KAAM,QAAS6N,KAAM,OAAAxC,EAAAgF,EAAQ9L,mBAAR8G,EAAAA,EAAsBwC,OAExD,MAAAtF,EAAWS,gBACT5B,GAAiB4B,IAAUA,EAAM7B,QACrC8G,EAAAtP,KAAKuN,KAAL1I,KAAe7E,KAAA,CACbqB,KAAM,QACNgJ,UAGC5B,GAAiB4B,KACpB,OAAA7C,GAAArB,EAAAU,EAAA7G,KAAK0N,GAAO/E,QAAOiB,UAAnBpC,EAAA3C,KAAAsB,EACEkE,EACArK,MAEF,OAAAsN,GAAAZ,EAAA7F,EAAA7G,KAAK0N,GAAO/E,QAAOqJ,YAAnB1E,EAAAzI,KAAA6H,EACE1M,KAAKiC,MAAM+C,KACXqF,EACArK,OAGJA,KAAK8M,YAAW,EA6CX,OA3CPrG,EAAAzG,KAAK4N,EAAWlF,GAAc,CAC5B5C,eAA8B,MAAdF,OAAc,EAAAA,EAAAE,eAC9BsE,GAAIsH,EAAQC,QACZ3G,MAAOkG,EAAgBlG,MAAM5K,KAAK8Q,GAClCvH,UAAY3E,gBACV,QAAa,IAATA,EAAJ,CASI,IACFhF,KAAKoP,QAAQpK,SACNqF,GAEP,YADAT,EAAQS,EACR,CAEF,OAAA7C,GAAArB,EAAAU,EAAA7G,KAAK0N,GAAO/E,QAAOgB,YAAnBnC,EAAA3C,KAAAsB,EAA+BnB,EAAMhF,MACrC,OAAAsN,GAAAZ,EAAA7F,EAAA7G,KAAK0N,GAAO/E,QAAOqJ,YAAnB1E,EAAAzI,KAAA6H,EACE1H,EACAhF,KAAKiC,MAAMoI,MACXrK,MAEFA,KAAK8M,YAdH,MADAlD,EAAQ,IAAI3D,MAAM,GAAGjG,KAAK2B,+BAeZ,EAElBiI,UACAe,OAAQ,CAAC1C,EAAcoC,KACrBiF,EAAAtP,KAAKuN,KAAL1I,KAAe7E,KAAA,CAAEqB,KAAM,SAAU4G,eAAcoC,WAEjDN,QAAS,KACPuF,EAAAtP,KAAKuN,EAALyD,GAAAnM,KAAA7E,KAAe,CAAEqB,KAAM,SAAA,EAEzB4I,WAAY,KACVqF,EAAAtP,KAAKuN,EAALyD,GAAAnM,KAAA7E,KAAe,CAAEqB,KAAM,YAAA,EAEzBiJ,MAAOoH,EAAQ7P,QAAQyI,MACvBC,WAAYmH,EAAQ7P,QAAQ0I,WAC5BlC,YAAaqJ,EAAQ7P,QAAQwG,YAC7BoB,OAAQ,KAAM,KAET5C,EAAA7G,KAAK4N,GAASxC,OAAM,GArQ7BoC,EAAA,IAAAlG,QACAmG,EACA,IAAAnG,QAAAoG,EAAA,IAAApG,QACAqG,EACA,IAAArG,QAAAsG,EAAA,IAAAtG,QACAuG,cACAC,EAPU,IAAAxG,QAAAiG,EAAA,IAAA0E,QAwQVjB,WAAUkB,GAmEHlS,KAAAiC,MAlEW,CAACA,IACf,OAAQiQ,EAAO7Q,MACb,IAAK,SACI,MAAA,IACFY,EACH4M,kBAAmBqD,EAAOjK,aAC1B6G,mBAAoBoD,EAAO7H,OAE/B,IAAK,QACI,MAAA,IACFpI,EACHV,YAAa,UAEjB,IAAK,WACI,MAAA,IACFU,EACHV,YAAa,YAEjB,IAAK,QACI,MAAA,IACFU,MAuDK+C,EAtDM/C,EAAM+C,KAsDNnD,EAtDY7B,KAAK6B,QAuDlC,CACLgN,kBAAmB,EACnBC,mBAAoB,KACpBvN,YAAa6G,GAASvG,EAAQwG,aAAe,WAAa,iBAC9C,IAATrD,GAAmB,CACpBqF,MAAO,KACPjI,OAAQ,aA5DF2M,UAAWmD,EAAOhD,MAAQ,MAE9B,IAAK,UACI,MAAA,IACFjN,EACH+C,KAAMkN,EAAOlN,KACbuJ,gBAAiBtM,EAAMsM,gBAAkB,EACzCC,cAAe0D,EAAO1D,eAAiBC,KAAKC,MAC5CrE,MAAO,KACP2E,eAAe,EACf5M,OAAQ,cACJ8P,EAAO1C,QAAU,CACnBjO,YAAa,OACbsN,kBAAmB,EACnBC,mBAAoB,OAG1B,IAAK,QACH,MAAMzE,EAAQ6H,EAAO7H,MACrB,OAAI5B,GAAiB4B,IAAUA,EAAM9B,QAAU1B,OAAK4G,GAC3C,IAAK5G,EAAK7G,KAAAyN,GAAclM,YAAa,QAEvC,IACFU,EACHoI,QACAsE,iBAAkB1M,EAAM0M,iBAAmB,EAC3CC,eAAgBH,KAAKC,MACrBG,kBAAmB5M,EAAM4M,kBAAoB,EAC7CC,mBAAoBzE,EACpB9I,YAAa,OACba,OAAQ,SAEZ,IAAK,aACI,MAAA,IACFH,EACH+M,eAAe,GAEnB,IAAK,WACI,MAAA,IACF/M,KACAiQ,EAAOjQ,OAatB,IAAoB+C,EAAMnD,CAZhB,EAGOsQ,CAAQnS,KAAKiC,OAC1BsJ,GAAcS,OAAM,KACbhM,KAAAiO,UAAU9G,SAAS0I,IACtBA,EAASuC,eAAc,IAEpBvL,EAAA7G,KAAA0N,GAAOkD,OAAO,CAAE1P,MAAOlB,KAAMqB,KAAM,UAAW6Q,UAAQ,GAC5D,EAjVO5E,GCPZ,IAAI+E,IAAaC,gBAAcxS,EAC7B,WAAAC,CAAY4I,EAAS,IACZvC,QAITC,EAAArG,KAAAuS,GAHEvS,KAAK2I,OAASA,EACTlC,EAAAzG,KAAAuS,MAA+BC,IACxC,CAEE,KAAAC,CAAMvE,EAAQrM,EAASI,GACrB,MAAMR,EAAWI,EAAQJ,SACnBE,EAAYE,EAAQF,WAAaC,GAAsBH,EAAUI,GACnE,IAAAX,EAAQlB,KAAKwR,IAAI7P,GAYd,OAXFT,IACHA,EAAQ,IAAImM,GAAM,CAChBa,SACAzM,WACAE,YACAE,QAASqM,EAAOwE,oBAAoB7Q,GACpCI,QACA8L,eAAgBG,EAAOyE,iBAAiBlR,KAE1CzB,KAAKM,IAAIY,IAEJA,CACX,CACE,GAAAZ,CAAIY,GACG2F,EAAK7G,KAAAuS,GAASK,IAAI1R,EAAMS,aAC3BkF,EAAA7G,KAAKuS,GAASM,IAAI3R,EAAMS,UAAWT,GACnClB,KAAK4Q,OAAO,CACVvP,KAAM,QACNH,UAGR,CACE,MAAAiO,CAAOjO,GACL,MAAM4R,EAAajM,EAAA7G,KAAKuS,GAASf,IAAItQ,EAAMS,WACvCmR,IACF5R,EAAM0L,UACFkG,IAAe5R,GACZ2F,EAAA7G,KAAAuS,GAAS/R,OAAOU,EAAMS,WAE7B3B,KAAK4Q,OAAO,CAAEvP,KAAM,UAAWH,UAErC,CACE,KAAA6R,GACExH,GAAcS,OAAM,KAClBhM,KAAKgT,SAAS7L,SAASjG,IACrBlB,KAAKmP,OAAOjO,EAAK,GAClB,GAEP,CACE,GAAAsQ,CAAI7P,GACK,OAAAkF,EAAA7G,KAAKuS,GAASf,IAAI7P,EAC7B,CACE,MAAAqR,GACE,MAAO,IAAInM,EAAK7G,KAAAuS,GAASU,SAC7B,CACE,IAAA5C,CAAKjP,GACH,MAAM8R,EAAmB,CAAE5R,OAAO,KAASF,GACpC,OAAApB,KAAKgT,SAAS3C,MAClBnP,GAAUC,GAAW+R,EAAkBhS,IAE9C,CACE,OAAAiS,CAAQ/R,EAAU,IACV,MAAAgS,EAAUpT,KAAKgT,SACrB,OAAOnQ,OAAOC,KAAK1B,GAASuC,OAAS,EAAIyP,EAAQtC,QAAQ5P,GAAUC,GAAWC,EAASF,KAAUkS,CACrG,CACE,MAAAxC,CAAOyC,GACL9H,GAAcS,OAAM,KACbhM,KAAAC,UAAUkH,SAAS9G,IACtBA,EAASgT,EAAK,GACf,GAEP,CACE,OAAA3M,GACE6E,GAAcS,OAAM,KAClBhM,KAAKgT,SAAS7L,SAASjG,IACrBA,EAAMwF,SAAS,GAChB,GAEP,CACE,QAAAgB,GACE6D,GAAcS,OAAM,KAClBhM,KAAKgT,SAAS7L,SAASjG,IACrBA,EAAMwG,UAAU,GACjB,GAEP,GAhFE6K,EANe,IAAAjL,QAAAgL,GCDbgB,IAAWC,gBAAc9G,GAI3B,WAAA1M,CAAY4I,GACHvC,QALIC,EAAArG,KAAAwT,GACbnN,EAAArG,KAAAyT,GACApN,EAAArG,KAAA0T,GACA9F,EAAAA,KAAAA,GAGE5N,KAAK2T,WAAahL,EAAOgL,WACzBlN,EAAAzG,KAAK0T,EAAiB/K,EAAOiL,eAC7BnN,EAAAzG,KAAKyT,EAAa,IACbzT,KAAAiC,MAAQ0G,EAAO1G,OAgNf,CACLyP,aAAS,EACT1M,UAAM,EACNqF,MAAO,KACPpC,aAAc,EACd4L,cAAe,KACfC,UAAU,EACV1R,OAAQ,OACR2R,eAAW,EACXC,YAAa,GAxNRhU,KAAAgO,WAAWrF,EAAO9G,SACvB7B,KAAK8M,YACT,CACE,UAAAkB,CAAWnM,GACT7B,KAAK6B,QAAUA,EACV7B,KAAAkN,aAAalN,KAAK6B,QAAQkL,OACnC,CACE,QAAImC,GACF,OAAOlP,KAAK6B,QAAQqN,IACxB,CACE,WAAAyB,CAAYd,GACLhJ,EAAA7G,KAAKyT,GAAWxP,SAAS4L,KACvBhJ,EAAA7G,KAAAyT,GAAW1H,KAAK8D,GACrB7P,KAAK6M,iBACLhG,EAAA7G,KAAK0T,GAAe9C,OAAO,CACzBvP,KAAM,gBACNc,SAAUnC,KACV6P,aAGR,CACE,cAAAgB,CAAehB,GACbpJ,EAAAzG,KAAKyT,EAAa5M,EAAK7G,KAAAyT,GAAW3C,QAAQR,GAAMA,IAAMT,KACtD7P,KAAK8M,aACLjG,EAAA7G,KAAK0T,GAAe9C,OAAO,CACzBvP,KAAM,kBACNc,SAAUnC,KACV6P,YAEN,CACE,cAAA5C,GACOpG,EAAK7G,KAAAyT,GAAW9P,SACO,YAAtB3D,KAAKiC,MAAMG,OACbpC,KAAK8M,aAEAjG,EAAA7G,KAAA0T,GAAevE,OAAOnP,MAGnC,CACE,iBACE,OAAO,OAAAmG,EAAAU,EAAA7G,KAAK4N,SAAL,EAAAzH,EAAe8E,aACtBjL,KAAKiU,QAAQjU,KAAKiC,MAAM8R,UAC5B,CACE,aAAME,CAAQF,+CACZ,MAAM9J,EAAa,KACjBqF,EAAAtP,KAAKwT,EAAAxC,GAALnM,KAAe7E,KAAA,CAAEqB,KAAM,YAAU,EAEnCoF,EAAAzG,KAAK4N,EAAWlF,GAAc,CAC5B0B,GAAI,IACGpK,KAAK6B,QAAQqS,WAGXlU,KAAK6B,QAAQqS,WAAWH,GAFtBhO,QAAQC,OAAO,IAAIC,MAAM,wBAIpC0E,OAAQ,CAAC1C,EAAcoC,KACrBiF,EAAAtP,KAAKwT,EAAAxC,GAALnM,KAAA7E,KAAe,CAAEqB,KAAM,SAAU4G,eAAcoC,WAEjDN,QAAS,KACPuF,EAAAtP,KAAKwT,EAAAxC,GAALnM,KAAe7E,KAAA,CAAEqB,KAAM,SAAO,EAEhC4I,aACAK,MAAOtK,KAAK6B,QAAQyI,OAAS,EAC7BC,WAAYvK,KAAK6B,QAAQ0I,WACzBlC,YAAarI,KAAK6B,QAAQwG,YAC1BoB,OAAQ,IAAM5C,EAAK7G,KAAA0T,GAAejK,OAAOzJ,SAErC,MAAAmU,EAAiC,YAAtBnU,KAAKiC,MAAMG,OACtB0R,GAAYjN,EAAK+G,KAAAA,GAASlE,WAC5B,IACF,GAAIyK,EACUlK,QACP,CACLqF,EAAAtP,KAAKwT,EAAAxC,GAALnM,KAAA7E,KAAe,CAAEqB,KAAM,UAAW0S,YAAWD,mBACvC,OAAAtM,GAAArB,EAAAU,EAAA7G,KAAK0T,GAAe/K,QAAOyL,iBAA3B5M,EAAA3C,KAAAsB,EACJ4N,EACA/T,OAEI,MAAA0R,QAAgB,OAAApE,GAAAZ,EAAA1M,KAAK6B,SAAQuS,eAAb,EAAA9G,EAAAzI,KAAA6H,EAAwBqH,IAC1CrC,IAAY1R,KAAKiC,MAAMyP,SACpBpC,EAAAtP,KAAAwT,EAAAxC,GAALnM,KAAe7E,KAAA,CACbqB,KAAM,UACNqQ,UACAqC,YACAD,YAGZ,CACM,MAAM9O,QAAa6B,EAAK+G,KAAAA,GAASxC,QAiB1B,aAhBD,OAAAmI,GAAAjB,EAAAzL,EAAA7G,KAAK0T,GAAe/K,QAAOgB,kBAA3B4J,EAAA1O,KAAAyN,EACJtN,EACA+O,EACA/T,KAAKiC,MAAMyP,QACX1R,aAEI,OAAAqU,GAAAC,EAAAtU,KAAK6B,SAAQ8H,gBAAb,EAAA0K,EAAAxP,KAAAyP,EAAyBtP,EAAM+O,EAAW/T,KAAKiC,MAAMyP,gBACrD,OAAA6C,GAAAC,EAAA3N,EAAA7G,KAAK0T,GAAe/K,QAAOqJ,gBAA3B,EAAAuC,EAAA1P,KAAA2P,EACJxP,EACA,KACAhF,KAAKiC,MAAM8R,UACX/T,KAAKiC,MAAMyP,QACX1R,aAEI,OAAAyU,GAAAC,EAAA1U,KAAK6B,SAAQmQ,gBAAb,EAAAyC,EAAA5P,KAAA6P,EAAyB1P,EAAM,KAAM+O,EAAW/T,KAAKiC,MAAMyP,UACjEpC,EAAAtP,KAAKwT,EAAAxC,GAALnM,KAAA7E,KAAe,CAAEqB,KAAM,UAAW2D,SAC3BA,CACR,OAAQqF,GACH,IAyBI,YAxBA,OAAAsK,GAAAC,EAAA/N,EAAA7G,KAAK0T,GAAe/K,QAAOiB,cAA3B,EAAA+K,EAAA9P,KAAA+P,EACJvK,EACA0J,EACA/T,KAAKiC,MAAMyP,QACX1R,aAEI,OAAA6U,GAAAC,EAAA9U,KAAK6B,SAAQ+H,cAAb,EAAAiL,EAAAhQ,KAAAiQ,EACJzK,EACA0J,EACA/T,KAAKiC,MAAMyP,gBAEP,OAAAqD,GAAAC,EAAAnO,EAAA7G,KAAK0T,GAAe/K,QAAOqJ,gBAA3B,EAAA+C,EAAAlQ,KAAAmQ,OACJ,EACA3K,EACArK,KAAKiC,MAAM8R,UACX/T,KAAKiC,MAAMyP,QACX1R,aAEI,OAAAiV,GAAAC,EAAAlV,KAAK6B,SAAQmQ,gBAAb,EAAAiD,EAAApQ,KAAAqQ,OACJ,EACA7K,EACA0J,EACA/T,KAAKiC,MAAMyP,UAEPrH,CACd,CAAgB,QACRiF,EAAAtP,KAAKwT,EAAAxC,GAALnM,KAAA7E,KAAe,CAAEqB,KAAM,QAASgJ,SACxC,CACA,CAAc,QACHxD,EAAA7G,KAAA0T,GAAeyB,QAAQnV,KAClC,CACA,GAnJEyT,cACAC,EACA9F,IAAAA,QAAAA,EAAA,YAHa4F,EAqJbxC,IAAAA,QAAAA,EAAS,SAACkB,GAsDHlS,KAAAiC,MArDW,CAACA,IACf,OAAQiQ,EAAO7Q,MACb,IAAK,SACI,MAAA,IACFY,EACHgG,aAAciK,EAAOjK,aACrB4L,cAAe3B,EAAO7H,OAE1B,IAAK,QACI,MAAA,IACFpI,EACH6R,UAAU,GAEd,IAAK,WACI,MAAA,IACF7R,EACH6R,UAAU,GAEd,IAAK,UACI,MAAA,IACF7R,EACHyP,QAASQ,EAAOR,QAChB1M,UAAM,EACNiD,aAAc,EACd4L,cAAe,KACfxJ,MAAO,KACPyJ,SAAU5B,EAAO4B,SACjB1R,OAAQ,UACR2R,UAAW7B,EAAO6B,UAClBC,YAAavF,KAAKC,OAEtB,IAAK,UACI,MAAA,IACFzM,EACH+C,KAAMkN,EAAOlN,KACbiD,aAAc,EACd4L,cAAe,KACfxJ,MAAO,KACPjI,OAAQ,UACR0R,UAAU,GAEd,IAAK,QACI,MAAA,IACF7R,EACH+C,UAAM,EACNqF,MAAO6H,EAAO7H,MACdpC,aAAchG,EAAMgG,aAAe,EACnC4L,cAAe3B,EAAO7H,MACtByJ,UAAU,EACV1R,OAAQ,SAEpB,EAEiB+P,CAAQnS,KAAKiC,OAC1BsJ,GAAcS,OAAM,KACbnF,EAAA7G,KAAAyT,GAAWtM,SAAS0I,IACvBA,EAASuF,iBAAiBlD,EAAM,IAElCrL,EAAA7G,KAAK0T,GAAe9C,OAAO,CACzBzO,SAAUnC,KACVqB,KAAM,UACN6Q,UACD,GAEP,EAtNeqB,GCCf,IAAI8B,IAAgBf,gBAAcxU,EAChC,WAAAC,CAAY4I,EAAS,IACZvC,QAMTC,EAAArG,KAAAsV,GACAjP,EAAArG,KAAAuV,GACAlP,EAAArG,KAAAwV,GAPExV,KAAK2I,OAASA,EACTlC,EAAAzG,KAAAsV,MAAiCpV,KACjCuG,EAAAzG,KAAAuV,MAA8B/C,KACnC/L,EAAAzG,KAAKwV,EAAc,EACvB,CAIE,KAAA/C,CAAMvE,EAAQrM,EAASI,GACf,MAAAE,EAAW,IAAImR,GAAS,CAC5BM,cAAe5T,KACf2T,aAAc8B,EAAAzV,KAAKwV,GAAL9S,EACdb,QAASqM,EAAOwH,uBAAuB7T,GACvCI,UAGK,OADPjC,KAAKM,IAAI6B,GACFA,CACX,CACE,GAAA7B,CAAI6B,GACG0E,EAAA7G,KAAAsV,GAAWhV,IAAI6B,GACd,MAAAwT,EAAQC,GAASzT,GACnB,GAAiB,iBAAVwT,EAAoB,CAC7B,MAAME,EAAkBhP,EAAA7G,KAAKuV,GAAQ/D,IAAImE,GACrCE,EACFA,EAAgB9J,KAAK5J,GAErB0E,EAAA7G,KAAKuV,GAAQ1C,IAAI8C,EAAO,CAACxT,GAEjC,CACInC,KAAK4Q,OAAO,CAAEvP,KAAM,QAASc,YACjC,CACE,MAAAgN,CAAOhN,GACL,GAAI0E,EAAK7G,KAAAsV,GAAW9U,OAAO2B,GAAW,CAC9B,MAAAwT,EAAQC,GAASzT,GACnB,GAAiB,iBAAVwT,EAAoB,CAC7B,MAAME,EAAkBhP,EAAA7G,KAAKuV,GAAQ/D,IAAImE,GACzC,GAAIE,EACE,GAAAA,EAAgBlS,OAAS,EAAG,CACxB,MAAAmS,EAAQD,EAAgBE,QAAQ5T,IACpB,IAAd2T,GACcD,EAAAG,OAAOF,EAAO,EAEjC,MAAUD,EAAgB,KAAO1T,GAC3B0E,EAAA7G,KAAAuV,GAAQ/U,OAAOmV,EAGhC,CACA,CACI3V,KAAK4Q,OAAO,CAAEvP,KAAM,UAAWc,YACnC,CACE,MAAAsH,CAAOtH,GACC,MAAAwT,EAAQC,GAASzT,GACnB,GAAiB,iBAAVwT,EAAoB,CAC7B,MAAMM,EAAyBpP,EAAA7G,KAAKuV,GAAQ/D,IAAImE,GAC1CO,EAA+C,MAAxBD,OAAwB,EAAAA,EAAA5F,MAClD8F,GAAyB,YAAnBA,EAAElU,MAAMG,SAEV,OAAC8T,GAAwBA,IAAyB/T,CAC/D,CACa,OAAA,CAEb,CACE,OAAAgT,CAAQhT,SACA,MAAAwT,EAAQC,GAASzT,GACnB,GAAiB,iBAAVwT,EAAoB,CAC7B,MAAMS,EAAgB,OAAAjQ,EAAAU,EAAK7G,KAAAuV,GAAQ/D,IAAImE,SAAjBxP,EAAAA,EAAyBkK,MAAM8F,GAAMA,IAAMhU,GAAYgU,EAAElU,MAAM6R,WAC9E,OAAA,MAAAsC,OAAA,EAAAA,EAAenL,aAAclF,QAAQiD,SAClD,CACM,OAAOjD,QAAQiD,SAErB,CACE,KAAA+J,GACExH,GAAcS,OAAM,KACbnF,EAAA7G,KAAAsV,GAAWnO,SAAShF,IACvBnC,KAAK4Q,OAAO,CAAEvP,KAAM,UAAWc,YAAU,IAE3C0E,EAAA7G,KAAKsV,GAAWvC,QAChBlM,EAAA7G,KAAKuV,GAAQxC,OAAO,GAE1B,CACE,MAAAC,GACS,OAAA7O,MAAMkS,KAAKxP,EAAA7G,KAAKsV,GAC3B,CACE,IAAAjF,CAAKjP,GACH,MAAM8R,EAAmB,CAAE5R,OAAO,KAASF,GACpC,OAAApB,KAAKgT,SAAS3C,MAClBlO,GAAaD,GAAcgR,EAAkB/Q,IAEpD,CACE,OAAAgR,CAAQ/R,EAAU,IACT,OAAApB,KAAKgT,SAASlC,QAAQ3O,GAAaD,GAAcd,EAASe,IACrE,CACE,MAAAyO,CAAOyC,GACL9H,GAAcS,OAAM,KACbhM,KAAAC,UAAUkH,SAAS9G,IACtBA,EAASgT,EAAK,GACf,GAEP,CACE,qBAAAiD,GACQ,MAAAC,EAAkBvW,KAAKgT,SAASlC,QAAQR,GAAMA,EAAErO,MAAM6R,WAC5D,OAAOvI,GAAcS,OACnB,IAAMjG,QAAQyQ,IACZD,EAAgBE,KAAKtU,GAAaA,EAAS8I,WAAW5B,MAAMtI,OAGpE,GArGEuU,EAAA,IAAAhO,QACAiO,cACAC,EAVkB,IAAAlO,QAAAgN,GA+GpB,SAASsB,GAASzT,SAChB,OAAO,OAAAgE,EAAAhE,EAASN,QAAQ8T,cAAjBxP,EAAwBuQ,EACjC,CCpHA,SAASC,GAAsBC,GACtB,MAAA,CACL7E,QAAS,CAACL,EAASxQ,mBACjB,MAAMW,EAAU6P,EAAQ7P,QAClBgV,EAAY,OAAAnK,EAAA,OAAAlF,EAAA,OAAArB,EAAAuL,EAAQ9L,mBAARO,EAAAA,EAAsB+I,WAAtB,EAAA1H,EAA4BsP,kBAA5BpK,EAAuCmK,UACnDE,GAAW,OAAAzJ,EAAAoE,EAAQzP,MAAM+C,WAAdsI,EAAAA,EAAoBsJ,QAAS,GACxCI,GAAgB,OAAA1E,EAAAZ,EAAQzP,MAAM+C,WAAdsN,EAAAA,EAAoB2E,aAAc,GACxD,IAAIhU,EAAS,CAAE2T,MAAO,GAAIK,WAAY,IAClCC,EAAc,EAClB,MAAMvF,EAAUwF,UACd,IAAIC,GAAY,EACV,MAeAvR,EAAUF,GAAc+L,EAAQ7P,QAAS6P,EAAQ9L,cACjDyR,EAAYF,MAAOnS,EAAMsS,EAAOC,KACpC,GAAIH,EACF,OAAOrR,QAAQC,SAEjB,GAAa,MAATsR,GAAiBtS,EAAK4R,MAAMjT,OACvB,OAAAoC,QAAQiD,QAAQhE,GAEzB,MAAM4M,EAAiB,CACrB1D,OAAQwD,EAAQxD,OAChBzM,SAAUiQ,EAAQjQ,SAClB+V,UAAWF,EACXT,UAAWU,EAAW,WAAa,UACnCrI,KAAMwC,EAAQ7P,QAAQqN,MA5BA,IAACmC,IA8BPO,EA7BX/O,OAAAyO,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACCE,EAAQD,OAAOgG,QACLL,GAAA,EAEJ1F,EAAAD,OAAO9K,iBAAiB,SAAS,KAC3ByQ,GAAA,CAAA,IAGT1F,EAAQD,UAoBnB,MAAMiG,QAAa7R,EACjB+L,IAEI+F,SAAEA,GAAajG,EAAQ7P,QACvB+V,EAAQL,EAAW/R,GAAaN,GAC/B,MAAA,CACL0R,MAAOgB,EAAM5S,EAAK4R,MAAOc,EAAMC,GAC/BV,WAAYW,EAAM5S,EAAKiS,WAAYK,EAAOK,GAC3C,EAEC,GAAAd,GAAaE,EAASpT,OAAQ,CAChC,MAAM4T,EAAyB,aAAdV,EAEXgB,EAAU,CACdjB,MAAOG,EACPE,WAAYD,GAERM,GALcC,EAAWO,GAAuBC,IAK5BlW,EAASgW,GACnC5U,QAAeoU,EAAUQ,EAASP,EAAOC,EACnD,KAAe,CACC,MAAAS,EAAiBpB,GAASG,EAASpT,OACtC,EAAA,CACK,MAAA2T,EAAwB,IAAhBJ,EAAoBF,EAAc,IAAMnV,EAAQoW,iBAAmBF,GAAiBlW,EAASoB,GACvG,GAAAiU,EAAc,GAAc,MAATI,EACrB,MAEOrU,QAAMoU,EAAUpU,EAAQqU,GACjCJ,GACD,OAAQA,EAAcc,EACjC,CACe,OAAA/U,CAAA,EAELyO,EAAQ7P,QAAQgQ,UAClBH,EAAQC,QAAU,aAChB,OAAO,OAAAnK,GAAArB,EAAAuL,EAAQ7P,SAAQgQ,kBAAhBrK,EAAA3C,KAAAsB,EACLwL,EACA,CACEzD,OAAQwD,EAAQxD,OAChBzM,SAAUiQ,EAAQjQ,SAClByN,KAAMwC,EAAQ7P,QAAQqN,KACtBuC,OAAQC,EAAQD,QAElBvQ,EAAA,EAIJwQ,EAAQC,QAAUA,CAC1B,EAGA,CACA,SAASoG,GAAiBlW,GAAS+U,MAAEA,EAAAK,WAAOA,IACpC,MAAAiB,EAAYtB,EAAMjT,OAAS,EAC1B,OAAAiT,EAAMjT,OAAS,EAAI9B,EAAQkW,iBAChCnB,EAAMsB,GACNtB,EACAK,EAAWiB,GACXjB,QACE,CACN,CACA,SAASa,GAAqBjW,GAAS+U,MAAEA,EAAAK,WAAOA,UAC9C,OAAOL,EAAMjT,OAAS,EAAI,OAAAwC,EAAAtE,EAAQiW,2BAAR3R,EAAAA,EAAAtB,KAA+BhD,EAAA+U,EAAM,GAAIA,EAAOK,EAAW,GAAIA,QAAc,CACzG,CC1FG,IAACkB,IAAc9D,EAAM,MAStB,WAAAtU,CAAY4I,EAAS,IARrBtC,EAAArG,KAAAoY,GACA1E,EAAAA,KAAAA,GACA7F,EAAAA,KAAAA,GACAxH,EAAArG,KAAAqY,GACAhS,EAAArG,KAAAsY,GACAjS,EAAArG,KAAAuY,GACAlS,EAAArG,KAAAwY,GACAnS,EAAArG,KAAAyY,GAEEhS,EAAAzG,KAAKoY,EAAczP,EAAO+P,YAAc,IAAIrG,IAC5C5L,EAAAzG,KAAK0T,EAAiB/K,EAAOiL,eAAiB,IAAIyB,IAC7CxH,EAAAA,KAAAA,EAAkBlF,EAAOoF,gBAAkB,CAAE,GAC7CtH,EAAAzG,KAAAqY,MAAqC7F,KACrC/L,EAAAzG,KAAAsY,MAAwC9F,KAC7C/L,EAAAzG,KAAKuY,EAAc,EACvB,CACE,KAAAI,GACElD,EAAAzV,KAAKuY,GAAL7V,IACyB,IAArBmE,EAAA7G,KAAKuY,KACT9R,EAAAzG,KAAKwY,EAAoBtS,GAAa/F,WAAUgX,MAAOnQ,IACjDA,UACIhH,KAAKsW,wBACXzP,EAAA7G,KAAKoY,GAAY1R,UACzB,KAEID,EAAAzG,KAAKyY,EAAqBlR,GAAcpH,WAAUgX,MAAOrP,IACnDA,UACI9H,KAAKsW,wBACXzP,EAAA7G,KAAKoY,GAAY1Q,WACzB,KAEA,CACE,OAAAkR,WACEnD,EAAAzV,KAAKuY,GAAL7V,IACyB,IAArBmE,EAAA7G,KAAKuY,KACT,OAAApS,EAAAU,EAAA7G,KAAKwY,KAALrS,EAAAtB,KAAA7E,MACAyG,EAAAzG,KAAKwY,OAAoB,GACzB,OAAAhR,EAAAX,EAAA7G,KAAKyY,KAALjR,EAAA3C,KAAA7E,MACAyG,EAAAzG,KAAKyY,OAAqB,GAC9B,CACE,UAAAI,CAAWzX,GACF,OAAAyF,EAAA7G,KAAKoY,GAAYjF,QAAQ,IAAK/R,EAASG,YAAa,aAAcoC,MAC7E,CACE,UAAAmV,CAAW1X,GACF,OAAAyF,EAAA7G,KAAK0T,GAAeP,QAAQ,IAAK/R,EAASgB,OAAQ,YAAauB,MAC1E,CAQE,YAAAoV,CAAatX,SACX,MAAMI,EAAU7B,KAAK0S,oBAAoB,CAAEjR,aACpC0E,OAAA,OAAAA,EAAAU,OAAKuR,GAAY5G,IAAI3P,EAAQF,iBAA7BwE,EAAAA,EAAyClE,MAAM+C,IAC1D,CACE,eAAAgU,CAAgBnX,GACR,MAAAoX,EAAmBjZ,KAAK0S,oBAAoB7Q,GAC5CX,EAAQ2F,EAAA7G,KAAKoY,GAAY3F,MAAMzS,KAAMiZ,GACrCC,EAAahY,EAAMe,MAAM+C,KAC/B,YAAmB,IAAfkU,EACKlZ,KAAKmZ,WAAWtX,IAErBA,EAAQuX,mBAAqBlY,EAAMiP,cAAcnP,EAAiBiY,EAAiBhY,UAAWC,KAC3FlB,KAAKqZ,cAAcJ,GAEnBlT,QAAQiD,QAAQkQ,GAC3B,CACE,cAAAI,CAAelY,GACN,OAAAyF,EAAA7G,KAAKoY,GAAYjF,QAAQ/R,GAASqV,KAAI,EAAGhV,WAAUQ,WAEjD,CAACR,EADKQ,EAAM+C,OAGzB,CACE,YAAAuU,CAAa9X,EAAU+X,EAAS3X,GAC9B,MAAMoX,EAAmBjZ,KAAK0S,oBAAoB,CAAEjR,aAC9CP,EAAQ2F,OAAKuR,GAAY5G,IAC7ByH,EAAiBtX,WAGbqD,EZ9FV,SAA0BwU,EAASC,GACjC,MAA0B,mBAAZD,EAAyBA,EAAQC,GAASD,CAC1D,CY4FiBE,CAAiBF,QADbtY,WAAOe,MAAM+C,MAE9B,QAAa,IAATA,EAGJ,OAAO6B,EAAK7G,KAAAoY,GAAY3F,MAAMzS,KAAMiZ,GAAkB7J,QAAQpK,EAAM,IAAKnD,EAAS2N,QAAQ,GAC9F,CACE,cAAAmK,CAAevY,EAASoY,EAAS3X,GAC/B,OAAO0J,GAAcS,OACnB,IAAMnF,EAAK7G,KAAAoY,GAAYjF,QAAQ/R,GAASqV,KAAI,EAAGhV,cAAe,CAC5DA,EACAzB,KAAKuZ,aAAa9X,EAAU+X,EAAS3X,OAG7C,CACE,aAAA+X,CAAcnY,SACZ,MAAMI,EAAU7B,KAAK0S,oBAAoB,CAAEjR,aACpC0E,OAAA,OAAAA,EAAAU,OAAKuR,GAAY5G,IACtB3P,EAAQF,mBADHwE,EAEJlE,KACP,CACE,aAAA4X,CAAczY,GACZ,MAAMsX,EAAa7R,EAAK7G,KAAAoY,GACxB7M,GAAcS,OAAM,KAClB0M,EAAWvF,QAAQ/R,GAAS+F,SAASjG,IACnCwX,EAAWvJ,OAAOjO,EAAK,GACxB,GAEP,CACE,YAAA4Y,CAAa1Y,EAASS,GACpB,MAAM6W,EAAa7R,EAAK7G,KAAAoY,GACjB,OAAA7M,GAAcS,OAAM,KACzB0M,EAAWvF,QAAQ/R,GAAS+F,SAASjG,IACnCA,EAAMyO,OAAO,IAER3P,KAAK+Z,eACV,CACE1Y,KAAM,YACHD,GAELS,KAGR,CACE,aAAAmY,CAAc5Y,EAAS2J,EAAgB,IACrC,MAAMkP,EAAyB,CAAE1R,QAAQ,KAASwC,GAC5CmP,EAAW3O,GAAcS,OAC7B,IAAMnF,EAAA7G,KAAKoY,GAAYjF,QAAQ/R,GAASqV,KAAKvV,GAAUA,EAAM4J,OAAOmP,OAE/D,OAAAlU,QAAQyQ,IAAI0D,GAAUlQ,KAAKjJ,GAAMsI,MAAMtI,EAClD,CACE,iBAAAoZ,CAAkB/Y,EAASS,EAAU,IAC5B,OAAA0J,GAAcS,OAAM,KACzBnF,EAAA7G,KAAKoY,GAAYjF,QAAQ/R,GAAS+F,SAASjG,IACzCA,EAAM6P,YAAY,IAES,UAAzB,MAAA3P,OAAA,EAAAA,EAASgZ,aACJrU,QAAQiD,UAEVhJ,KAAK+Z,eACV,IACK3Y,EACHC,MAAM,MAAAD,OAAA,EAAAA,EAASgZ,eAAe,MAAAhZ,OAAA,EAAAA,EAASC,OAAQ,UAEjDQ,KAGR,CACE,cAAAkY,CAAe3Y,EAASS,EAAU,IAChC,MAAM+D,EAAe,IAChB/D,EACH4O,cAAe5O,EAAQ4O,gBAAiB,GAEpCyJ,EAAW3O,GAAcS,OAC7B,IAAMnF,EAAK7G,KAAAoY,GAAYjF,QAAQ/R,GAAS0P,QAAQ5P,IAAWA,EAAM8O,eAAcyG,KAAKvV,IAClF,IAAI2J,EAAU3J,EAAM+P,WAAM,EAAQrL,GAIlC,OAHKA,EAAayU,eACNxP,EAAAA,EAAQxB,MAAMtI,IAES,WAA5BG,EAAMe,MAAMV,YAA2BwE,QAAQiD,UAAY6B,CAAA,MAGtE,OAAO9E,QAAQyQ,IAAI0D,GAAUlQ,KAAKjJ,EACtC,CACE,UAAAoY,CAAWtX,GACH,MAAAoX,EAAmBjZ,KAAK0S,oBAAoB7Q,QACnB,IAA3BoX,EAAiB3O,QACnB2O,EAAiB3O,OAAQ,GAE3B,MAAMpJ,EAAQ2F,EAAA7G,KAAKoY,GAAY3F,MAAMzS,KAAMiZ,GAC3C,OAAO/X,EAAMiP,cACXnP,EAAiBiY,EAAiBhY,UAAWC,IAC3CA,EAAM+P,MAAMgI,GAAoBlT,QAAQiD,QAAQ9H,EAAMe,MAAM+C,KACpE,CACE,aAAAqU,CAAcxX,GACL,OAAA7B,KAAKmZ,WAAWtX,GAASmI,KAAKjJ,GAAMsI,MAAMtI,EACrD,CACE,kBAAAuZ,CAAmBzY,GAEV,OADCA,EAAAiQ,SAAW6E,GAAsB9U,EAAQ+U,OAC1C5W,KAAKmZ,WAAWtX,EAC3B,CACE,qBAAA0Y,CAAsB1Y,GACb,OAAA7B,KAAKsa,mBAAmBzY,GAASmI,KAAKjJ,GAAMsI,MAAMtI,EAC7D,CACE,uBAAAyZ,CAAwB3Y,GAEf,OADCA,EAAAiQ,SAAW6E,GAAsB9U,EAAQ+U,OAC1C5W,KAAKgZ,gBAAgBnX,EAChC,CACE,qBAAAyU,GACM,OAAA/O,GAAcQ,WACTlB,EAAA7G,KAAK0T,GAAe4C,wBAEtBvQ,QAAQiD,SACnB,CACE,aAAAmF,GACE,OAAOtH,EAAK7G,KAAAoY,EAChB,CACE,gBAAAqC,GACE,OAAO5T,EAAK6M,KAAAA,EAChB,CACE,iBAAAgH,GACE,OAAO7T,EAAKgH,KAAAA,EAChB,CACE,iBAAA8M,CAAkB9Y,GAChB4E,EAAAzG,KAAK6N,EAAkBhM,EAC3B,CACE,gBAAA+Y,CAAiBnZ,EAAUI,GACzBgF,EAAA7G,KAAKqY,GAAexF,IAAIvQ,GAAQb,GAAW,CACzCA,WACAsM,eAAgBlM,GAEtB,CACE,gBAAA8Q,CAAiBlR,GACf,MAAMoZ,EAAW,IAAIhU,EAAK7G,KAAAqY,GAAepF,UACnChQ,EAAS,CAAE,EAMV,OALE4X,EAAA1T,SAAS2T,IACZhZ,GAAgBL,EAAUqZ,EAAarZ,WAClCoB,OAAAuG,OAAOnG,EAAQ6X,EAAa/M,eAC3C,IAEW9K,CACX,CACE,mBAAA8X,CAAoB1Y,EAAaR,GAC/BgF,EAAA7G,KAAKsY,GAAkBzF,IAAIvQ,GAAQD,GAAc,CAC/CA,cACA0L,eAAgBlM,GAEtB,CACE,mBAAAmZ,CAAoB3Y,GAClB,MAAMwY,EAAW,IAAIhU,EAAK7G,KAAAsY,GAAkBrF,UACtChQ,EAAS,CAAE,EAMV,OALE4X,EAAA1T,SAAS2T,IACZhZ,GAAgBO,EAAayY,EAAazY,cACrCQ,OAAAuG,OAAOnG,EAAQ6X,EAAa/M,eAC3C,IAEW9K,CACX,CACE,mBAAAyP,CAAoB7Q,GAClB,GAAIA,EAAQoZ,WACH,OAAApZ,EAET,MAAMoX,EAAmB,IACpBpS,OAAKgH,GAAgBuF,WACrBpT,KAAK2S,iBAAiB9Q,EAAQJ,aAC9BI,EACHoZ,YAAY,GAoBP,OAlBFhC,EAAiBtX,YACpBsX,EAAiBtX,UAAYC,GAC3BqX,EAAiBxX,SACjBwX,SAGwC,IAAxCA,EAAiBiC,qBACFjC,EAAAiC,mBAAsD,WAAjCjC,EAAiB5Q,kBAEnB,IAAlC4Q,EAAiBoB,eACFpB,EAAAoB,eAAiBpB,EAAiBkC,WAEhDlC,EAAiB5Q,aAAe4Q,EAAiBpH,YACpDoH,EAAiB5Q,YAAc,gBAE7B4Q,EAAiBpT,UAAYJ,KAC/BwT,EAAiBlJ,SAAU,GAEtBkJ,CACX,CACE,sBAAAvD,CAAuB7T,GACrB,aAAIA,WAASoZ,YACJpZ,EAEF,IACFgF,OAAKgH,GAAgBuN,cACZ,MAATvZ,OAAS,EAAAA,EAAAQ,cAAerC,KAAKgb,oBAAoBnZ,EAAQQ,gBACzDR,EACHoZ,YAAY,EAElB,CACE,KAAAlI,GACElM,EAAA7G,KAAKoY,GAAYrF,QACjBlM,EAAA7G,KAAK0T,GAAeX,OACxB,GA1REqF,EAAA,IAAA9Q,QACAoM,EAAA,IACA7F,QAAAA,EAAA,YACAwK,EACA,IAAA/Q,QAAAgR,EAAA,IAAAhR,QACAiR,EACA,IAAAjR,QAAAkR,EAAA,IAAAlR,QACAmR,EARgB,IAAAnR,QAAA+M,GCXdgH,GAAqBC,EAAmBC,mBAC1C,GAYEC,GAAsB,EACxBtN,SACAuN,eAEAC,EAAAA,WAAgB,KACdxN,EAAOyK,QACA,KACLzK,EAAO0K,SAAS,IAEjB,CAAC1K,IACmByN,EAAAA,IAAIN,GAAmBO,SAAU,CAAE1X,MAAOgK,EAAQuN", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}
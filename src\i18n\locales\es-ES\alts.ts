export default {
  profile: {
    photo: "Foto de perfil de Tarcisio Bispo",
    ixdfLogo: "Logo de Interaction Design Foundation",
    ixdfSeal: "Sello de certificación IxDF"
  },
  projects: {
    fgvLaw: "Captura de pantalla del proyecto FGV LAW - diseñadores trabajando en equipo en la oficina",
    direitoGV: "Captura de pantalla del proyecto Investigación Derecho FGV - mesa de madera con libro frente a estantería",
    taliparts: "Captura de pantalla del proyecto Taliparts - e-commerce de autopartes",
    tvInstitucional: "Captura de pantalla del proyecto FGV TV Institucional - cartel blanco colgado en el lateral de un edificio"
  },
  icons: {
    menu: "Ícono de menú",
    close: "Ícono de cerrar",
    expand: "Ícono de expandir",
    collapse: "Ícono de contraer",
    external: "Ícono de enlace externo",
    download: "Ícono de descarga",
    email: "Ícono de email",
    phone: "Ícono de teléfono",
    linkedin: "Ícono de LinkedIn",
    whatsapp: "Ícono de WhatsApp",
    github: "Ícono de GitHub",
    sun: "Ícono de sol",
    moon: "Ícono de luna",
    globe: "Ícono de globo",
    volume: "Ícono de volumen",
    success: "Ícono de éxito",
    error: "Ícono de error",
    warning: "Ícono de advertencia",
    info: "Ícono de información"
  },
  decorative: {
    gradient: "Gradiente decorativo",
    pattern: "Patrón decorativo",
    divider: "Divisor visual",
    background: "Imagen de fondo decorativa"
  }
};

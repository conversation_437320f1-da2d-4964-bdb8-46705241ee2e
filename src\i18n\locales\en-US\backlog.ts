export default {
  title: "Strategic Backlog Cycle",
  description: "Practical demonstration of how I transform business challenges into measurable UX solutions. Each case presents applied methodology, achieved results and strategic insights that generate real impact for stakeholders and users.",
  solution: "Solution",
  result: "Result",
  note: "Note",
  noItems: "No items on this page.",
  previous: "Previous",
  next: "Next",
  items: [
    {
      challenge: "Users abandoned long forms without completing registration",
      solution: "Implemented step-by-step form with progress bar and real-time validation",
      result: "40% increase in registration completion rate in 2 weeks",
      note: "Breaking complex tasks into smaller steps reduces anxiety and improves experience."
    },
    {
      challenge: "Low adoption of premium features due to lack of clarity about benefits",
      solution: "Created interactive onboarding with practical demonstrations of premium features",
      result: "60% growth in conversions to paid plans in 1 month",
      note: "Showing value through practical experience is more effective than just listing features."
    },
    {
      challenge: "Users couldn't easily find support when they needed help",
      solution: "Redesigned contextual help system with intelligent chatbot and dynamic FAQ",
      result: "50% reduction in support tickets and 35% increase in satisfaction",
      note: "Contextual help at the right moment prevents frustrations and improves user autonomy."
    },
    {
      challenge: "Complex interface caused confusion in beginner users",
      solution: "Developed simplified mode with progressive tutorial and adaptive tooltips",
      result: "45% decrease in new user abandonment rate",
      note: "Adapting complexity to user experience level significantly improves adoption."
    },
    {
      challenge: "Checkout process had high abandonment rate at the last step",
      solution: "Simplified flow by removing unnecessary fields and adding express payment options",
      result: "30% increase in purchase completion and 25% reduction in checkout time",
      note: "Each extra field in checkout is a potential barrier. Simplicity generates conversion."
    },
    {
      challenge: "Users didn't notice important product updates",
      solution: "Implemented in-app notification system with non-intrusive design and personalization",
      result: "55% improvement in engagement with new features",
      note: "Effective communication about changes keeps users informed without interrupting flow."
    },
    {
      challenge: "Difficulty finding relevant content in extensive knowledge base",
      solution: "Created intelligent search system with contextual filters and automatic suggestions",
      result: "70% increase in knowledge base usage and 40% reduction in repetitive queries",
      note: "Efficient search transforms abundant information into accessible knowledge."
    },
    {
      challenge: "Low engagement in platform's collaborative features",
      solution: "Redesigned collaboration interface with visual activity indicators and subtle gamification",
      result: "80% growth in user collaboration in 6 weeks",
      note: "Making collaboration visible and rewarding naturally encourages participation."
    },
    {
      challenge: "Users lost progress when navigating between app sections",
      solution: "Implemented auto-save system with visual indicators of save status",
      result: "Elimination of 95% of complaints about data loss",
      note: "Trust in technology grows when users see their work is always protected."
    },
    {
      challenge: "Non-responsive interface caused frustration on mobile devices",
      solution: "Developed mobile-first version with intuitive gestures and touch-optimized navigation",
      result: "120% increase in mobile usage and improvement from 4.2 to 4.7 in app store rating",
      note: "Mobile-first design ensures consistent experience regardless of device used."
    },
    {
      challenge: "Users with visual impairments faced accessibility barriers",
      solution: "Implemented WCAG 2.1 standards with keyboard navigation and screen reader compatibility",
      result: "200% increase in usage by people with disabilities and recognition in accessibility award",
      note: "Accessibility is not just compliance — it's inclusive design that benefits all users."
    },
    {
      challenge: "Technical texts confused non-specialized users",
      solution: "Rewrote microcopy with clear language and added contextual explanations when necessary",
      result: "60% reduction in user doubts and improvement in perceived ease of use",
      note: "Small decisions in text have great impact on reading experience and comprehension."
    }
  ]
};

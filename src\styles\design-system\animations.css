/**
 * 🎯 SISTEMA DE ANIMAÇÕES
 * 
 * Animações baseadas nos tokens
 * Performance otimizada
 */

/* ===== TRANSITION UTILITIES ===== */
.transition-none { transition: none !important; }
.transition-all { transition: all var(--duration-200) var(--ease-out) !important; }
.transition-colors { transition: color var(--duration-200) var(--ease-out), background-color var(--duration-200) var(--ease-out), border-color var(--duration-200) var(--ease-out) !important; }
.transition-opacity { transition: opacity var(--duration-200) var(--ease-out) !important; }
.transition-shadow { transition: box-shadow var(--duration-200) var(--ease-out) !important; }
.transition-transform { transition: transform var(--duration-200) var(--ease-out) !important; }

/* ===== DURATION UTILITIES ===== */
.duration-75 { transition-duration: var(--duration-75) !important; }
.duration-100 { transition-duration: var(--duration-100) !important; }
.duration-150 { transition-duration: var(--duration-150) !important; }
.duration-200 { transition-duration: var(--duration-200) !important; }
.duration-300 { transition-duration: var(--duration-300) !important; }
.duration-500 { transition-duration: var(--duration-500) !important; }
.duration-700 { transition-duration: var(--duration-700) !important; }
.duration-1000 { transition-duration: var(--duration-1000) !important; }

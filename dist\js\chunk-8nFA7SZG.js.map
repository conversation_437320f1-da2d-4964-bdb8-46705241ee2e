{"version": 3, "file": "chunk-8nFA7SZG.js", "sources": ["../../src/utils/secureValidation.ts", "../../src/config/analytics.ts"], "sourcesContent": ["/**\n * Secure validation utilities to prevent regex-based security vulnerabilities\n * Replaces potentially vulnerable regex patterns with safer alternatives\n */\n\n/**\n * Secure hostname validation using allowlist approach\n * Prevents regex bypass attacks by using exact string matching\n */\nexport const validateHostname = (hostname: string): boolean => {\n  if (typeof hostname !== 'string' || !hostname) {\n    return false;\n  }\n\n  // Allowlist of permitted hostnames - exact match only\n  const ALLOWED_HOSTNAMES = [\n    'tarcisiobispo.github.io',\n    'localhost',\n    '127.0.0.1',\n    '::1', // IPv6 localhost\n    'images.unsplash.com',\n    'unsplash.com',\n    'plus.unsplash.com',\n    // Analytics and tracking domains (secure validation)\n    'production.wootric.com',\n    'web.delighted.com',\n    'e.logrocket.com',\n    'api.logrocket.com'\n  ];\n\n  // Normalize hostname (lowercase, trim)\n  const normalizedHostname = hostname.toLowerCase().trim();\n\n  // Exact match validation - no regex needed\n  return ALLOWED_HOSTNAMES.includes(normalizedHostname);\n};\n\n/**\n * Secure URL validation without vulnerable regex patterns\n * Uses native URL constructor for robust parsing\n */\nexport const validateUrl = (url: string): boolean => {\n  if (typeof url !== 'string' || !url) {\n    return false;\n  }\n\n  try {\n    const parsedUrl = new URL(url);\n\n    // Validate protocol\n    if (parsedUrl.protocol !== 'https:' && parsedUrl.protocol !== 'http:') {\n      return false;\n    }\n\n    // Validate hostname using secure function\n    return validateHostname(parsedUrl.hostname);\n  } catch {\n    return false;\n  }\n};\n\n/**\n * Secure email validation using native HTML5 validation\n * Avoids complex regex patterns that can be vulnerable\n */\nexport const validateEmail = (email: string): boolean => {\n  if (typeof email !== 'string' || !email) {\n    return false;\n  }\n\n  // Basic format check\n  if (!email.includes('@') || email.length < 5) {\n    return false;\n  }\n\n  try {\n    // Use native HTML5 validation which is more secure\n    const input = document.createElement('input');\n    input.type = 'email';\n    input.value = email;\n    return input.validity.valid && input.value === email;\n  } catch {\n    return false;\n  }\n};\n\n/**\n * Secure string sanitization without regex\n * Uses safe string methods to prevent injection\n */\nexport const sanitizeString = (input: string): string => {\n  if (typeof input !== 'string') {\n    return '';\n  }\n\n  // Use safe string methods instead of regex\n  return input\n    .split('<').join('&lt;')\n    .split('>').join('&gt;')\n    .split('\"').join('&quot;')\n    .split(\"'\").join('&#x27;')\n    .split('&').join('&amp;')\n    .trim();\n};\n\n/**\n * Secure path validation for routing\n * Prevents path traversal without complex regex\n */\nexport const validatePath = (path: string): boolean => {\n  if (typeof path !== 'string' || !path) {\n    return false;\n  }\n\n  // Check for dangerous patterns using simple string methods\n  const dangerousPatterns = ['../', '..\\\\', '<script', 'javascript:', 'data:', 'vbscript:'];\n  const lowerPath = path.toLowerCase();\n\n  for (const pattern of dangerousPatterns) {\n    if (lowerPath.includes(pattern)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * Secure domain extraction from URL\n * Uses native URL parsing instead of regex\n */\nexport const extractDomain = (url: string): string | null => {\n  if (typeof url !== 'string' || !url) {\n    return null;\n  }\n\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.hostname;\n  } catch {\n    return null;\n  }\n};\n\n/**\n * Secure query parameter parsing\n * Avoids regex-based parsing vulnerabilities\n */\nexport const parseQueryParams = (search: string): Record<string, string> => {\n  if (typeof search !== 'string' || !search) {\n    return {};\n  }\n\n  const params: Record<string, string> = {};\n\n  try {\n    const urlParams = new URLSearchParams(search);\n    for (const [key, value] of urlParams.entries()) {\n      // Sanitize both key and value\n      const safeKey = sanitizeString(key);\n      const safeValue = sanitizeString(value);\n      if (safeKey) {\n        params[safeKey] = safeValue;\n      }\n    }\n  } catch {\n    // Return empty object on error\n  }\n\n  return params;\n};\n\n/**\n * Secure content type validation\n * Uses allowlist approach instead of regex\n */\nexport const validateContentType = (contentType: string): boolean => {\n  if (typeof contentType !== 'string' || !contentType) {\n    return false;\n  }\n\n  const ALLOWED_CONTENT_TYPES = [\n    'text/html',\n    'text/plain',\n    'application/json',\n    'application/javascript',\n    'text/css',\n    'image/jpeg',\n    'image/png',\n    'image/gif',\n    'image/webp',\n    'image/svg+xml'\n  ];\n\n  const normalizedType = contentType.toLowerCase().split(';')[0].trim();\n  return ALLOWED_CONTENT_TYPES.includes(normalizedType);\n};\n\n/**\n * Secure file extension validation\n * Uses allowlist instead of regex patterns\n */\nexport const validateFileExtension = (filename: string): boolean => {\n  if (typeof filename !== 'string' || !filename) {\n    return false;\n  }\n\n  const ALLOWED_EXTENSIONS = [\n    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',\n    '.pdf', '.txt', '.json', '.css', '.js', '.html'\n  ];\n\n  const lastDotIndex = filename.lastIndexOf('.');\n  if (lastDotIndex === -1) {\n    return false;\n  }\n\n  const extension = filename.slice(lastDotIndex).toLowerCase();\n  return ALLOWED_EXTENSIONS.includes(extension);\n};\n\n/**\n * Secure validation for analytics and tracking domains\n * Prevents regex bypass attacks on third-party analytics services\n */\nexport const validateAnalyticsDomain = (hostname: string): boolean => {\n  if (typeof hostname !== 'string' || !hostname) {\n    return false;\n  }\n\n  // Specific allowlist for analytics domains\n  const ANALYTICS_DOMAINS = [\n    'production.wootric.com',\n    'web.delighted.com',\n    'e.logrocket.com',\n    'api.logrocket.com',\n    'www.google-analytics.com',\n    'analytics.google.com',\n    'clarity.microsoft.com'\n  ];\n\n  const normalizedHostname = hostname.toLowerCase().trim();\n  return ANALYTICS_DOMAINS.includes(normalizedHostname);\n};\n\n/**\n * Secure URL validation specifically for analytics endpoints\n * Uses exact string matching instead of vulnerable regex patterns\n */\nexport const validateAnalyticsUrl = (url: string): boolean => {\n  if (typeof url !== 'string' || !url) {\n    return false;\n  }\n\n  try {\n    const parsedUrl = new URL(url);\n\n    // Must use HTTPS for analytics\n    if (parsedUrl.protocol !== 'https:') {\n      return false;\n    }\n\n    // Validate against analytics domain allowlist\n    return validateAnalyticsDomain(parsedUrl.hostname);\n  } catch {\n    return false;\n  }\n};\n\n/**\n * Secure pattern matching for analytics endpoints\n * Replaces vulnerable regex patterns with safe string operations\n */\nexport const matchesAnalyticsPattern = (url: string, pattern: 'wootric' | 'delighted' | 'logrocket'): boolean => {\n  if (typeof url !== 'string' || !url) {\n    return false;\n  }\n\n  try {\n    const parsedUrl = new URL(url);\n\n    switch (pattern) {\n      case 'wootric':\n        return parsedUrl.hostname === 'production.wootric.com' &&\n               parsedUrl.pathname.startsWith('/responses');\n\n      case 'delighted':\n        return parsedUrl.hostname === 'web.delighted.com' &&\n               parsedUrl.pathname.includes('/e/') &&\n               parsedUrl.pathname.includes('/c');\n\n      case 'logrocket':\n        return (parsedUrl.hostname === 'e.logrocket.com' ||\n                parsedUrl.hostname === 'api.logrocket.com');\n\n      default:\n        return false;\n    }\n  } catch {\n    return false;\n  }\n};\n\n/**\n * Export all validation functions as a namespace\n */\nexport const SecureValidation = {\n  validateHostname,\n  validateUrl,\n  validateEmail,\n  sanitizeString,\n  validatePath,\n  extractDomain,\n  parseQueryParams,\n  validateContentType,\n  validateFileExtension,\n  validateAnalyticsDomain,\n  validateAnalyticsUrl,\n  matchesAnalyticsPattern\n};\n\nexport default SecureValidation;\n", "// Configurações de Analytics\r\nexport const ANALYTICS_CONFIG = {\r\n  // Google Tag Manager ID\r\n  GTM_ID: 'GTM-M2NFRBD9',\r\n  \r\n  // Habilitar analytics apenas em produção\r\n  ENABLED: import.meta.env.PROD,\r\n  \r\n  // Configurações de eventos\r\n  EVENTS: {\r\n    // Navegação\r\n    PAGE_VIEW: 'page_view',\r\n    NAVIGATION: 'navigation',\r\n    \r\n    // Interações do usuário\r\n    BUTTON_CLICK: 'button_click',\r\n    FORM_SUBMIT: 'form_submit',\r\n    DOWNLOAD: 'download',\r\n    EXTERNAL_LINK: 'external_link',\r\n    \r\n    // Portfolio específico\r\n    PROJECT_VIEW: 'project_view',\r\n    CONTACT_FORM: 'contact_form_submit',\r\n    CV_DOWNLOAD: 'cv_download',\r\n    SOCIAL_CLICK: 'social_media_click',\r\n    \r\n    // Acessibilidade\r\n    ACCESSIBILITY_TOGGLE: 'accessibility_toggle',\r\n    THEME_CHANGE: 'theme_change',\r\n    LANGUAGE_CHANGE: 'language_change',\r\n    \r\n    // Performance\r\n    PERFORMANCE_METRIC: 'performance_metric',\r\n    ERROR: 'error',\r\n  },\r\n  \r\n  // Categorias de eventos\r\n  CATEGORIES: {\r\n    ENGAGEMENT: 'engagement',\r\n    NAVIGATION: 'navigation',\r\n    PORTFOLIO: 'portfolio',\r\n    ACCESSIBILITY: 'accessibility',\r\n    PERFORMANCE: 'performance',\r\n    ERROR: 'error',\r\n  },\r\n} as const;\r\n\r\n// Tipos para eventos\r\nexport interface GTMEvent {\r\n  event: string;\r\n  event_category?: string;\r\n  event_label?: string;\r\n  value?: number;\r\n  custom_parameters?: Record<string, any>;\r\n}\r\n\r\n// Helper para criar eventos padronizados\r\nexport const createGTMEvent = (\r\n  eventName: string,\r\n  category?: string,\r\n  label?: string,\r\n  value?: number,\r\n  customParams?: Record<string, any>\r\n): GTMEvent => ({\r\n  event: eventName,\r\n  event_category: category,\r\n  event_label: label,\r\n  value,\r\n  custom_parameters: customParams,\r\n});\r\n\r\n// Eventos pré-definidos para o portfolio\r\nexport const PORTFOLIO_EVENTS = {\r\n  // Navegação\r\n  navigateToSection: (section: string) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.NAVIGATION,\r\n    ANALYTICS_CONFIG.CATEGORIES.NAVIGATION,\r\n    section\r\n  ),\r\n  \r\n  // Projetos\r\n  viewProject: (projectName: string) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.PROJECT_VIEW,\r\n    ANALYTICS_CONFIG.CATEGORIES.PORTFOLIO,\r\n    projectName\r\n  ),\r\n  \r\n  // Contato\r\n  submitContactForm: (formType: string) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.CONTACT_FORM,\r\n    ANALYTICS_CONFIG.CATEGORIES.ENGAGEMENT,\r\n    formType\r\n  ),\r\n  \r\n  // Downloads\r\n  downloadCV: () => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.CV_DOWNLOAD,\r\n    ANALYTICS_CONFIG.CATEGORIES.ENGAGEMENT,\r\n    'curriculum_vitae'\r\n  ),\r\n  \r\n  // Redes sociais\r\n  clickSocialMedia: (platform: string) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.SOCIAL_CLICK,\r\n    ANALYTICS_CONFIG.CATEGORIES.ENGAGEMENT,\r\n    platform\r\n  ),\r\n  \r\n  // Acessibilidade\r\n  toggleAccessibility: (feature: string, enabled: boolean) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.ACCESSIBILITY_TOGGLE,\r\n    ANALYTICS_CONFIG.CATEGORIES.ACCESSIBILITY,\r\n    feature,\r\n    enabled ? 1 : 0\r\n  ),\r\n  \r\n  // Tema\r\n  changeTheme: (theme: string) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.THEME_CHANGE,\r\n    ANALYTICS_CONFIG.CATEGORIES.ACCESSIBILITY,\r\n    theme\r\n  ),\r\n  \r\n  // Idioma\r\n  changeLanguage: (language: string) => createGTMEvent(\r\n    ANALYTICS_CONFIG.EVENTS.LANGUAGE_CHANGE,\r\n    ANALYTICS_CONFIG.CATEGORIES.ACCESSIBILITY,\r\n    language\r\n  ),\r\n};\r\n"], "names": ["validateHostname", "hostname", "normalizedHostname", "toLowerCase", "trim", "ALLOWED_HOSTNAMES", "includes", "sanitizeString", "input", "split", "join", "validateAnalyticsDomain", "ANALYTICS_DOMAINS", "SecureValidation", "validateUrl", "url", "parsedUrl", "URL", "protocol", "validateEmail", "email", "length", "document", "createElement", "type", "value", "validity", "valid", "validatePath", "path", "dangerousPatterns", "lowerPath", "pattern", "extractDomain", "parseQueryParams", "search", "params", "urlParams", "URLSearchParams", "key", "entries", "<PERSON><PERSON><PERSON>", "safeValue", "validateContentType", "contentType", "normalizedType", "ALLOWED_CONTENT_TYPES", "validateFileExtension", "filename", "lastDotIndex", "lastIndexOf", "extension", "slice", "ALLOWED_EXTENSIONS", "validateAnalyticsUrl", "matchesAnalyticsPattern", "pathname", "startsWith", "ANALYTICS_CONFIG", "GTM_ID", "EVENTS", "PAGE_VIEW"], "mappings": "AASaA,MAAAA,EAAoBC,IAC/B,GAAwB,iBAAbA,IAA0BA,EAC5B,OAAA,EAIT,MAgBMC,EAAqBD,EAASE,cAAcC,OAG3CC,MAnBmB,CACxB,0BACA,YACA,YACA,MACA,sBACA,eACA,oBAEA,yBACA,oBACA,kBACA,qBAOuBC,SAASJ,EAAAA,EAwDvBK,EAAkBC,GACR,iBAAVA,EACF,GAIFA,EACJC,MAAM,KAAKC,KAAK,QAChBD,MAAM,KAAKC,KAAK,QAChBD,MAAM,KAAKC,KAAK,UAChBD,MAAM,KAAKC,KAAK,UAChBD,MAAM,KAAKC,KAAK,SAChBN,OA2HQO,EAA2BV,IACtC,GAAwB,iBAAbA,IAA0BA,EAC5B,OAAA,EAIT,MAUMC,EAAqBD,EAASE,cAAcC,OAC3CQ,MAXmB,CACxB,yBACA,oBACA,kBACA,oBACA,2BACA,uBACA,yBAIuBN,SAASJ,EAAAA,EAgEvBW,EAAmB,CAC9Bb,mBACAc,YA3Q0BC,IAC1B,GAAmB,iBAARA,IAAqBA,EACvB,OAAA,EAGL,IACIC,MAAAA,EAAY,IAAIC,IAAIF,GAG1B,OAA2B,WAAvBC,EAAUE,UAAgD,UAAvBF,EAAUE,WAK1ClB,EAAiBgB,EAAUf,SAAQ,CACpC,MACC,OAAA,CAAA,GA4PTkB,cApP4BC,IAC5B,GAAqB,iBAAVA,IAAuBA,EACzB,OAAA,EAIT,IAAKA,EAAMd,SAAS,MAAQc,EAAMC,OAAS,EAClC,OAAA,EAGL,IAEIb,MAAAA,EAAQc,SAASC,cAAc,SAGrC,OAFAf,EAAMgB,KAAO,QACbhB,EAAMiB,MAAQL,EACPZ,EAAMkB,SAASC,OAASnB,EAAMiB,QAAUL,CAAAA,CACzC,MACC,OAAA,CAAA,GAoOTb,iBACAqB,aA1M2BC,IAC3B,GAAoB,iBAATA,IAAsBA,EACxB,OAAA,EAIT,MAAMC,EAAoB,CAAC,MAAO,OAAQ,UAAW,cAAe,QAAS,aACvEC,EAAYF,EAAK1B,cAEvB,IAAA,MAAW6B,KAAWF,EAChBC,GAAAA,EAAUzB,SAAS0B,GACd,OAAA,EAIJ,OAAA,CAAA,EA4LPC,cArL4BlB,IAC5B,GAAmB,iBAARA,IAAqBA,EACvB,OAAA,KAGL,IAEF,OADkB,IAAIE,IAAIF,GACTd,QAAAA,CACX,MACC,OAAA,IAAA,GA6KTiC,iBArK+BC,IAC/B,GAAsB,iBAAXA,IAAwBA,EACjC,MAAO,CAAC,EAGV,MAAMC,EAAiC,CAAC,EAEpC,IACIC,MAAAA,EAAY,IAAIC,gBAAgBH,GACtC,IAAA,MAAYI,EAAKd,KAAUY,EAAUG,UAAW,CAExCC,MAAAA,EAAUlC,EAAegC,GACzBG,EAAYnC,EAAekB,GAC7BgB,IACFL,EAAOK,GAAWC,EACpB,CACF,CACM,MAAA,CAIDN,OAAAA,CAAAA,EAiJPO,oBA1IkCC,IAClC,GAA2B,iBAAhBA,IAA6BA,EAC/B,OAAA,EAGT,MAaMC,EAAiBD,EAAYzC,cAAcM,MAAM,KAAK,GAAGL,OACxD0C,MAduB,CAC5B,YACA,aACA,mBACA,yBACA,WACA,aACA,YACA,YACA,aACA,iBAI2BxC,SAASuC,EAAAA,EAwHtCE,sBAjHoCC,IACpC,GAAwB,iBAAbA,IAA0BA,EAC5B,OAAA,EAGT,MAKMC,EAAeD,EAASE,YAAY,KAC1C,IAAyB,IAArBD,EACK,OAAA,EAGT,MAAME,EAAYH,EAASI,MAAMH,GAAc9C,cACxCkD,MAXoB,CACzB,OAAQ,QAAS,OAAQ,OAAQ,QAAS,OAC1C,OAAQ,OAAQ,QAAS,OAAQ,MAAO,SAShB/C,SAAS6C,EAAAA,EAkGnCxC,0BACA2C,qBApEmCvC,IACnC,GAAmB,iBAARA,IAAqBA,EACvB,OAAA,EAGL,IACIC,MAAAA,EAAY,IAAIC,IAAIF,GAGtBC,MAAuB,WAAvBA,EAAUE,UAKPP,EAAwBK,EAAUf,SAAQ,CAC3C,MACC,OAAA,CAAA,GAqDTsD,wBA7CqC,CAACxC,EAAaiB,KACnD,GAAmB,iBAARjB,IAAqBA,EACvB,OAAA,EAGL,IACIC,MAAAA,EAAY,IAAIC,IAAIF,GAE1B,OAAQiB,GACN,IAAK,UACH,MAA8B,2BAAvBhB,EAAUf,UACVe,EAAUwC,SAASC,WAAW,cAEvC,IAAK,YACIzC,MAAuB,sBAAvBA,EAAUf,UACVe,EAAUwC,SAASlD,SAAS,QAC5BU,EAAUwC,SAASlD,SAAS,MAErC,IAAK,YACH,MAA+B,oBAAvBU,EAAUf,UACa,sBAAvBe,EAAUf,SAEpB,QACS,OAAA,EACX,CACM,MACC,OAAA,CAAA,IC1SEyD,EAAmB,CAE9BC,OAAQ,eAMRC,OAAQ,CAENC,UAAW"}
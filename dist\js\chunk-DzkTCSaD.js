const e={init(e){!function(e){try{return t=window,r=document,n="clarity",o="script",a=e,void(r.getElementById("clarity-script")||(t[n]=t[n]||function(){(t[n].q=t[n].q||[]).push(arguments)},(i=r.createElement(o)).async=1,i.src="https://www.clarity.ms/tag/"+a+"?ref=npm",i.id="clarity-script",(c=r.getElementsByTagName(o)[0]).parentNode.insertBefore(i,c)))}catch(s){return}var t,r,n,o,a,i,c}(e)},setTag(e,t){window.clarity("set",e,t)},identify(e,t,r,n){window.clarity("identify",e,t,r,n)},consent(e=!0){window.clarity("consent",e)},upgrade(e){window.clarity("upgrade",e)},event(e){window.clarity("event",e)}};var t,r={exports:{}};function n(){return t?r.exports:(t=1,e=function(){return function(){var e={"./packages/@logrocket/console/src/index.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r("./packages/@logrocket/console/src/registerConsole.js")).default;t.default=o},"./packages/@logrocket/console/src/registerConsole.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=[];return["log","warn","info","error","debug"].forEach((function(r){t.push((0,a.default)(console,r,(function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.addEvent("lr.core.LogEvent",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.isEnabled;if("object"===(0,o.default)(a)&&!1===a[r]||!1===a)return null;if("error"===r&&t.shouldAggregateConsoleErrors)if(n&&n.length>=2&&"ERROR"===n[0]){var c="";try{c=" ".concat(n[1])}catch(s){}i.Capture.captureMessage(e,"".concat(n[0]).concat(c),n,{},!0)}else i.Capture.captureMessage(e,n[0],n,{},!0);return{logLevel:r.toUpperCase(),args:n}}))})))})),function(){t.forEach((function(e){return e()}))}};var o=n(r("./node_modules/@babel/runtime/helpers/typeof.js")),a=n(r("./packages/@logrocket/utils/src/enhanceFunc.ts")),i=r("./packages/@logrocket/exceptions/src/index.js")},"./packages/@logrocket/exceptions/src/Capture.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.captureMessage=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i={exceptionType:a?"CONSOLE":"MESSAGE",message:t,messageArgs:r,browserHref:window.location?window.location.href:""};(0,o.scrubException)(i,n),e.addEvent("lr.core.Exception",(function(){return i}))},t.captureException=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"WINDOW",s=n||a.default.computeStackTrace(t),u={exceptionType:c,errorType:s.name,message:s.message,browserHref:window.location?window.location.href:""};(0,o.scrubException)(u,r);var l={_stackTrace:(0,i.default)(s)};e.addEvent("lr.core.Exception",(function(){return u}),l)};var o=r("./packages/@logrocket/utils/src/scrubException.ts"),a=n(r("./packages/@logrocket/utils/src/TraceKit.js")),i=n(r("./packages/@logrocket/exceptions/src/stackTraceFromError.js"))},"./packages/@logrocket/exceptions/src/index.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js"),o=r("./node_modules/@babel/runtime/helpers/typeof.js");Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"registerExceptions",{enumerable:!0,get:function(){return a.default}}),t.Capture=void 0;var a=n(r("./packages/@logrocket/exceptions/src/registerExceptions.js")),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=c(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r("./packages/@logrocket/exceptions/src/Capture.js"));function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}t.Capture=i},"./packages/@logrocket/exceptions/src/raven/raven.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r("./node_modules/@babel/runtime/helpers/classCallCheck.js")),a=n(r("./node_modules/@babel/runtime/helpers/createClass.js")),i=n(r("./packages/@logrocket/utils/src/TraceKit.js")),c=Object.prototype;function s(e){return"function"==typeof e}function u(e,t,r,n){var o=e[t];e[t]=r(o),n&&n.push([e,t,o])}var l="undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{},d=function(){function e(t){var r=t.captureException;(0,o.default)(this,e),this._errorHandler=this._errorHandler.bind(this),this._ignoreOnError=0,this._wrappedBuiltIns=[],this.captureException=r,i.default.report.subscribe(this._errorHandler),this._instrumentTryCatch()}return(0,a.default)(e,[{key:"uninstall",value:function(){var e;for(i.default.report.unsubscribe(this._errorHandler);this._wrappedBuiltIns.length;){var t=(e=this._wrappedBuiltIns.shift())[0],r=e[1],n=e[2];t[r]=n}}},{key:"_errorHandler",value:function(e){this._ignoreOnError||this.captureException(e)}},{key:"_ignoreNextOnError",value:function(){var e=this;this._ignoreOnError+=1,setTimeout((function(){e._ignoreOnError-=1}))}},{key:"context",value:function(e,t,r){return s(e)&&(r=t||[],t=e,e=void 0),this.wrap(e,t).apply(this,r)}},{key:"wrap",value:function(e,t,r){var n,o,a=this;if(void 0===t&&!s(e))return e;if(s(e)&&(t=e,e=void 0),!s(t))return t;try{if(t.__lr__)return t;if(t.__lr_wrapper__)return t.__lr_wrapper__;if(!Object.isExtensible(t))return t}catch(d){return t}function u(){var n=[],o=arguments.length,c=!e||e&&!1!==e.deep;for(r&&s(r)&&r.apply(this,arguments);o--;)n[o]=c?a.wrap(e,arguments[o]):arguments[o];try{return t.apply(this,n)}catch(d){throw a._ignoreNextOnError(),a.captureException(i.default.computeStackTrace(d),e),d}}for(var l in t)n=t,o=l,c.hasOwnProperty.call(n,o)&&(u[l]=t[l]);return u.prototype=t.prototype,t.__lr_wrapper__=u,u.__lr__=!0,u.__inner__=t,u}},{key:"_instrumentTryCatch",value:function(){var e=this,t=e._wrappedBuiltIns;function r(t){return function(r,n){for(var o=new Array(arguments.length),a=0;a<o.length;++a)o[a]=arguments[a];var i=o[0];return s(i)&&(o[0]=e.wrap(i)),t.apply?t.apply(this,o):t(o[0],o[1])}}u(l,"setTimeout",r,t),u(l,"setInterval",r,t),l.requestAnimationFrame&&u(l,"requestAnimationFrame",(function(t){return function(r){return t(e.wrap(r))}}),t);for(var n,o,a=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],i=0;i<a.length;i++)o=void 0,(o=l[n=a[i]]&&l[n].prototype)&&o.hasOwnProperty&&o.hasOwnProperty("addEventListener")&&(u(o,"addEventListener",(function(t){return function(r,n,o,a){try{n&&n.handleEvent&&(n.handleEvent=e.wrap(n.handleEvent))}catch(i){}return t.call(this,r,e.wrap(n,void 0,void 0),o,a)}}),t),u(o,"removeEventListener",(function(e){return function(t,r,n,o){try{var a=null==r?void 0:r.__lr_wrapper__;a&&e.call(this,t,a,n,o)}catch(i){}return e.call(this,t,r,n,o)}}),void 0));var c=l.jQuery||l.$;c&&c.fn&&c.fn.ready&&u(c.fn,"ready",(function(t){return function(r){return t.call(this,e.wrap(r))}}),t)}}]),e}();t.default=d},"./packages/@logrocket/exceptions/src/registerExceptions.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js"),o=r("./node_modules/@babel/runtime/helpers/typeof.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=new a.default({captureException:function(t){i.captureException(e,null,null,t)}}),r=function(t){t.reason instanceof Error?i.captureException(e,t.reason,null,null,"UNHANDLED_REJECTION"):e.addEvent("lr.core.Exception",(function(){return{exceptionType:"UNHANDLED_REJECTION",message:t.reason||"Unhandled Promise rejection"}}))};return window.addEventListener("unhandledrejection",r),function(){window.removeEventListener("unhandledrejection",r),t.uninstall()}};var a=n(r("./packages/@logrocket/exceptions/src/raven/raven.js")),i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!==o(e)&&"function"!=typeof e)return{default:e};var r=c(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r("./packages/@logrocket/exceptions/src/Capture.js"));function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(c=function(e){return e?r:t})(e)}},"./packages/@logrocket/exceptions/src/stackTraceFromError.js":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(e){return null===e?void 0:e}return e.stack?e.stack.map((function(e){return{lineNumber:t(e.line),columnNumber:t(e.column),fileName:t(e.url),functionName:t(e.func)}})):void 0}},"./packages/@logrocket/network/src/fetchIntercept.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r("./node_modules/@babel/runtime/helpers/toConsumableArray.js")),a=r("./packages/@logrocket/network/src/registerXHR.js"),i=[];function c(e,t){for(var r=i.reduce((function(e,t){return[t].concat(e)}),[]),n=arguments.length,c=new Array(n>2?n-2:0),s=2;s<n;s++)c[s-2]=arguments[s];var u=Promise.resolve(c);return r.forEach((function(e){var r=e.request,n=e.requestError;(r||n)&&(u=u.then((function(e){return r.apply(void 0,[t].concat((0,o.default)(e)))}),(function(e){return n.apply(void 0,[t].concat((0,o.default)(e)))})))})),u=u.then((function(t){var r,n;(0,a.setActive)(!1);try{r=e.apply(void 0,(0,o.default)(t))}catch(i){n=i}if((0,a.setActive)(!0),n)throw n;return r})),r.forEach((function(e){var r=e.response,n=e.responseError;(r||n)&&(u=u.then((function(e){return r(t,e)}),(function(e){return n&&n(t,e)})))})),u}function s(e){if(e.fetch&&e.Promise){var t=e.fetch.polyfill;e.fetch=function(e){var t=0;return function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return c.apply(void 0,[e,t++].concat(n))}}(e.fetch),t&&(e.fetch.polyfill=t)}}var u=!1,l={register:function(e){return u||(u=!0,s(window)),i.push(e),function(){var t=i.indexOf(e);t>=0&&i.splice(t,1)}},clear:function(){i=[]}};t.default=l},"./packages/@logrocket/network/src/index.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{isReactNative:!1,isDisabled:!1};if(!0===(null==t?void 0:t.isDisabled))return function(){};var r=t.isReactNative,n=t.shouldAugmentNPS,o=t.shouldParseXHRBlob,d={},p=function(e){var t=e;if("object"===(0,a.default)(e)&&null!=e){var r=Object.getPrototypeOf(e);r!==Object.prototype&&null!==r||(t=JSON.stringify(e))}if(t&&t.length&&t.length>4096e3&&"string"==typeof t){var n=t.substring(0,1e3);return"".concat(n," ... LogRocket truncating to first 1000 characters.\n      Keep data under 4MB to prevent truncation. https://docs.logrocket.com/reference/network")}return e},g=function(t,r){var n=r.method;e.addEvent("lr.network.RequestEvent",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=e.isEnabled,a=void 0===o||o,i=e.requestSanitizer,c=void 0===i?function(e){return e}:i;if(!a)return null;var s=null;try{s=c(f(f({},r),{},{reqId:t}))}catch(h){console.error(h)}if(s){var u=s.url;if("undefined"!=typeof document&&"function"==typeof document.createElement){var g=document.createElement("a");g.href=s.url,u=g.href}return{reqId:t,url:u,headers:(0,l.default)(s.headers,(function(e){return"".concat(e)})),body:p(s.body),method:n,referrer:s.referrer||void 0,mode:s.mode||void 0,credentials:s.credentials||void 0}}return d[t]=!0,null}))},h=function(t,r){var n=r.method,o=r.status,a=r.responseType;e.addEvent("lr.network.ResponseEvent",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.isEnabled,c=void 0===i||i,s=e.responseSanitizer,u=void 0===s?function(e){return e}:s;if(!c)return null;if(d[t])return delete d[t],null;var g=null;try{g=u(f(f({},r),{},{reqId:t}))}catch(h){console.error(h)}return g?{reqId:t,responseType:a,status:g.status,headers:(0,l.default)(g.headers,(function(e){return"".concat(e)})),body:p(g.body),method:n}:{reqId:t,responseType:a,status:o,headers:{},body:null,method:n}}))},v=function(t){return e.isDisabled||!0===d[t]},b=(0,i.default)({addRequest:g,addResponse:h,isIgnored:v}),y=(0,u.default)({addRequest:g,addResponse:h,isIgnored:v,logger:e,shouldAugmentNPS:n,shouldParseXHRBlob:o}),m=(0,c.registerIonic)({addRequest:g,addResponse:h,isIgnored:v}),w=r?function(){}:(0,s.default)(e);return function(){w(),b(),y(),m()}};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),a=n(r("./node_modules/@babel/runtime/helpers/typeof.js")),i=n(r("./packages/@logrocket/network/src/registerFetch.js")),c=r("./packages/@logrocket/network/src/registerIonic.ts"),s=n(r("./packages/@logrocket/network/src/registerNetworkInformation.js")),u=n(r("./packages/@logrocket/network/src/registerXHR.js")),l=n(r("./packages/@logrocket/utils/src/mapValues.ts"));function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},"./packages/@logrocket/network/src/registerFetch.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.addRequest,r=e.addResponse,n=e.isIgnored,o="fetch-",a={},c=i.default.register({request:function(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];var c;if("undefined"!=typeof Request&&n[0]instanceof Request){var u;try{u=n[0].clone().text()}catch(d){u=Promise.resolve("LogRocket fetch error: ".concat(d.message))}c=u.then((function(e){return s(s({},l(n[0])),{},{body:e})}),(function(e){return s(s({},l(n[0])),{},{body:"LogRocket fetch error: ".concat(e.message)})}))}else c=Promise.resolve(s(s({},l(n[1])),{},{url:"".concat(n[0]),body:(n[1]||{}).body}));return c.then((function(r){return a[e]=r.method,t("".concat(o).concat(e),r),n}))},requestError:function(e,t){return Promise.reject(t)},response:function(e,t){var i,c;if(n("".concat(o).concat(e)))return t;if("text/event-stream"===t.headers.get("content-type"))c=Promise.resolve("LogRocket skipped consuming an event-stream body.");else{try{i=t.clone()}catch(p){var s={url:t.url,responseType:t.type.toUpperCase(),status:t.status,headers:u(t.headers),body:"LogRocket fetch error: ".concat(p.message),method:a[e]};return delete a[e],r("".concat(o).concat(e),s),t}try{if(window.TextDecoder&&i.body){var l=i.body.getReader(),d=new window.TextDecoder("utf-8"),f="";c=l.read().then((function e(t){var r=t.done,n=t.value;if(r)return f;var o=n?d.decode(n,{stream:!0}):"";return f+=o,l.read().then(e)}))}else c=i.text()}catch(g){c=Promise.resolve("LogRocket error reading body: ".concat(g.message))}}return c.catch((function(e){if(!("AbortError"===e.name&&e instanceof DOMException))return"LogRocket error reading body: ".concat(e.message)})).then((function(n){var i={url:t.url,responseType:t.type.toUpperCase(),status:t.status,headers:u(t.headers),body:n,method:a[e]};delete a[e],r("".concat(o).concat(e),i)})),t},responseError:function(e,t){var n={url:void 0,status:0,headers:{},body:"".concat(t)};return r("".concat(o).concat(e),n),Promise.reject(t)}});return c};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),a=n(r("./packages/@logrocket/utils/src/mapValues.ts")),i=n(r("./packages/@logrocket/network/src/fetchIntercept.js"));function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var u=function(e){return(0,a.default)(function(e){if(null==e||"function"!=typeof e.forEach)return e;var t={};return e.forEach((function(e,r){t[r]?t[r]="".concat(t[r],",").concat(e):t[r]="".concat(e)})),t}(e),(function(e){return"".concat(e)}))};function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{url:e.url,headers:u(e.headers),method:e.method&&e.method.toUpperCase(),referrer:e.referrer||void 0,mode:e.mode||void 0,credentials:e.credentials||void 0}}},"./packages/@logrocket/network/src/registerIonic.ts":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.mergeHeaders=O,t.serializeQueryParams=function(e,t){return S("",e,t)},t.appendQueryParamsString=P,t.processData=D,t.registerIonic=function(e){var t,r,n,o=e.addRequest,a=e.addResponse,i=e.isIgnored,s=null===(t=window.cordova)||void 0===t||null===(r=t.plugin)||void 0===r?void 0:r.http,u={},l=!1;if(void 0===s)return function(){};var d=null===(n=window.ionic)||void 0===n?void 0:n.platforms;if(void 0!==d&&"function"==typeof d.some&&d.some((function(e){return v.has(e)})))return function(){};var b=s.sendRequest,y=(0,c.default)((function(e,t,r){if(!i("".concat(h).concat(r)))try{var n={url:e.url||"",status:e.status<600&&e.status>=100?e.status:0,headers:e.headers||{},body:t?e.data:e.error,method:u[r].toUpperCase()};a("".concat(h).concat(r),n)}catch(c){var o={url:e.url||"",status:e.status<600&&e.status>=100?e.status:0,headers:e.headers||{},body:"LogRocket fetch error: ".concat(c.message),method:u[r].toUpperCase()};a("".concat(h).concat(r),o)}}));return s.sendRequest=function(e,t,r,n){var a=++L;if(!l)try{var i=function(e,t){var r,n=(e=e||{}).data;try{r=k(p,e.serializer||t.getDataSerializer(),"serializer / data payload type")}catch(o){r=k(g,e.serializer||t.getDataSerializer(),"serializer / data payload type"),n={}}return{data:n,filePath:e.filePath,followRedirect:e.followRedirect,headers:j(e.headers||{},m,"Invalid header type, must be string"),method:k(f,e.method||f[0],"method"),name:e.name,params:j(e.params||{},w,"Invalid param, must be of type string or array"),responseType:e.responseType,serializer:r,connectTimeout:e.connectTimeout,readTimeout:e.readTimeout,timeout:e.timeout}}(t,s),c=P(e,S("",i.params,!0)),d=function(e,t,r){var n=r.getHeaders("*")||{},o=function(e,t){var r=new URL(e),n=r.host;return t.getHeaders(n)||null}(e,r)||{};return O(O(n,o),t)}(e,i.headers,s),v=i.method||"get";u[a]=v;var _={url:c,method:v.toUpperCase(),headers:d||{},body:D(i.data||{},i.serializer)};o("".concat(h).concat(a),_)}catch(x){var E={url:e,method:(t.method||"get").toUpperCase(),headers:{},body:"LogRocket fetch error: ".concat(x.message)};o("".concat(h).concat(a),E)}return b(e,t,(function(e){l||(y(e,!0,a),delete u[a]),r(e)}),(function(e){l||(y(e,!1,a),delete u[a]),n(e)}))},function(){l=!0,s.sendRequest=b,u={}}};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),a=n(r("./node_modules/@babel/runtime/helpers/typeof.js")),i=n(r("./node_modules/@babel/runtime/helpers/toConsumableArray.js")),c=n(r("./packages/@logrocket/utils/src/protectFunc.ts"));function s(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}(e))||t){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){c=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(c)throw a}}}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var f=new Set(["get","put","post","patch","head","delete","options","upload","download"]),p=new Set(["urlencoded","json","utf8"]),g=new Set(["raw","multipart"]),h="ionic-",v=new Set(["desktop","mobileweb","pwa"]),b=new Set(["FormData"]),y=new Set,m=new Set(["string"]),w=new Set(["string","array"]),_={utf8:m,urlencoded:new Set(["object"]),json:new Set(["array","object"]),raw:new Set(["Uint8Array","ArrayBuffer"]),default:y};function k(e,t,r){if("string"!=typeof t)throw new Error("".concat(r," must be one of: ").concat((0,i.default)(e).join(", ")));if(t=t.trim().toLowerCase(),!e.has(t))throw new Error("".concat(r," must be one of: ").concat((0,i.default)(e).join(", ")));return t}function j(e,t,r){if("object"!==(0,a.default)(e))throw new Error(r);for(var n=0,o=Object.keys(e);n<o.length;n++){var i=o[n];if(!t.has((0,a.default)(e[i])))throw new Error(r)}return e}function O(e,t){return d(d({},e),t)}function E(e,t){return t?encodeURIComponent(e):e}function x(e,t,r){return e.length?r?"".concat(encodeURIComponent(e),"[").concat(encodeURIComponent(t),"]"):"".concat(e,"[").concat(t,"]"):r?encodeURIComponent(t):t}function R(e,t,r){var n,o=[],i=s(t);try{for(i.s();!(n=i.n()).done;){var c=n.value;Array.isArray(c)?o.push(R("".concat(e,"[]"),c,r)):"object"!==(0,a.default)(c)?o.push("".concat(x(e,"",r),"=").concat(E(c,r))):o.push(S("".concat(e,"[]").concat(c),r,void 0))}}catch(u){i.e(u)}finally{i.f()}return o.join("&")}function S(e,t,r){var n=[];for(var o in t)if(t.hasOwnProperty(o)){var i=e.length?"".concat(e,"[").concat(o,"]"):o;Array.isArray(t[o])?n.push(R(i,t[o],r)):"object"!==(0,a.default)(t[o])||null===t[o]?n.push("".concat(x(e,o,r),"=").concat(E(t[o],r))):n.push(S(i,t[o],r))}return n.join("&")}function P(e,t){if(!e.length||!t.length)return e;var r=new URL(e),n=r.host,o=r.pathname,a=r.search,i=r.hash,c=r.protocol;return"".concat(c,"//").concat(n).concat(o).concat(a.length?"".concat(a,"&").concat(t):"?".concat(t)).concat(i)}function D(e,t){var n=(0,a.default)(e),o=function(e){return _[e]||_.default}(t),c=function(e){return"multipart"===e?b:y}(t);if(c.size>0){var s=!1;if(c.forEach((function(t){r.g[t]&&e instanceof r.g[t]&&(s=!0)})),!s)throw new Error("INSTANCE_TYPE_MISMATCH_DATA ".concat((0,i.default)(c).join(", ")))}if(0===c.size&&!o.has(n))throw new Error("TYPE_MISMATCH_DATA ".concat((0,i.default)(o).join(", ")));return"utf8"===t?e:JSON.stringify(e,void 0,2)}var L=0},"./packages/@logrocket/network/src/registerNetworkInformation.js":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=void 0;function n(){var n={online:window.navigator.onLine,effectiveType:"UNKOWN"};window.navigator.onLine?window.navigator.connection&&window.navigator.connection.effectiveType&&(n.effectiveType=r[window.navigator.connection.effectiveType]||"UNKNOWN"):n.effectiveType="NONE",t&&n.online===t.online&&n.effectiveType===t.effectiveType||(t=n,e.addEvent("lr.network.NetworkStatusEvent",(function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).isEnabled;return void 0===e||e?n:null})))}return setTimeout(n),window.navigator.connection&&"function"==typeof window.navigator.connection.addEventListener&&window.navigator.connection.addEventListener("change",n),window.addEventListener("online",n),window.addEventListener("offline",n),function(){window.removeEventListener("offline",n),window.removeEventListener("online",n),window.navigator.connection&&"function"==typeof window.navigator.connection.removeEventListener&&window.navigator.connection.removeEventListener("change",n)}};var r={"slow-2g":"SLOW2G","2g":"TWOG","3g":"THREEG","4g":"FOURG"}},"./packages/@logrocket/network/src/registerXHR.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.setActive=function(e){s=e},t.default=function(e){var t=e.addRequest,r=e.addResponse,n=e.isIgnored,l=e.logger,d=e.shouldAugmentNPS,f=void 0===d||d,p=e.shouldParseXHRBlob,g=void 0!==p&&p,h=XMLHttpRequest,v=new WeakMap,b=!1,y="xhr-";return window._lrXMLHttpRequest=XMLHttpRequest,XMLHttpRequest=function(e,d){var p=new h(e,d);if(!s)return p;v.set(p,{xhrId:++u,headers:{}});var m=p.open,w=p.send;f&&(p.open=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];try{var n=t[1];if(window.URL&&"function"==typeof window.URL&&0===n.search(c.WOOTRIC_RESPONSES_REGEX)){var o=new window.URL(l.recordingURL);o.searchParams.set("nps","wootric");var a=new window.URL(n),i=a.searchParams.get("response[text]"),s=i?"".concat(i,"\n\n"):"";a.searchParams.set("response[text]","".concat(s,"<").concat(o.href,"|View LogRocket session>")),t[1]=a.href}}catch(u){}return m.apply(this,t)},p.send=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];try{var n=v.get(p);if(window.URL&&"function"==typeof window.URL&&n&&n.url&&0===n.url.search(c.DELIGHTED_RESPONSES_REGEX)&&t.length&&-1!==t[0].indexOf(c.DELIGHTED_FEEDBACK_PREFIX)){var o=new window.URL(l.recordingURL);o.searchParams.set("nps","delighted");var a=encodeURIComponent(o.href),s=t[0].split("&").map((function(e){if((0,i.default)(e,c.DELIGHTED_FEEDBACK_PREFIX)){var t=e===c.DELIGHTED_FEEDBACK_PREFIX;return"".concat(e).concat(t?"":"\n\n","<").concat(a,"|View LogRocket session>")}return e})).join("&");t[0]=s}}catch(u){}return w.apply(this,t)}),(0,a.default)(p,"open",(function(e,t){if(!b){var r=v.get(p);r.method=e,r.url=t}})),(0,a.default)(p,"send",(function(e){if(!b){var r=v.get(p);if(r){var n={url:r.url,method:r.method&&r.method.toUpperCase(),headers:(0,o.default)(r.headers||{},(function(e){return e.join(", ")})),body:e};t("".concat(y).concat(r.xhrId),n)}}})),(0,a.default)(p,"setRequestHeader",(function(e,t){if(!b){var r=v.get(p);r&&(r.headers=r.headers||{},r.headers[e]=r.headers[e]||[],r.headers[e].push(t))}}));var _={readystatechange:function(){if(!b&&4===p.readyState){var e=v.get(p);if(!e)return;if(n("".concat(y).concat(e.xhrId)))return;var t,o=(p.getAllResponseHeaders()||"").split(/[\r\n]+/).reduce((function(e,t){var r=e,n=t.split(": ");if(n.length>0){var o=n.shift(),a=n.join(": ");e[o]?r[o]+=", ".concat(a):r[o]=a}return r}),{});try{switch(p.responseType){case"json":t=l._shouldCloneResponse?JSON.parse(JSON.stringify(p.response)):p.response;break;case"arraybuffer":case"blob":t=p.response;break;case"document":t=p.responseXML;break;case"text":case"":t=p.responseText;break;default:t=""}}catch(c){t="LogRocket: Error accessing response."}var a={url:e.url,status:p.status,headers:o,body:t,method:(e.method||"").toUpperCase()};if(g&&a.body instanceof Blob){var i=new FileReader;i.readAsText(a.body),i.onload=function(){try{a.body=JSON.parse(i.result)}catch(t){}r("".concat(y).concat(e.xhrId),a)}}else r("".concat(y).concat(e.xhrId),a)}}};return Object.keys(_).forEach((function(e){p.addEventListener(e,_[e])})),p},XMLHttpRequest.prototype=h.prototype,["UNSENT","OPENED","HEADERS_RECEIVED","LOADING","DONE"].forEach((function(e){XMLHttpRequest[e]=h[e]})),function(){b=!0,XMLHttpRequest=h}};var o=n(r("./packages/@logrocket/utils/src/mapValues.ts")),a=n(r("./packages/@logrocket/utils/src/enhanceFunc.ts")),i=n(r("./packages/@logrocket/utils/src/startsWith.js")),c=r("./packages/@logrocket/utils/src/constants/nps.js"),s=!0,u=0},"./packages/@logrocket/now/src/index.js":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=Date.now.bind(Date),n=r(),o="undefined"!=typeof performance&&performance.now?performance.now.bind(performance):function(){return r()-n};t.default=o},"./packages/@logrocket/redux/src/createEnhancer.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.stateSanitizer,n=void 0===r?function(e){return e}:r,o=t.actionSanitizer,i=void 0===o?function(e){return e}:o;return function(t){return function(r,o,u){var l=t(r,o,u),d=l.dispatch,f=s++;return e.addEvent("lr.redux.InitialState",(function(){var e;try{e=n(l.getState())}catch(t){console.error(t.toString())}return{state:e,storeId:f}})),c(c({},l),{},{dispatch:function(t){var r,o,c=(0,a.default)();try{o=d(t)}catch(u){r=u}finally{var s=(0,a.default)()-c;e.addEvent("lr.redux.ReduxAction",(function(){var e=null,r=null;try{e=n(l.getState()),r=i(t)}catch(o){console.error(o.toString())}return e&&r?{storeId:f,action:r,duration:s,stateDelta:e}:null}))}if(r)throw r;return o}})}}};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),a=n(r("./packages/@logrocket/now/src/index.js"));function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var s=0},"./packages/@logrocket/redux/src/createMiddleware.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.stateSanitizer,n=void 0===r?function(e){return e}:r,i=t.actionSanitizer,c=void 0===i?function(e){return e}:i;return function(t){var r=a++;return e.addEvent("lr.redux.InitialState",(function(){var e;try{e=n(t.getState())}catch(o){console.error(o.toString())}return{state:e,storeId:r}})),function(a){return function(i){var s,u,l=(0,o.default)();try{u=a(i)}catch(f){s=f}finally{var d=(0,o.default)()-l;e.addEvent("lr.redux.ReduxAction",(function(){var e=null,o=null;try{e=n(t.getState()),o=c(i)}catch(a){console.error(a.toString())}return e&&o?{storeId:r,action:o,duration:d,stateDelta:e}:null}))}if(s)throw s;return u}}}};var o=n(r("./packages/@logrocket/now/src/index.js")),a=0},"./packages/@logrocket/redux/src/index.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createEnhancer",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"createMiddleware",{enumerable:!0,get:function(){return a.default}});var o=n(r("./packages/@logrocket/redux/src/createEnhancer.js")),a=n(r("./packages/@logrocket/redux/src/createMiddleware.js"))},"./packages/@logrocket/utils/src/TraceKit.js":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={collectWindowErrors:!0,debug:!1},o="undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{},a=[].slice,i="?",c=/^(?:Uncaught (?:exception: )?)?((?:Eval|Internal|Range|Reference|Syntax|Type|URI)Error): ?(.*)$/;function s(){return"undefined"==typeof document||void 0===document.location?"":document.location.href}n.report=function(){var e,t,r=[],u=null,l=null,d=null;function f(e,t){var o=null;if(!t||n.collectWindowErrors){for(var i in r)if(r.hasOwnProperty(i))try{r[i].apply(null,[e].concat(a.call(arguments,2)))}catch(c){o=c}if(o)throw o}}function p(t,r,o,a,u){if(d)n.computeStackTrace.augmentStackTraceWithInitialElement(d,r,o,t),g();else if(u)f(n.computeStackTrace(u),!0);else{var l,p={url:r,line:o,column:a},h=void 0,v=t;"[object String]"==={}.toString.call(t)&&(l=t.match(c))&&(h=l[1],v=l[2]),p.func=i,f({name:h,message:v,url:s(),stack:[p]},!0)}return!!e&&e.apply(this,arguments)}function g(){var e=d,t=u;u=null,d=null,l=null,f.apply(null,[e,!1].concat(t))}function h(e,t){var r=a.call(arguments,1);if(d){if(l===e)return;g()}var o=n.computeStackTrace(e);if(d=o,l=e,u=r,setTimeout((function(){l===e&&g()}),o.incomplete?2e3:0),!1!==t)throw e}return h.subscribe=function(n){t||(e=o.onerror,o.onerror=p,t=!0),r.push(n)},h.unsubscribe=function(e){for(var t=r.length-1;t>=0;--t)r[t]===e&&r.splice(t,1)},h.uninstall=function(){t&&(o.onerror=e,t=!1,e=void 0),r=[]},h}(),n.computeStackTrace=function(){function e(e){if(void 0!==e.stack&&e.stack){for(var t,r,n=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|<anonymous>).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,o=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|resource|\[native).*?)(?::(\d+))?(?::(\d+))?\s*$/i,a=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,c=e.stack.split("\n"),u=[],l=0,d=c.length;l<d;++l){if(t=n.exec(c[l])){var f=t[2]&&-1!==t[2].indexOf("native");r={url:f?null:t[2],func:t[1]||i,args:f?[t[2]]:[],line:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}else if(t=a.exec(c[l]))r={url:t[2],func:t[1]||i,args:[],line:+t[3],column:t[4]?+t[4]:null};else{if(!(t=o.exec(c[l])))continue;r={url:t[3],func:t[1]||i,args:t[2]?t[2].split(","):[],line:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}!r.func&&r.line&&(r.func=i),u.push(r)}return u.length?(u[0].column||void 0===e.columnNumber||(u[0].column=e.columnNumber+1),{name:e.name,message:e.message,url:s(),stack:u}):null}}function t(e,t,r,n){var o={url:t,line:r};if(o.url&&o.line){if(e.incomplete=!1,o.func||(o.func=i),e.stack.length>0&&e.stack[0].url===o.url){if(e.stack[0].line===o.line)return!1;if(!e.stack[0].line&&e.stack[0].func===o.func)return e.stack[0].line=o.line,!1}return e.stack.unshift(o),e.partial=!0,!0}return e.incomplete=!0,!1}function r(e,a){for(var c,u,l=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,d=[],f={},p=!1,g=r.caller;g&&!p;g=g.caller)if(g!==o&&g!==n.report){if(u={url:null,func:i,line:null,column:null},g.name?u.func=g.name:(c=l.exec(g.toString()))&&(u.func=c[1]),void 0===u.func)try{u.func=c.input.substring(0,c.input.indexOf("{"))}catch(v){}f[""+g]?p=!0:f[""+g]=!0,d.push(u)}a&&d.splice(0,a);var h={name:e.name,message:e.message,url:s(),stack:d};return t(h,e.sourceURL||e.fileName,e.line||e.lineNumber),h}function o(t,o){var a=null;o=null==o?0:+o;try{if(a=e(t))return a}catch(i){if(n.debug)throw i}try{if(a=r(t,o+1))return a}catch(i){if(n.debug)throw i}return{name:t.name,message:t.message,url:s()}}return o.augmentStackTraceWithInitialElement=t,o.computeStackTraceFromStackProp=e,o}();var u=n;t.default=u},"./packages/@logrocket/utils/src/constants/nps.js":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.DELIGHTED_FEEDBACK_PREFIX=t.DELIGHTED_RESPONSES_REGEX=t.WOOTRIC_RESPONSES_REGEX=void 0,t.WOOTRIC_RESPONSES_REGEX=/^https:\/\/production.wootric.com\/responses/,t.DELIGHTED_RESPONSES_REGEX=/^https:\/\/web.delighted.com\/e\/[a-zA-Z-]*\/c/,t.DELIGHTED_FEEDBACK_PREFIX="comment="},"./packages/@logrocket/utils/src/createUnsubListener.ts":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.createUnsubListener=function(e){return function(){e.clear()}},t.Handler=void 0;var o=n(r("./node_modules/@babel/runtime/helpers/classCallCheck.js")),a=n(r("./node_modules/@babel/runtime/helpers/createClass.js")),i=function(){function e(t){(0,o.default)(this,e),this._value=void 0,this._value=t}return(0,a.default)(e,[{key:"get",value:function(){return this._value}},{key:"clear",value:function(){this._value=void 0}}]),e}();t.Handler=i},"./packages/@logrocket/utils/src/enhanceFunc.ts":function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){if("function"!=typeof e[t])return o;try{var a=function(){for(var e,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var o=i.apply(this,r);return null===(e=c.get())||void 0===e||e.apply(this,r),o},i=e[t],c=new n.Handler(r);return e[t]=a,function(){c.clear(),e[t]===a&&(e[t]=i)}}catch(s){return o}};var n=r("./packages/@logrocket/utils/src/createUnsubListener.ts"),o=function(){}},"./packages/@logrocket/utils/src/logError.js":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r="undefined"!=typeof console&&console.error&&console.error.bind?console.error.bind(console):function(){};t.default=r},"./packages/@logrocket/utils/src/mapValues.ts":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(null==e)return{};var r={};return Object.keys(e).forEach((function(n){r[n]=t(e[n])})),r}},"./packages/@logrocket/utils/src/protectFunc.ts":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};return function(){var r;try{r=e.apply(void 0,arguments)}catch(i){if("undefined"!=typeof window&&window._lrdebug)throw i;var n=t(i);(0,a.default)("LogRocket",i),(0,o.default)(i,n)}return r}};var o=n(r("./packages/@logrocket/utils/src/sendTelemetryData.js")),a=n(r("./packages/@logrocket/utils/src/logError.js"))},"./packages/@logrocket/utils/src/scrubException.ts":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.scrubException=function(e,t){if(t){var r,n=a(s);try{for(n.s();!(r=n.n()).done;){var o=r.value,i=t[o];c(i)&&(e[o]=i.toString())}}catch(m){n.e(m)}finally{n.f()}var l,d=a(u);try{for(d.s();!(l=d.n()).done;){for(var f=l.value,p=t[f]||{},g={},h=0,v=Object.keys(p);h<v.length;h++){var b=v[h],y=p[b];c(y)&&(g[b.toString()]=y.toString())}e[f]=g}}catch(m){d.e(m)}finally{d.f()}}};var o=n(r("./node_modules/@babel/runtime/helpers/typeof.js"));function a(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return i(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}}(e))||t){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,c=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return c=e.done,e},e:function(e){s=!0,a=e},f:function(){try{c||null==r.return||r.return()}finally{if(s)throw a}}}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c(e){return/boolean|number|string/.test((0,o.default)(e))}var s=["level","logger"],u=["tags","extra"]},"./packages/@logrocket/utils/src/sendTelemetryData.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.sendTelemetry=function(e,t){"undefined"!=typeof window&&window._lrdebug?(0,a.default)(e):t&&t.extra&&t.extra.appID&&"function"==typeof t.extra.appID.indexOf&&0===t.extra.appID.indexOf("au2drp/")&&Math.random()>=.25||l(s({message:e},t))},t.default=function(e,t){try{var r,n,o=e.message;try{r=JSON.stringify(t).slice(0,1e3)}catch(c){try{r="Could not stringify payload: ".concat(Object.prototype.toString.call(t))}catch(s){}}try{n=i.default.computeStackTrace(e).stack.map((function(e){return{filename:e.url,lineno:e.line,colno:e.column,function:e.func||"?"}}))}catch(c){}l({message:o,extra:{stringPayload:r},exception:{values:[{type:e.type,value:o,stacktrace:{frames:n}}]}})}catch(c){(0,a.default)("Failed to send",c)}};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),a=n(r("./packages/@logrocket/utils/src/logError.js")),i=n(r("./packages/@logrocket/utils/src/TraceKit.js"));function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach((function(t){(0,o.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var u="ebaecd9a57844dd1dc6ccde131dc931d7f22ffb8";function l(e){try{var t,r,n=e.message,o="https://e.logrocket.com/api/3/store/?sentry_version=7&sentry_client=http%2F3.8.0&sentry_key=b64*****************************",i=JSON.stringify(s({message:n,logger:"javascript",platform:"javascript",request:{headers:{"User-Agent":"undefined"!=typeof navigator&&navigator.userAgent},url:"undefined"!=typeof location&&location.href},release:u,environment:(null===(t=window)||void 0===t||null===(r=t.__SDKCONFIG__)||void 0===r?void 0:r.scriptEnv)||"prod"},e));if("undefined"!=typeof window){var c=new(window._lrXMLHttpRequest||XMLHttpRequest);c.open("POST",o),c.send(i)}else"undefined"!=typeof fetch&&fetch(o,{method:"POST",body:i}).catch((function(e){(0,a.default)("Failed to send via fetch",e)}))}catch(l){(0,a.default)("Failed to send",l)}}},"./packages/@logrocket/utils/src/startsWith.js":function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return e&&t&&e.substring(r,r+t.length)===t}},"./packages/logrocket/src/LogRocket.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.MAX_QUEUE_SIZE=void 0;var o=n(r("./node_modules/@babel/runtime/helpers/classCallCheck.js")),a=n(r("./node_modules/@babel/runtime/helpers/createClass.js")),i=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),c=n(r("./node_modules/@babel/runtime/helpers/objectWithoutProperties.js")),s=n(r("./packages/@logrocket/network/src/index.js")),u=r("./packages/@logrocket/exceptions/src/index.js"),l=n(r("./packages/@logrocket/console/src/index.js")),d=r("./packages/@logrocket/redux/src/index.js");function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){(0,i.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}t.MAX_QUEUE_SIZE=1e3;var g=function(){function e(){var t=this;(0,o.default)(this,e),this._buffer=[],["log","info","warn","error","debug"].forEach((function(e){t[e]=function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];t.addEvent("lr.core.LogEvent",(function(){return"error"===e&&(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).shouldAggregateConsoleErrors&&u.Capture.captureMessage(t,n[0],n,{},!0),{logLevel:e.toUpperCase(),args:n}}),{shouldCaptureStackTrace:!0})}})),this._isInitialized=!1,this._installed=[],window._lr_surl_cb=this.getSessionURL.bind(this)}return(0,a.default)(e,[{key:"addEvent",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=Date.now();this._run((function(o){o.addEvent(e,t,p(p({},r),{},{timeOverride:n}))}))}},{key:"onLogger",value:function(e){for(this._logger=e;this._buffer.length>0;)this._buffer.shift()(this._logger)}},{key:"_run",value:function(e){if(!this._isDisabled)if(this._logger)e(this._logger);else{if(this._buffer.length>=1e3)return this._isDisabled=!0,console.warn("LogRocket: script did not load. Check that you have a valid network connection."),void this.uninstall();this._buffer.push(e.bind(this))}}},{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this._isInitialized){var r,n=t.shouldAugmentNPS,o=void 0===n||n,a=t.shouldParseXHRBlob,i=void 0!==a&&a,d=t.shouldDetectExceptions;(void 0===d||d)&&this._installed.push((0,u.registerExceptions)(this)),this._installed.push((0,s.default)(this,{shouldAugmentNPS:!!o,shouldParseXHRBlob:!!i,isDisabled:!1===(null==t||null===(r=t.network)||void 0===r?void 0:r.isEnabled)})),this._installed.push((0,l.default)(this)),this._isInitialized=!0,this._run((function(r){r.init(e,function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.ingestServer,r=(0,c.default)(e,["ingestServer"]);return t?p({serverURL:"".concat(t,"/i"),statsURL:"".concat(t,"/s")},r):r}(t))}))}}},{key:"start",value:function(){this._run((function(e){e.start()}))}},{key:"uninstall",value:function(){this._installed.forEach((function(e){return e()})),this._buffer=[],this._run((function(e){e.uninstall()}))}},{key:"identify",value:function(e,t){this._run((function(r){r.identify(e,t)}))}},{key:"startNewSession",value:function(){this._run((function(e){e.startNewSession()}))}},{key:"track",value:function(e,t){this._run((function(r){r.track(e,t)}))}},{key:"getSessionURL",value:function(e){if("function"!=typeof e)throw new Error("LogRocket: must pass callback to getSessionURL()");this._run((function(t){t.getSessionURL?t.getSessionURL(e):e(t.recordingURL)}))}},{key:"trackScrollEvent",value:function(e){this._logger&&this._logger.trackScrollEvent(e)}},{key:"getVersion",value:function(e){this._run((function(t){e(t.version)}))}},{key:"captureMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};u.Capture.captureMessage(this,e,[e],t)}},{key:"captureException",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};u.Capture.captureException(this,e,t)}},{key:"version",get:function(){return this._logger&&this._logger.version}},{key:"sessionURL",get:function(){return this._logger&&this._logger.recordingURL}},{key:"recordingURL",get:function(){return this._logger&&this._logger.recordingURL}},{key:"recordingID",get:function(){return this._logger&&this._logger.recordingID}},{key:"threadID",get:function(){return this._logger&&this._logger.threadID}},{key:"tabID",get:function(){return this._logger&&this._logger.tabID}},{key:"reduxEnhancer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,d.createEnhancer)(this,e)}},{key:"reduxMiddleware",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,d.createMiddleware)(this,e)}},{key:"isDisabled",get:function(){return!!(this._isDisabled||this._logger&&this._logger._isDisabled)}}]),e}();t.default=g},"./packages/logrocket/src/makeLogRocket.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){return new o.default};if("undefined"!=typeof navigator&&"ReactNative"===navigator.product)throw new Error(a);if("undefined"!=typeof window){if(window._disableLogRocket)return t();if(window.MutationObserver&&window.WeakMap){window._lrMutationObserver=window.MutationObserver;var n=r();return e(n),n}}return i()};var o=n(r("./packages/logrocket/src/LogRocket.js")),a="LogRocket on React Native requires the LogRocket React Native specific SDK. See setup guide here https://docs.logrocket.com/reference/react-native.",i=function(){return{init:function(){},uninstall:function(){},log:function(){},info:function(){},warn:function(){},error:function(){},debug:function(){},addEvent:function(){},identify:function(){},start:function(){},get threadID(){return null},get recordingID(){return null},get recordingURL(){return null},reduxEnhancer:function(){return function(e){return function(){return e.apply(void 0,arguments)}}},reduxMiddleware:function(){return function(){return function(e){return function(t){return e(t)}}}},track:function(){},getSessionURL:function(){},getVersion:function(){},startNewSession:function(){},onLogger:function(){},setClock:function(){},captureMessage:function(){},captureException:function(){}}}},"./packages/logrocket/src/setup.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.getDomainsAndEnv=c,t.setupBaseSDKCONFIG=s,t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.enterpriseServer,r=e.sdkVersion,n=void 0===r?"10.0.0":r,i=(0,o.default)(e,["enterpriseServer","sdkVersion"]),u=c(n),l=u.scriptEnv,d=u.scriptOrigin,f=u.scriptIngest,p=i.sdkServer||t,g=i.ingestServer||t||f,h=(0,a.default)((function(){var e=document.createElement("script");g&&(s(g),window.__SDKCONFIG__.scriptEnv=l),p?e.src="".concat(p,"/logger.min.js"):window.__SDKCONFIG__&&window.__SDKCONFIG__.loggerURL?e.src=window.__SDKCONFIG__.loggerURL:window._lrAsyncScript?e.src=window._lrAsyncScript:e.src="".concat(d,"/logger-1.min.js"),e.async=!0,document.head.appendChild(e),e.onload=function(){"function"==typeof window._LRLogger?setTimeout((function(){h.onLogger(new window._LRLogger({sdkVersion:n}))})):(console.warn("LogRocket: script execution has been blocked by a product or service."),h.uninstall())},e.onerror=function(){console.warn("LogRocket: script could not load. Check that you have a valid network connection."),h.uninstall()}}));return h};var o=n(r("./node_modules/@babel/runtime/helpers/objectWithoutProperties.js")),a=n(r("./packages/logrocket/src/makeLogRocket.js")),i={"cdn.logrocket.com":"https://r.logrocket.io","cdn.logrocket.io":"https://r.logrocket.io","cdn.lr-ingest.io":"https://r.lr-ingest.io","cdn.lr-in.com":"https://r.lr-in.com","cdn.lr-in-prod.com":"https://r.lr-in-prod.com","cdn.lr-ingest.com":"https://r.lr-ingest.com","cdn.ingest-lr.com":"https://r.ingest-lr.com","cdn.lr-intake.com":"https://r.lr-intake.com","cdn.intake-lr.com":"https://r.intake-lr.com","cdn.logr-ingest.com":"https://r.logr-ingest.com","cdn.lrkt-in.com":"https://r.lrkt-in.com","cdn.lgrckt-in.com":"https://r.lgrckt-in.com","cdn-staging.logrocket.io":"https://staging-i.logrocket.io","cdn-staging.lr-ingest.io":"https://staging-i.lr-ingest.io","cdn-staging.lr-in.com":"https://staging-i.lr-in.com","cdn-staging.lr-in-prod.com":"https://staging-i.lr-in-prod.com","cdn-staging.lr-ingest.com":"https://staging-i.lr-ingest.com","cdn-staging.ingest-lr.com":"https://staging-i.ingest-lr.com","cdn-staging.lr-intake.com":"https://staging-i.lr-intake.com","cdn-staging.intake-lr.com":"https://staging-i.intake-lr.com","cdn-staging.logr-ingest.com":"https://staging-i.logr-ingest.com","cdn-staging.lrkt-in.com":"https://staging-i.lrkt-in.com","cdn-staging.lgrckt-in.com":"https://staging-i.lgrckt-in.com"};function c(e){if("script"===e||"shopify-pixel"===e){try{var t=document.currentScript.src.match(/^(https?:\/\/([^\\]+))\/.+$/),r=t&&t[2];if(r&&i[r])return{scriptEnv:(n=r,n.startsWith("cdn-staging")?"staging":n.startsWith("localhost")?"development":"prod"),scriptOrigin:t&&t[1],scriptIngest:i[r]}}catch(o){}return{scriptEnv:"prod",scriptOrigin:"https://cdn.logrocket.io"}}return{scriptEnv:void 0,scriptOrigin:"https://cdn.lgrckt-in.com",scriptIngest:"https://r.lgrckt-in.com"};var n}function s(e){void 0===window.__SDKCONFIG__&&(window.__SDKCONFIG__={}),window.__SDKCONFIG__.serverURL="".concat(e,"/i"),window.__SDKCONFIG__.statsURL="".concat(e,"/s")}},"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js":function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/arrayLikeToArray.js");e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/classCallCheck.js":function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/createClass.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/toPropertyKey.js");function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/defineProperty.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/toPropertyKey.js");e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/interopRequireDefault.js":function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/iterableToArray.js":function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/nonIterableSpread.js":function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js");e.exports=function(e,t){if(null==e)return{};var r,o,a=n(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js":function(e){e.exports=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/toConsumableArray.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js"),o=r("./node_modules/@babel/runtime/helpers/iterableToArray.js"),a=r("./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js"),i=r("./node_modules/@babel/runtime/helpers/nonIterableSpread.js");e.exports=function(e){return n(e)||o(e)||a(e)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/toPrimitive.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/typeof.js").default;e.exports=function(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/toPropertyKey.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/typeof.js").default,o=r("./node_modules/@babel/runtime/helpers/toPrimitive.js");e.exports=function(e){var t=o(e,"string");return"symbol"===n(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/typeof.js":function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},"./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/arrayLikeToArray.js");e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var n={};return function(){var e=n,t=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=(0,t(r("./packages/logrocket/src/setup.js")).default)();e.default=o}(),n=n.default}()},r.exports=e());var e}export{e as C,n as r};
//# sourceMappingURL=chunk-DzkTCSaD.js.map

var e=Object.defineProperty,t=(t,o,a)=>((t,o,a)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[o]=a)(t,"symbol"!=typeof o?o+"":o,a);import{j as o,m as a}from"./chunk-L3uak9dD.js";import{r as n}from"./chunk-D3Ns84uO.js";import{S as r}from"./chunk-Bt4ub4Kz.js";import{c as s,a as i}from"./index-B8F6PHpu.js";const l=i("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-error text-error-foreground shadow-sm hover:bg-error/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:border-primary/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md",ghost:"hover:bg-accent hover:text-accent-foreground hover:shadow-sm",link:"text-primary underline-offset-4 hover:underline hover:text-primary/80",gradient:"bg-gradient-to-r from-primary to-secondary text-white shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]",success:"bg-success text-success-foreground shadow-sm hover:bg-success/90",warning:"bg-warning text-warning-foreground shadow-sm hover:bg-warning/90"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-11 rounded-lg px-8",xl:"h-12 rounded-xl px-10 text-base",icon:"h-10 w-10","icon-sm":"h-8 w-8","icon-lg":"h-12 w-12"}},defaultVariants:{variant:"default",size:"default"}});n.forwardRef((({className:e,variant:t,size:a,asChild:n=!1,loading:i=!1,leftIcon:c,rightIcon:d,children:u,disabled:h,...g},v)=>{const m=n?r:"button",p=h||i;return o.jsxs(m,{className:s(l({variant:t,size:a,className:e})),ref:v,disabled:p,"aria-disabled":p,...g,children:[i&&o.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"}),!i&&c&&c,u,!i&&d&&d]})})).displayName="Button";const c=new class{constructor(){t(this,"sounds",new Map),t(this,"config",{volume:.3,enabled:!0,preload:!0}),t(this,"initialized",!1),this.loadConfig(),this.initializeAudioContext()}loadConfig(){try{const e=localStorage.getItem("portfolio-sound-config");e&&(this.config={...this.config,...JSON.parse(e)})}catch(e){console.warn("Failed to load sound config:",e)}}saveConfig(){try{localStorage.setItem("portfolio-sound-config",JSON.stringify(this.config))}catch(e){console.warn("Failed to save sound config:",e)}}initializeAudioContext(){if(this.initialized)return;const e=()=>{this.initialized=!0,this.preloadSounds(),document.removeEventListener("click",e),document.removeEventListener("keydown",e),document.removeEventListener("touchstart",e)};document.addEventListener("click",e,{once:!0}),document.addEventListener("keydown",e,{once:!0}),document.addEventListener("touchstart",e,{once:!0})}createSoundDataUrl(e){const t=(new(window.AudioContext||window.webkitAudioContext)).sampleRate;switch(e){case"success":return this.generateTone([523.25,659.25,783.99],.3,t);case"error":return this.generateTone([220,261.63],.2,t);case"hover":default:return this.generateTone([440],.1,t);case"click":return this.generateTone([800],.05,t);case"toggle":return this.generateTone([523.25,659.25],.15,t)}}generateTone(e,t,o){const a=Math.floor(o*t),n=new ArrayBuffer(44+2*a),r=new DataView(n),s=(e,t)=>{for(let o=0;o<t.length;o++)r.setUint8(e+o,t.charCodeAt(o))};s(0,"RIFF"),r.setUint32(4,36+2*a,!0),s(8,"WAVE"),s(12,"fmt "),r.setUint32(16,16,!0),r.setUint16(20,1,!0),r.setUint16(22,1,!0),r.setUint32(24,o,!0),r.setUint32(28,2*o,!0),r.setUint16(32,2,!0),r.setUint16(34,16,!0),s(36,"data"),r.setUint32(40,2*a,!0);for(let l=0;l<a;l++){let t=0;const a=l/o;e.forEach((e=>{t+=Math.sin(2*Math.PI*e*a)})),t/=e.length;const n=Math.exp(3*-a);t*=n;const s=Math.max(-32768,Math.min(32767,32767*t));r.setInt16(44+2*l,s,!0)}const i=new Blob([n],{type:"audio/wav"});return URL.createObjectURL(i)}preloadSounds(){if(!this.config.preload||!this.config.enabled)return;["success","error","hover","click","toggle"].forEach((e=>{try{const t=new Audio;t.src=this.createSoundDataUrl(e),t.volume=this.config.volume,t.preload="auto",t.addEventListener("error",(()=>{console.warn(`Failed to load sound: ${e}`)})),this.sounds.set(e,t)}catch(t){console.warn(`Failed to create sound: ${e}`,t)}}))}play(e,t={}){if(this.config.enabled&&this.initialized)try{let o=this.sounds.get(e);o||(o=new Audio,o.src=this.createSoundDataUrl(e),this.sounds.set(e,o));const a=o.cloneNode();a.volume=(t.volume??this.config.volume)*this.config.volume;const n=a.play();n&&n.catch((e=>{"NotAllowedError"!==e.name&&console.warn("Sound play failed:",e)}))}catch(o){console.warn("Sound play error:",o)}}updateConfig(e){this.config={...this.config,...e},this.saveConfig(),void 0!==e.volume&&this.sounds.forEach((e=>{e.volume=this.config.volume}))}getConfig(){return{...this.config}}setEnabled(e){this.updateConfig({enabled:e})}setVolume(e){const t=Math.max(0,Math.min(1,e));this.updateConfig({volume:t})}isSupported(){return"undefined"!=typeof Audio&&"undefined"!=typeof AudioContext||void 0!==window.webkitAudioContext}},d=()=>c.getConfig(),u=(e={})=>{const[t,o]=n.useState(d()),[a,r]=n.useState({});n.useEffect((()=>{const e=()=>{o(d())};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}}),[]);const s=n.useCallback(((t,o={})=>{const n=Date.now(),s=e.throttle||100;if(a[t]&&n-a[t]<s)return;r((e=>({...e,[t]:n})));const i=o.volume??e.volume;((e,t)=>{c.play(e,t)})(t,i?{volume:i}:void 0)}),[e.volume,e.throttle,a]),i=n.useCallback((e=>{(e=>{c.setEnabled(e)})(e),o(d())}),[]),l=n.useCallback((e=>{(e=>{c.setVolume(e)})(e),o(d())}),[]);return{play:s,isEnabled:t.enabled,volume:t.volume,isSupported:c.isSupported(),setEnabled:i,setVolume:l,config:t}},h=()=>{const e=u({throttle:200});return{playSubmitSuccess:n.useCallback((()=>e.play("success")),[e]),playSubmitError:n.useCallback((()=>e.play("error")),[e]),playFieldFocus:n.useCallback((()=>e.play("hover",{volume:.05})),[e]),playFieldValid:n.useCallback((()=>e.play("click",{volume:.1})),[e]),...e}},g=()=>{const e=u({throttle:150});return{playPageTransition:n.useCallback((()=>e.play("toggle")),[e]),playMenuOpen:n.useCallback((()=>e.play("click")),[e]),playMenuClose:n.useCallback((()=>e.play("click",{volume:.1})),[e]),playButtonHover:n.useCallback((()=>e.play("hover",{volume:.08})),[e]),playButtonClick:n.useCallback((()=>e.play("click",{volume:.15})),[e]),...e}},v=()=>{const e=u({throttle:100});return{playCardHover:n.useCallback((()=>e.play("hover",{volume:.06})),[e]),playCardClick:n.useCallback((()=>e.play("click",{volume:.12})),[e]),playExpand:n.useCallback((()=>e.play("toggle",{volume:.1})),[e]),playCollapse:n.useCallback((()=>e.play("toggle",{volume:.08})),[e]),...e}},m=({children:e,onClick:t,href:n,variant:r="primary",size:s="md",icon:i,iconPosition:l="right",disabled:c=!1,loading:d=!1,className:u="",ariaLabel:h,target:v,rel:m,enableSound:p=!0})=>{const{playButtonHover:f,playButtonClick:b}=g(),y=`cta-button variant-${r} size-${s} ${u}`.trim(),w=()=>{p&&b(),null==t||t()},x=()=>{p&&f()},C={initial:{scale:1},hover:{scale:1.05,y:-2},tap:{scale:.98,y:0}},k=o.jsxs(a.div,{className:"flex items-center justify-center gap-2 relative z-10",variants:{initial:{y:0},hover:{y:-1}},children:[i&&"left"===l&&o.jsx(i,{className:"w-5 h-5","aria-hidden":"true"}),o.jsx("span",{children:d?o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"}),o.jsx("span",{children:"Carregando..."})]}):e}),i&&"right"===l&&!d&&o.jsx(i,{className:"w-5 h-5","aria-hidden":"true"})]});return n?o.jsx(a.a,{href:n,target:v,rel:m,className:y,"aria-label":h,onClick:w,onMouseEnter:x,style:{textDecoration:"none",pointerEvents:c?"none":"auto",opacity:c?.5:1},variants:C,initial:"initial",whileHover:"hover",whileTap:"tap",children:k}):o.jsx(a.button,{onClick:c?void 0:w,onMouseEnter:x,className:y,disabled:c||d,"aria-label":h,variants:C,initial:"initial",whileHover:"hover",whileTap:"tap",children:k})};export{m as C,g as a,h as b,v as u};
//# sourceMappingURL=chunk-Bthhypug.js.map

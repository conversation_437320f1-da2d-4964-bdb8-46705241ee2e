{"version": 3, "file": "CookieConsent-ek5hpc7h.js", "sources": ["../../src/components/CookieConsent.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { <PERSON>, <PERSON><PERSON>, <PERSON>, BarChart3, Eye } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface CookiePreferences {\n  necessary: boolean;\n  analytics: boolean;\n  marketing: boolean;\n}\n\nconst CookieConsent: React.FC = () => {\n  const { t } = useTranslation();\n  const [showBanner, setShowBanner] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n  const [preferences, setPreferences] = useState<CookiePreferences>({\n    necessary: true, // Always true, cannot be disabled\n    analytics: false,\n    marketing: false\n  });\n\n  useEffect(() => {\n    // Check if user has already made a choice\n    const consent = localStorage.getItem('cookie-consent');\n    if (!consent) {\n      // Show banner after a short delay\n      setTimeout(() => setShowBanner(true), 2000);\n    } else {\n      // Load saved preferences\n      try {\n        const savedPreferences = JSON.parse(consent);\n        setPreferences(savedPreferences);\n        // Initialize analytics based on consent\n        if (savedPreferences.analytics) {\n          initializeAnalytics();\n        }\n      } catch (error) {\n        console.error('Error parsing cookie consent:', error);\n      }\n    }\n  }, []);\n\n  const initializeAnalytics = () => {\n    // Initialize Google Analytics\n    if (typeof window !== 'undefined' && window.gtag) {\n      window.gtag('consent', 'update', {\n        analytics_storage: 'granted'\n      });\n    }\n\n    // Initialize LogRocket\n    if (typeof window !== 'undefined' && window.LogRocket) {\n      // LogRocket is already initialized, just enable it\n      console.log('Analytics enabled via cookie consent');\n    }\n  };\n\n  const handleAcceptAll = () => {\n    const newPreferences = {\n      necessary: true,\n      analytics: true,\n      marketing: true\n    };\n    \n    setPreferences(newPreferences);\n    localStorage.setItem('cookie-consent', JSON.stringify(newPreferences));\n    localStorage.setItem('cookie-consent-date', new Date().toISOString());\n    \n    initializeAnalytics();\n    setShowBanner(false);\n  };\n\n  const handleAcceptNecessary = () => {\n    const newPreferences = {\n      necessary: true,\n      analytics: false,\n      marketing: false\n    };\n    \n    setPreferences(newPreferences);\n    localStorage.setItem('cookie-consent', JSON.stringify(newPreferences));\n    localStorage.setItem('cookie-consent-date', new Date().toISOString());\n    \n    setShowBanner(false);\n  };\n\n  const handleSavePreferences = () => {\n    localStorage.setItem('cookie-consent', JSON.stringify(preferences));\n    localStorage.setItem('cookie-consent-date', new Date().toISOString());\n    \n    if (preferences.analytics) {\n      initializeAnalytics();\n    }\n    \n    setShowBanner(false);\n    setShowDetails(false);\n  };\n\n  const handlePreferenceChange = (type: keyof CookiePreferences) => {\n    if (type === 'necessary') return; // Cannot disable necessary cookies\n    \n    setPreferences(prev => ({\n      ...prev,\n      [type]: !prev[type]\n    }));\n  };\n\n  if (!showBanner) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ y: 100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        exit={{ y: 100, opacity: 0 }}\n        transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}\n        className=\"fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg\"\n        role=\"dialog\"\n        aria-labelledby=\"cookie-consent-title\"\n        aria-describedby=\"cookie-consent-description\"\n      >\n        <div className=\"max-w-7xl mx-auto p-4\">\n          {!showDetails ? (\n            // Simple Banner\n            <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\">\n              <div className=\"flex items-start gap-3 flex-1\">\n                <Cookie className=\"w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 flex-shrink-0\" />\n                <div>\n                  <h3 id=\"cookie-consent-title\" className=\"font-semibold text-gray-900 dark:text-white mb-1\">\n                    {t('cookies.title')}\n                  </h3>\n                  <p id=\"cookie-consent-description\" className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    {t('cookies.description')}{' '}\n                    <button\n                      onClick={() => setShowDetails(true)}\n                      className=\"text-blue-600 dark:text-blue-400 hover:underline font-medium\"\n                    >\n                      {t('cookies.learnMore')}\n                    </button>\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"flex flex-col sm:flex-row gap-2 w-full sm:w-auto\">\n                <button\n                  onClick={handleAcceptNecessary}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  {t('cookies.acceptNecessary')}\n                </button>\n                <button\n                  onClick={() => setShowDetails(true)}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  {t('cookies.customize')}\n                </button>\n                <button\n                  onClick={handleAcceptAll}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors\"\n                >\n                  {t('cookies.acceptAll')}\n                </button>\n              </div>\n            </div>\n          ) : (\n            // Detailed Preferences\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {t('cookies.preferences.title')}\n                </h3>\n                <button\n                  onClick={() => setShowDetails(false)}\n                  className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n                  aria-label={t('common.close')}\n                >\n                  <X className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              <div className=\"grid gap-4\">\n                {/* Necessary Cookies */}\n                <div className=\"flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <Shield className=\"w-5 h-5 text-green-600 dark:text-green-400 mt-1\" />\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {t('cookies.types.necessary.title')}\n                      </h4>\n                      <span className=\"text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded\">\n                        {t('cookies.required')}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      {t('cookies.types.necessary.description')}\n                    </p>\n                  </div>\n                </div>\n\n                {/* Analytics Cookies */}\n                <div className=\"flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <BarChart3 className=\"w-5 h-5 text-blue-600 dark:text-blue-400 mt-1\" />\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {t('cookies.types.analytics.title')}\n                      </h4>\n                      <label className=\"relative inline-flex items-center cursor-pointer\">\n                        <input\n                          type=\"checkbox\"\n                          checked={preferences.analytics}\n                          onChange={() => handlePreferenceChange('analytics')}\n                          className=\"sr-only peer\"\n                        />\n                        <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                      </label>\n                    </div>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      {t('cookies.types.analytics.description')}\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      {t('cookies.types.analytics.providers')}: Google Analytics, LogRocket\n                    </p>\n                  </div>\n                </div>\n\n                {/* Marketing Cookies */}\n                <div className=\"flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <Eye className=\"w-5 h-5 text-purple-600 dark:text-purple-400 mt-1\" />\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {t('cookies.types.marketing.title')}\n                      </h4>\n                      <label className=\"relative inline-flex items-center cursor-pointer\">\n                        <input\n                          type=\"checkbox\"\n                          checked={preferences.marketing}\n                          onChange={() => handlePreferenceChange('marketing')}\n                          className=\"sr-only peer\"\n                        />\n                        <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                      </label>\n                    </div>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      {t('cookies.types.marketing.description')}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\">\n                <button\n                  onClick={handleAcceptNecessary}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n                >\n                  {t('cookies.acceptNecessary')}\n                </button>\n                <button\n                  onClick={handleSavePreferences}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors\"\n                >\n                  {t('cookies.savePreferences')}\n                </button>\n                <button\n                  onClick={handleAcceptAll}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors\"\n                >\n                  {t('cookies.acceptAll')}\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default CookieConsent;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "t", "useTranslation", "showBanner", "setShowBanner", "useState", "showDetails", "setShowDetails", "preferences", "setPreferences", "necessary", "analytics", "marketing", "useEffect", "consent", "localStorage", "getItem", "savedPreferences", "JSON", "parse", "initializeAnalytics", "error", "setTimeout", "window", "gtag", "analytics_storage", "handleAcceptAll", "newPreferences", "setItem", "stringify", "Date", "toISOString", "handleAcceptNecessary", "handlePreferenceChange", "type", "prev", "AnimatePresence", "motion", "div", "initial", "y", "opacity", "animate", "exit", "transition", "duration", "ease", "className", "role", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "children", "h3", "button", "onClick", "aria-label", "X", "Shield", "h4", "span", "p", "BarChart3", "label", "input", "checked", "onChange", "Eye", "<PERSON><PERSON>", "id"], "mappings": "sOAWA,MAAMA,EAA0B,KACxB,MAAAC,EAAEA,GAAMC,KACPC,EAAYC,GAAiBC,EAAAA,UAAS,IACtCC,EAAaC,GAAkBF,EAAAA,UAAS,IACxCG,EAAaC,GAAkBJ,WAA4B,CAChEK,WAAW,EACXC,WAAW,EACXC,WAAW,IAGbC,EAAAA,WAAU,KAEFC,MAAAA,EAAUC,aAAaC,QAAQ,kBACrC,GAAKF,EAKC,IACIG,MAAAA,EAAmBC,KAAKC,MAAML,GACpCL,EAAeQ,GAEXA,EAAiBN,WACnBS,UAEKC,GACCA,QAAAA,MAAM,gCAAiCA,EAAAA,MAXjDC,YAAW,IAAMlB,GAAc,IAAO,IAYtC,GAED,IAEH,MAAMgB,EAAsB,KAEJ,oBAAXG,QAA0BA,OAAOC,MACnCA,OAAAA,KAAK,UAAW,SAAU,CAC/BC,kBAAmB,WACrB,EAUEC,EAAkB,KACtB,MAAMC,EAAiB,CACrBjB,WAAW,EACXC,WAAW,EACXC,WAAW,GAGbH,EAAekB,GACfZ,aAAaa,QAAQ,iBAAkBV,KAAKW,UAAUF,IACtDZ,aAAaa,QAAQ,uBAAuB,IAAIE,MAAOC,eAEvDX,IACAhB,GAAc,EAAA,EAGV4B,EAAwB,KAC5B,MAAML,EAAiB,CACrBjB,WAAW,EACXC,WAAW,EACXC,WAAW,GAGbH,EAAekB,GACfZ,aAAaa,QAAQ,iBAAkBV,KAAKW,UAAUF,IACtDZ,aAAaa,QAAQ,uBAAuB,IAAIE,MAAOC,eAEvD3B,GAAc,EAAA,EAeV6B,EAA0BC,IACjB,cAATA,GAEJzB,GAAe0B,IAAS,IACnBA,EACHD,CAACA,IAAQC,EAAKD,MAChB,EAGE,OAAC/B,QAGFiC,EAAAA,UACEC,EAAAA,IAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,EAAG,IAAKC,QAAS,GAC5BC,QAAS,CAAEF,EAAG,EAAGC,QAAS,GAC1BE,KAAM,CAAEH,EAAG,IAAKC,QAAS,GACzBG,WAAY,CAAEC,SAAU,GAAKC,KAAM,CAAC,IAAM,EAAG,GAAK,IAClDC,UAAU,uHACVC,KAAK,SACLC,kBAAgB,uBAChBC,mBAAiB,6BAEjBC,eAACb,MAAAA,CAAIS,UAAU,wBACZI,SAAC7C,SA4CCgC,MAAAA,CAAIS,UAAU,6BACZT,MAAAA,CAAIS,UAAU,oDACZK,KAAAA,CAAGL,UAAU,+DACX9C,EAAE,qCAEJoD,SAAAA,CACCC,QAAS,IAAM/C,GAAe,GAC9BwC,UAAU,oFACVQ,aAAYtD,EAAE,gBAEdkD,eAACK,EAAAA,CAAET,UAAU,wBAIhBT,MAAAA,CAAIS,UAAU,8BAEZT,MAAAA,CAAIS,UAAU,oFACZU,EAAAA,CAAOV,UAAU,2DACjBT,MAAAA,CAAIS,UAAU,0BACZT,MAAAA,CAAIS,UAAU,yDACZW,KAAAA,CAAGX,UAAU,qDACX9C,EAAE,yCAEJ0D,OAAAA,CAAKZ,UAAU,uGACb9C,EAAE,+BAGN2D,IAAAA,CAAEb,UAAU,oDACV9C,EAAE,sDAMRqC,MAAAA,CAAIS,UAAU,oFACZc,EAAAA,CAAUd,UAAU,yDACpBT,MAAAA,CAAIS,UAAU,0BACZT,MAAAA,CAAIS,UAAU,yDACZW,KAAAA,CAAGX,UAAU,qDACX9C,EAAE,0CAEJ6D,QAAAA,CAAMf,UAAU,mEACdgB,QAAAA,CACC7B,KAAK,WACL8B,QAASxD,EAAYG,UACrBsD,SAAU,IAAMhC,EAAuB,aACvCc,UAAU,uBAEXT,MAAAA,CAAIS,UAAU,4cAGlBa,IAAAA,CAAEb,UAAU,oDACV9C,EAAE,gDAEJ2D,IAAAA,CAAEb,UAAU,0DACV9C,EAAE,qCAAqC,gDAM7CqC,MAAAA,CAAIS,UAAU,oFACZmB,EAAAA,CAAInB,UAAU,6DACdT,MAAAA,CAAIS,UAAU,0BACZT,MAAAA,CAAIS,UAAU,yDACZW,KAAAA,CAAGX,UAAU,qDACX9C,EAAE,0CAEJ6D,QAAAA,CAAMf,UAAU,mEACdgB,QAAAA,CACC7B,KAAK,WACL8B,QAASxD,EAAYI,UACrBqD,SAAU,IAAMhC,EAAuB,aACvCc,UAAU,uBAEXT,MAAAA,CAAIS,UAAU,4cAGlBa,IAAAA,CAAEb,UAAU,oDACV9C,EAAE,yDAMVqC,MAAAA,CAAIS,UAAU,qGACZM,SAAAA,CACCC,QAAStB,EACTe,UAAU,6KAET9C,EAAE,mCAEJoD,SAAAA,CACCC,QA7Kc,KAC5BvC,aAAaa,QAAQ,iBAAkBV,KAAKW,UAAUrB,IACtDO,aAAaa,QAAQ,uBAAuB,IAAIE,MAAOC,eAEnDvB,EAAYG,WACdS,IAGFhB,GAAc,GACdG,GAAe,EAAA,EAqKDwC,UAAU,+GAET9C,EAAE,mCAEJoD,SAAAA,CACCC,QAAS5B,EACTqB,UAAU,iHAET9C,EAAE,oCAhJRqC,MAAAA,CAAIS,UAAU,+FACZT,MAAAA,CAAIS,UAAU,gDACZoB,EAAAA,CAAOpB,UAAU,uEACjBT,MAAAA,iBACEc,KAAAA,CAAGgB,GAAG,uBAAuBrB,UAAU,4DACrC9C,EAAE,0BAEJ2D,IAAAA,CAAEQ,GAAG,6BAA6BrB,UAAU,qDAC1C9C,EAAE,uBAAwB,UAC1BoD,SAAAA,CACCC,QAAS,IAAM/C,GAAe,GAC9BwC,UAAU,wEAET9C,EAAE,uCAMVqC,MAAAA,CAAIS,UAAU,mEACZM,SAAAA,CACCC,QAAStB,EACTe,UAAU,6KAET9C,EAAE,mCAEJoD,SAAAA,CACCC,QAAS,IAAM/C,GAAe,GAC9BwC,UAAU,6KAET9C,EAAE,6BAEJoD,SAAAA,CACCC,QAAS5B,EACTqB,UAAU,+GAET9C,EAAE,mCArDK"}
{"navigation": {"profile": "Perfil", "projects": "Projetos", "backlog": "Backlog", "contact": "Contato", "home": "Início", "goToProfile": "Ir para sessão Perfil", "goToProjetos": "Ir para sessão Projetos", "goToBacklog": "Ir para sessão Backlog", "goToContato": "Ir para sessão Contato"}, "profile": {"title": "UX/Product Designer com foco em estratégia, impacto e experiência", "bio": "Sou UX/Product Designer com forte atuação no design de produtos digitais focados em experiência do usuário, conversão e impacto de negócio. Com background em Marketing Digital, SEO e IA, integro estratégia, design e usabilidade em processos contínuos de melhoria e inovação.", "exploreProjects": "Explore projetos", "letsChat": "Vamos Conversar", "downloadCV": "Download CV", "linkedin": "LinkedIn"}, "projects": {"title": "Projetos", "overview": "Visão Geral", "discovery": "Descoberta", "solution": "Solução", "iteration": "Iteração", "outcomes": "Resul<PERSON><PERSON>", "insights": "Insights", "seeMore": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "seeLess": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "projectImage": "Imagem do projeto", "fgvLaw": {"title": "FGV LAW", "category": "Navegação e Usabilidade", "overview": "Reestruturação da área de cursos jurídicos da FGV LAW com foco em usabilidade e organização da informação. O projeto teve como objetivo aumentar a visibilidade das ofertas e facilitar a navegação entre opções.", "discovery": "Identifiquei que os usuários enfrentavam dificuldade para localizar e comparar cursos. A estrutura atual gerava confusão e dispersão. Através de entrevistas e análise heurística, percebi que o excesso de conteúdo não estava ajudando na decisão, e sim atrapalhando.", "solution": "Projetei um novo painel com sistema de abas e filtros temáticos, reorganizando a hierarquia visual e informacional. As opções foram agrupadas com base nos objetivos dos usuários (área jurídica, tipo de curso, carga horária).", "iteration": "Após testes com usuários, simplificamos a terminologia dos filtros e ajustamos a ordem das informações com base em feedback direto. Refinei o layout para leitura rápida e adaptação mobile.", "outcomes": ["Aumento significativo na visibilidade e interação com os cursos", "Melhora na taxa de conversão de acessos em inscrições ou leads", "Redução do tempo de navegação até a escolha do curso desejado"], "insights": "A estrutura de navegação precisa guiar, não apenas mostrar. Clareza e agrupamento relevante influenciam diretamente a percepção de valor de um curso."}, "direitoGV": {"title": "Direito GV", "category": "Mapas, Fluxos e Pesquisa", "overview": "Reorganização da área de pesquisa da Direito GV para melhorar a visibilidade dos projetos acadêmicos e facilitar o acesso a informações sobre pesquisadores e suas linhas de trabalho.", "discovery": "A área de pesquisa estava fragmentada e pouco acessível. Pesquisadores tinham dificuldade para divulgar seus trabalhos e usuários externos não conseguiam encontrar informações relevantes sobre projetos em andamento.", "solution": "Desenvolvi uma nova arquitetura de informação com categorização por áreas temáticas, perfis de pesquisadores e linha do tempo de projetos. Criei também um sistema de busca avançada.", "iteration": "Realizamos testes com alunos, professores e pesquisadores. A navegação foi ajustada com base em feedback sobre nomenclatura e ordem de prioridades. Validei cada alteração com os stakeholders envolvidos.", "outcomes": ["Redução no tempo de navegação para encontrar projetos ou temas específicos", "Aumento no número de visitas às páginas de pesquisadores", "Valorização institucional da produção acadêmica"], "insights": "Áreas institucionais ganham relevância quando são navegáveis, atualizadas e refletidas de forma estratégica na arquitetura da informação."}, "taliparts": {"title": "Taliparts", "category": "UX Estratégico + B2B", "overview": "Projeto de estruturação e validação digital da Taliparts, focado na publicação de peças automotivas no Mercado Livre, com integração às operações físicas. O objetivo era aprender rápido, com baixo custo, sobre demanda real, comportamento do comprador e diferencial competitivo.", "discovery": "Conduzi benchmark detalhado com concorrentes do setor automotivo. Entrevistei mecânicos e lojistas, modelei personas e apliquei a Matriz CSD para identificar certezas, suposições e dúvidas no catálogo físico.", "solution": "Criei uma estratégia de validação com SEO para Mercado Livre, padronização visual de anúncios, categorização centrada no vocabulário do comprador e histórico de buscas. Também organizei KPIs e defini plano de priorização de produtos.", "iteration": "Testei produtos por blocos temáticos, monitorando cliques, perguntas e taxa de conversão. Refinei descrições, títulos e até a seleção de itens com base em performance real.", "outcomes": ["Crescimento de vendas com os produtos priorizados estrategicamente", "Redução de dúvidas dos compradores por melhorias nas descrições", "Criação de processo replicável de publicação + análise + reposicionamento"], "insights": "Validar digitalmente com baixo custo é possível — e necessário. A lógica de produto precisa considerar contexto físico, vocabulário técnico e diferenciais percebidos pelo cliente."}, "tvInstitucional": {"title": "FGV TV Institucional", "category": "Engajamento e Comunicação Visual", "overview": "Criação de um sistema visual para TVs no hall da FGV, com o objetivo de comunicar eventos e atualizações institucionais de forma atrativa, dinâmica e acessível a alunos e visitantes.", "discovery": "Alunos ignoravam murais físicos e e-mails institucionais. Identifiquei que a linguagem dos canais era desatualizada e pouco integrada com a rotina visual dos espaços.", "solution": "Implementei um painel digital com curadoria de conteúdo semanal, foco em ritmo visual e clareza imediata das mensagens. A plataforma foi pensada para ser automatizada, com flexibilidade de atualização remota.", "iteration": "Testamos tipos de animações, tempo de exibição e contraste. Ajustamos o calendário visual e otimizamos o layout com base em feedback de alunos e coordenação.", "outcomes": ["Aumento de 72% no engajamento com eventos", "Retenção de mensagens em painéis muito superior a e-mails anteriores", "Reaproveitamento da solução por outros setores da escola"], "insights": "Ambientes físicos também são interfaces. Quando bem projetados, informam, engajam e conectam — sem precisar de login."}}, "backlog": {"title": "Ciclo de Backlogs Estratégicos", "description": "Desafios reais enfrentados por stakeholders e soluções de UX aplicadas estrategicamente para resolver problemas de negócio.", "solution": "Solução", "result": "<PERSON><PERSON><PERSON><PERSON>", "note": "<PERSON>a", "noItems": "Nenhum item nesta página.", "previous": "Anterior", "next": "Próxima", "page": "<PERSON><PERSON><PERSON><PERSON>", "of": "de"}, "contact": {"title": "Vamos conversar?", "description": "Estou sempre aberto a novas oportunidades e colaborações. Entre em contato para discutir projetos, parcerias ou apenas trocar ideias sobre UX e design de produtos.", "form": {"name": "Nome", "email": "E-mail", "subject": "<PERSON><PERSON><PERSON>", "message": "Mensagem", "send": "Enviar mensagem", "sending": "Enviando...", "success": "Mensagem enviada com sucesso!", "error": "Erro ao enviar mensagem. Tente novamente.", "nameRequired": "Nome é obrigatório", "emailRequired": "E-mail é obrigatório", "emailInvalid": "E-mail inválido", "subjectRequired": "Assunto é obrigatório", "messageRequired": "Mensagem é obrigatória"}, "info": {"email": "<EMAIL>", "location": "São Paulo, Brasil", "availability": "Disponível para projetos freelance e oportunidades full-time"}, "social": {"linkedin": "LinkedIn", "whatsapp": "WhatsApp", "email": "E-mail"}}, "accessibility": {"title": "Acessibilidade", "subtitle": "Personalize sua experiência de navegação", "description": "Configure as opções de acessibilidade para melhorar sua experiência.", "instructions": "Use as opções abaixo para personalizar a interface.", "shortcut": "Atalho: Shift + A", "openMenu": "Abrir menu de acessibilidade", "closeMenu": "Fechar menu de acessibilidade", "menuLabel": "Menu de Acessibilidade", "menuTooltip": "Configurações de acessibilidade (Shift + A)"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Sua opinião é importante", "description": "Compartilhe sua experiência e sugestões", "form": {"type": "Tipo de <PERSON>", "message": "<PERSON>a mensagem", "email": "Seu e-mail (opcional)", "send": "Enviar feedback", "sending": "Enviando...", "success": "Feedback enviado com sucesso!", "error": "Erro ao enviar feedback. Tente novamente.", "messageRequired": "Mensagem é obrigatória"}}}
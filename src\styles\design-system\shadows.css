/**
 * 🎯 SISTEMA DE SOMBRAS
 * 
 * Sombras baseadas nos tokens
 * Suporte ao modo escuro
 */

/* ===== SHADOW UTILITIES ===== */
.shadow-none { box-shadow: var(--shadow-none) !important; }
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-base) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }
.shadow-inner { box-shadow: var(--shadow-inner) !important; }

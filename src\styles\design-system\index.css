/**
 * 🎯 DESIGN SYSTEM UNIFICADO
 * 
 * Sistema de design tokens centralizado
 * WCAG 2.2 compliant
 * Suporte completo ao modo escuro
 */

/* ===== IMPORTAÇÕES DO DESIGN SYSTEM ===== */
@import './tokens.css';
@import './colors.css';
@import './typography.css';
@import './spacing.css';
@import './shadows.css';
@import './animations.css';
@import './themes.css';

/* ===== DESIGN TOKENS BASE ===== */
:root {
  /* ===== CORES PRIMÁRIAS ===== */
  --color-primary: #1d4ed8;
  --color-primary-rgb: 29, 78, 216;
  --color-primary-hover: #1e40af;
  --color-primary-active: #1e3a8a;
  
  /* ===== CORES SECUNDÁRIAS ===== */
  --color-secondary: #059669;
  --color-secondary-rgb: 5, 150, 105;
  --color-secondary-hover: #047857;
  --color-secondary-active: #065f46;
  
  /* ===== CORES DE TEXTO ===== */
  --color-text: #111827;
  --color-text-muted: #374151;
  --color-text-light: #6b7280;
  --color-text-inverse: #ffffff;
  
  /* ===== CORES DE BACKGROUND ===== */
  --color-bg: #ffffff;
  --color-surface: #f9fafb;
  --color-surface-hover: #f3f4f6;
  --color-surface-active: #e5e7eb;
  
  /* ===== CORES DE BORDA ===== */
  --color-border: #d1d5db;
  --color-border-hover: #9ca3af;
  --color-border-active: #6b7280;
  
  /* ===== CORES DE STATUS ===== */
  --color-success: #10b981;
  --color-success-bg: #d1fae5;
  --color-error: #ef4444;
  --color-error-bg: #fee2e2;
  --color-warning: #f59e0b;
  --color-warning-bg: #fef3c7;
  --color-info: #3b82f6;
  --color-info-bg: #dbeafe;
  
  /* ===== TIPOGRAFIA ===== */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-base: 1.5;
  --line-height-relaxed: 1.75;
  
  /* ===== ESPAÇAMENTOS ===== */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  
  /* ===== BORDAS ===== */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 0.75rem;  /* 12px */
  --border-radius-xl: 1rem;     /* 16px */
  --border-radius-full: 9999px;
  
  --border-width: 1px;
  --border-width-thick: 2px;
  
  /* ===== SOMBRAS ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* ===== TRANSIÇÕES ===== */
  --transition-fast: 0.15s ease-out;
  --transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* ===== Z-INDEX ===== */
  --z-dropdown: 10;
  --z-sticky: 20;
  --z-fixed: 30;
  --z-modal-backdrop: 40;
  --z-modal: 50;
  --z-popover: 60;
  --z-tooltip: 70;
  --z-toast: 80;
  --z-max: 9999;
}

/* ===== MODO ESCURO ===== */
[data-theme="dark"] {
  /* ===== CORES PRIMÁRIAS ===== */
  --color-primary: #60a5fa;
  --color-primary-rgb: 96, 165, 250;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #2563eb;
  
  /* ===== CORES SECUNDÁRIAS ===== */
  --color-secondary: #10b981;
  --color-secondary-rgb: 16, 185, 129;
  --color-secondary-hover: #059669;
  --color-secondary-active: #047857;
  
  /* ===== CORES DE TEXTO ===== */
  --color-text: #f9fafb;
  --color-text-muted: #d1d5db;
  --color-text-light: #9ca3af;
  --color-text-inverse: #111827;
  
  /* ===== CORES DE BACKGROUND ===== */
  --color-bg: #111827;
  --color-surface: #1f2937;
  --color-surface-hover: #374151;
  --color-surface-active: #4b5563;
  
  /* ===== CORES DE BORDA ===== */
  --color-border: #4b5563;
  --color-border-hover: #6b7280;
  --color-border-active: #9ca3af;
  
  /* ===== CORES DE STATUS ===== */
  --color-success: #34d399;
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-error: #f87171;
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-warning: #fbbf24;
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-info: #60a5fa;
  --color-info-bg: rgba(59, 130, 246, 0.1);
  
  /* ===== SOMBRAS MODO ESCURO ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* ===== PREFERÊNCIA DO SISTEMA ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Aplicar tema escuro automaticamente se não há preferência definida */
    --color-primary: #60a5fa;
    --color-primary-rgb: 96, 165, 250;
    --color-text: #f9fafb;
    --color-text-muted: #d1d5db;
    --color-bg: #111827;
    --color-surface: #1f2937;
    --color-border: #4b5563;
  }
}

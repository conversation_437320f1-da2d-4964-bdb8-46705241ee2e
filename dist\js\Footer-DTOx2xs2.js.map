{"version": 3, "file": "Footer-DTOx2xs2.js", "sources": ["../../src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst Footer: React.FC = () => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <footer className=\"w-full flex items-center justify-center min-h-[200px]\">\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        whileInView={{ opacity: 1 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.6 }}\r\n        className=\"text-center\"\r\n      >\r\n        <div className=\"space-y-2 text-sm text-gray-700 dark:text-gray-300\">\r\n          <p>{t('footer.copyright')}</p>\r\n          <p className=\"font-medium\">{t('footer.title')}</p>\r\n        </div>\r\n      </motion.div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": ["Footer", "t", "useTranslation", "footer", "className", "motion", "div", "initial", "opacity", "whileInView", "viewport", "once", "transition", "duration", "children", "p"], "mappings": "+IAIA,MAAMA,EAAmB,KACjB,MAAAC,EAAEA,GAAMC,iBAGXC,SAAAA,CAAOC,UAAU,iEACfC,EAAAA,IAAAA,EAAOC,IAAG,CACTC,QAAS,CAAEC,QAAS,GACpBC,YAAa,CAAED,QAAS,GACxBE,SAAU,CAAEC,MAAM,GAClBC,WAAY,CAAEC,SAAU,IACxBT,UAAU,cAEVU,gBAACR,MAAAA,CAAIF,UAAU,qEACZW,IAAAA,UAAGd,EAAE,4BACLc,IAAAA,CAAEX,UAAU,uBAAeH,EAAE"}